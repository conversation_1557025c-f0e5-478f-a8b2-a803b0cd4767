---
type: "agent_requested"
description: "Frontend guidelines for working in the react frontend. Applies for the app folder."
---

The React frontend guidelines are declared in many markdown documents inside the app folder.
You are obligated to follow the coding standards, preferences, guidelines, and design for React with TypeScript, TailwindCSS, Bun, and TypeScript in the following files (path relative to `app` folder:

- .github/instructions/coding.instructions.md
- .github/instructions/design.instructions.md
- docs/design.json

## Verifying the work

When working in the app (frontend), execute `bunx tsc --noEmit` inside the `app` directory to verify there are no types errors. Fix any errors before proceeding.
