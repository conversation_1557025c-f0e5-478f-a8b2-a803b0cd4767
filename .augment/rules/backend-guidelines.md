---
type: "agent_requested"
description: "Backend guidelines for working in directus backend. Applies for the directus and docs folder."
---

### Migration files

Migration files are used to migrate the database from one state to another. They are located in the `directus/migrations` directory. They are executed in order, based on the filename. The filename must follow the pattern `YYYYMMDDA-migration-name.js`, where `YYYYMMDD` is the date of creation and `A` is a letter indicating the order of execution. The first migration file must have the letter `A`, the second `B`, and so on.
Two migrations can not have the same combination of date and letter. You must check it and ensure the keys `YYYYMMDDA` do not conflict with each other. The letter should be the next in the alphabet, based on the existing migrations.

### Seed script

The seed script is used to populate the database with initial data for development purposes. It is located in the `directus/src/seed/index.ts` file.
Any changes to the database structure must be accompanied by a change to the seed script to ensure the data is consistent.
If a table is dropped, the corresponding data must be removed from the seed script.
If a table is created, the seed script must be updated to include the new table.
If a table is altered, the seed script must be updated to reflect the changes.

### Documentation updates

Any changes in the database structure must be documented. The documents that MUST be update are:

- docs/001-ENTITIES.md
- docs/002-ENTITY_DIAGRAMS.md
- docs/DDL.md

The documents that may be relevant for updating are:

- docs/000-DESCRIPTION.md
- docs/003-BUSINESS_RULES_AND_CONSTRAINTS.md
