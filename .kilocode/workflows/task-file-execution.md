# Task File Execution Workflow

## 1. Description

Executes tasks from a TASKS file.

## 2. Arguments hint

file=[task-file], <id=[task-id]>

## 3. Instructions

The user provided the task file and optional task id.

Please implement the PENDING tasks specified in the file provide by the user by following the guidelines and instructions outlined in /tasks/INSTRUCTIONS.md.

First, read both files to understand:

1. The specific tasks that need to be implemented in the TASKS file
2. The implementation guidelines, coding standards, and procedures from INSTRUCTIONS.md

Then proceed to implement the tasks systematically, ensuring you:

- Follow all coding standards and conventions specified in the instructions
- Use the recommended tools and approaches outlined in the guidelines
- Break down complex tasks into manageable steps if needed
- Test your implementations as you go
- Ask for clarification if any task requirements are unclear or ambiguous

Begin by examining both files to understand the scope of work and implementation approach required.
