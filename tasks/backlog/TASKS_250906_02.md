# Task list info:

- name: 250906-02-rainfall-handling-integration
- base_branch: develop

---

# Task list context:

Implement rainfall handling for LIC devices with rain gauge. Process SystemStatusPackage.rainfall (accumulated last 24h, in mm after multiplying by rain_gauge_resolution_mm from property), SystemStatusPackage.raining (boolean), and property flags (rain_gauge_enabled, precipitation_volume_limit_mm, precipitation_suspended_duration_hours). Create current_rainfall_interval table (unique per device) and rainfall_interval history table (populated by triggers). Process in mqtt-integration status-package processor with modular algorithm for interval creation/update. Add frontend support for display on dashboard.

---

# Tasks

## Task 1. Create Directus migrations for current_rainfall_interval and rainfall_interval tables

**Description**
Create migrations similar to reservoir_state: A for current table, B for history hypertable, C for ID trigger, D for upsert trigger, E for Directus config. Include fields: start/end timestamps, millimeters, device (FK), property (FK nullable), date_created/updated, packet_date, precipitation_volume_limit_reached (boolean).

**Target directories**

- packages/directus/migrations (migration JS files)

**Status:** Pending

### Subtask 1.1. Create migration for current_rainfall_interval table

**Description**
Create 20250906A-create-current-rainfall-interval-table.js with schema: id (uuid, default gen_random_uuid()), device (uuid FK to device, unique), start_time/end_time (timestamp nullable), millimeters (decimal), property (uuid FK nullable), date_created/updated (timestamp default now()), packet_date (timestamp), precipitation_volume_limit_reached (boolean default false). Add comments and unique constraint on device.

**Target directories**

- packages/directus/migrations (new JS file)

**Status:** Pending

### Subtask 1.2. Create migration for rainfall_interval hypertable

**Description**
Create 20250906B-create-rainfall-interval-hypertable.js with schema: id (bigIncrements), device (uuid FK), start_time/end_time (timestamp nullable), millimeters (decimal), property (uuid FK nullable), date_created (timestamp default now()), packet_date (timestamp). Make hypertable on packet_date (monthly chunks), add compression policy (3 months, segment by device).

**Target directories**

- packages/directus/migrations (new JS file)

**Status:** Pending

### Subtask 1.3. Create set ID trigger migration

**Description**
Create 20250906C-rainfall-interval-set-id-trigger.js for BEFORE INSERT on current_rainfall_interval to set id = device.

**Target directories**

- packages/directus/migrations (new JS file)

**Status:** Pending

### Subtask 1.4. Create upsert trigger migration

**Description**
Create 20250906D-rainfall-interval-upsert-trigger.js with functions for INSERT/UPDATE to populate history, handle duplicates, update date_updated. Similar to reservoir_state.

**Target directories**

- packages/directus/migrations (new JS file)

**Status:** Pending

### Subtask 1.5. Create Directus config migration

**Description**
Create 20250906E-rainfall-interval-directus-config.js to add collections, fields, relations, permissions for both tables (read scoped by device/property ownership, create/update system-managed).

**Target directories**

- packages/directus/migrations (new JS file)

**Status:** Pending

### Subtask 1.6. Create tests for triggers

**Description**
Add integration tests in packages/directus/tests to verify triggers: insert/update current, check history population, unique per device, limit reached flag.

**Target directories**

- packages/directus/tests (test files)

**Status:** Pending

## Task 2. Implement MQTT-integration changes for rainfall processing

**Description**
Add DB queries, mutations, types for rainfall intervals. Create modular rainfall processor in status-package.ts, elaborate algorithm: if rain_gauge_enabled, calculate mm = rainfall \* resolution; if raining and no current interval, start new (start=packet_date, mm=0); update mm if interval exists; if mm >= limit, set flag true, pause irrigation (integrate with project state?); end interval if not raining and was raining.

**Target directories**

- packages/mqtt-integration/src/db (queries/mutations)
- packages/mqtt-integration/src/irriganet/package-processors/default-processors (processor files)
- packages/mqtt-integration/src/types (TS types)

**Status:** Pending

### Subtask 2.1. Define types for rainfall intervals

**Description**
Add TS interfaces for CurrentRainfallInterval and RainfallInterval in relevant types file, matching DB schema.

**Target directories**

- packages/mqtt-integration/src/types (or irriganet/types.ts)

**Status:** Pending

### Subtask 2.2. Create DB queries and mutations

**Description**
Implement getCurrentRainfallIntervalByDevice, upsertCurrentRainfallInterval, getRainfallIntervalsByDevice (for history) using postgres client.

**Target directories**

- packages/mqtt-integration/src/db/queries (query functions)
- packages/mqtt-integration/src/db/mutations (mutation functions)

**Status:** Pending

### Subtask 2.3. Implement rainfall interval processor module

**Description**
Create separate rainfallIntervalProcessor.ts with algorithm: fetch property config via licTree, if enabled and has_rainfall, calc mm, check/create/update current interval, handle start/end based on raining, set limit reached if applicable.

**Target directories**

- packages/mqtt-integration/src/irriganet/package-processors/default-processors (new TS file)

**Status:** Pending

### Subtask 2.4. Update processStatusPackage to include rainfall handling

**Description**
In status-package.ts, after project state calc, call rainfallIntervalProcessor with statusData, ctx, packetDate. Handle errors/logging.

**Target directories**

- packages/mqtt-integration/src/irriganet/package-processors/default-processors/status-package.ts

**Status:** Pending

## Task 3. Implement Frontend changes for rainfall intervals

**Description**
Add model types, update API schema, create CRUD services, Jotai atoms with polling, RainfallIntervalCard UI component following guidelines, integrate into Dashboard.

**Target directories**

- packages/app/src/api/model (TS types)
- packages/app/src/api (client schema)
- packages/app/src/api/services (CRUD)
- packages/app/src/store (Jotai atoms)
- packages/app/src/components (UI card)
- packages/app/src/pages/main (Dashboard integration)

**Status:** Pending

### Subtask 3.1. Update model types for rainfall intervals

**Description**
Add RainfallInterval and CurrentRainfallInterval interfaces in model/rainfall-interval.ts or extend existing, with relations to device/property.

**Target directories**

- packages/app/src/api/model (new or existing TS file)

**Status:** Pending

### Subtask 3.2. Update API client schema

**Description**
Extend GraphQL schema/queries/mutations for rainfall_interval and current_rainfall_interval (read current by device, list history, etc.).

**Target directories**

- packages/app/src/api (schema files)

**Status:** Pending

### Subtask 3.3. Create CRUD services

**Description**
Implement services for fetching current/history intervals by device/property, using Directus SDK.

**Target directories**

- packages/app/src/api/services (new TS file for rainfall)

**Status:** Pending

### Subtask 3.4. Create Jotai atoms with polling

**Description**
Add atoms for current rainfall interval, history list, with polling (e.g., every 5min) based on device ID.

**Target directories**

- packages/app/src/store (Jotai atoms)

**Status:** Pending

### Subtask 3.5. Create RainfallIntervalCard component

**Description**
Build UI card displaying start/end, mm, raining status, limit reached, following Tailwind/Jotai patterns and guidelines in docs/guidelines/frontend.

**Target directories**

- packages/app/src/components (new card TSX)

**Status:** Pending

### Subtask 3.6. Integrate RainfallIntervalCard into Dashboard

**Description**
Add card to dashboard layout, using atoms for data, conditional render if rain gauge enabled.

**Target directories**

- packages/app/src/pages/main/components/Dashboard (or relevant file)

**Status:** Pending
