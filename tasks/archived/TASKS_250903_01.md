# Task list info:

- name: 250903_01
- base_branch: develop

---

# Tasks

## Task 1. lic_state_history table population test

**Description**
Write tests to verify the automatic population of the lic_state_history table from the lic_state table via database triggers. The tests should cover:

- INSERT operation on lic_state creates a new record in lic_state_history with state_date = date_created
- UPDATE operation on lic_state updates the corresponding record in lic_state_history with state_date = date_updated
- Data consistency between lic_state and lic_state_history after multiple INSERT/UPDATE operations

Relevant files:

- packages/directus/migrations/20250903A-create-lic-state-history-table.js
- packages/directus/migrations/20250903B-create-lic-state-history-hypertable.js
- packages/directus/migrations/20250903C-lic-state-history-trigger.js
- docs/DDL.md

The folder packages/directus/tests/helpers contains helper functions to create test data and query the database.
If any new helper functions are needed, add them to packages/directus/tests/helpers/fixtures.ts.
The test file to be created should be packages/directus/tests/lic_state_history.test.ts.
Use /home/<USER>/geocontrol/repositorios/agro/irriga-mais/packages/directus/tests/mesh_constraints.test.ts as reference.

Create a new database before running the tests in the test file:

- From the packages/directus folder, run `bun run docker:database:create-db lic_state_history_test --recreate`. Execute the process programatically from the lic_state_history.test.ts file using beforeAll hook. Make sure the process CWD is packages/directus, as the docker-compose.yml file is located there. Await the execution of the command.
- set process.env.TEST_DB_DATABASE to lic_state_history_test in the beforeAll hook.
- Write a test to verify the creation of the database by selecting the database name using the knex instance created in the test file.

**Target directories**

- packages/directus/tests (backend test code)

**Status:** Done
