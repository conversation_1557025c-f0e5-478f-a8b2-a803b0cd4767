# Instructions for executing a task

- Mark its status as "In Progress" before starting to work on it.
- Create a branch for the task. The branch name must start with the task list name, followed by a hiphen, followed by the task number and followed by a hyphen and a descriptive name. For example: `250729-1-property-address-required-fields`.
- Execute the task
- Mark its status as "Done" when it is finished.
- Commit the changes with a message following the Conventional Commits format. For example: `chore: 250729-1-property-address-required-fields`
- merge the branch into develop

> Task list name can be found in the task list info below.

> Task status can be: Pending, In Progress, Done, Canceled and Error.

# Guidelines

Guidelines are defined in the following files:

- /app/.github/instructions/react.instructions.md
- /app/.github/instructions/coding.instructions.md
- /app/.github/instructions/design.instructions.md
- /app/.github/instructions/tech-stack.instructions.md
- /app/docs/design.json

---

# Task list info:

- name: 250729-1

---

# Tasks

## Task 1. Property address required fields

**Description**
The required fields are city, state, country and postal code. This is for the frontend only. The migration remain as is.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 2. Hide backwash_delay_seconds in the UI

**Description**
The field will only be used in the future.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 3. use a wizard for Creating/updating properties :

**Description**

- Step 1: Name, timezone, point
- Step 2: Address
- Step 3: Backwash
- Step 4: Rain gauge
- Step 5: Review and create/update

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 4. Device identifier input requirements:

**Description**

- Field should be capitalized automatically
- identifier of devices of type LIC have 12 hex characters
- Other devices have 6 hex characters

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 5. Water pump flow rate

**Description**
Although the PumpDetailModal component has a field for flow rate ("Vazão"), it is not being used because the information is not being saved in the database.
Let's create a new nullable field in the database to store this information.
The unit of measure is liters per hour (L/h).
Let's update any models and types in the frontend to align with it.
Let's update the PumpDetailModal component to save the information in the database.
Let's document it.

**Target directories**

- app (frontend)
- directus (backend)
- docs (documentation)

**Status:** Done

---
