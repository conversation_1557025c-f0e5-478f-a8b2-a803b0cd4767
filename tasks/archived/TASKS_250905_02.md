# Task list info:

- name: 250905-02-signup
- base_branch: develop

---

# Task list context:

Implement user Sign Up in Directus.

- Backend

We need a migration to add cpf and phone_number fields to the users collection in Directus "directus_users".
We need a migration to configure the new fields in Directus, including permissions.

- Frontend

  - The frontend page and form validation are already implemented.
  - The signup in the api service is already implemented. We need to use it in the SignUpPage.
  - After signup, the user will receive a verification email. At this moment, the user will be redirected to a "check your email" page. This page will have a simple design, following the same style as the LoginPage, and a link to the login page.
  - The email verification is enabled in Directus. We need to implement the verification page (or route), which will receive the token via URL query params from the email link and complete the verification by calling the appropriate api service method. The emailed link includes a token, which is then used to activate the registered user. The email link will look like this: http://localhost:3000/#/verify-email?token=1234567890

- Other Considerations:

1. **Email Verification Page:**

   - Should handle both success and error states
   - Include a option to resend verification email
   - Consider auto-redirect to login after successful verification

2. **Additional Features:**

   - Add loading states during form submission
   - Implement form resubmission prevention

3. **Error Handling:**

   - Network error handling in frontend
   - Database error handling in migrations
   - Validation error display in forms

4. **User Experience:**

   - Success message after verification
   - Loading indicators
   - Error message display

5. **Security:**
   - Token expiration handling
   - Input sanitization for new fields

- Documentation:
  - Update DDL documentation (/docs/DDL.md) with the new fields and constraints
  - Update feature documentation to reflect the new signup and verification flow

---

# Tasks

## Task 1.1: Backend Database Migrations

**Description**
Create Directus migrations for user fields (CPF and phone_number)

- Create migration to add `cpf` and `phone_number` fields to `directus_users` collection

**Target directories**

- packages/directus (backend)

**Status:** Done

---

## Task 1.2: Directus Field Configuration and Permissions

**Description**
Migration to Configure Directus fields and permissions for the new directus_users collection fields

**Target directories**

- packages/directus (backend)

**Status:** Done

---

## Task 1.3: Frontend Signup Integration

**Description**
Integrate existing signup form with API service

- Connect the existing SignUpPage to the API service signup method (apiService.auth.register)
- Implement loading states during form submission
- Add form resubmission prevention
- Handle network errors and display appropriate messages

**Target directories**

- packages/app (frontend)

**Status:** Done

---

## Task 1.4: Email Verification Flow Implementation

**Description**
Implement email verification page and flow

- Create "Check your email" page after signup (following LoginPage style)
- Implement email verification page that handles token from URL query params
- Add verification API call with success/error handling
- Implement resend verification email functionality
- Add auto-redirect to login after successful verification

**Target directories**

- packages/app (frontend)

**Status:** Done

---

## Task 1.5: Documentation and Final Integration

**Description**
Update documentation and finalize integration

- Update DDL documentation with new fields and constraints
- Update feature documentation with signup and verification flow
- Add security considerations (token expiration, input sanitization)
- Final testing of complete signup flow
- Code review and cleanup

**Target directories**

- docs (documentation)
- packages/app (frontend)
- packages/directus (backend)

**Status:** Done
