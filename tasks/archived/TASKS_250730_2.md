# Task list info:

- name: 250730-2
- base_branch: develop

---

# Tasks

## Task 1. IrrigationPlanStepsPanel2 / ChooseSectorsDialog: sector choosing not working as expected.

**Description**
When choosing sectors and clicking save, the steps with deselected sectors get deleted but the new steps are not created.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 2. IrrigationPlanStepsPanel2 / BulkActionBar: edit many not working as expected.

**Description**
When editing multiple steps and clicking save, the changes are not applied to the steps.

**Target directories**

- app (frontend)

**Status:** Done

---

# Task 3. IrrigationPlanStepsPanel2 / ChooseSectorsDialog: Sector clicking behavior not working as expected.

**Description**
When the user clicks on a sector, the sector is correctly selected, But if the user clicks the checkbox, the sector is not selected.
The expected behavior is: when the user clicks on any region of the sector card, including the checkbox, the checkbox is toggled.

**Target directories**

- app (frontend)

**Status:** Done

---

# Task 4. StepList - Check / uncheck all steps

**Description**
Add a way to check/uncheck all steps in the list in the StepList component. Follow best UI/UX practices. Choose the best approach.

**Target directories**

- app (frontend)

**Status:** Done

---

# Task 5. ChooseSectorsDialog - Check / uncheck all steps

**Description**
Add a way to check/uncheck all steps in the list in the ChooseSectorsDialog component. Follow best UI/UX practices. Choose the best approach.

**Target directories**

- app (frontend)

**Status:** Done

---

# Task 6. IrrigationPlanStepsPanel2 - step reordering when deleting or adding steps

**Description**
When deleting or adding steps (via ChooseSectorsDialog) , the step order is not being recalculated correctly.
Let's say we have the following steps:
[ A, C, B, D, F ]
Their order will be:
A - 1
C - 2
B - 3
D - 4
F - 5

The expected behavior is:

If we delete step C, the step array becomes [ A, B, D, F ], and the order will be:
A - 1
B - 2
D - 3
F - 4

If we add a new step, lets say E, the step array becomes [ A, B, D, F, E ], and the order will be:
A - 1
B - 2
D - 3
F - 4
E - 5

if we re-add C, the step array becomes [ A, B, D, F, E, C ], and the order will be:
A - 1
B - 2
D - 3
F - 4
E - 5
C - 6

if we delete B, F and E, the step array becomes [ A, D, C ], and the order will be:
A - 1
D - 2
C - 3

if we add F, H and K, the step array becomes [ A, D, C, F, H, K ], and the order will be:
A - 1
D - 2
C - 3
F - 4
H - 5
K - 6

---

Thus, adding steps (via ChooseSectorsDialog) always add the new steps to the end of the list. The new order of steps after deleting or adding steps must be recalculated and persisted, using the index of the steps in the array plus one. Only the steps that had its order changed must be persisted.

**Target directories**

- app (frontend)

**Status:** Done

---
