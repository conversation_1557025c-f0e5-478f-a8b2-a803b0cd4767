# Task list info:

- name: 250730-1
- base_branch: develop

---

# Tasks

## Task 1. Deconstruct complex React component (app/src/pages/main/components/IrrigationPlanStepsPanel2.tsx) into modular, maintainable structure.

**Description**

# Proposed Component Structure

## Folder Structure

```
app/src/pages/main/components/
├── IrrigationPlanStepsPanel2/
│   ├── index.tsx                          # Main orchestrating component
│   ├── components/
│   │   ├── SummarySection/
│   │   │   ├── index.tsx
│   │   │   └── SummarySection.module.css  # Optional: component-specific styles
│   │   ├── StepCard/
│   │   │   ├── index.tsx
│   │   │   └── StepCard.module.css
│   │   ├── StepList/
│   │   │   └── index.tsx                  # New: Manages the list of steps
│   │   ├── BulkActionBar/
│   │   │   └── index.tsx                  # New: Handles bulk operations UI
│   │   └── dialogs/
│   │       ├── ChooseSectorsDialog/
│   │       │   └── index.tsx
│   │       ├── StepEditDialog/
│   │       │   └── index.tsx
│   │       └── StepEditManyDialog/
│   │           └── index.tsx
│   ├── hooks/
│   │   ├── useStepSelection.ts            # Manages step selection state
│   │   ├── useStepActions.ts              # Handles step CRUD operations
│   │   └── useSectorManagement.ts         # Manages sector selection logic
│   ├── types/
│   │   └── index.ts                       # All TypeScript interfaces
│   └── utils/
│       ├── formatters.ts                  # Duration formatting utilities
│       └── stepHelpers.ts                 # Step manipulation utilities
```

## Component Breakdown

### 1. **Main Component** (`index.tsx`)

**Responsibility**: Orchestration, state management, and layout

- Manages overall state
- Handles callbacks from child components
- Provides layout structure

### 2. **SummarySection** (`components/SummarySection/index.tsx`)

**Responsibility**: Display plan overview and statistics

- Shows total duration, step count, available sectors
- Pure presentation component

### 3. **StepList** (`components/StepList/index.tsx`) - _New Component_

**Responsibility**: Manages the list of step cards

- Renders list of StepCard components
- Handles empty state
- Manages list-level interactions

### 4. **StepCard** (`components/StepCard/index.tsx`)

**Responsibility**: Individual step display and basic interactions

- Shows step details
- Handles move up/down, edit, delete actions
- Checkbox selection

### 5. **BulkActionBar** (`components/BulkActionBar/index.tsx`) - _New Component_

**Responsibility**: Bulk operation controls

- Shows when steps are selected
- Edit/Delete selected buttons
- Selection count display

### 6. **Dialog Components**

Each dialog becomes its own component with single responsibility:

#### **ChooseSectorsDialog** (`components/dialogs/ChooseSectorsDialog/index.tsx`)

- Sector selection with search

#### **StepEditDialog** (`components/dialogs/StepEditDialog/index.tsx`)

- Single step editing

#### **StepEditManyDialog** (`components/dialogs/StepEditManyDialog/index.tsx`)

- Bulk step editing

## Custom Hooks

### 1. **useStepSelection** (`hooks/useStepSelection.ts`)

```typescript
// Manages which steps are selected for bulk operations
const useStepSelection = () => {
  const [selectedSteps, setSelectedSteps] = useState<Set<string>>(new Set());

  const toggleStepSelect = useCallback((stepId: string) => {
    /* ... */
  });
  const clearSelection = useCallback(() => {
    /* ... */
  });
  const selectAll = useCallback((stepIds: string[]) => {
    /* ... */
  });

  return { selectedSteps, toggleStepSelect, clearSelection, selectAll };
};
```

### 2. **useStepActions** (`hooks/useStepActions.ts`)

```typescript
// Handles all step CRUD operations
const useStepActions = (onUpdateStep, onRemoveStep, onSaveStep) => {
  const handleStepEdit = useCallback((stepId: string) => {
    /* ... */
  });
  const handleStepDelete = useCallback((stepId: string) => {
    /* ... */
  });
  const handleBulkEdit = useCallback((updates) => {
    /* ... */
  });
  const handleBulkDelete = useCallback((stepIds) => {
    /* ... */
  });

  return { handleStepEdit, handleStepDelete, handleBulkEdit, handleBulkDelete };
};
```

### 3. **useSectorManagement** (`hooks/useSectorManagement.ts`)

```typescript
// Manages sector selection and step creation/removal logic
const useSectorManagement = (steps, sectors, planData, callbacks) => {
  const selectedSectorIds = useMemo(
    () => steps.map((step) => step.sectorId),
    [steps]
  );

  const handleSectorsChange = useCallback((sectorIds: string[]) => {
    /* ... */
  });

  return { selectedSectorIds, handleSectorsChange };
};
```

## Utility Functions

### **formatters.ts** (`utils/formatters.ts`)

```typescript
export const formatDuration = (seconds: number): string => {
  /* ... */
};
export const formatMinutesToReadable = (minutes: number): string => {
  /* ... */
};
```

### **stepHelpers.ts** (`utils/stepHelpers.ts`)

```typescript
export const createNewStep = (
  sector: Sector,
  planData: IrrigationPlanFormData
): IrrigationPlanStepFormData => {
  /* ... */
};
export const calculateTotalDuration = (
  steps: IrrigationPlanStepFormData[]
): number => {
  /* ... */
};
```

## Types File (`types/index.ts`)

```typescript
// All interfaces and types used across components
export interface Sector {
  /* ... */
}
export interface IrrigationPlanStepsPanel2Props {
  /* ... */
}
export interface SummarySectionProps {
  /* ... */
}
// ... etc
```

## Benefits of This Structure

1. **Single Responsibility**: Each component has one clear purpose
2. **Reusability**: Components can be easily reused in other contexts
3. **Testability**: Smaller components are easier to unit test
4. **Maintainability**: Changes are isolated to specific components
5. **Readability**: Code is easier to understand and navigate
6. **Performance**: Smaller components can be optimized individually
7. **Collaboration**: Multiple developers can work on different components simultaneously

## Migration Strategy

1. Start with extracting utility functions and types
2. Extract custom hooks to manage state logic
3. Extract dialog components (they're most self-contained)
4. Extract SummarySection and StepCard
5. Create StepList and BulkActionBar components
6. Refactor main component to use the new structure

This approach maintains the same functionality while making the codebase much more maintainable and scalable.

**Target directories**

- app (frontend)

**Status:** Done

---
