# Task list info:

- name: 250825_02
- base_branch: develop

---

# Tasks

## Task 1. current_lic_packet support in the react app

**Description**
Add support for retrieving current_lic_packet in the React app:

- add the model in app/src/models/current-lic-packet.ts
- add the collection in app/src/api/client.ts
- add CRUD in app/src/api/service.ts
- add jotai store to store latest current_lic_packet's per device in app/src/store/data.ts (There can be many current_lic_packet's per device, one for each payload_type, all of them should be loaded and stored in the store)
- add query in app/src/api/queries/current-lic-packet.ts to load the current_lic_packet's for all lic devices in the selected property.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 1.1. Create current_lic_packet model

**Description**
Create TypeScript model definition following existing patterns in `packages/app/src/api/model/`

**Target directories**

- packages/app/src/api/model/ (frontend)

**Status:** Done

### Subtask 1.2. Add current_lic_packet to client schema

**Description**
Add the collection to AppSchema interface in client.ts following existing pattern

**Target directories**

- packages/app/src/api/ (frontend)

**Status:** Done

### Subtask 1.3. Add CRUD operations to API service

**Description**
Add CRUD methods to apiService following the existing pattern used for other collections

**Target directories**

- packages/app/src/api/ (frontend)

**Status:** Done

### Subtask 1.4. Add Jotai store for current_lic_packet data

**Description**
Create atoms in data.ts to store latest packets per device/payload_type, following existing data storage patterns

**Target directories**

- packages/app/src/store/ (frontend)

**Status:** Done

### Subtask 1.5. Create specialized query for property LIC packets

**Description**
Create query module to load current_lic_packet data for all LIC devices in selected property

**Target directories**

- packages/app/src/api/queries/ (frontend)

**Status:** Done

---

## Task 2. polling current_lic_packet's for all LIC devices in the selected property of the React app

**Description**
Poll the current_lic_packet's for all LIC devices in the selected property periodically. The interval should be configurable, but a reasonable default is 5 seconds.
Create a dedicated service to handle the polling and querying of the current_lic_packet's.

**Target directories**

- app (frontend)

**Status:** Done

## Task 3. Create a react hook for LIC communication testing

**Description**
Create a React hook called `useLICCommunicationTest` that receives a LIC device ID as a parameter and tests communication with the device by implementing a two-phase polling mechanism.

The hook must also receive:

- a optional polling interval in milliseconds defaulting is 2000 ms.
- a optional timeout in milliseconds, defaulting to 30000 ms.

##### Phase 1: Device Message Request Creation and Polling

1. Create a device_message_request using `apiService.deviceMessageRequest.create()` from `packages/app/src/api/service.ts` with a "request_info" message type
2. Poll the created device_message_request using `apiService.deviceMessageRequest.getOne(id)` where `id` is the ID returned from the create method
3. Continue polling until the status changes from initial state
4. Handle the following status transitions:
   - If status becomes "failed", "expired", or "cancelled": stop polling and return error state
   - If status becomes "acknowledged": stop polling and return success state (skip Phase 2)
   - If status becomes "sent": proceed to Phase 2

##### Phase 2: Current LIC Packet Polling (only if Phase 1 status is "sent")

1. Use `loadCurrentLICPacketForDeviceAndType` query from `packages/app/src/api/queries/current-lic-packet.ts`
2. Poll for current_lic_packet with payload_type "info"
3. Continue polling until receiving a packet where `packet_date > device_message_request.date_created`
4. When this condition is met, return "acknowledged" status

##### Hook Return Value

The hook should return an object containing:

- `status`: Current communication status (one of: "created", "pending", "sent", "failed", "acknowledged")
- `error`: Error information if status is "failed"
- Any other relevant state for the caller

##### Status Definitions

- "created": Initial state after device_message_request is created
- "pending": device_message_request status is "pending" or "processing"
- "sent": device_message_request status is "sent" (transitioning to Phase 2)
- "failed": device_message_request status is "failed", "expired", or "cancelled"
- "acknowledged": Either device_message_request status is "acknowledged" OR valid info packet received in Phase 2

##### Implementation Requirements

- Use proper cleanup to stop polling when component unmounts
- Implement appropriate polling intervals (suggest reasonable defaults)
- Provide status change notifications to the caller
- Handle error cases gracefully
- Follow React hooks best practices for state management and side effects

**Target directories**

- packages/app/src/hooks/ (frontend)

**Status:** Done

### Subtask 3.1. Hook Structure and Interface Setup

**Description**
Create the hook file structure, TypeScript interfaces, and basic hook skeleton with proper parameter handling and return type definition

**Target directories**

- packages/app/src/hooks/ (frontend)

**Status:** Done

### Subtask 3.2. Phase 1 - Device Message Request Logic

**Description**
Implement device message request creation and polling logic, including status transition handling and Phase 1 completion detection

**Target directories**

- packages/app/src/hooks/ (frontend)

**Status:** Done

### Subtask 3.3. Phase 2 - Current LIC Packet Polling

**Description**
Implement current LIC packet polling with date comparison logic and "acknowledged" status detection

**Target directories**

- packages/app/src/hooks/ (frontend)

**Status:** Done

### Subtask 3.4. State Management and Status Transitions

**Description**
Implement comprehensive state management covering all status transitions, error states, and proper state synchronization between phases

**Target directories**

- packages/app/src/hooks/ (frontend)

**Status:** Done

### Subtask 3.5. Cleanup, Error Handling, and Testing

**Description**
Implement proper cleanup mechanisms, timeout handling, comprehensive error management, and create basic usage verification

**Target directories**

- packages/app/src/hooks/ (frontend)

**Status:** Done
