# Task list info:

- name: 250905_end_time_fix
- base_branch: develop

---

# Task list context:

Fix invariant for automation cycle end_time so it is set once per cycle (same start_time) and never moved by subsequent packets; only resets when a new cycle begins (start_time changes). Apply to both reservoir_state and irrigation_plan_state. Enforce primarily at DB triggers; align application upserts; extend tests and docs.

---

# Tasks

## Task 1. Database trigger logic updates (authoritative behavior)

**Description**
Add new migrations that replace the existing upsert/update trigger functions for current_reservoir_state and current_irrigation_plan_state, enforcing:

- For packets within the same cycle (NEW.start_time equals existing.start_time): do not replace current row; if existing.end_time is NULL and NEW.end_time IS NOT NULL, update current row to set end_time once; otherwise ignore, but always insert a history row.
- For a new cycle (NEW.start_time differs): replace current row (delete + insert new), and continue to insert history rows as today.

Do not edit past migrations; add forward migrations that drop/recreate only the trigger functions, preserving table data.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.1. Reservoir: add migration to replace upsert/update trigger functions

**Description**
Create `packages/directus/migrations/20250905A-update-reservoir-state-upsert-logic.js` to redefine:

- handle_reservoir_state_upsert()
- handle_reservoir_state_update()
- keep set_reservoir_state_id() and date_updated trigger as-is
  with start_time-aware logic and one-time end_time setting.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.2. Irrigation plan: add migration to replace upsert/update trigger functions

**Description**
Create `packages/directus/migrations/20250905B-update-irrigation-plan-state-upsert-logic.js` to redefine:

- handle_irrigation_plan_state_upsert()
- handle_irrigation_plan_state_update()
  with the same start_time-aware logic and one-time end_time setting.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.3. DDL docs: clarify invariant and trigger behavior

**Description**
Update `docs/DDL.md` trigger sections for both entities to state: end_time is set at most once per cycle (same start_time) and does not move; resets only when start_time changes.

**Target directories**

- docs (documentation)

**Status:** In Progress

## Task 2. Application upserts: guard end_time updates

**Description**
Align upsert statements to avoid moving end_time within the same cycle. Use CASE expression so end_time remains COALESCE(current.end_time, EXCLUDED.end_time) when start_time matches; otherwise (new cycle) accept EXCLUDED.end_time.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.1. Reservoir current state upsert CASE logic

**Description**
Update `packages/mqtt-integration/src/db/mutations/reservoir-state.ts` upsert to conditionally set end_time using CASE + comparison on start_time.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.2. Irrigation plan current state upsert CASE logic

**Description**
Update `packages/mqtt-integration/src/db/mutations/irrigation-plan-state.ts` upsert to apply the same conditional end_time logic.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

## Task 3. Tests: invariant coverage

**Description**
Extend Directus DB tests to cover the invariant for both entities; ensure history still records every packet while current respects one-time end_time setting per cycle.

**Target directories**

- directus (backend tests)

**Status:** Done

### Subtask 3.1. Reservoir: current end_time does not move within same start_time

**Description**
Add test cases to `packages/directus/tests/reservoir_state_triggers.test.ts`:

- Insert first packet (status=1, no end_time provided) that sets end_time to packet_date once; subsequent packets with same start_time and status=1 must not change end_time.
- When start_time changes, end_time resets (null) and can be set once again.

**Target directories**

- directus (backend tests)

**Status:** Done

### Subtask 3.2. Irrigation plan: mirror invariant tests

**Description**
Add equivalent tests to `packages/directus/tests/irrigation_plan_state_triggers.test.ts` validating same-cycle end_time stability and new-cycle reset.

**Target directories**

- directus (backend tests)

**Status:** Done

## Task 4. Validation and type checks

**Description**

- Run `bunx tsc --noEmit` in `packages/directus` and `packages/mqtt-integration`.
- Execute relevant Directus DB tests to validate trigger behavior.

**Target directories**

- directus (backend)
- mqtt-integration (backend)

**Status:** Done
