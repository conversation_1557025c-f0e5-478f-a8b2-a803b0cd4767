# Task list info:

- name: 250801-button-replacement
- base_branch: develop

---

# Task list context:

Replace raw HTML <button> usages across app/src with the shared Button component (app/src/components/ui/Button.tsx). Prioritize user-facing pages and shared components. Preserve behavior (types, sizes, variants, disabled, loading, icons). Verify types with bunx tsc --noEmit under /app.

---

# Tasks

## Task 1. Replace buttons in shared components

**Description**
Refactor shared components to use Button: ConfirmModal, Toast, ErrorToast, PWAInstallBanner, Tabs.

**Target directories**

- app/src/components (shared UI components)

**Status:** Done

## Task 2. Replace buttons in navigation and layout components

**Description**
Refactor BottomTabNavigation and Header (skip commented code) to use Button, preserving styles and interactions.

**Target directories**

- app/src/pages/main/components (navigation/layout)

**Status:** Done

## Task 3. Replace buttons in Settings and panels

**Description**
Refactor Settings, SectorsPanel, SectorDetailPanel, ProjectConfigPanel to use But<PERSON>. Ensure toggle/special controls that are not semantic buttons remain as divs/inputs if needed.

**Target directories**

**Status:** Done

## Task 4. Replace buttons in modals and selection flows

**Description**
Refactor AssignToLICModal, SelectDeviceForLICModal, ManageNetworkModal, DeviceDetailModal to use Button.

**Target directories**

**Status:** Done

## Task 5. Replace buttons in Irrigation Plan pages (v1 panel)

**Description**
Refactor IrrigationPlanConfigPanel and IrrigationPlanStepsPanel to use Button.

**Target directories**

- app/src/pages/main/components (irrigation plan components)

**Status:** Done

## Task 6. Replace buttons in Irrigation Plan pages (v2 panel)

**Description**
Refactor IrrigationPlanStepsPanel2 subcomponents: BulkActionBar, StepCard, dialogs (ChooseSectorsDialog, StepEditDialog, StepEditManyDialog, StepReorderAssistant) to use Button.

**Target directories**

- app/src/pages/main/components/IrrigationPlanStepsPanel2 (and subfolders)

**Status:** Done

## Task 7. Replace buttons in pages: plans and wizard

**Description**
Refactor IrrigationPlansPage, IrrigationPlanWizardPage, IrrigationPlanPage to use Button.

**Target directories**

- app/src/pages/main (plan pages)

**Status:** Done

## Task 8. Replace buttons in Project and Water Pump pages

**Description**
Refactor ProjectsPage, ProjectDetailPage, ProjectConfigPage, WaterPumpsPage to use Button.

**Target directories**

- app/src/pages/main (project/pump pages)

**Status:** Done

## Task 9. Replace buttons in Property pages

**Description**
Refactor PropertyDetailsPage and NoPropertyPage to use Button.

**Target directories**

- app/src/pages (property pages)

**Status:** Done

## Task 10. Replace remaining buttons and providers

**Description**
Refactor store/Provider.tsx and any remaining raw button instances to use Button. Skip instances where native button semantics are misused for toggles/links; convert appropriately.

**Target directories**

- app/src/store
- app/src (catch-all)

**Status:** Done

## Task 11. Verification and cleanup

**Description**
Run typecheck in app: bunx tsc --noEmit. Fix type/style issues. Ensure Button props cover replaced usages; extend Button if necessary without breaking API. Smoke-test main flows.

**Target directories**

- app/

**Status:** Done
