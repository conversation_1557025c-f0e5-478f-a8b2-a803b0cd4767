# Task list info:

- name: 250904_reservoir_automation
- base_branch: develop

---

# Task list context:

Reservoir Automation Handling with historical tracking:

- migrations
- trigger tests
- mqtt-integration implementation
- frontend implementation

---

# Tasks

## Task 1. Database tables and directus collections configuration

**Description**
Very similar to irrigation_plan, which has current_irrigation_plan_state and irrigation_plan_state tables, we need to create current_automation_state and automation_state tables.
An automation refers to the automatic activation of a pump based on a sensor input in a reservoir.
Thus, the table names will be current_reservoir_state and reservoir_state.
current_reservoir_state will have the following fields:

- id (uuid, auto-set to reservoir)
- reservoir (uuid FK, unique)
- packet_date (timestamptz, when the original device automation_report packet was recorded)
- start_time (timestamptz) - when the automation started - the pump was turned on
- restart_time (timestamptz) - when the automation was restarted - the pump was turned off and then on again. This can happen if the pump was turned off manually or by the safety time, for example, and the reservoir is still low.
- end_time (nullable) - when the automation ended - the pump was turned off
- date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
- date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,

reservoir_state will be a TimescaleDB hypertable with the same fields as current_reservoir_state, plus a composite primary key (reservoir, packet_date).
It will be populated from current_reservoir_state through database triggers AFTER INSERT and UPDATE operations on current_reservoir_state. It is exactly the same pattern as current_irrigation_plan_state and irrigation_plan_state. Including indexes , compression policies and conflict resolution.

We need to create the migrations and directus configuration for the new tables (see ppackages/directus/migrations/20250903E-create-current-irrigation-plan-state-table.js
packages/directus/migrations/20250903F-create-irrigation-plan-state-hypertable.js
packages/directus/migrations/20250903G-irrigation-plan-state-set-id-trigger.js
packages/directus/migrations/20250903H-irrigation-plan-state-upsert-trigger.js
packages/directus/migrations/20250903I-irrigation-plan-state-directus-config.js)

We need to create test to ensure the triggers are working correctly (see packages/directus/tests/irrigation_plan_state_triggers.test.ts)

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.1. Create current_reservoir_state table migration

**Description**
Create migration to define `current_reservoir_state` table mirroring `current_irrigation_plan_state` fields and conventions. Include columns: `id` (uuid, default to `reservoir`), `reservoir` (uuid FK unique), `packet_date` (timestamptz), `start_time` (timestamptz), `restart_time` (timestamptz), `end_time` (nullable), `date_created` (timestamptz default now), `date_updated` (timestamptz default now). Follow patterns in `20250903E-create-current-irrigation-plan-state-table.js`.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.2. Create reservoir_state hypertable migration

**Description**
Create hypertable `reservoir_state` with the same schema as `current_reservoir_state` plus composite primary key `(reservoir, packet_date)`. Add necessary indexes, compression, and retention policies following `20250903F-create-irrigation-plan-state-hypertable.js`.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.3. Implement triggers for ID and upsert into hypertable

**Description**
Add triggers/functions: (1) set `id = reservoir` on `current_reservoir_state` (like `20250903G`), and (2) upsert into `reservoir_state` on `AFTER INSERT/UPDATE` from `current_reservoir_state` with conflict handling mirroring `20250903H`.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.4. Directus collections and permissions configuration

**Description**
Add Directus configuration migrations to register collections/fields for `current_reservoir_state` and `reservoir_state`, and set read permissions consistent with similar state tables. Follow `20250903I-irrigation-plan-state-directus-config.js` patterns.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.5. Trigger tests for reservoir state

**Description**
Create tests ensuring triggers populate `reservoir_state` correctly on insert/update to `current_reservoir_state`. Model after `packages/directus/tests/irrigation_plan_state_triggers.test.ts`.

**Target directories**

- directus (backend)

**Status:** Done

## Task 2. MQTT Integration: automation report processing

**Description**
Just like scheduling_report is processed by packages/mqtt-integration/src/irriganet/package-processors/default-processors/scheduling-report-package.ts, we need to process automation_report messages.

The architecture is very similar:

- codec-manager receives the message and calls handleMessage(topic, payload)
- handleMessage calls packetProcessor.process(packet, context)
- default-package-processor.ts routes the message to the correct processor based on the payload type
- automation-report-package.ts (the new processor file) processes the message and stores the data in the database:

  - It will call a helper function to calculate the reservoir state from the automation report. see calculateIrrigationPlanState at packages/mqtt-integration/src/irriganet/irrigation-plan-state-calculator.ts for reference.
  - It will store the calculated state in the database using a mutation function. See packages/mqtt-integration/src/db/mutations/irrigation-plan-state.ts for reference.

- How to find out what reservoir the automation report belongs to?
  The automation report has a number auto_idx field. We need to find the LICState.devices with elementType === "reservoir" and elementVariation === auto_idx.toString(). This device has a elementId, which is the id of the reservoir.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.1. Add reservoir state calculator helper

**Description**
Create a helper to derive the reservoir automation state from `automation_report` packets, similar to `irriganet/irrigation-plan-state-calculator.ts`. Define inputs/outputs and edge cases (start, restart, end) aligned with DB schema.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.2. Implement automation-report package processor

**Description**
Create `irriganet/package-processors/default-processors/automation-report-package.ts` to parse payloads, resolve reservoir via `LICState.devices` where `elementType === "reservoir"` and `elementVariation === auto_idx.toString()`, call calculator, and persist via DB mutations.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.3. Add DB mutation for reservoir state

**Description**
Add mutations mirroring `db/mutations/irrigation-plan-state.ts` to upsert `current_reservoir_state` and let triggers write to `reservoir_state`. Update shared types if needed.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.4. Wire processor into default router

**Description**
Update `default-package-processor.ts` to route `automation_report` payloads to the new processor. Ensure `codec-manager` registration and type guards align with existing scheduling/status processors.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.5. Tests for automation report processing

**Description**
Add unit/integration tests to validate reservoir resolution, calculator outputs, and DB writes for typical flows (start, restart, end). Use existing DB/mqtt test helpers.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

## Task 3. Frontend integration

**Description**
We need to add support for the new current_reservoir_state and reservoir_state tables in the frontend. This will involve:

- Creating the TypeScript models for the new tables in app/src/api/model/ (see packages/app/src/api/model/current-irrigation-plan-state.ts and packages/app/src/api/model/irrigation-plan-state.ts for reference)
- Adding the new collections to AppSchema in app/src/api/client.ts
- Implementing CRUD operations in apiService.ts (see app/src/api/service.ts for reference)
- data fetching with polling and jotai store integration (see Current Irrigation Plan State atoms in packages/app/src/store/data.ts for reference)
- Update packages/app/src/pages/main/AppShell.tsx to just use the atoms to fetch the data (see the way it is done for current irrigation plan state)
- Create a ReservoirStateCard component to display the current state of the reservoir (see packages/app/src/components/ProjectStateCard.tsx for reference)
- Integrate the ReservoirStateCard in the DashboardPage (see packages/app/src/pages/main/DashboardPage.tsx for reference)

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 3.1. Add API models for reservoir states

**Description**
Create `src/api/model/current-reservoir-state.ts` and `src/api/model/reservoir-state.ts` mirroring irrigation plan state models. Export from `api/index.ts` if applicable.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 3.2. Extend AppSchema and client services

**Description**
Add the new collections to `src/api/client.ts` AppSchema and implement typed helpers in `src/api/service.ts` (or `src/api/services`) for fetching current state; follow existing patterns.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 3.3. Add atoms and polling for current reservoir state

**Description**
Introduce Jotai atoms with polling similar to Current Irrigation Plan State in `src/store/data.ts` (and `src/api/services/current-lic-packet-polling.ts` pattern if reused) to keep UI state updated.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 3.4. Implement ReservoirStateCard component

**Description**
Create `components/ReservoirStateCard.tsx` to display current reservoir automation status and timestamps (start/restart/end). Base on `ProjectStateCard.tsx` styling and data patterns.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 3.5. Integrate into Dashboard and AppShell

**Description**
Use atoms to fetch data in `pages/main/AppShell.tsx` (if needed) and render `ReservoirStateCard` in `pages/main/DashboardPage.tsx`. Verify navigation and loading states.

**Target directories**

- app (frontend)

**Status:** Done
