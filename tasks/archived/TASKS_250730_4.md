# Task list info:

- name: 250730-4
- base_branch: develop

---

# Tasks

## Task 1. Use PropertyWizard component to edit properties

**Description**
The PropertyWizard component is being used to create properties. Let's use it to edit properties as well.
In the Header component, when the user is in the "Propriedade" menu, open the PropertyWizard component in edit mode.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 2. Irrigation Plan creation workflow

**Description**
When creating an irrigation plan, the current behavior is to start a wizard.
Let's change it. Instead of starting a wizard, lets navigate to IrrigationPlanPage in create mode.
The user will be able to define the plan configuration in the "config" tab and the "sectors" tab will be hidden.
After creating the plan with empty steps, the user will be redirected to the IrrigationPlanPage in edit mode with the "sectors" tab visible.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 3. Add estimated completion time to irrigation plan to the SummarySection of IrrigationPlanStepsPanel2

**Description**
Add an estimated completion time to the irrigation plan page's SummarySection, based on the sum of all steps duration.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 4. StepEditManyDialog: times validation when editing many steps

**Description**
A project can have a pipe_wash_time_seconds value configured.
This is the time needed to clean the pipes after fertigation.

The combination of all durations involved in a step must be validated.
The validation logic is implemented on `validateFertigationTiming` function in StepEditDialog.

When editing multiple steps, before saving the changes, the validation must be performed for each step.
If any of the steps has a validation error, the save button must be disabled and the error must be shown.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 5. IrrigationPlanStepsPanel2: Step reordering assistant

**Description**

In the IrrigationPlanStepsPanel2, add a "Assistente de Ordenação" button just below the "Escolher Setores" button.
When clicked, it will show a Modal with a StepReorderAssistant component.

## StepReorderAssistant Component Specification

### UI Layout

- **Header**: "Reordenar Setores" title
- **Question Section**: Dynamic question text that updates as user progresses
  - Initially: "Clique no 1º setor a irrigar?"
  - Updates to: "Clique no 2º setor a irrigar?", "Clique no 3º setor a irrigar?", etc.
  - Final state: "Ordem completa! Confirme se está correta:"
- **Sector Grid**: Responsive grid of large buttons showing available sectors
  - Each button displays sector name (e.g., "Setor 1", "Setor 2")
  - Buttons should be thumb-friendly (minimum 60px height)
  - Grid adapts to screen size
- **Order Display**: Box showing current selection progress
  - Label: "Nova ordem:"
  - Content: Comma-separated list of selected sectors (e.g., "Setor 4, Setor 1")
  - Shows placeholder text when empty: "Nenhum setor selecionado ainda"
- **Action Buttons**: Three buttons in a row
  - "Cancelar" (secondary style)
  - "Recomeçar" (accent style)
  - "Confirmar Ordem" (primary style, disabled until all sectors are selected)

### Functionality

1. **Sector Selection Process**:

   - User clicks a sector button
   - Button disappears from grid immediately
   - Sector name appends to "Nova ordem" list
   - Question updates to next ordinal number
   - Process continues until all sectors selected

2. **Button Behaviors**:

   - **Cancelar**: Shows confirmation dialog, closes modal without changes
   - **Recomeçar**: Resets selection, returns all sectors to grid, restarts from "1º setor"
   - **Confirmar Ordem**: Applies new order to irrigation plan steps

3. **State Management**:
   - Track `selectedSectors` array (ordered selection)
   - Track `availableSectors` array (remaining options)
   - Update question text using ordinal numbers array: ['1º', '2º', '3º', '4º', '5º', '6º']

### Technical Requirements

- **Mobile-first responsive design**
- **Accessible button sizes** (minimum 44px touch targets)
- **Smooth animations** for button removal and state changes
- **Error prevention**: Disable confirm button until all sectors are selected
- **Confirmation dialogs** for destructive actions (cancel, reset if partially complete)

### Integration Details

- Pass current irrigation plan steps and its sectors data as props to component
- Modal should close automatically after successful confirmation
- Handle edge cases: empty sector list, single sector, etc.

### Styling Notes

- Use project's existing style guidelines

The component should feel intuitive for non-technical users, with clear visual feedback at each step and no possibility of making errors in the reordering process.

**Target directories**

- app (frontend)

**Status:** Done

---
