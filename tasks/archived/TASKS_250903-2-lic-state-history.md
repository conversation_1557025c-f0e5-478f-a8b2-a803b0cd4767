# Task list info:

- name: 250903-2-lic-state-history
- base_branch: develop

---

# Task list context:

This task implements historical tracking for the LIC state table (lic_state) by creating a TimescaleDB hypertable that stores the complete history of state changes. The implementation follows the pattern established by other historical tables in the system (lic_packet, project_state) where a trigger automatically populates the history table from the main table.

The lic_state_history table will:

- Store the complete history of LIC state changes
- Be a TimescaleDB hypertable chunked by month for efficient time-series queries
- Use (device, state_date) as the primary key
- Include a state_date timestamptz field that tracks when the state was recorded (from date_created on insert or date_updated on update)
- Not include audit fields (date_created, date_updated, user_created, user_updated)

---

# Tasks

## Task 1. LIC State History Implementation

**Description**
Create a historical tracking system for the lic_state table by implementing lic_state_history as a TimescaleDB hypertable. The table will be automatically populated via database triggers whenever records are upserted into lic_state, ensuring the most recent record for each device in lic_state_history matches the current state in lic_state. The implementation includes database migrations for table creation, hypertable configuration, trigger setup, and Directus integration for administrative access.

**Target directories**

- directus (backend - migrations and configuration)
- mqtt-integration (backend - may need updates if any state access patterns change)

**Status:** Done

### Subtask 1.1. Create lic_state_history table migration

**Description**
Create migration file to implement the lic_state_history table with all required columns mirroring the lic_state structure but with state_date instead of audit fields. The table should include device FK, all LICState JSONB columns (lic, groups, devices, meshDevices, schedules, sectorSchedules, deviceSchedules), configuration timestamp tracking columns (devices_last_request, scheduling_last_request, etc.), and state_date timestamptz field. Follow existing migration patterns and include proper foreign key constraints.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.2. Configure TimescaleDB hypertable

**Description**
Add TimescaleDB hypertable configuration to the migration, setting up monthly chunking on the state_date column. Include proper index creation for performance optimization, particularly for time-range queries and device-specific historical lookups. Follow existing hypertable patterns from lic_packet and project_state implementations.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.3. Implement database trigger for automatic population

**Description**
Create database trigger function that automatically inserts records into lic_state_history AFTER INSERT or UPDATE operations on lic_state. The trigger should use date_created for new records and date_updated for updated records as the state_date value. Ensure the trigger handles upsert operations correctly and maintains data consistency between the two tables.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.4. Create Directus configuration

**Description**
Implement Directus configuration for the new lic_state_history table including: collection definition, field configurations, permissions (read-only access for most roles), display templates, and relationship definitions to lic_state and device tables. Follow existing Directus configuration patterns and ensure proper integration with the admin interface.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.5. Update documentation

**Description**
Update required documentation files per project guidelines including: docs/001-ENTITIES.md (add lic_state_history table description), docs/002-ENTITY_DIAGRAMS.md (add ERD relationships), docs/DDL.md (document table schema, indexes, and triggers). Include information about the historical tracking pattern and usage examples.

**Target directories**

- docs (documentation)

**Status:** Done

### Subtask 1.6. Verify implementation and test

**Description**
Test the complete implementation by: verifying migration execution, testing trigger functionality with sample upsert operations, confirming TimescaleDB hypertable behavior, validating Directus configuration accessibility, and ensuring documentation accuracy. Perform integration testing to confirm historical data integrity and query performance.

**Target directories**

- directus (backend)
- docs (documentation)

**Status:** Done
