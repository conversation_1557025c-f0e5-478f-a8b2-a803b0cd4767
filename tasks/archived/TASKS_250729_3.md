# Task list info:

- name: 250729-3
- base_branch: develop

---

# Tasks

## Task 1. IrrigationPlanStepsPanel alternative

**Description**
Let's create a IrrigationPlanStepsPanel alternative.
The original IrrigationPlanStepsPanel must be preserved, as is.
The new component must be created in a new file, and must be called IrrigationPlanStepsPanel2.
It will have the same responsibilities of IrrigationPlanStepsPanel, but it will be done in a different way.

This is a wireframe of the new component:

|-----------------|
| summary section |
|-----------------|
| choose sectors button |
|-----------------|
| step card list|
|-----------------|
| edit many button | delete many button |
|-----------------|

The step card list will have one card for each sector. The card wireframe is:

|---------------|-----------------------------------------------------------------------------------|
| move up btn | checkbox | Sector name |edit icon btn|delete icon btn |
| | | | |  
| <curr pos> |-----------------------------------------------------------------------------------|
| | Duration | Fertigation delay | Fertigation duration |
| move down btn | <duration text> | <fertigation delay text> | <fertigation duration text> |
|---------------|-----------------------------------------------------------------------------------|

---

### Summary section

The original component has a summary section. This section will be kept in the new component.

### Choose sectors button

The "choose sectors button" will be display a dialog with a list of the project sectors.
The user will be able to select multiple sectors.
The selected sectors will be added to the card list.
The deselected sectors will be removed from the card list.
The dialog must have a search box to filter the sectors.

### Step card list

The card list will have one card for each selected sector in the "choose sectors dialog".
It will manage the list of IrrigationPlanStep entities belonging to the IrrigationPlan.

### Step card list actions

The user will be able to:

- Select multiple cards by clicking the checkbox. When multiple cards are selected, the "edit many" and "delete many" buttons will be enabled.
- Move one card at time by clicking the "move up" and "move down" buttons.
- Edit a single step by clicking the "edit icon btn".
- Delete a single step by clicking the "delete icon btn". When deleting a single step, a confirmation dialog will be shown, just like in the original component.
- Delete multiple steps by clicking the "delete many" button. A confirmation dialog will be shown, just like in the original component.

### Single step editing

When the user clicks the "edit icon btn", a dialog will be shown with the step form fields.
This dialog will be called "StepEditDialog".
The user will be able to edit the duration, fertigation delay and fertigation duration.
The dialog must have a "save" and a "cancel" button.
When the user clicks "save", the changes will be applied to the step.
When the user clicks "cancel", the dialog will be closed and the changes will be discarded.
The dialog will be like the "Step Content" section of the original component. But it will not allow to change the sector, will only display the sector name.
The same validation rules of the original component must be applied.

### Multiple steps editing

When the user selects multiple cards, the "edit many" button will be enabled.
When the user clicks the "edit many" button, StepEditManyDialog will be shown.
This dialog will be called "StepEditManyDialog". It will be very similar to the "StepEditDialog", but it will allow the user to edit multiple steps at once:

- Instead of displaying the sector name, it will display the text "Selected sectors: sector1, sector2, sector3, ...".
- The duration fields will be a single field, with empty value.
- The fertigation delay and duration fields will be a single field, with empty value.

When the user clicks "save", the changes will be applied to all the selected steps.
When the user clicks "cancel", the dialog will be closed and the changes will be discarded.

Only fields with valid values ​​will be updated. Empty fields will be ignored.

---

After the component is created, it must be used in the IrrigationPlanPage, instead of the original IrrigationPlanStepsPanel.

**Target directories**

- app (frontend)

**Status:** Pending

---

## Task 2. ProjectConfigPage creation behavior

**Description**
When creating a new project, the sectors tab is not available. This is the right behavior.
But after saving the project, the user is redirected to the project list page, instead of the project detail page.
The correct behavior would be to redirect the user to the project config page (location: /app/projects/:projectId/config), after saving the project.

**Target directories**

- app (frontend)

**Status:** Pending

---
