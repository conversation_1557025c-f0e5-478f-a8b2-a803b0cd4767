# Task list info:

- name: 250731-2
- base_branch: develop

---

# Task list context:

This task list continues the button standardization initiative for the Irriga+ application. A standardized `Button` component (`app/src/components/ui/Button.tsx`) has been created, and key components such as `AssignToLICModal`, `ManageNetworkModal`, `DeviceDetailModal`, `PumpDetailModal`, and `LoginPage` have been refactored to use it, as detailed in the `BUTTON_ANALYSIS_REPORT.md`.

The goal of this task list is to achieve near-100% compliance with the new button design system across the entire application. The tasks outlined below are based on the suggested follow-ups from the initial refactoring phase and target specific components and patterns identified in the `BUTTON_ANALYSIS_REPORT.md` that still require standardization. This includes migrating forms, pages, floating action buttons, and creating utility components to ensure consistency and reduce code duplication.

---

# Tasks

## Task 1. Migrate PropertyWizard and PropertyForm to Standardized Button Component

**Description**
Migrate all inline `<button>` elements in `PropertyWizard` and `PropertyForm` components to use the new standardized `Button` component. This involves:

- Removing any non-standard button variants, specifically the blue variants used in `PropertyWizard`.
- Ensuring that submit/save actions use the `Button` component with the `primary` variant.
- Ensuring that navigation or back actions use the `Button` component with the `secondary` variant.
- Updating any other action buttons to use the appropriate `Button` variant (`ghost`, `destructive`) based on their context.
- Removing any redundant or conflicting inline styles and relying on the `Button` component's built-in styling and variants.

**Target directories**

- app/src/pages/main/components/ (PropertyWizard, PropertyForm)

**Status:** Done

---

## Task 2. Migrate AccountSelectionPage and NoAccountPage to Standardized Button Component

**Description**
Replace all custom-styled gray and green buttons in `AccountSelectionPage` and `NoAccountPage` with the new standardized `Button` component. This involves:

- Identifying buttons that currently use custom gray background styles and replacing them with the `Button` component using the `secondary` variant.
- Identifying buttons that currently use custom green background styles and replacing them with the `Button` component using the `primary` variant.
- Migrating any text-only action buttons (e.g., links that look like buttons) to use the `Button` component with the `ghost` variant.
- Ensuring all buttons adhere to the design system's sizing, spacing, and interaction states.

**Target directories**

- app/src/pages/ (AccountSelectionPage, NoAccountPage)

**Status:** Done

---

## Task 3. Standardize Floating Action Button (FAB) Component

**Description**
Audit the application for all Floating Action Button (FAB) usages and standardize them. This involves:

- Searching the codebase for existing FAB implementations (e.g., in `HardwarePage`).
- If multiple FAB usages are found, extract a shared, reusable `FAB` component. This component should enforce:
  - Consistent sizing: 56px diameter (`w-14 h-14`).
  - Consistent icon sizing: 14x14 pixels.
  - `rounded-full` for circular shape.
  - Standardized focus rings for accessibility.
  - Smooth transitions for hover and active states.
- If only one FAB exists, refactor it to either use the standard `Button` component with specific props (e.g., `size="lg"`, `fullWidth`, `rounded-full`) or create the dedicated `FAB` component for future consistency.
- Update all FAB instances throughout the application to use this new standardized component or pattern.

**Target directories**

- app/src/components/ui/ (for the new FAB component, if created)
- app/src/pages/ (for updating FAB usages)

**Status:** Done

---

## Task 4. Create Optional ModalFooter Utility Component

**Description**
Create an optional `ModalFooter` utility component to reduce code duplication and ensure consistency in modal action footers. This involves:

- Creating a new component, likely in `app/src/components/ui/ModalFooter.tsx`.
- This component should encapsulate the common footer pattern: a `div` with classes `flex gap-3 pt-4 border-t border-gray-200`.
- It should accept children (the action buttons) as props.
- Optionally, it could enforce or suggest a standard button order (e.g., secondary action on the left, primary on the right).
- Update existing modals (both previously refactored ones and new ones) to utilize this `ModalFooter` component, removing the repeated inline className strings for the footer container.

**Target directories**

- app/src/components/ui/ (ModalFooter)

**Status:** Done

---

## Task 5. Audit and Migrate Remaining Components to Standardized Button Component

**Description**
Conduct a comprehensive audit of the remaining components in the application to identify and migrate any hardcoded or non-standard action buttons to the new `Button` component. This involves:

- Performing a codebase search (e.g., using `search_files` or `grep`) for `<button>` tags and common button-related `className` patterns outside of the already refactored components.
- Paying special attention to components mentioned in the `BUTTON_ANALYSIS_REPORT.md` or discovered during initial searches, such as `SectorsPanel`, `ReservoirForm`, `DeviceFilterBar`, `Settings` pages, and `Header` components.
- For each identified instance, replace the inline button styling with the appropriate `Button` component variant (`primary`, `secondary`, `ghost`, `destructive`) and size.
- Ensure that all buttons, including those in lists, tables, and toolbars, are consistent with the design system.
- This task serves as a final sweep to achieve near-100% compliance.

**Target directories**

- app/src/ (comprehensive search and update across various components and pages)

**Status:** In Progress

---
