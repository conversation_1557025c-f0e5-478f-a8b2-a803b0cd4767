# Task list info:

- name: 250730-3
- base_branch: develop

---

# Tasks

## Task 1. apiService.account.getAccountUserTree debouncing in jotai store "data"

**Description**
apiService.account.getAccountUserTree is being called too many times in a short period.
We should use a debouncing mechanism with a configurable delay of 300ms to avoid this.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 2. Database deadlock when deleting and adding steps at same time

**Description**
When deleting and adding steps at the same time (via ChooseSectorsDialog), a database deadlock occurs in the backend side.
AS it is not possible to use transactions in the frontend, we must execute the delete, add and update operations serially.

Right now, the delete and add operations are being executed at same time (no await) in `handleSectorsChange` at `useSectorManagement.ts` which is causing the deadlock.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 3. StepEditDialog: Number input does not allow empty string

**Description**
When editing a step, if the user clears the input, the value becomes 0 (or 1, depending on the validation) instead of empty.
This is annoying because the user has to move the cursor to the beginning of the input, type something and then delete the last character.
Example:

- Lets say the duration input has value 20, the user wants to set to 30.
- The user clicks on the input and delete the content using backspace. The first backspace will delete the number 0. The second backspace will delete the number 2, but the input will not be empty, instead will have value 1.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 4. StepEditDialog: pipe_wash_time_seconds validation when editing a single step

**Description**
A project can have a pipe_wash_time_seconds value configured.
This is the time needed to clean the pipes after fertigation.
Thus, if a plan has fertigation enabled, the total fertigation time (start delay + duration) must be less or equal than the step duration minus the pipe wash time. Example: for pipe_wash_time_seconds=5 and duration=30, fertigation (start delay + duration) must be <= 25.
If the user tries to set a value greater than the available time, a validation error must be shown.

If pipe_wash_time_seconds is not configured, fertigation (start delay + duration) must be <= step duration.

**Target directories**

- app (frontend)

**Status:** Done

---
