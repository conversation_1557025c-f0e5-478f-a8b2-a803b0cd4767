# Task list info:

- name: 250825_03
- base_branch: develop

---

# Tasks

## Task 1. Create a react hook for LIC communication testing

**Description**
Create a React hook called `useLICCommunicationTest` that receives a LIC device ID as a parameter and tests communication with the device by implementing a two-phase polling mechanism.

The hook must also receive:

- a optional polling interval in milliseconds defaulting is 2000 ms.
- a optional timeout in milliseconds, defaulting to 30000 ms.

##### Phase 1: Device Message Request Creation and Polling

1. Create a device_message_request using `apiService.deviceMessageRequest.create()` from `packages/app/src/api/service.ts` with a "request_info" message type
2. Poll the created device_message_request using `apiService.deviceMessageRequest.getOne(id)` where `id` is the ID returned from the create method
3. Continue polling until the status changes from initial state
4. Handle the following status transitions:
   - If status becomes "failed", "expired", or "cancelled": stop polling and return error state
   - If status becomes "acknowledged": stop polling and return success state (skip Phase 2)
   - If status becomes "sent": proceed to Phase 2

##### Phase 2: Current LIC Packet Polling (only if Phase 1 status is "sent")

1. Use `loadCurrentLICPacketForDeviceAndType` query from `packages/app/src/api/queries/current-lic-packet.ts`
2. Poll for current_lic_packet with payload_type "info"
3. Continue polling until receiving a packet where `packet_date > device_message_request.date_created`
4. When this condition is met, return "acknowledged" status

##### Hook Return Value

The hook should return an object containing:

- `status`: Current communication status (one of: "created", "pending", "sent", "failed", "acknowledged")
- `error`: Error information if status is "failed"
- Any other relevant state for the caller

##### Status Definitions

- "created": Initial state after device_message_request is created
- "pending": device_message_request status is "pending" or "processing"
- "sent": device_message_request status is "sent" (transitioning to Phase 2)
- "failed": device_message_request status is "failed", "expired", or "cancelled"
- "acknowledged": Either device_message_request status is "acknowledged" OR valid info packet received in Phase 2

##### Implementation Requirements

- Use proper cleanup to stop polling when component unmounts
- Implement appropriate polling intervals (suggest reasonable defaults)
- Provide status change notifications to the caller
- Handle error cases gracefully
- Follow React hooks best practices for state management and side effects

**Target directories**

- packages/app/src/hooks/ (frontend)

**Status:** Done

### Subtask 1.1. Hook Structure and Interface Setup

**Description**
Create the hook file structure, TypeScript interfaces, and basic hook skeleton with proper parameter handling and return type definition

**Target directories**

- packages/app/src/hooks/ (frontend)

**Status:** Done

### Subtask 1.2. Phase 1 - Device Message Request Logic

**Description**
Implement device message request creation and polling logic, including status transition handling and Phase 1 completion detection

**Target directories**

- packages/app/src/hooks/ (frontend)

**Status:** Done

### Subtask 1.3. Phase 2 - Current LIC Packet Polling

**Description**
Implement current LIC packet polling with date comparison logic and "acknowledged" status detection

**Target directories**

- packages/app/src/hooks/ (frontend)

**Status:** Done

### Subtask 1.4. State Management and Status Transitions

**Description**
Implement comprehensive state management covering all status transitions, error states, and proper state synchronization between phases

**Target directories**

- packages/app/src/hooks/ (frontend)

**Status:** Done

### Subtask 1.5. Cleanup, Error Handling, and Testing

**Description**
Implement proper cleanup mechanisms, timeout handling, comprehensive error management, and create basic usage verification

**Target directories**

- packages/app/src/hooks/ (frontend)

**Status:** Done

## Task 2. Rewrite packages/app/src/components/TestLICCommunicationButton.tsx to use the new hook

**Description**
Rewrite the button component to use the new hook packages/app/src/hooks/useLICCommunicationTest.ts. The button should be disabled while the test is running. The text should change to "Testando" while the test is running. On error, the button should show the error message. On success, the button should show a success message. The button should be enabled again after the test is finished.
The button text should be updated conforms the status changes. The text should be:

- "Testar Comunicação" when the test is not running
- "Testando" when the test is running
- "Erro: {error.message}" when the test fails
- "Sucesso" when the test succeeds

**Target directories**

- packages/app/src/components/ (frontend)

**Status:** Done
