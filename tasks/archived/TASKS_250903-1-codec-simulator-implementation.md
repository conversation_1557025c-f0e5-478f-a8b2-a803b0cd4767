# Task list info:

- name: 250903-1-codec-simulator-implementation
- base_branch: develop

---

# Task list context:

This task list covers the full implementation of the CODEK Simulator. The goal is to create a fully functional simulator that can replace a physical CODEK device for development and testing purposes.

The implementation will be based on the analysis of the CODEK's behavior and the architecture defined in the following documents:

- **Primary Architecture Document**: `packages/codec-simulator/REPORT.md`

- **System and Protocol Documentation**:

  - `docs/DEVICES.md`
  - `docs/MESSAGES.md`

- **Firmware Behavior Analysis Documents**:

  - `tasks/analysis/lic-simulator/DEVICE_STATE_PERSISTENCE_DESIGN.md`
  - `tasks/analysis/lic-simulator/MQTT_COMMUNICATION_ANALYSIS.md`
  - `tasks/analysis/lic-simulator/PROTOBUF_MESSAGE_ANALYSIS.md`

- **Implementation References**:

  - `@packages/mqtt-integration/src/irriganet/proto.ts` (Reference for encoding outgoing packets)
  - `@packages/mqtt-integration/src/irriganet/codec-manager.ts` (Reference for decoding incoming packets)

- **ESP32 Firmware Code**: `engineering/codec_wifi/` (For reference on device behavior and edge cases)

The implementation will follow the modular design outlined in the report, using the specified technology stack.

---

# Tasks

## Task 1. Project Setup and Foundation

**Description**
Initialize the project structure and install all necessary dependencies. This task sets up the foundational elements required for all subsequent development, including the main entry point, type definitions, and basic configurations.

**Target directories**

- `packages/codec-simulator/` (Root of the simulator package)

**Status:** Done

### Subtask 1.1. Install Dependencies

**Description**
Install all the third-party libraries proposed in the technology stack section of the report. This includes `mqtt`, `commander`, `pino`, and `immer`.

**Target directories**

- `packages/codec-simulator/package.json` (Dependencies file)

**Status:** Done

### Subtask 1.2. Create Directory Structure

**Description**
Create the complete directory structure as outlined in the `REPORT.md`. This includes creating the `src`, `src/mqtt`, `src/protocol`, `src/state`, `src/logic`, and `state-data` directories with empty placeholder files.

**Target directories**

- `packages/codec-simulator/src/` (Source code directory)

**Status:** Done

### Subtask 1.3. Define Core State Types

**Description**
Create the `types.ts` file and define all the TypeScript interfaces for the simulator's state (`LICSimulatorState`, `DeviceConfig`, `DeviceState`, `SystemConfig`, `ScheduleConfig`, etc.), as detailed in the report.

**Target directories**

- `packages/codec-simulator/src/state/types.ts` (State type definitions)

**Status:** Done

### Subtask 1.4. Implement CLI Entry Point

**Description**
Create the main entry point `index.ts`. Use the `commander` library to set up the CLI, allowing it to accept `--codec-id` arguments to run one or more simulator instances.

**Target directories**

- `packages/codec-simulator/src/index.ts` (Main application entry point)

**Status:** Done

### Subtask 1.5. Setup Logger

**Description**
Configure the `pino` logger in a utility file. The logger should be easily importable and usable throughout the application.

**Target directories**

- `packages/codec-simulator/src/utils/logger.ts` (Logger configuration)

**Status:** Done

---

## Task 2. State Persistence Layer

**Description**
Implement the logic for persisting the simulator's state to the file system. This is crucial for ensuring that the simulator can maintain its state across restarts, mimicking the NVS of a real device.

**Target directories**

- `packages/codec-simulator/src/state/` (State management module)

**Status:** Done

### Subtask 2.1. Implement State Persistence Class

**Description**
Create the `LICStatePersistence` class in `persistence.ts`. This class will manage all file I/O operations for the state.

**Target directories**

- `packages/codec-simulator/src/state/persistence.ts` (Persistence logic)

**Status:** Done

### Subtask 2.2. Implement State Loading

**Description**
Implement the logic to read the JSON files from the `state-data/{codecId}` directory and populate the in-memory `LICSimulatorState` object. If files do not exist, create a default initial state. On every successful load, increment the `resets` counter.

**Target directories**

- `packages/codec-simulator/src/state/persistence.ts` (Persistence logic)

**Status:** Done

### Subtask 2.3. Implement State Saving

**Description**
Implement the logic to write the current in-memory state to the respective JSON files. This should include methods for saving the entire configuration, as well as periodic saves of the runtime state.

**Target directories**

- `packages/codec-simulator/src/state/persistence.ts` (Persistence logic)

**Status:** Done

---

## Task 3. Communication Layer (MQTT & Protocol)

**Description**
Implement the components responsible for communicating with the outside world. This involves connecting to the MQTT broker and handling the Protobuf-based protocol.

**Target directories**

- `packages/codec-simulator/src/mqtt/`
- `packages/codec-simulator/src/protocol/`

**Status:** Done

### Subtask 3.1. Implement MQTT Client Wrapper

**Description**
Create a wrapper class for the `mqtt` library in `client.ts`. This class will handle connection, subscription to the downlink topic, publishing to the report topic, and passing messages to the protocol handler.

**Target directories**

- `packages/codec-simulator/src/mqtt/client.ts`

**Status:** Done

### Subtask 3.2. Implement Protocol Handler

**Description**
Create the `ProtocolHandler` class in `handler.ts`. This class will be responsible for the full message lifecycle (CRC check, deserialization, serialization, CRC generation).

**Target directories**

- `packages/codec-simulator/src/protocol/handler.ts`

**Status:** Done

### Subtask 3.3. Implement CRC16 Utility

**Description**
Create a utility function to calculate the CRC16 checksum used by the protocol. This will be used by the Protocol Handler for both validating incoming messages and generating checksums for outgoing messages.

**Target directories**

- `packages/codec-simulator/src/protocol/crc16.ts`

**Status:** Done

---

## Task 4. State Engine and Core Logic

**Description**
Implement the central `CODEKSimulator` class, which acts as the state engine, orchestrating all other components and processing incoming messages.

**Target directories**

- `packages/codec-simulator/src/simulator.ts`

**Status:** Done

### Subtask 4.1. Implement the Simulator Class

**Description**
Create the main `CODEKSimulator` class. It should initialize and hold instances of the persistence manager, MQTT client, and protocol handler. It will contain the main loop and message processing logic.

**Target directories**

- `packages/codec-simulator/src/simulator.ts`

**Status:** Done

### Subtask 4.2. Implement Configuration Message Handlers

**Description**
Implement the handler methods within the simulator class for all configuration-related messages (`ConfigPackage`, `DevicesPackage`, `SchedulingPackage`, `DeviceSchedulingPackage`, `AutomationPackage`). These handlers will update the state, trigger persistence, and respond with an `InfoPackage`.

**Target directories**

- `packages/codec-simulator/src/simulator.ts`

**Status:** Done

### Subtask 4.3. Implement Control and Command Handlers

**Description**
Implement handlers for `ControlPackage`, `CommandPackage` (pause/resume), and `RequestInfoPackage`. These will delegate actions to the logic modules or respond directly with the requested information.

**Target directories**

- `packages/codec-simulator/src/simulator.ts`

**Status:** Done

---

## Task 5. Operational Logic Implementation

**Description**
Implement the modules that simulate the actual work of the CODEK device, such as running schedules and controlling hardware.

**Target directories**

- `packages/codec-simulator/src/logic/`

**Status:** Done

### Subtask 5.1. Implement Device Logic

**Description**
Create the `DeviceLogic` module. This module will manage the state of individual devices (on/off, timers), update the `on_bitmask` and `input_bitmask`, and handle direct control commands.

**Target directories**

- `packages/codec-simulator/src/logic/device.ts`

**Status:** Done

### Subtask 5.2. Implement Scheduling Logic

**Description**
Create the `SchedulingLogic` module. This is the most complex logic component. It needs a main timer (e.g., `setInterval`) to check for schedules that need to run, execute them step-by-step according to their `DeviceScheduling` configuration, and generate `SchedulingReportPackage`s upon completion.

**Target directories**

- `packages/codec-simulator/src/logic/scheduling.ts`

**Status:** Done

### Subtask 5.3. Implement Automation Logic

**Description**
Create the `AutomationLogic` module. This module will monitor device input states and trigger other devices (like pumps) based on the configured automation rules.

**Target directories**\_test

- `packages/codec-simulator/src/logic/automation.ts`

**Status:** Done

---

## Task 6. Testing and Finalization

**Description**
Ensure the simulator is robust and behaves as expected by writing a comprehensive suite of tests.

**Target directories**

- `packages/codec-simulator/src/` (Tests will be co-located with source files)

**Status:** Done

### Subtask 6.1. Write Unit Tests

**Description**
Write unit tests for each module using `bun:test`. Pay special attention to the Protocol Handler (CRC logic), State Persistence, and the individual logic modules.

**Target directories**

- `packages/codec-simulator/src/**/*.test.ts`

**Status:** Done

### Subtask 6.2. Write Integration Test

**Description**
Create an end-to-end integration test that starts a simulator instance, sends it a series of configuration and control messages via an MQTT client, and asserts that the simulator responds correctly and its state is updated as expected.

**Target directories**

- `packages/codec-simulator/tests/integration.test.ts`

**Status:** Done
