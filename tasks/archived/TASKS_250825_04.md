# Task list info:

- name: 250825_04
- base_branch: develop

---

# Tasks

## Task 1. loadLICStateByIdentifier testing

**Description**
write a test for loadLICStateByIdentifier in packages/mqtt-integration/src/irriganet/db-loader/index.ts.

For this, the following data must be created:

- A user
- An account
- A property
- A LIC device
- A property_device for the LIC device associating the LIC to the property - Set the wifiSSID and wifiPassword in the metadata of the property_device
- A WPC-PL10 device (for managing irrigation pumps)
- A property_device for the WPC-PL10 device associating the WPC-PL10 to the property
- A WPC-PL50 device (for reservoir pumps)
- A property_device for the WPC-PL50 device associating the WPC-PL50 to the property
- A VC device
- A property_device for the VC device associating the VC to the property
- A RM device
- A property_device for the RM device associating the RM to the property
- A mesh_device_mapping associating the WPC-PL10 to the LIC
- A mesh_device_mapping associating the VC to the LIC
- A mesh_device_mapping associating the RM to the LIC
- A mesh_device_mapping associating the WPC-PL50 to the LIC
- A irrigation water_pump associated with the WPC-PL10
- A fertigation water_pump associated with the WPC-PL10
- A service water_pump associated with the WPC-PL50
- A reservoir associated with the RM and the service water_pump
- A project associated with the property, the LIC, the irrigation water_pump and the fertigation water_pump
- 4 sectors associated with the project and the VC
- 1 irrigation plan associated with the project and with backwash and fertigation enabled
- 1 irrigation plan step for each sector

**Target directories**

- mqtt-integration (backend)

**Status:** Done

## Task 2. packages/mqtt-integration/src/irriganet/proto.ts testing

**Description**
write a test for the functions in packages/mqtt-integration/src/irriganet/proto.ts using createCompletePropertySetup from packages/mqtt-integration/tests/helpers/fixtures.ts to create the necessary data and loadLICStateByIdentifier from packages/mqtt-integration/src/irriganet/db-loader/index.ts to load the data into the LICState object. When testing configPackage, also test if the wifiSSID and wifiPassword from the property_device are being used.

**Target directories**

- mqtt-integration (backend)

**Status:** Done
