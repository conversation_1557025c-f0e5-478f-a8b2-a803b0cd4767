# Task list info:

- name: 250905-02-signup
- base_branch: develop

---

# Tasks

## Task 1. Change password

**Description**
Implement change password functionality.

- Create a ChangePasswordModal component
- The packages/app/src/pages/main/components/Settings.tsx component already has a "change password" button. We need to implement the modal that appears when the button is clicked.
- The modal must have the current password, new password and confirm new password fields.
- The modal must have a "change password" button that calls the apiService.auth.changePassword method.
- The modal must have a cancel button that closes the modal.
- The modal must have proper validation by using the validatePassword function from packages/app/src/utils/validation.ts.
- The modal must have loading state and error handling.
- After a successful change, the modal must be closed and a success message must be shown and the user must be logged out.
- Make sure to use the frontend guidelines.
- Prefer using the existing components, like packages/app/src/components/ConfirmModal.tsx.

**Target directories**

- packages/app (frontend)

**Status:** Done

---

## Task 2. Password reset

**Description**
Implement password reset functionality:

Frontend:

- The LoginPage already has a "forgot password" button. We need to implement the flow that appears when the button is clicked.
- The user will be asked to enter their email: ForgotPasswordPage.
- A password reset email will be sent to the user (apiService.auth.requestPasswordReset).
- At this moment, the user will be redirected to a "check your email" page.
- The email will have a link to a password reset page, the link will contain a token. The link will look like this: http://localhost:3000/#/reset-password?token=1234567890.
- We need a ResetPasswordPage that receives the token from the URL query params and uses it to reset the password. The route in packages/app/src/Routes.tsx must be added as "/reset-password".
- The form must have password confirmation and proper validation by using the validatePassword function from packages/app/src/utils/validation.ts.
- The form must have loading state and error handling.
- After a successful change, the user must be redirected to the login page.
- Make sure to use the frontend guidelines.
- Prefer using the existing components, like packages/app/src/components/ConfirmModal.tsx.

Backend:

- create a password-reset.liquid template based on packages/directus/templates/user-registration.liquid

**Target directories**

- packages/app (frontend)

**Status:** Done
