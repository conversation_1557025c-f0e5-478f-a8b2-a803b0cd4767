# Task list info:

- name: 250729-2
- base_branch: develop

---

# Tasks

## Task 1. Verify form cleanup before display

**Description**
When creating data, like water pumps or devices, the form is not being cleaned up before display. Let's fix it. Ensure all forms are cleaned up before display when in create mode.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 2. Water pump controllers filter

**Description**
When creating a new water pump or updating an existing one, the user should only be able to select water pump controllers devices (WPC-PL10 and WPC-PL50) that are not already assigned to another water pump of the same type, following the logic below:

- If a device is not assigned to any water pump, it is available for selection.
- If a device is assigned to a water pump of the same type, it is not available for selection.
- If a device is assigned to a water pump of type IRRIGATION, it will only be available for selection for a water pump of type FERTIGATION (if it is not already assigned to a water pump of type FERTIGATION).
- If a device is assigned to a water pump of type FERTIGATION, it will only be available for selection for a water pump of type IRRIGATION (if it is not already assigned to a water pump of type IRRIGATION).
- If a device is assigned to a water pump of type SERVICE, it will not be available for selection for any other water pump.

The unavailable options should still display but be disabled in the select dropdown.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 3. Project assets filter / validation

**Description**
When creating a new project or updating an existing one, the following rules apply:

- The user should only be able to select water pumps that are not already assigned to another project
- irrigation_water_pump must be a water pump of type IRRIGATION
- fertigation_water_pump must be a water pump of type FERTIGATION
- fertigation_water_pump is optional
- localized_irrigation_controller must be a device of type LIC

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 4. Backwash is optional in irrigation plans

**Description**
When defining a irrigation plan, the user must be able to define if the plan will include backwash operations, just like it can define fertigation operations using fertigation_enabled field.
We must:

- [migration] Add a backwash_enabled field to the irrigation_plan table
- [migration] Update directus config to include the new field and include it in the createa and update permissions
- [docs] Update docs to include the new field: DESCRIPTION, ENTITY and DIAGRAMS documentation
- [ui] update any models and types in the frontend to align with it.
- [ui] update forms and cards interfaces to include the new field

**Target directories**

- app (frontend)
- directus (backend)
- docs (documentation)

**Status:** Done

---

## Task 5. Hide start_date, end_date and backwash related fields in ProjectConfigPanel

**Description**
The backwash related fields can be confusing for the user. start_date and end_date will not be used for now.
Let's hide them in the ProjectConfigPanel.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 6. Disable used valve controller outputs in SectorDetailPanel

**Description**
When creating a new sector or updating an existing one, the user should only be able to select valve controller outputs that are not already assigned to another sector. The unavailable options should still display but be disabled. IF a valve controller has all its outputs assigned, it should not be available for selection at all in the valve_controller select dropdown.

**Target directories**

- app (frontend)

**Status:** Done

---
