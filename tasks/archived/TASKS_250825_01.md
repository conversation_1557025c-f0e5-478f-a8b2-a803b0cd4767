# Task list info:

- name: 250825_01
- base_branch: develop

---

# Tasks

## Task 1. Plan the LIC simulator

**Description**
We need a LIC simulator to test the MQTT integration.
It should be able to connect to the MQTT broker, subscribe to the topics, and handle the messages.
It should also be able to publish messages to the topics.
The main purpose is to avoid the necessity of having a real LIC device to test the integration. Thus, it does not need to implement the LoRa mesh network or communicate with other devices, but it should be able to handle the protobuf messages and simulate a real LIC behavior.
The task is to create a detailed plan of what the simulator should be able to do and how it should be implemented. (Bun and TypeScript will be used in the implementation).

Some relevant files to consider when creating the plan:

- /engineering/irriganet.md - information about the Android simplified configuration tool
- /engineering/codec_wifi - firmware code for the LIC device (ESP32 firmware)
- /packages/protobuf - Bun package with protobuf messages definition and types generated from it
- /packages/mqtt-integration - Bun package with MQTT integration code
- /docs/000-DESCRIPTION.md - project documentation
- docs/PRODUCT_OVERVIEW.md - product overview
- docs/STRUCTURE-OVERVIEW.md - project structure overview

A note on protobuf messages:

- codecs/incoming_packet.proto is the root message that is sent to the LIC.
- codecs/outgoing_packet.proto is the root message that is sent from the LIC.

What is expected from the simulator:

- It must behave exactly like a real LIC device.
- It should be able to connect to the MQTT broker, subscribe to the topics, and handle the messages.
- It should also be able to publish messages to the topics.
- It should be able to handle the protobuf messages and simulate a real LIC behavior.
- It should store the state of the LIC in file.

The task result will be markdown file called LIC_SIMULATOR_PLAN.md with the plan, in the folder tasks/analysis.

**Target directories**

- docs (documentation)
- packages (code)

**Status:** Done

### Subtask 1.1. Analyze MQTT Communication and Message Flow

**Description**
Research and document the MQTT topic structure, connection patterns, and message flow between the system and LIC devices. Analyze the ESP32 firmware's MQTT implementation and message handling logic.

**Target directories**

- engineering/codec_wifi (ESP32 firmware)
- packages/mqtt-integration (MQTT integration code)
- tasks/analysis (documentation output)

**Status:** Done

### Subtask 1.2. Document Protobuf Message Types and Processing Logic

**Description**
Analyze all incoming and outgoing protobuf message types, their structure, and how the ESP32 firmware processes each message type. Document the expected responses and state changes for each message.

**Target directories**

- packages/protobuf (protobuf definitions)
- engineering/codec_wifi (firmware message processing)
- tasks/analysis (documentation output)

**Status:** Done

### Subtask 1.3. Design Device State Management and Persistence

**Description**
Plan how the simulator will maintain device state, configuration, and operational data. Design the file-based persistence mechanism and state synchronization logic based on real LIC behavior.

**Target directories**

- engineering/codec_wifi (firmware state management)
- packages/mqtt-integration (state handling patterns)
- tasks/analysis (documentation output)

**Status:** Done

### Subtask 1.4. Create Implementation Architecture and Technical Specifications

**Description**
Design the overall simulator architecture using Bun and TypeScript, define the module structure, interfaces, and create the final comprehensive plan document with implementation guidelines.

**Target directories**

- packages (code structure reference)
- tasks/analysis (final plan document)

**Status:** Done
