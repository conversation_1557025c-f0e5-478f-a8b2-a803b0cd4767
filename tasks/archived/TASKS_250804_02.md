# Task list info:

- name: 250804_02
- base_branch: develop

---

# Tasks

## Task 1. LIC WIFI configuration should be stored in property_device metadata instead of device metadata

**Description**
The LIC WIFI configuration (SSID and password) is currently stored in the device metadata. This is incorrect because the same LIC can be associated with different properties and thus have different WIFI configurations. The WIFI configuration must be stored in the property_device metadata instead.
if WIFI configuration has changed, updateProperty<PERSON>ev<PERSON><PERSON><PERSON> must be called, instead of updateDev<PERSON><PERSON><PERSON>, in order to update the WIFI configuration in app/src/pages/main/components/DeviceDetailModal.tsx.

**Target directories**

- app (frontend)
- mqtt-integration (backend)

**Status:** Done

## Task 2. property_device label should be editable in the frontend

**Description**
The property_device label is currently not editable in the frontend. It should be editable.
A new input field should be added to app/src/pages/main/components/DeviceDetailModal.tsx to edit the label. The label is a nullabe existing field in the property_device table.
If the label is changed, updatePropertyDev<PERSON><PERSON><PERSON> must be called in order to update the label in the database.

**Target directories**

- app (frontend)

**Status:** Done

## Task 3. Mesh network validation when configuring Reservoirs and Projects

**Description**

- When configuring Reservoirs, the user should only be able to select Reservoir Monitors and Service Water Pumps that are in the same mesh network (mesh_device_mapping).
- When configuring Projects, the user should only be able to select Irrigation Water Pumps, Fertigation Water Pumps and Valve Controllers that are mapped to the Project's LIC (mesh_device_mapping).
- The frontend should filter the available devices based on the above rules, and the database should enforce them via triggers.

**Target directories**

- app (frontend)
- directus (backend)

**Status:** Done

### Subtask 3.1. Reservoir form: mesh-aware filtering for RM and pump

**Description**
Implement filtering in the Reservoir form (`app/src/pages/main/components/ReservoirForm.tsx`) so the Reservoir Monitor (RM) and Service Water Pump options only show devices that are in the same mesh network as each other. Use each device’s `property_device.current_mesh_device_mapping.lic_property_device` to compare. When one side changes and the other no longer matches, auto-clear and hint the user.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 3.2. Project config: mesh-aware filtering for irrigation and fertigation pumps

**Description**
In the Project configuration (`app/src/pages/main/ProjectConfigPage.tsx`), after selecting `lic_property_device`, filter both irrigation and fertigation water pump options to only those whose controllers are currently mapped to the selected LIC (via `current_mesh_device_mapping.lic_property_device`). Maintain existing logic that excludes pumps already assigned to other projects.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 3.3. Sector config: mesh-aware filtering for valve controllers

**Description**
While configuring sectors in the project (`app/src/pages/main/ProjectConfigPage.tsx` and `app/src/pages/main/components/SectorDetailPanel.tsx`), restrict valve controller options to controllers whose current LIC mapping equals the project’s `lic_property_device`. Keep the current behavior that excludes controllers with all outputs used.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 3.4. Backend database enforcement for mesh constraints

**Description**
Create a migration adding BEFORE INSERT/UPDATE triggers to enforce mesh rules at the database level:

- Reservoir: if both RM and pump are set, their mesh devices’ current LICs must be equal.
- Project: irrigation and fertigation pumps’ controller LICs must equal the project LIC when set.
- Sector: valve controller’s LIC must equal the project LIC.
  Leverage `property_device.current_mesh_device_mapping` for fast resolution and return clear error messages on violation.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 3.5. Documentation updates

**Description**
Update documentation to reflect the new invariants and triggers: `directus/docs/DDL.md` and `directus/docs/007-MESH_NETWORK.md`.

**Target directories**

- directus (backend)
- docs (documentation)

**Status:** Done

## Task 4. Add "mode" to water_pump

**Description**
Add a mode column to the water_pump table. The mode can be one of the following values: "PULSE", "CONTINUOUS"

**Target directories**

- directus (backend)
- app (frontend)
- mqtt-integration (backend)

**Status:** Done

### Subtask 4.1. Database schema and Directus configuration

**Description**
Create migration to add `mode` column to water_pump table (enum with values "PULSE", "CONTINUOUS", not null, default "PULSE"), update Directus field configuration with read/create/update permissions for Account Admin role and add `mode` field to Directus collection.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 4.2. Frontend model and UI implementation

**Description**
Update WaterPump TypeScript interface in `app/src/api/model/water-pump.ts`, add mode field to PumpDetailModal form with appropriate UI control (dropdown/radio buttons).

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 4.3. MQTT integration protocol updates

**Description**
Update `mqtt-integration/src/proto/builder/devices.ts` to use pump's `mode` field instead of hardcoded `isPulse = false` in device parameter functions.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 4.4. Seed data and documentation updates

**Description**
Update water pump creation in `directus/src/seed/operations/water-pump-operations.ts` to include mode values, update WaterPumpData type definition, and document changes in relevant docs files.

**Target directories**

- directus (backend)
- docs (documentation)

**Status:** Done

## Task 5. The project must have a field to choose if the backwashing will be done using the irrigation pump or the fertigation pump.

**Description**
When configuring a project, the user must be able to choose if the backwashing will be done using the irrigation pump, the fertigation pump or if it will not be done at all.
For this, a new field must be added to the project table. The field must be named "backwash_pump_type" and can have the values "IRRIGATION", "FERTIGATION" or NULL. The default value must be NULL. If the project does not have a fertigation pump configured, the field must not have the value "FERTIGATION".
Backwash is configured per project, not per irrigation plan as it is implemented right now, thus ,the backwash_enabled field in the irrigation_plan table must be removed.

- frontend: add the field to the project model, add to the form in the config tab and disable it if the project does not have a fertigation pump configured.
- backend: add the field to the project table (migration), update directus config to include the field as well as add read, create and update permissions (migration) for the Account Admin role.
- backend: remove the backwash_enabled field from the irrigation_plan table (migration), update directus config to remove the field as well as remove read, create and update permissions (migration) for the Account Admin role.
- mqtt-integration: update mqtt-integration/src/proto/builder/devices.ts to use the correct pump for backwashing.

**Target directories**

- directus (backend)
- app (frontend)
- mqtt-integration (backend)

**Status:** Done

### Subtask 5.1. Database schema and Directus configuration updates

**Description**
Create migrations to add `backwash_pump_type` column to project table, remove `backwash_enabled` from irrigation_plan table, and update Directus field configurations and permissions for the Account Admin role.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 5.2. Frontend model and type definitions update

**Description**
Update TypeScript interfaces for Project and IrrigationPlan models, removing `backwash_enabled` from IrrigationPlan and adding `backwash_pump_type` to Project.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 5.3. Project configuration UI implementation

**Description**
Add backwash pump type selection field to ProjectConfigPanel, implement conditional enabling based on fertigation pump availability, update form handling logic in ProjectConfigPage.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 5.4. MQTT integration protocol updates

**Description**
Update device protocol builder to use project-level `backwash_pump_type` instead of plan-level `backwash_enabled`, implement pump selection logic for backwash operations.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

## Task 6. Add "power" to sector

**Description**
For water pumps with variable frequency inverter, it is possible to configure the power of the pump. To support this, we need to implement the following:

- [backend/migration/DDL] Add a power column to the sector table. The power will be a integer between 0 and 100. This values is required and any existing sector must have the value 0.
- [backend/migration/directus] Add the corresponding field to the sector collection in directus.
- [backend/migration/directus] Add the power column to directus, with read, create and update permissions for the Account Admin role.
- [backend/seed] Add the power column to the sector creation in directus/src/seed/index.ts. The value must be a random number between 1 and 100 if the sector's project irrigation pump has a variable frequency inverter, or 0 otherwise.
- [frontend] Add the power column to the sector model in app/src/api/model/sector.ts.
- [frontend] Add the power column to the sector form in app/src/pages/main/components/SectorDetailPanel.tsx. The field must be disabled if the project's irrigation pump does not have a variable frequency inverter.
- [frontend] Add the power column to the sector list item in app/src/pages/main/components/SectorsPanel.tsx. The field must be hidden if the project's irrigation pump does not have a variable frequency inverter.
- [frontend] Add the power column to the sector card in app/src/pages/main/components/IrrigationPlanStepsPanel2.tsx. The field must be hidden if the project's irrigation pump does not have a variable frequency inverter.
- [docs] Document the changes in docs/001-ENTITIES.md, docs/002-ENTITY_DIAGRAMS.md, docs/DDL.md and docs/006-IRRIGATION.md.

**Target directories**

- directus (backend)
- app (frontend)
- docs (documentation)

**Status:** Done

### Subtask 6.1. Database schema and Directus configuration

**Description**
Create migration to add `power` column to sector table (integer 0-100, required, default 0 for existing), update Directus field configuration with proper permissions for Account Admin role.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 6.2. Backend seed data updates

**Description**
Update sector creation in `directus/src/seed/index.ts` to generate appropriate power values based on irrigation pump's `has_frequency_inverter` property (random 1-100 if true, 0 if false).

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 6.3. Frontend model and UI implementation

**Description**
Update sector model in `app/src/api/model/sector.ts`, implement power field in SectorDetailPanel form (disabled when pump lacks frequency inverter), add power display in SectorsPanel list and IrrigationPlanStepsPanel2 card (hidden when pump lacks frequency inverter).

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 6.4. Documentation updates

**Description**
Document schema changes and business rules in `docs/001-ENTITIES.md`, `docs/002-ENTITY_DIAGRAMS.md`, `docs/DDL.md`, and `docs/006-IRRIGATION.md`.

**Target directories**

- docs (documentation)

**Status:** Done

## Task 7. Add "power" value to mqtt integration

**Description**

The protobuf message `DevicesPackage` was updated to include the power value for each virtual device.
The power value was added to the sector entity in the task 6.
The mqtt-integration must be updated to include the power value in the message when the message is updated.

**Relevant files**

- protobuf/proto/devices.proto
- mqtt-integration/src/proto/builder/devices.ts

**Target directories**

- mqtt-integration (backend)

**Status:** Done
