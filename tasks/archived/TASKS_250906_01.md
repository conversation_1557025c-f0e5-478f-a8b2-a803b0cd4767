# Task list info:

- name: 250906-02-rainfall-handling-integration
- base_branch: develop

---

# Task list context:

If the LIC device has rainfall gauge, it will send rainfall data in the SystemStatusPackage message periodically.
The rainfall data is the accumulated rainfall in the last 24 hours (SystemStatusPackage.rainfall). The unit is already millimeters, so no conversion is needed.

The property collection has a flag indicating if the rainfall gauge is enabled or not (rain_gauge_enabled).
It also has a precipitation_volume_limit_mm field, which is the minimum amount of accumulated rainfall (in mm) that will trigger the irrigation pause.
When the precipitation_volume_limit_mm is reached, the irrigation will be paused for a certain amount of time (precipitation_suspended_duration_hours).

A property can have many LICs, and each of them can have the rainfall gauge. It is possible to have a property with no rainfall gauge, or with only one LIC with rainfall gauge, or with many LICs with rainfall gauge.

The frontend has a jotai atom "currentLICPacketsAtom" that holds the latest current_lic_packet's for all LIC devices in the selected property. We should look for lic packets with payload_type "status" and extract the relevant data, if available, which are rainfall and raining.

The task is to implement the frontend changes to display the current rainfall data:

- A RainfallCard in packages/app/src/components/RainfallCard.tsx which displays the summary when collapsed, and the details when expanded.
- When collapsed, it should will display the following information:
  - "Chuvendo": sim / não - The raining field of the latest status package for the LIC device. If there are multiple LICs, it should be "sim" if any of them is raining.
  - "Acumulado 24h": XX mm - The accumulated rainfall in the last 24 hours (rainfall field of the latest status package for the LIC device). If there are multiple LICs, it should be the average of all of them.
  - "Pluviômetros:" N - the number of LICs with rainfall gauge.
- When expanded, it should display the following information for each LIC with rainfall gauge:
  - LIC label (stored in property_device metadata) or lic identifier (we need to identify the LIC as a property can have many LICs)
  - Rain status: raining or not (raining boolean)
  - Total rainfall in the last 24 hours (rainfall field of the latest status package for the LIC device)

---

# Tasks

## Task 1. Implement RainfallCard frontend component

**Description**
Create the RainfallCard component in packages/app/src/components/RainfallCard.tsx that displays rainfall data summary when collapsed and detailed view when expanded. The component should:

- Display "Chuvendo": sim/não based on raining status from any LIC
- Show "Acumulado 24h": average rainfall across all LICs with gauges
- Indicate "Pluviômetros:" count of LICs with rainfall gauges
- Expand to show detailed per-LIC information including LIC label, rain status, and rainfall amount

**Target directories**

- packages/app/src/components/ (frontend component implementation)
- packages/app/src/store/ (state management for rainfall data)

**Status:** Done

## Subtasks

### Subtask 1.1: Create Rainfall Data Processing Utilities

**Description**
Create utility functions to extract rainfall data from LIC status packets. Implement logic to filter packets with payload_type "status", parse SystemStatusPackage data, and create helper functions for calculating averages and determining rain status across multiple LICs.

**Target files**

- packages/app/src/utils/rainfall-data.ts (new file)
- packages/app/src/utils/index.ts (export utilities)

**Status:** Done

### Subtask 1.2: Implement Rainfall Store Atoms

**Description**
Create derived atoms in the store for rainfall data aggregation. Implement `rainfallDataByPropertyAtom` to process and aggregate rainfall data from current LIC packets, and `licsWithRainGaugeAtom` to count devices with rainfall gauges enabled.

**Target files**

- packages/app/src/store/data.ts (add new atoms)
- packages/app/src/store/index.ts (export new atoms)

**Status:** Done

### Subtask 1.3: Build RainfallCard Component

**Description**
Implement the main RainfallCard component with collapsible functionality. Create both collapsed view (summary information) and expanded view (per-LIC details) with proper styling following existing component patterns.

**Target files**

- packages/app/src/components/RainfallCard.tsx (new component)
- packages/app/src/components/index.ts (export component)

**Status:** Done

### Subtask 1.4: Integrate RainfallCard with Dashboard

**Description**
Add the RainfallCard component to the DashboardPage layout. Handle empty states when no rainfall data is available and ensure proper integration with existing data refresh mechanisms.

**Target files**

- packages/app/src/pages/main/DashboardPage.tsx (integration)
- packages/app/src/pages/main/components/ (optional section component)

**Status:** Done
