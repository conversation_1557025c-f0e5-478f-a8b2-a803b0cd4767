# Task list info:

- name: 250822_02
- base_branch: develop

---

# Tasks

## Task 1. Create Directus configuration for lic_packet

**Description**
Create Directus configuration migrations for lic_packet table including collection setup, field definitions, permissions, and administrative interface. Configure proper display templates and field validation.
The creation table migration is directus/migrations/20250822C-create-lic-packet-hypertable.js

**Target directories**

- directus (backend)

**Status:** Done

## Task 2. Create a service to process device_message_request queue in mqtt-integration package

**Description**
Create a service that monitors the device_message_request table for pending messages and processes them according to priority, schedule, and dependencies.
The service will have a single responsibility: process the queue. All other concerns (e.g. message building, retries, etc.) will be handled by other services.
It will work as a event emitter of device_message_request of interest. Other services will listen to the events and act accordingly.
The service will not update the device_message_request table. It will only query it and emit events.
The service will be implemented in mqtt-integration/src/db/services/device-message-queue.ts.
It will use pooling pattern to query the database every <INTERVAL> seconds.
The service name will be DeviceMessageQueueService.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.1. Create device_message_request database query functions

**Description**
Create database query functions for fetching pending device_message_request records with proper filtering, ordering, and dependency handling. Functions should query messages that are ready to be processed based on status, schedule, and parent message dependencies.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.2. Create DeviceMessageQueueService class structure

**Description**
Implement the DeviceMessageQueueService class with EventEmitter inheritance, polling timer mechanism, and basic lifecycle methods (start, stop, destroy). Set up the foundation for the service without the actual message processing logic.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.3. Implement message processing and event emission logic

**Description**
Add the core logic to process queried messages and emit appropriate events for each device_message_request. Implement the polling loop that queries the database and emits events for pending messages.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 2.4. Add service configuration and error handling

**Description**
Add configuration options for polling interval, logging integration, and comprehensive error handling for database failures and other edge cases. Ensure the service is robust and production-ready.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

## Task 3. Update CodecManagerRegistry listen to device_message_request events an send the messages to LIC devices in mqtt-integration package

**Description**
Update CodecManagerRegistry to handle device_message_request events emitted by the service created in task 2.
The glue code that connects the DeviceMessageQueueService and CodecManagerRegistry will be implemented in mqtt-integration/src/index.ts, where there is an existing CodecManagerRegistry instance and a DeviceMessageQueueService instance needs to be created.
On device_message_request events, CodecManagerRegistry will call the appropriate CodecManager to send the message to the LIC device by calling the send() method.
CodecManager.send has 3 parameters: - messageType: device_message_request.payload_type is the message type to be sent. - id: device_message_request.packet_id is the packet id. - ...params: device_message_request.payload_data the message parameters. if it is null, it should be ignored.
Before sending the message, CodecManager must update the device_message_request row setting the status to "processing" and "attempts += 1" and payload_bytes to the compiled protobuf binary data.
After sending the message, CodecManager must update the device_message_request depending on the result.

- If the message was sent successfully, the status must be set to "sent" and the sent_at field must be set to the current date. The acknowledged_at field must be set to null
- If the message failed to send, the status must be set to "failed". The last_error field must be set to the error message.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 3.1. Create device_message_request database mutation functions

**Description**
Create database mutation functions to update device_message_request records including status updates, attempt increments, payload_bytes updates, timestamp updates (sent_at, acknowledged_at), and error message logging. These functions will be used by CodecManager during message processing.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 3.2. Enhance CodecManager.send() method for database integration

**Description**
Modify the CodecManager.send() method to accept device_message_request data and handle database updates before and after sending messages. The method should update status to "processing", increment attempts, store payload_bytes, then update final status based on send result.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 3.3. Add event listener methods to CodecManagerRegistry

**Description**
Add methods to CodecManagerRegistry to handle device_message_request events emitted by DeviceMessageQueueService. Implement logic to route messages to appropriate CodecManager instances based on device identifier and coordinate the sending process.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 3.4. Update service initialization in index.ts

**Description**
Create DeviceMessageQueueService instance in mqtt-integration/src/index.ts and wire the event listeners between DeviceMessageQueueService and CodecManagerRegistry. Ensure proper service lifecycle management and initialization order.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

### Subtask 3.5. Add comprehensive error handling and logging

**Description**
Add proper error handling for all failure scenarios including database update failures, CodecManager lookup failures, and message sending failures. Implement comprehensive logging to track message processing lifecycle and troubleshoot issues.

**Target directories**

- mqtt-integration (backend)

**Status:** Done

## Task 3. device_message_request data fill trigger

**Description**
Create a before insert trigger in device_message_request table that:

- sets scheduled_at to now() if it is null
- sets packet_id to the scheduled_at time epoch seconds if it is null
- if device is null and property_device is not null, sets device to property_device.device_id
- if property_device is null and device is not null, find out the property_device for the device at scheduled_at time and set property_device to it. `scheduled_at >= COALESCE(property_device.start_date, '-infinity'::timestamp) AND ${referenceDate} <= COALESCE(property_device.end_date, 'infinity'::timestamp) AND property_device.device_id = device`
- sets payload_data to jsonb null if it is NULL

**Target directories**

- directus (backend)

**Status:** Done

## Task 4. app/src/components/TestLICCommunicationButton.tsx

**Description**
Create a button that sends a test message to a LIC device. It will work like this:

- The component will receive all Button (app/src/components/ui/Button.tsx) props and an additional device of type AUTDevice (app/src/api/queries/account.ts) prop
- The button text will be "Testar Comunicação"
- When the user clicks the button:
  - It becomes loading (disabled with a spinner)
  - A request_info message si sent to the LIC device by creating a device_message_request (example in testCommunication of app/src/pages/main/components/ManageNetworkModal.tsx)
  - If the device_message_request creation fails, the button stops loading and shows an error toast message as well as change the button text to "Erro: Tente novamente" and becomes enabled again
  - If the device_message_request creation succeeds, the button text changes to "Pendente..." and still stays loading. The button does not become enabled again.
  - In case of success, the result of the operation will contain the device_message_request object in the data field. The component will periodically query the device_message_request status until it is either "sent", "acknowledged" or "failed". The polling interval will configured as prop, defaulting to 2000 ms.
  - If the status is "processing", the button text is "Processando..." and it stays loading.
  - If the status is "sent" or "acknowledged", the button text changes to "Sucesso" and a success toast message is shown. The button becomes enabled again.
  - If the status is "failed", the button text changes to "Erro: Tente novamente" and a failure toast message is shown. The button becomes enabled again.

A jotai atom will be created to perform the query using apiService.deviceMessageRequest.getOne and store the result. The polling will be implemented using a setInterval that is cleared when the status is not "processing".

If the component is unmounted while polling, the polling must be stopped.

DO not you this button in any component yet.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 4.1. Create TestLICCommunicationButton component structure and basic functionality

**Description**
Create the basic React component structure extending Button props, implement the device prop typing with AUTDevice, set up initial button text and click handler structure without polling logic.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 4.2. Implement device message request creation and initial state handling

**Description**
Add the logic to create device_message_request on button click, implement loading state management, error handling for request creation failures, and basic button text state changes.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 4.3. Create jotai atom for device message request polling

**Description**
Implement a jotai atom that performs periodic queries using apiService.deviceMessageRequest.getOne, handle polling interval management, and integrate with the component state.

**Target directories**

- app (frontend)

**Status:** Done

### Subtask 4.4. Add polling lifecycle management and toast notifications

**Description**
Implement polling start/stop logic based on message status, add proper cleanup on component unmount, integrate toast notifications for success/failure states, and finalize all button text state transitions.

**Target directories**

- app (frontend)

**Status:** Done
