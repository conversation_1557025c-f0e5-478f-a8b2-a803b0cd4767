# Task list info:

- name: 250822_01
- base_branch: develop

---

# Tasks

## Task 1. Migrations for tables for storing LIC packets

**Description**
Create a comprehensive database migration solution for storing LIC (Localized Irrigation Controller) packets with both current state and historical tracking using TimescaleDB hypertables and complex trigger-based UPSERT logic.

```sql
CREATE TABLE IF NOT EXISTS current_lic_packet (
  id SERIAL PRIMARY KEY,
  device uuid NOT NULL REFERENCES device(id),
  date_created TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  packet_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  payload_type VARCHAR(50) NOT NULL,
  payload_data JSONB NOT NULL
);
```

Requirements:

1.  current_lic_packet table must have (device, payload_type) as a unique constraint to ensure only the most recent packet per combination.
2.  current_lic_packet table must have an index for (device, packet_date) for performance.
3.  current_lic_packet will store only the most recent packet for a given (device, payload_type) combination.
4.  lic_packet table will store the complete history of packets with the same structure as current_lic_packet. It must have a unique constraint on (device, packet_date, payload_type) to prevent duplicate packets.
5.  As we are using timescale, the lic_packet table must be created as hypertable. It must be chunked by month and must have a compression policy.
6.  The lic_packet will be populated by a trigger on current_lic_packet using conditional UPSERT strategy:

- When a new packet is inserted/updated in current_lic_packet:
  a. Always INSERT new record into lic_packet (preserving all history)
  b. Check if new packet_date is newer than existing record in current_lic_packet for same (device, payload_type)
  c. If newer: DELETE existing record and INSERT new record into current_lic_packet
  d. If older: do NOT update current_lic_packet (only historical record in lic_packet is created)

7.  UPSERT strategy clarified: Conditional approach ensures current_lic_packet always contains the most recent packet by timestamp, while lic_packet preserves complete chronological history including out-of-order arrivals.
8.  Knex migrations will be created for the changes.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.1. Create current_lic_packet table structure

**Description**
Create the current_lic_packet table with proper schema and corrected unique constraint (device, payload_type) to ensure only the most recent packet per combination. Implement the basic table structure with device reference, timestamps, and JSONB payload storage.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.2. Create Directus configuration for current_lic_packet

**Description**
Create Directus configuration migrations for current_lic_packet table including collection setup, field definitions, and permissions. This table will be managed by Directus for API access.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.3. Create lic_packet hypertable with TimescaleDB features

**Description**
Create lic_packet table as TimescaleDB hypertable with monthly chunking and compression policy. Include unique constraint on (device, packet_date, payload_type) to prevent duplicate packets. Configure proper time-series optimization for historical packet storage. Note: This table won't need Directus configuration as TimescaleDB hypertables are not supported by Directus.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.4. Implement trigger function for conditional UPSERT logic

**Description**
Create the trigger function that handles the conditional UPSERT logic between current_lic_packet and lic_packet tables. Implement: (a) always insert to lic_packet, (b) check if packet_date is newer than existing, (c) if newer: delete existing and insert new to current_lic_packet, (d) if older: skip current_lic_packet update.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.5. Create performance indexes and final constraints

**Description**
Add all required indexes for optimal performance including the (device, packet_date) index and any additional constraints needed for data integrity.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 1.6. Update documentation and verify implementation

**Description**
Update entity documentation (/docs/001-ENTITIES.md) and DDL documentation (/docs/DDL.md), verify the complete implementation works as expected with the trigger logic.

**Target directories**

- directus (backend)
- docs (documentation)

**Status:** Done

## Task 2. Device Message Request Queue Table

**Description**
Create a comprehensive database migration for the device_message_request table that provides a robust queue-based mechanism for sending protobuf messages to LIC (Localized Irrigation Controller) devices via MQTT. This system manages the complete lifecycle of outgoing messages, from creation to delivery confirmation, with support for retry mechanisms, message correlation, and sequential operations.

Requirements:

1. Create device_message_request table with complete schema following existing database patterns (audit fields, foreign keys, constraints)
2. Support all IncomingPacket protobuf payload types with proper validation constraints
3. Implement dual payload storage: payload_data (jsonb) and payload_bytes (bytea, nullable)
4. Include packet_id field with unique constraint per device for protobuf correlation
5. Support message relationships via parent_message_id and correlation_id fields
6. Implement comprehensive status tracking with proper check constraints
7. Add all performance indexes optimized for queue processing
8. Follow existing naming patterns and constraint conventions from DDL.md
9. Create Directus configuration for API access and administration
10. Update documentation including DDL.md and feature documentation
11. Note: payload_bytes can be NULL as it will be populated when message is processed
12. The document describing the feature is /docs/features/DEVICE_MESSAGE_REQUEST.md

**Target directories**

- directus (backend)
- docs (documentation)

**Status:** Done

### Subtask 2.1. Create device_message_request table structure

**Description**
Create the device_message_request table with complete schema including all fields for device relationships, message content (IncomingPacket structure), message correlation, delivery control, timing, retry logic, and standard audit fields. Implement all business logic constraints and foreign key relationships.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 2.2. Create performance indexes for queue processing

**Description**
Add all required indexes for optimal queue performance including primary queue processing index, device-specific lookups, scheduled messages, deduplication, message correlation, retry logic, and cleanup operations.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 2.3. Create Directus configuration for device_message_request

**Description**
Create Directus configuration migrations for device_message_request table including collection setup, field definitions, permissions, and administrative interface. Configure proper display templates and field validation.

**Target directories**

- directus (backend)

**Status:** Done

### Subtask 2.4. Update documentation

**Description**
Update DDL documentation (/docs/DDL.md) with the new device_message_request table structure and constraints. Ensure feature documentation is aligned with implementation.

**Target directories**

- docs (documentation)

**Status:** Done

### Subtask 2.5. Update frontend api client

**Description**
Update frontend api client to include device_message_request collection and associated types. Add to app/src/api/model/device-message-request.ts, app/src/api/client.ts and app/src/api/service.ts.
Also add relevant jotai atoms to app/src/store/data.ts and app/src/store/crud.ts.

**Target directories**

- app (frontend)

**Status:** Done

---
