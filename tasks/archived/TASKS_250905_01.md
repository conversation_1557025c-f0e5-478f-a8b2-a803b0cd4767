# Task list info:

- name: 250905-01-signup-page
- base_branch: develop

---

# Task list context:

Create a Sign Up page for user registration with email, first name, last name, phone number, CPF, password, and password confirmation fields. The page should follow the modern design style of the existing LoginPage and include proper navigation between login and signup pages.

---

# Tasks

## Task 1. Create Sign Up Page Component

**Description**
Create a new SignUpPage component in packages/app/src/pages/ with a modern, sleek design following the LoginPage style. The page should include form fields for email, first name, last name, phone number, CPF, password, and password confirmation with proper validation.

**Target directories**

- packages/app/src/pages/ (React page components)
- packages/app/src/components/ui/ (UI components if needed)

**Status:** Done

### Subtask 1.1. Create SignUpPage.tsx component

**Description**
Create the main SignUpPage component with form structure, styling matching LoginPage, and proper form validation for all required fields including CPF validation and password confirmation matching.

**Target directories**

- packages/app/src/pages/SignUpPage.tsx (Main signup page component)

**Status:** Done

### Subtask 1.2. Add form validation and state management

**Description**
Implement form validation for email format, CPF format validation, password strength requirements, and password confirmation matching. Add proper state management for form fields and validation errors.

**Target directories**

- packages/app/src/pages/SignUpPage.tsx (Validation logic and state)

**Status:** Done

## Task 2. Update LoginPage with Sign Up Link

**Description**
Update the existing LoginPage component to include a proper link to the Sign Up page, replacing the current "Solicite acesso" button with a navigation link.

**Target directories**

- packages/app/src/pages/LoginPage.tsx (Update login page navigation)

**Status:** Done

## Task 3. Add Routing for Sign Up Page

**Description**
Add the Sign Up page to the application routing system using Wouter, ensuring proper navigation between login and signup pages.

**Target directories**

- packages/app/src/Routes.tsx (Updated routing configuration)

**Status:** Done

## Task 4. Add Sign Up Link to Sign Up Page

**Description**
Add a link on the Sign Up page to navigate back to the Login page, maintaining consistent navigation flow between authentication pages.

**Target directories**

- packages/app/src/pages/SignUpPage.tsx (Add login page link)

**Status:** Done
