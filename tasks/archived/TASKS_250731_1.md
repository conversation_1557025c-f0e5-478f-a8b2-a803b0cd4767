# Task list info:

- name: 250731-1
- base_branch: develop

---

# Task list context:

### Mesh devices association with Localized Irrigation Controller (LIC)

As stated in the docs/PRODUCT_OVERVIEW.md, the LIC act as a coordinator and gateway for the mesh network. It is responsible for communicating with the other devices and commanding them when to turn on and off.
Other device types (WPC, VC, RM) will communicate with the LIC to report their status and receive commands using LoRa Mesh, thus, they are often called "mesh devices".
The Irriga+ system must know which mesh devices are associated with which LIC, so that it can communicate with them. For example, if the user wants to turn on a service water pump (it can be done via the "Acionamento da Bomba" modal in app/src/pages/main/WaterPumpsPage.tsx), the system must know which LIC is associated with the water pump, so that it can command the LIC to turn on the water pump.
We need to implement it in the backend and frontend.

---

# Tasks

## Task 1. Mesh devices association with Localized Irrigation Controller Migrations

**Description**
A mesh device can be associated with a LIC if they belong to the same property, thus, we need to create association between property_device records.
A property_device is the association between a property and a device.
We call "mesh property_device" the property_device record that has a device that is a mesh device (WPC, VC, or RM).
We call "LIC property_device" the property_device record that has a device that is a LIC.

### Database structure

We create a table to store the association between a mesh property_device and a LIC property_device.
It will be called `mesh_device_mapping`. It will have the following fields:

- id: UUID (primary key)
- mesh_property_device: UUID (foreign key to property_device)
- lic_property_device: UUID (foreign key to property_device)
- start_date: timestamptz (when the association started)
- end_date: timestamptz (when the association ended, can be null, indicating that the association is active indefinitely)
- date_created: timestamptz (when the record was created, nullable)
- user_created: UUID (who created the record, nullable)
- date_updated: timestamptz (when the record was last updated, nullable)
- user_updated: UUID (who last updated the record, nullable)

We also need to create a trigger to update the date_updated field when the record is updated, using the update_timestamp_column() trigger function.

We also create a column in the property_device table to hold the current mesh_device_mapping record. It will be called `current_mesh_device_mapping` and will be a UUID (foreign key to mesh_device_mapping). Only mesh property_device records will have this field set.

current_mesh_device_mapping needs to be updated regularly, For this we create a PLSQL function that will be called by a job scheduler. This function will update the current_mesh_device_mapping field for a set of mesh property_device records ids passed as parameter. It will also receive a optional timestamptz "reference_date" that defaults to now(). reference_date is the date that will be used to check which mesh_device_mapping record is active for the given mesh property_device records at that time. This active record is the one where start_date <= reference_date <= end_date and is the one that will be set in the current_mesh_device_mapping field. If no active record is found, the current_mesh_device_mapping field will be set to null. The function name will be `im_update_current_mesh_device_mapping` (note the im\* prefix, it stands for Irriga Mais).

We also need to create a trigger to check the constraints and business rules listed below.

### Constraints

- A mesh property_device record can be associated with only one LIC property_device record at a time. A single LIC property_device record can have multiple mesh property_device records associated with it.
- A mesh property_device record can only be associated with a LIC property_device record if they belong to the same property.
- A mesh property_device record can only be associated with a LIC property_device record if the mesh device and the LIC are both active at the time of association (start_date <= now() <= end_date).
- mesh_device_mapping records must not overlap in time for a given mesh property_device record.

### Business rules

- When a mesh_device_mapping is create or has its start_date or end_date changed, we need to check for overlapping mesh_device_mapping records for the same mesh property_device record. If any are found, they must be updated to accommodate the new record. this is the accommodation logic:
  - If the existing overlapping record's start_date is before the new record's start_date, the existing record's end_date must be set to the new record's start_date.
  - If the existing overlapping record's end_date is after the new record's end_date, the existing record's start_date must be set to the new record's end_date + 1 second.
  - If the existing overlapping record is completely contained within the new record, an error must be thrown saying that there is an association already active in the period requested.
  - If the existing overlapping record contains the new record, the existing record must be split: The existing record's end_date must be set to the new record's start_date, and a new record must be created with the new record's end_date as start_date and the existing record's end_date as end_date. In this case, foreign key pointing to the original record must be checked against the period to decide if they must be updated or not. This FK check will not be implemented in this task, just leave a comment in the code saying that it must be implemented in the future.
- When a mesh_device_mapping is create or has its start_date or end_date changed, the im_update_current_mesh_device_mapping function must be called with now() as reference_date for the mesh property_device record.

## Useful info

- The directus/migrations/20250617A-create_property_device_table.js can be used as reference for creating the mesh_device_mapping table.
- The im_update_current_mesh_device_mapping function can be created in the same migration file.
- The update_timestamp_column() trigger function already exists.
- Pay attention on the migration file naming convention.

**Target directories**

- directus (backend)

**Status:** Done

---

## Task 2. Mesh devices mapping directus config

**Description**
We need to configure the directus to manage the mesh_device_mapping table.
We need to:

- [migration] Add the mesh_device_mapping collection, fields, and relations to directus (see 20250625B-reservoir-directus-config.js as reference)
- [migration] Add read, create, update and delete permissions for the mesh_device_mapping table (see 20250705B-directus_create_create_permissions.js, 20250705C-directus_create_update_permissions.js, and 20250730A-directus_create_delete_permissions.js as reference)
- [migration] add the new field and relation to the property_device collection (current_mesh_device_mapping), as well as update the read permissions for it;

**Target directories**

- directus (backend)

**Status:** Done

---

## Task 3. Mesh devices mapping frontend api support

**Description**
We need to add support for the mesh_device_mapping table in the frontend api structure.
No UI is needed for this task.
We need to:

- [model] Add a MeshDeviceMapping model to app/src/models/mesh-device-mapping.ts
- [model] Add new field to PropertyDevice model to include current_mesh_device_mapping relation in app/src/models/property-device.ts
- [directus-client] Update AppSchema to include mesh_device_mapping in app/src/api/client.ts
- [directus-service] Add meshDeviceMapping CRUD to app/src/api/service.ts
- [query] Update getAccountUserTree to include mesh_device_mapping collection and current_mesh_device_mapping field in app/src/api/queries/account.ts
- Verify if other relevant api or jotai store code needs to be updated to support the new model and new field in property_device.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 4. Mesh devices mapping frontend: UI/UX update plan

**Description**
We need to update the UI/UX to support the mesh devices mapping.
Things to take into account:

- PropertyDevice.current_mesh_device_mapping can not be changed by the user, it is read only and updated automatically by the im_update_current_mesh_device_mapping function.
- The devices tab in HardwarePage.tsx is the place to manage it.
- The user must be able to see the current mapping and change it by adding new mappings.
- Seeing the mappings history is not needed.
- The device list in HardwarePage.tsx must let the user know that the device is a mesh device and show the current mapping in a intuitive way. Maybe group the mesh devices by LIC? Maybe present like a tree, where the LIC is the root and the mesh devices are the children?

your job is to brainstorm a solution and present it in a diagram or prototype.
You can present up to 3 different solutions.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 5. Mesh devices mapping frontend: UI/UX implementation

**Description**
We need to implement the UI/UX solution for the mesh devices mapping.
Task 4 produced 3 different solutions. The chosen one will be the Final Recommendation: Solution 3 (Enhanced List)
Two documents were produced:

- tasks/MESH_DEVICE_MAPPING_UI_SOLUTIONS.md: describes the 3 solutions and the reasons for choosing the final one.
- tasks/ENHANCED_LIST_PROTOTYPE.md: describes the final solution in more detail and proposes a implementation plan.

Your job is to implement the final solution based on the prototype document.

**Target directories**

- app (frontend)

**Status:** Done

---

## Task 6. Mesh devices mapping frontend: final touches

**Description**

- Implement data and crud jotai atoms for mesh_device_mapping.
- Implement "Adicionar Dispositivo" button in ManageNetworkModal that opens a modal to select a device to be mapped to the LIC.
- Implement "Mapear" button in AssignToLICModal.
- When clicking a unmapped or mapped mesh device in the device list, it should show DeviceDetailModal to allow editing the device. DeviceDetailModal should have a "Mapear" button that opens the AssignToLICModal to change the mapping.
- "Adicionar Dispositivo" button in ManageNetworkModal should show devices mapped to other LICs, because the user may want to change the mapping. It should display in the dropdown item the current mapping.
- DeviceDetailModal should allow to change the mapping.

**Target directories**

- app (frontend)

**Status:** Done

---
