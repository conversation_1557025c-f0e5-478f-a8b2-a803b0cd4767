# Task list info:

- name: 250908_01
- base_branch: develop

---

# Tasks

## Task 1. Ask the user to inform his CPF and phone number before proceeding to the app

**Description**
When the user registers himself in the system, the Directus api does not allow to send any other user data than first name, last name, email and password.
As we need to allow the user to inform his CPF and phone number, we need to "hook" into the login process and ask for this information before proceeding, if it is not already set.
Later, the user will be able to change it in his profile (button "Editar Perfil" in packages/app/src/pages/main/components/Settings.tsx).
Thus, we need a reusable component to edit the user profile, which will be used in the login flow and in the settings page.
The place to check if the user needs to inform his CPF and phone number is packages/app/src/pages/main/AppShell.tsx. If the user is logged in and has no CPF and phone number, show the profile edit component.
Make sure to use the existing components and guidelines.
Make sure to use the existing validation and error handling.
Make sure to validate the CPF format and phone number format (all brazilian formats).

**Target directories**

- packages/app (frontend)

**Status:** Done
**Assignee:** Codex

### Subtask 1.1. Extend auth service for profile fields

**Description**
Update `apiService.auth.fetchUser` to request `cpf` and `phone_number` from Directus `readMe`, and add an `updateProfile` method that wraps `updateMe({ cpf, phone_number, first_name?, last_name? })`. Keep typing changes minimal (augment a local narrow type used by the form or extend `AuthUser` if aligned) and do not broaden unrelated types.

**Target directories**

- packages/app/src/api (Directus auth service)

**Status:** Done
**Assignee:** Codex

### Subtask 1.2. Create UserProfileForm component

**Description**
Build a reusable form component to edit CPF and phone number with proper masking, validation, and error display. Reuse the CPF validation logic from `SignUpPage` and implement Brazilian phone validation/formatting. Expose callbacks for submit/cancel and accept initial values. Use existing UI primitives and patterns.

**Target directories**

- packages/app/src/components (shared form component)
- packages/app/src/pages (if colocated UI helpers are needed)

**Status:** Done
**Assignee:** Codex

### Subtask 1.3. Add AppShell profile-completion guard

**Description**
In `AppShell`, after existing auth/account/property guards, check the current user for missing `cpf` or `phone_number`. If missing, open a blocking modal with `UserProfileForm`. On successful save, call the new `auth.updateProfile`, refresh the user in state, and close the modal; otherwise keep blocking until complete.

**Target directories**

- packages/app/src/pages/main (AppShell integration)

**Status:** Done
**Assignee:** Codex

### Subtask 1.4. Integrate form in Settings

**Description**
Enable the “Editar Perfil” action in Settings to open the same `UserProfileForm` inside the existing Settings modal. Pre-fill from current user, submit via `auth.updateProfile`, show success/error using existing operation handling patterns.

**Target directories**

- packages/app/src/pages/main/components (Settings integration)

**Status:** Done
**Assignee:** Codex

### Subtask 1.5. Wire validations and operation handling

**Description**
Centralize CPF and phone validators/formatters in a small utility (exported for reuse). Wrap profile updates with `withOperationHandlingAtom` to standardize loading and error states. Ensure user state is refreshed after update so guards/settings reflect changes immediately.

**Target directories**

- packages/app/src/utils (validation helpers)
- packages/app/src/store (operation handling wiring)

**Status:** Done
