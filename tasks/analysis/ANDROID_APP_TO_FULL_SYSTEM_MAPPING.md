# Android App to Full System Mapping Analysis

This document maps the relationships between the Android Kotlin configurator app data models, the comprehensive irriga+ database schema, and protobuf message fields used for MQTT communication.

## Overview

The Android app (`irriganet-src-main/`) provides a simplified configuration interface that maps to the comprehensive database-driven approach of the full irriga+ system. This analysis identifies correspondences and gaps between the two systems.

## Database Schema Mapping

### 1. Codec/LIC Device Management

**Android App (`Codecs` table):**
```sql
CREATE TABLE Codecs (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    identity TEXT NOT NULL,
    name TEXT NOT NULL,
    wifi_ssid TEXT,
    wifi_passwd TEXT,
    last_devices_update INTEGER,
    last_scheduling_update INTEGER,
    last_device_scheduling_update INTEGER,
    last_automation_update INTEGER,
    last_config_update INTEGER,
    enabled INTEGER DEFAULT 1
)
```

**Full System Mapping:**
- `identity` → `device.identifier` (where `device.model = 'LIC'`)
- `name` → User-defined name (could be stored in `device.metadata` or separate table)
- `wifi_ssid/wifi_passwd` → Could be stored in `device.metadata` as JSON
- `last_*_update` → **Missing in full system** - should be added for change tracking
- `enabled` → Could map to `property_device.end_date` (NULL = enabled)

**Recommendation:** Add timestamp fields to full system for efficient change detection.

### 2. Device Hierarchy

**Android App Structure:**
```
Codecs (LIC devices)
  └── Groups (logical groupings)
      └── Mesh_Devices (mesh network devices)
          └── Devices (individual device endpoints)
```

**Full System Structure:**
```
Property
  └── PropertyDevice (device associations)
      ├── Device (LIC, WPC, VC, RM devices)
      └── MeshDeviceMapping (mesh-LIC relationships)
          └── Sector (irrigation zones)
              └── Project (irrigation projects)
```

**Key Differences:**
- Android app uses simple hierarchical grouping
- Full system uses property-based ownership with temporal validity
- Android app lacks property/account ownership concepts
- Full system has more complex device relationship modeling

### 3. Scheduling Configuration

**Android App (`Schedulings` table):**
```sql
CREATE TABLE Schedulings (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    ord_idx INTEGER NOT NULL,
    group_idx INTEGER NOT NULL,
    name TEXT NOT NULL,
    hour INTEGER NOT NULL,
    min INTEGER NOT NULL,
    start_time INTEGER NOT NULL,
    end_time INTEGER DEFAULT NULL,
    days_of_week INTEGER NOT NULL,
    number_of_steps INTEGER NOT NULL,
    allow_ferti INTEGER NOT NULL,
    allow_backwash INTEGER NOT NULL,
    waterpump_idx INTEGER DEFAULT NULL,
    waterpump_ord_idx INTEGER DEFAULT NULL,
    waterpump_working_time INTEGER DEFAULT NULL,
    ferti_idx INTEGER DEFAULT NULL,
    ferti_ord_idx INTEGER DEFAULT NULL,
    backwash_idx INTEGER DEFAULT NULL,
    backwash_ord_idx INTEGER DEFAULT NULL,
    enabled INTEGER DEFAULT 1
)
```

**Full System Mapping:**
- `name` → `irrigation_plan.name`
- `start_time` → `irrigation_plan.start_time` (converted from minutes to time)
- `days_of_week` → `irrigation_plan.days_of_week` (bitmask to JSON array)
- `allow_ferti` → `irrigation_plan.fertigation_enabled`
- `allow_backwash` → `irrigation_plan.backwash_enabled`
- `enabled` → `irrigation_plan.is_enabled`
- `number_of_steps` → Count of `irrigation_plan_step` records
- `waterpump_*` → `project.irrigation_water_pump` reference
- `ferti_*` → `project.fertigation_water_pump` reference

### 4. Device Scheduling

**Android App (`Device_Schedulings` table):**
```sql
CREATE TABLE Device_Schedulings (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    ord_idx INTEGER NOT NULL,
    scheduling_idx INTEGER NOT NULL,
    device_idx INTEGER NOT NULL,
    n_order INTEGER NOT NULL,
    status INTEGER NOT NULL,
    type INTEGER NOT NULL,
    time INTEGER NOT NULL,
    sector_working_time INTEGER NOT NULL,
    ferti_working_time INTEGER DEFAULT NULL,
    ferti_delay INTEGER NOT NULL
)
```

**Full System Mapping:**
- `scheduling_idx` → `irrigation_plan_step.irrigation_plan`
- `device_idx` → `irrigation_plan_step.sector` (via sector.valve_controller)
- `n_order` → `irrigation_plan_step.order`
- `sector_working_time` → `irrigation_plan_step.duration_seconds`
- `ferti_working_time` → `irrigation_plan_step.fertigation_duration_seconds`
- `ferti_delay` → `irrigation_plan_step.fertigation_start_delay_seconds`

## Protobuf Message Field Mapping

### 1. ConfigPackage

**Android App Implementation:**
```kotlin
val cfg = Config.ConfigPackage.newBuilder()
    .setBackwashCycle(backwashCycle.toInt())
    .setBackwashDuration(backwashDuration.toInt())
    .setBackwashDelay(30)
    .setRaingaugeEnabled(rainGaugeEnabled)
    .setRaingaugeFactor((1.0/rainGaugeResolution.toFloat()).toInt())
    .setRainfallLimit(rainfallLimit.toInt())
    .setRainfallPauseDuration(rainfallPauseDuration.toInt())
    .setWifi(wifiCfg)
    .build()
```

**Full System Mapping:**
- `backwashCycle` ← `property.backwash_period_minutes`
- `backwashDuration` ← `property.backwash_duration_minutes`
- `backwashDelay` ← `property.backwash_delay_seconds`
- `raingaugeEnabled` ← `property.rain_gauge_enabled`
- `raingaugeFactor` ← `1.0 / property.rain_gauge_resolution_mm`
- `rainfallLimit` ← `property.precipitation_volume_limit_mm`
- `rainfallPauseDuration` ← `property.precipitation_suspended_duration_hours * 60`

### 2. DevicesPackage

**Android App Implementation:**
```kotlin
val meshDevices = mapList.map { item ->
    Devices.DevicesData.newBuilder()
        .setIdx(item["ix"] as Int)
        .setMeshId(item["mi"] as Int)
        .setDeviceId(item["di"] as Int)
        .setDeviceType(item["tp"] as Int)
        .setOut1(item["o1"] as Int)
        .setOut2(item["o2"] as Int)
        .setInput(item["ip"] as Int)
        .setMode(item["md"] as Int)
        .setSector(item["sc"] as Int)
        .setGroupIdx(item["gp"] as Int)
        .setEqptVer(item["eq"] as Int)
        .build()
}
```

**Full System Mapping:**
- `idx` ← Sequential index of devices for LIC
- `meshId` ← From mesh device configuration
- `deviceId` ← From device identifier
- `deviceType` ← Derived from `device.model`
- `sector` ← From `sector.valve_controller_output`
- `groupIdx` ← From project grouping

## Configuration Gaps and Recommendations

### 1. Missing in Android App

**Property Management:**
- No concept of properties/accounts
- No address/location information
- No timezone handling

**Advanced Features:**
- No reservoir management
- No water pump configuration
- No project-based organization
- No temporal validity for device associations

**Business Rules:**
- No constraint enforcement
- No validation of device compatibility
- No overlap prevention for scheduling

### 2. Missing in Full System

**Change Tracking:**
- No `last_*_update` timestamp fields
- No efficient change detection mechanism
- No manual sync capabilities

**Communication Features:**
- No CRC validation for MQTT messages
- No dual communication mode support
- No direct device communication

### 3. Implementation Recommendations

**For Full System:**
1. Add timestamp-based change tracking fields to relevant tables
2. Implement CRC validation for MQTT communication
3. Add manual configuration push capabilities
4. Consider simplified configuration views for field use

**For Android App:**
1. Add property/account concepts for multi-tenant support
2. Implement basic validation rules
3. Add support for more device types
4. Enhance error handling and retry mechanisms

## Conclusion

The Android app demonstrates a successful simplified approach to LIC configuration that could inform improvements to the full system. The key insight is the effectiveness of timestamp-based change detection and direct MQTT communication for field configuration scenarios.

The full system's comprehensive data model provides better long-term maintainability and business rule enforcement, while the Android app's simplicity makes it more suitable for field technician use.

A hybrid approach combining both systems' strengths would provide the most robust solution for LIC configuration management.
