# Enhanced List Prototype - Mesh Device Mapping UI

## Overview
This prototype enhances the existing HardwarePage device list to show mesh device mappings with minimal UI changes while providing clear relationship indicators.

## Visual Design

### Filter Bar (New Component)
```
┌─────────────────────────────────────────────────────────┐
│ [All Devices ▼] [🔍 Search...]                         │
│ Filters: [📡 LICs] [💧 WPCs] [🌱 VCs] [🏗️ RMs] [⚠️ Unassigned] │
└─────────────────────────────────────────────────────────┘
```

### Enhanced Device List Items

#### LIC Device (Coordinator)
```
┌─────────────────────────────────────────────────────────┐
│ 📡 S/N: ABC123456789                              [>]   │
│ Controlador de Irrigação Localizado                    │
│ 🔗 Coordenador para 3 dispositivos                     │
│ Última atividade: 2 min atrás                          │
└─────────────────────────────────────────────────────────┘
```

#### Mesh Device (Mapped)
```
┌─────────────────────────────────────────────────────────┐
│   ├─ 💧 S/N: DEF123                               [>]   │
│   │   Controlador de Bomba d'Água - PL10               │
│   │   📡 → LIC-001 • Mapeado: 15 Jan 2024              │
│   │   Última atividade: 5 min atrás                    │
└─────────────────────────────────────────────────────────┘
```

#### Mesh Device (Unmapped)
```
┌─────────────────────────────────────────────────────────┐
│ ⚠️ 💧 S/N: STU345                                 [>]   │
│ Controlador de Bomba d'Água - PL10                     │
│ ❌ Não mapeado para nenhum LIC                         │
│ Última atividade: 10 min atrás                         │
└─────────────────────────────────────────────────────────┘
```

## Color Coding System

### Background Colors
- **LIC Devices**: Light green background (`bg-green-50`)
- **Mapped Mesh Devices**: Light gray background (`bg-gray-50`)  
- **Unmapped Mesh Devices**: Light yellow background (`bg-yellow-50`)

### Border Colors
- **LIC Devices**: Green border (`border-green-200`)
- **Mapped Mesh Devices**: Gray border (`border-gray-200`)
- **Unmapped Mesh Devices**: Yellow border (`border-yellow-200`)

### Text Colors
- **Mapping Status**: Green for mapped (`text-green-600`), Red for unmapped (`text-red-600`)
- **LIC Reference**: Blue for LIC identifier (`text-blue-600`)

## Interaction Patterns

### Device Actions
1. **Tap LIC Device**: Open "Manage Network" modal
   - View all mapped devices
   - Add new device to network
   - Remove devices from network

2. **Tap Mapped Mesh Device**: Open device detail modal with mapping info
   - View device details
   - Change mapping (reassign to different LIC)
   - Remove from current mapping

3. **Tap Unmapped Mesh Device**: Open "Assign to LIC" modal
   - Select available LIC from dropdown
   - Set mapping start date
   - Confirm assignment

### Filter Actions
- **All Devices**: Show complete list with relationships
- **By Network**: Group by LIC, show only mapped devices
- **Unassigned**: Show only unmapped mesh devices
- **Device Type Filters**: Show only selected device types

## Modal Designs

### Manage Network Modal (LIC Device)
```
┌─────────────────────────────────────────────────────────┐
│ Gerenciar Rede - LIC-001                         [×]   │
├─────────────────────────────────────────────────────────┤
│ 📡 ABC123456789                                         │
│ Controlador de Irrigação Localizado                    │
│                                                         │
│ Dispositivos Conectados (3):                           │
│                                                         │
│ ├─ 💧 DEF123 (WPC-PL10)                         [×]    │
│ │   Mapeado desde: 15 Jan 2024                         │
│ │                                                       │
│ ├─ 🌱 GHI456 (VC)                               [×]    │
│ │   Mapeado desde: 20 Jan 2024                         │
│ │                                                       │
│ └─ 🏗️ JKL789 (RM)                               [×]    │
│     Mapeado desde: 25 Jan 2024                         │
│                                                         │
│ [+ Adicionar Dispositivo]                              │
│                                                         │
│                                    [Cancelar] [Salvar] │
└─────────────────────────────────────────────────────────┘
```

### Assign to LIC Modal (Unmapped Device)
```
┌─────────────────────────────────────────────────────────┐
│ Mapear Dispositivo                               [×]   │
├─────────────────────────────────────────────────────────┤
│ 💧 STU345                                               │
│ Controlador de Bomba d'Água - PL10                     │
│                                                         │
│ Selecionar LIC:                                         │
│ [LIC-001 (ABC123456789) ▼]                             │
│                                                         │
│ Data de Início:                                         │
│ [31/07/2024 ▼]                                          │
│                                                         │
│ ⚠️ Este dispositivo será associado ao LIC selecionado   │
│ e poderá receber comandos através dele.                 │
│                                                         │
│                                    [Cancelar] [Mapear] │
└─────────────────────────────────────────────────────────┘
```

## Implementation Notes

### Component Updates Required
1. **HardwarePage.tsx**: Add filter bar and enhanced device rendering
2. **DeviceDetailModal.tsx**: Add mapping management section
3. **New Components**:
   - `DeviceFilterBar.tsx`
   - `ManageNetworkModal.tsx`
   - `AssignToLICModal.tsx`

### Data Processing
- Group devices by LIC relationships
- Sort: LICs first, then their mesh devices, then unmapped devices
- Add visual indicators based on mapping status

### State Management
- Extend device atoms to include mapping information
- Add filter state atoms
- Create mapping CRUD operation atoms
