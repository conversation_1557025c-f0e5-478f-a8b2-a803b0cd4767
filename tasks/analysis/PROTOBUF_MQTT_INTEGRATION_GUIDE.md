# Protobuf MQTT Integration Guide

This document provides comprehensive documentation for all protobuf messages used in the LIC (Localized Irrigation Controller) MQTT communication system, including implementation guidance for the `/mqtt-integration/` package.

## Overview

The LIC communication system uses Protocol Buffers (protobuf) for structured message exchange via MQTT. Messages are categorized into:

- **Downlink Messages** (TO LIC): Configuration and control commands sent from the system to LIC devices
- **Uplink Messages** (FROM LIC): Status reports and acknowledgments sent from LIC devices to the system

## MQTT Topic Structure

**Topic Pattern**: `/codec/{device_identifier}/{direction}`

- **Downlink Topic**: `/codec/{device_identifier}/downlink` - Messages sent TO LIC devices
- **Uplink Topic**: `/codec/{device_identifier}/report` - Messages received FROM LIC devices

**Message Encoding**: All messages are protobuf-encoded binary data with CRC16 checksum appended (2 bytes)

## Message Catalog

### 1. DOWNLINK MESSAGES (TO LIC)

All downlink messages are wrapped in the `IncomingPacket` container:

```protobuf
message IncomingPacket {
  uint64 id = 1;                                    // Message timestamp/ID
  oneof payload {
    codec.in.config.ConfigPackage config = 2;
    codec.in.devices.DevicesPackage devices = 3;
    codec.in.scheduling.SchedulingPackage scheduling = 4;
    codec.in.device_scheduling.DeviceSchedulingPackage dev_scheduling = 5;
    codec.in.automation.AutomationPackage automation = 6;
    codec.in.control.ControlPackage control = 7;
    codec.in.pause.PauseSchedulingPackage pause = 8;
    codec.in.request_info.RequestInfoPackage request_info = 9;
    codec.in.firmware_update.FirmwareUpdatePackage firmware_update = 10;
  }
}
```

#### 1.1 ConfigPackage - System Configuration

**Purpose**: Configure global LIC system parameters including backwash, rain gauge, and WiFi settings.

**Trigger Conditions**:

- Changes to `property` table fields: `backwash_*`, `rain_gauge_*`, `precipitation_*`
- WiFi credential updates
- Manual configuration push

**Message Structure**:

```protobuf
message ConfigPackage {
  uint32 backwash_cycle = 1;           // Backwash cycle period (cycles)
  uint32 backwash_duration = 2;        // Backwash duration (seconds)
  uint32 backwash_delay = 3;           // Delay before backwash (seconds)
  bool raingauge_enabled = 4;          // Rain gauge enable/disable
  uint32 raingauge_factor = 5;         // Rain gauge conversion factor
  uint32 rainfall_limit = 6;           // Rainfall threshold (mm)
  uint32 rainfall_pause_duration = 7;  // Pause duration after rain (minutes)
  WifiConfig wifi = 8;                 // WiFi configuration
  MeshConfig mesh = 9;                 // Mesh network configuration
}

message WifiConfig {
  string ssid = 1;                     // WiFi network name
  string password = 2;                 // WiFi password
}

message MeshConfig {
  uint32 encryption = 1;               // Mesh encryption type
  uint32 channel = 2;                  // Mesh communication channel
}
```

**Field Mappings**:

- `backwash_cycle` ← `property.backwash_period_minutes`
- `backwash_duration` ← `property.backwash_duration_minutes`
- `backwash_delay` ← `property.backwash_delay_seconds`
- `raingauge_enabled` ← `property.rain_gauge_enabled`
- `raingauge_factor` ← `1.0 / property.rain_gauge_resolution_mm`
- `rainfall_limit` ← `property.precipitation_volume_limit_mm`
- `rainfall_pause_duration` ← `property.precipitation_suspended_duration_hours`
- `wifi` ← Derived from device metadata, `device.metadata.wifiPassword` and `device.metadata.wifiSSID`. Will only be set if both values are set.
- `mesh` ← Won't be populated in the new system

**Android App Implementation**:

```kotlin
val cfgBuilder = Config.ConfigPackage.newBuilder()
    .setBackwashCycle(backwashCycle.toInt())
    .setBackwashDuration(backwashDuration.toInt())
    .setBackwashDelay(30)
    .setRaingaugeEnabled(rainGaugeEnabled)
    .setRaingaugeFactor((1.0/rainGaugeResolution.toFloat()).toInt())
    .setRainfallLimit(rainfallLimit.toInt())
    .setRainfallPauseDuration(rainfallPauseDuration.toInt())
    .setWifi(wifiCfg)
    .build()
```

**Database Trigger**: `trg_property_config_change` on `property` table
**Function**: `fn_send_lic_config_update(property_id UUID)`

#### 1.2 DevicesPackage - Mesh Device Configuration

**Purpose**: Configure mesh network devices and their operational parameters.

**Trigger Conditions**:

- Changes to `mesh_device_mapping` table
- Changes to `property_device` associations
- Device activation/deactivation

**Message Structure**:

```protobuf
message DevicesPackage {
  repeated DevicesData data = 1;       // List of device configurations
}

message DevicesData {
  uint32 idx = 1;                      // Device slot index
  uint32 mesh_id = 2;                  // Mesh device ID
  uint32 device_id = 3;                // Individual device ID
  uint32 device_type = 4;              // Device type code
  uint32 out1 = 5;                     // Output 1 state
  uint32 out2 = 6;                     // Output 2 state
  uint32 input = 7;                    // Input state
  uint32 mode = 8;                     // Operating mode
  uint32 sector = 9;                   // Sector assignment
  uint32 group_idx = 10;               // Group index
  uint32 eqpt_ver = 11;                // Equipment version
}
```

**Field Mappings**:

- `idx` ← Sequential device index for LIC
- `mesh_id` ← From mesh device configuration
- `device_id` ← From device metadata
- `device_type` ← Derived from `device.model`
- `sector` ← From `sector.valve_controller_output`
- `group_idx` ← From project grouping

**Android App Implementation**:

```kotlin
val meshDevices = mapList.map { item ->
    Devices.DevicesData.newBuilder()
        .setIdx(item["ix"] as Int)
        .setMeshId(item["mi"] as Int)
        .setDeviceId(item["di"] as Int)
        .setDeviceType(item["tp"] as Int)
        .setOut1(item["o1"] as Int)
        .setOut2(item["o2"] as Int)
        .setInput(item["ip"] as Int)
        .setMode(item["md"] as Int)
        .setSector(item["sc"] as Int)
        .setGroupIdx(item["gp"] as Int)
        .setEqptVer(item["eq"] as Int)
        .build()
}
val devicePackage = Devices.DevicesPackage.newBuilder()
    .addAllData(meshDevices)
    .build()
```

**Database Trigger**: `trg_mesh_device_mapping_config_change` on `mesh_device_mapping` table
**Function**: `fn_send_lic_devices_update(lic_property_device_id UUID)`

#### 1.3 SchedulingPackage - Irrigation Scheduling

**Purpose**: Configure irrigation schedules with timing, pump assignments, and fertigation settings.

**Trigger Conditions**:

- Changes to `irrigation_plan` table
- Changes to `irrigation_plan_step` table
- Schedule activation/deactivation

**Message Structure**:

```protobuf
message SchedulingPackage {
  MsgType type = 1;                    // Message type (scheduling only/with device scheduling)
  repeated SchedulingData scheduling_data = 2;  // List of schedules
}

message SchedulingData {
  uint32 idx = 1;                      // Schedule index
  uint32 start_time = 2;               // Start time (minutes since midnight)
  uint32 days_of_week = 3;             // Days bitmask (bit 0=Sunday, bit 6=Saturday)
  uint32 number_of_steps = 4;          // Number of irrigation steps
  uint32 waterpump_idx = 5;            // Water pump device index
  uint32 waterpump_working_time = 6;   // Total pump working time (minutes)
  bool allow_ferti = 7;                // Enable fertigation
  uint32 ferti_idx = 8;                // Fertigation pump device index
  bool allow_backwash = 9;             // Enable backwash
  uint32 backwash_idx = 10;            // Backwash device index
  uint32 group = 11;                   // Group assignment
}

enum MsgType {
  MSG_SCHEDULING_ONLY = 0;             // Scheduling data only
  MSG_SCHEDULING_WITH_DEVICE = 1;      // Scheduling with device scheduling
}
```

**Field Mappings**:

- `idx` ← Sequential schedule index
- `start_time` ← `irrigation_plan.start_time` (converted to minutes since midnight)
- `days_of_week` ← `irrigation_plan.days_of_week` (JSON array to bitmask)
- `number_of_steps` ← Count of `irrigation_plan_step` records
- `allow_ferti` ← `irrigation_plan.fertigation_enabled`
- `allow_backwash` ← `irrigation_plan.backwash_enabled`
- `waterpump_working_time` ← Sum of all step durations

**Database Trigger**: `trg_irrigation_plan_config_change` on `irrigation_plan` table
**Function**: `fn_send_lic_scheduling_update(project_id UUID)`

#### 1.4 DeviceSchedulingPackage - Device-Specific Scheduling

**Purpose**: Configure detailed device-level scheduling steps with timing and fertigation parameters.

**Trigger Conditions**:

- Changes to `irrigation_plan_step` table
- Device scheduling modifications

**Message Structure**:

```protobuf
message DeviceSchedulingPackage {
  repeated DeviceSchedulingData data = 1;  // List of device scheduling steps
}

message DeviceSchedulingData {
  uint32 idx = 1;                      // Device scheduling index
  uint32 scheduling_idx = 2;           // Reference to main scheduling
  uint32 device_idx = 3;               // Device index
  uint32 order = 4;                    // Execution order
  uint32 sector_working_time = 5;      // Sector operation time (minutes)
  uint32 ferti_working_time = 6;       // Fertigation time (minutes)
  uint32 ferti_delay = 7;              // Fertigation start delay (minutes)
}
```

**Field Mappings**:

- `scheduling_idx` ← `irrigation_plan_step.irrigation_plan`
- `device_idx` ← From `sector.valve_controller` mapping
- `order` ← `irrigation_plan_step.order`
- `sector_working_time` ← `irrigation_plan_step.duration_seconds / 60`
- `ferti_working_time` ← `irrigation_plan_step.fertigation_duration_seconds / 60`
- `ferti_delay` ← `irrigation_plan_step.fertigation_start_delay_seconds / 60`

**Android App Implementation**:

```kotlin
val deviceScheduling = deviceSchedulingList.map { item ->
    DeviceSchedulingOuterClass.DeviceSchedulingData.newBuilder()
        .setIdx(item["ix"] as Int)
        .setSchedulingIdx(item["si"] as Int)
        .setDeviceIdx(item["di"] as Int)
        .setOrder(item["or"] as Int)
        .setSectorWorkingTime(item["st"] as Int)
        .setFertiWorkingTime(item["ft"] as Int)
        .setFertiDelay(item["fd"] as Int)
        .build()
}
```

#### 1.5 AutomationPackage - Automation Rules

**Purpose**: Configure automation rules for pumps and level sensors.

**Trigger Conditions**:

- Changes to `reservoir` table
- Changes to `water_pump` automation settings

**Message Structure**:

```protobuf
message AutomationPackage {
  repeated AutomationData data = 1;    // List of automation rules
}

message AutomationData {
  uint32 level_idx = 1;                // Level sensor device index
  uint32 pump_idx = 2;                 // Pump device index
  uint32 mask = 3;                     // Automation condition mask
  uint32 value = 4;                    // Trigger threshold value
  uint32 working_time = 5;             // Pump operation duration (minutes)
}
```

**Field Mappings**:

- `level_idx` ← From `reservoir.reservoir_monitor` device mapping
- `pump_idx` ← From `reservoir.water_pump` mapping
- `mask` ← Automation condition configuration
- `value` ← Trigger threshold from reservoir settings
- `working_time` ← Pump operation duration

#### 1.6 ControlPackage - Direct Device Control

**Purpose**: Send immediate control commands to specific devices.

**Trigger Conditions**:

- User-initiated device control
- Emergency stop commands
- Manual device operation

**Message Structure**:

```protobuf
message ControlPackage {
  repeated ControlData data = 1;       // List of control commands
}

message ControlData {
  uint32 device_idx = 1;               // Target device index
  uint32 out1 = 2;                     // Output 1 command
  uint32 out2 = 3;                     // Output 2 command
  uint32 mode = 4;                     // Operating mode
  uint32 working_time = 5;             // Operation duration (minutes)
}
```

#### 1.7 PauseSchedulingPackage - Schedule Pause Control

**Purpose**: Pause or resume irrigation scheduling system-wide.

**Trigger Conditions**:

- User-initiated pause/resume
- Rain detection pause
- Emergency pause conditions

**Message Structure**:

```protobuf
message PauseSchedulingPackage {
  uint32 state = 1;                    // Pause state (0=resume, 1=pause)
  uint32 duration = 2;                 // Pause duration (minutes, 0=indefinite)
}
```

**Android App Implementation**:

```kotlin
val pause = Pause.PauseSchedulingPackage.newBuilder()
    .setState(if (pauseScheduling) 1 else 0)
    .setDuration(schedulePauseDuration)
    .build()
```

#### 1.8 RequestInfoPackage - Information Request

**Purpose**: Request specific information from LIC device.

**Trigger Conditions**:

- User-initiated information request
- System health checks
- Diagnostic queries

**Message Structure**:

```protobuf
message RequestInfoPackage {
  int32 type = 1;                      // Information type requested
}
```

**Information Types**:

- `0` - System status
- `1` - Device status
- `2` - Scheduling status
- `3` - Automation status

#### 1.9 FirmwareUpdatePackage - Firmware Update Control

**Purpose**: Initiate firmware update process on LIC device.

**Trigger Conditions**:

- Scheduled firmware updates
- Manual firmware deployment
- Security updates

**Message Structure**:

```protobuf
message FirmwareUpdatePackage {
  MsgType type = 1;                    // Update target (ESP/STM)
  MsgProtocol protocol = 2;            // Update protocol (HTTP/HTTPS)
  int32 activation_code = 3;           // Unique activation code
  int32 version = 4;                   // Target firmware version
}

enum MsgType {
  MSG_ESP = 0;                         // ESP32 firmware
  MSG_STM = 1;                         // STM32 firmware
}

enum MsgProtocol {
  MSG_HTTPS = 0;                       // HTTPS download
  MSG_HTTP = 1;                        // HTTP download
}
```

### 2. UPLINK MESSAGES (FROM LIC)

All uplink messages are wrapped in the `OutgoingPacket` container:

```protobuf
message OutgoingPacket {
  uint64 id = 1;                                    // Message timestamp/ID
  oneof payload {
    codec.out.info.InfoPackage info = 2;
    codec.out.status.SystemStatusPackage status = 3;
    codec.out.ack.AckPackage ack = 4;
    codec.out.scheduling_report.SchedulingReportPackage scheduling_report = 5;
    codec.out.automation_report.AutomationReportPackage automation_report = 6;
  }
}
```

#### 2.1 InfoPackage - Device Information

**Purpose**: Provides device identification and capability information.

**Trigger Conditions**:

- Response to RequestInfoPackage
- Device startup/initialization
- Periodic status updates

**Message Structure**:

```protobuf
message InfoPackage {
  string identity = 1;                 // Device unique identifier
  string version = 2;                  // Firmware version
  uint32 device_type = 3;              // Device type code
  repeated uint32 capabilities = 4;     // Device capability flags
  uint64 uptime = 5;                   // Device uptime (seconds)
  uint32 free_memory = 6;              // Available memory (bytes)
}
```

**Usage**: Store device information in database for monitoring and management.

#### 2.2 SystemStatusPackage - System Status Report

**Purpose**: Reports current system operational status and device states.

**Trigger Conditions**:

- Periodic status reporting (every 5-10 minutes)
- Status change events
- Response to status request

**Message Structure**:

```protobuf
message SystemStatusPackage {
  uint32 resets = 1;                   // Number of system resets
  uint32 scheduling_running = 2;       // Active scheduling count
  uint32 scheduling_paused = 3;        // Scheduling pause state
  uint32 paused_time = 4;              // Minutes since pause activated
  uint32 raining = 5;                  // Rain detection status (1=raining)
  uint32 rainfall = 6;                 // 24-hour rainfall accumulation
  uint64 sync_bitmask = 7;             // Synchronized devices bitmask
  uint64 on_bitmask = 8;               // Active devices bitmask
  uint64 input_bitmask = 9;            // Device input states bitmask
  uint32 failed_bitmask = 12;          // System failure bitmask
}
```

**Field Descriptions**:

- `resets` - System restart counter for stability monitoring
- `scheduling_running` - Number of active irrigation schedules
- `scheduling_paused` - Pause state (0=active, 1=paused)
- `paused_time` - Duration of current pause in minutes
- `raining` - Current rain detection status
- `rainfall` - Accumulated rainfall in last 24 hours (mm)
- `sync_bitmask` - Bitmask of devices successfully synchronized
- `on_bitmask` - Bitmask of currently active/powered devices
- `input_bitmask` - Bitmask of device input states
- `failed_bitmask` - Bitmask indicating device failures

**Usage**: Monitor system health, device connectivity, and operational status.

#### 2.3 AckPackage - Command Acknowledgment

**Purpose**: Acknowledges receipt and processing status of downlink commands.

**Trigger Conditions**:

- Response to any downlink command
- Configuration update confirmation
- Error reporting

**Message Structure**:

```protobuf
message AckPackage {
  uint32 package = 1;                  // Original message type acknowledged
  uint32 value = 2;                    // Acknowledgment status/result
}
```

**Acknowledgment Values**:

- `0` - Success/OK
- `1` - Invalid message format
- `2` - Configuration error
- `3` - Device not found
- `4` - Operation failed
- `5` - Insufficient resources

**Usage**: Confirm successful configuration updates and detect communication issues.

#### 2.4 SchedulingReportPackage - Irrigation Execution Report

**Purpose**: Reports detailed information about irrigation schedule execution.

**Trigger Conditions**:

- Schedule completion
- Schedule interruption/failure
- Periodic execution updates

**Message Structure**:

```protobuf
message SchedulingReportPackage {
  repeated SchedulingReportData data = 1;  // List of execution reports
}

message SchedulingReportData {
  int32 scheduling_idx = 1;            // Schedule identifier
  uint64 start_time = 2;               // Execution start timestamp
  uint64 end_time = 3;                 // Execution end timestamp
  uint64 sector_bitmask1 = 4;          // Sectors activated (bits 0-63)
  uint64 sector_bitmask2 = 5;          // Sectors activated (bits 64-127)
  uint64 ferti_bitmask1 = 6;           // Fertigation sectors (bits 0-63)
  uint64 ferti_bitmask2 = 7;           // Fertigation sectors (bits 64-127)
  uint32 waterpump = 8;                // Water pump status
  uint32 backwash = 9;                 // Backwash execution status
  uint64 backwash_time = 10;           // Backwash start timestamp
  int32 status = 11;                   // Execution status code
}
```

**Status Codes**:

- `0` - Completed successfully
- `1` - Interrupted by user
- `2` - Interrupted by rain
- `3` - Device failure
- `4` - Timeout
- `5` - Configuration error

**Usage**: Track irrigation execution history, analyze performance, and detect issues.

#### 2.5 AutomationReportPackage - Automation Execution Report

**Purpose**: Reports automation rule execution and pump operation history.

**Trigger Conditions**:

- Automation rule execution
- Pump activation/deactivation
- Level sensor events

**Message Structure**:

```protobuf
message AutomationReportPackage {
  repeated AutomationReportData data = 1;  // List of automation reports
}

message AutomationReportData {
  int32 auto_idx = 1;                  // Automation rule identifier
  uint64 start_time = 2;               // Execution start timestamp
  uint64 restart_time = 3;             // Restart timestamp (0 if no restart)
  uint64 end_time = 4;                 // Execution end timestamp (0 if ongoing)
  int32 status = 5;                    // Execution status
}
```

**Status Codes**:

- `0` - Completed successfully
- `1` - Interrupted
- `2` - Sensor failure
- `3` - Pump failure
- `4` - Manual override

**Usage**: Monitor automation system performance and pump operation history.

## 3. IMPLEMENTATION GUIDANCE

### 3.1 Message Encoding and CRC Validation

**Message Format**: `[Protobuf Message][CRC16 Checksum (2 bytes)]`

**CRC Implementation** (based on Android app):

```typescript
function calculateCRC16(data: Uint8Array): number {
  let crc = 0xffff;
  for (let i = 0; i < data.length; i++) {
    crc ^= data[i];
    for (let j = 0; j < 8; j++) {
      if (crc & 1) {
        crc = (crc >> 1) ^ 0xa001;
      } else {
        crc >>= 1;
      }
    }
  }
  return crc;
}

function encodeMessage(protobufMessage: Uint8Array): Uint8Array {
  const crc = calculateCRC16(protobufMessage);
  const crcBytes = new Uint8Array(2);
  crcBytes[0] = (crc >> 8) & 0xff; // High byte
  crcBytes[1] = crc & 0xff; // Low byte

  const result = new Uint8Array(protobufMessage.length + 2);
  result.set(protobufMessage);
  result.set(crcBytes, protobufMessage.length);
  return result;
}

function validateMessage(data: Uint8Array): {
  valid: boolean;
  message: Uint8Array;
} {
  if (data.length < 2) return { valid: false, message: new Uint8Array(0) };

  const message = data.slice(0, -2);
  const receivedCRC = (data[data.length - 2] << 8) | data[data.length - 1];
  const calculatedCRC = calculateCRC16(message);

  return {
    valid: receivedCRC === calculatedCRC,
    message: message,
  };
}
```

### 3.2 Database Change Detection

**Timestamp-Based Approach** (inspired by Android app):

```sql
-- Add timestamp tracking fields to relevant tables
ALTER TABLE property ADD COLUMN last_config_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE mesh_device_mapping ADD COLUMN last_devices_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE irrigation_plan ADD COLUMN last_scheduling_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE irrigation_plan_step ADD COLUMN last_scheduling_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE reservoir ADD COLUMN last_automation_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Create update triggers
CREATE OR REPLACE FUNCTION update_config_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.last_config_update = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_property_config_timestamp
  BEFORE UPDATE ON property
  FOR EACH ROW
  WHEN (OLD.backwash_duration_minutes IS DISTINCT FROM NEW.backwash_duration_minutes OR
        OLD.backwash_period_minutes IS DISTINCT FROM NEW.backwash_period_minutes OR
        OLD.rain_gauge_enabled IS DISTINCT FROM NEW.rain_gauge_enabled OR
        OLD.rain_gauge_resolution_mm IS DISTINCT FROM NEW.rain_gauge_resolution_mm OR
        OLD.precipitation_volume_limit_mm IS DISTINCT FROM NEW.precipitation_volume_limit_mm OR
        OLD.precipitation_suspended_duration_hours IS DISTINCT FROM NEW.precipitation_suspended_duration_hours)
  EXECUTE FUNCTION update_config_timestamp();
```

### 3.3 MQTT Integration Package Enhancement

**Enhanced Message Handler** (`mqtt-integration/src/handle-proto.ts`):

```typescript
import { codec } from "proto";
import { logger } from "./log";
import type { MQTTTopicMessage, MQTTTopicType } from "./types";

export function handleMQTTTopicMessage(
  message: MQTTTopicMessage<MQTTTopicType>
) {
  if (message.topicType === "report") {
    return handleUplinkMessage(message);
  } else if (message.topicType === "downlink") {
    return handleDownlinkMessage(message);
  }
}

function handleUplinkMessage(message: MQTTTopicMessage<"report">) {
  // Validate CRC
  const validation = validateMessage(message.payload);
  if (!validation.valid) {
    logger.error(`CRC validation failed for device ${message.deviceId}`);
    return;
  }

  try {
    // Decode OutgoingPacket
    const packet = codec.out.OutgoingPacket.decode(validation.message);

    switch (packet.payload.case) {
      case "info":
        handleInfoPackage(message.deviceId, packet.payload.value);
        break;
      case "status":
        handleStatusPackage(message.deviceId, packet.payload.value);
        break;
      case "ack":
        handleAckPackage(message.deviceId, packet.payload.value);
        break;
      case "schedulingReport":
        handleSchedulingReport(message.deviceId, packet.payload.value);
        break;
      case "automationReport":
        handleAutomationReport(message.deviceId, packet.payload.value);
        break;
    }
  } catch (error) {
    logger.error(
      `Failed to decode uplink message from ${message.deviceId}:`,
      error
    );
  }
}

async function handleStatusPackage(
  deviceId: string,
  status: codec.out.status.SystemStatusPackage
) {
  // Store status in database
  await db.query(
    `
    INSERT INTO device_status (device_id, resets, scheduling_running, scheduling_paused,
                              raining, rainfall, sync_bitmask, on_bitmask, input_bitmask,
                              failed_bitmask, timestamp)
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW())
    ON CONFLICT (device_id) DO UPDATE SET
      resets = EXCLUDED.resets,
      scheduling_running = EXCLUDED.scheduling_running,
      scheduling_paused = EXCLUDED.scheduling_paused,
      raining = EXCLUDED.raining,
      rainfall = EXCLUDED.rainfall,
      sync_bitmask = EXCLUDED.sync_bitmask,
      on_bitmask = EXCLUDED.on_bitmask,
      input_bitmask = EXCLUDED.input_bitmask,
      failed_bitmask = EXCLUDED.failed_bitmask,
      timestamp = NOW()
  `,
    [
      deviceId,
      status.resets,
      status.schedulingRunning,
      status.schedulingPaused,
      status.raining,
      status.rainfall,
      status.syncBitmask.toString(),
      status.onBitmask.toString(),
      status.inputBitmask.toString(),
      status.failedBitmask,
    ]
  );
}
```

### 3.4 Configuration Message Builders

**Config Message Builder**:

```typescript
async function buildConfigMessage(propertyId: string): Promise<Uint8Array> {
  const property = await db.query(
    `
    SELECT backwash_duration_minutes, backwash_period_minutes, backwash_delay_seconds,
           rain_gauge_enabled, rain_gauge_resolution_mm, precipitation_volume_limit_mm,
           precipitation_suspended_duration_hours
    FROM property WHERE id = $1
  `,
    [propertyId]
  );

  const config = codec.in.config.ConfigPackage.create({
    backwashCycle: property.backwash_period_minutes,
    backwashDuration: property.backwash_duration_minutes,
    backwashDelay: property.backwash_delay_seconds,
    raingaugeEnabled: property.rain_gauge_enabled,
    raingaugeFactor: Math.round(1.0 / property.rain_gauge_resolution_mm),
    rainfallLimit: property.precipitation_volume_limit_mm,
    rainfallPauseDuration: property.precipitation_suspended_duration_hours * 60,
    wifi: {
      ssid: await getWifiSSID(propertyId),
      password: await getWifiPassword(propertyId),
    },
  });

  const packet = codec.in.IncomingPacket.create({
    id: BigInt(Date.now()),
    payload: { case: "config", value: config },
  });

  const encoded = codec.in.IncomingPacket.encode(packet).finish();
  return encodeMessage(encoded);
}
```

### 3.5 Database Functions Implementation

**Configuration Update Function**:

```sql
CREATE OR REPLACE FUNCTION fn_send_lic_config_update(property_id UUID)
RETURNS VOID AS $$
DECLARE
  lic_devices RECORD;
BEGIN
  -- Find all LIC devices for this property
  FOR lic_devices IN
    SELECT d.identifier
    FROM device d
    JOIN property_device pd ON d.id = pd.device
    WHERE pd.property = property_id
      AND d.model = 'LIC'
      AND pd.end_date IS NULL
  LOOP
    -- Queue MQTT message for each LIC device
    INSERT INTO mqtt_message_queue (device_identifier, message_type, property_id, created_at)
    VALUES (lic_devices.identifier, 'config', property_id, NOW());
  END LOOP;
END;
$$ LANGUAGE plpgsql;
```

### 3.6 Message Queue Processing

**Background Message Processor**:

```typescript
async function processMessageQueue() {
  const pendingMessages = await db.query(`
    SELECT id, device_identifier, message_type, property_id
    FROM mqtt_message_queue
    WHERE processed_at IS NULL
    ORDER BY created_at
    LIMIT 10
  `);

  for (const msg of pendingMessages) {
    try {
      let messageData: Uint8Array;

      switch (msg.message_type) {
        case "config":
          messageData = await buildConfigMessage(msg.property_id);
          break;
        case "devices":
          messageData = await buildDevicesMessage(msg.property_id);
          break;
        case "scheduling":
          messageData = await buildSchedulingMessage(msg.property_id);
          break;
        // ... other message types
      }

      const topic = `/codec/${msg.device_identifier}/downlink`;
      await mqttClient.publish(topic, messageData);

      await db.query(
        `
        UPDATE mqtt_message_queue
        SET processed_at = NOW()
        WHERE id = $1
      `,
        [msg.id]
      );
    } catch (error) {
      logger.error(`Failed to process message ${msg.id}:`, error);
      await db.query(
        `
        UPDATE mqtt_message_queue
        SET error_count = error_count + 1, last_error = $2
        WHERE id = $1
      `,
        [msg.id, error.message]
      );
    }
  }
}

// Run every 30 seconds
setInterval(processMessageQueue, 30000);
```

## 4. MESSAGE FLOW AND SEQUENCING

### 4.1 Configuration Update Flow

```
Database Change → Trigger → Function → Queue Message → Background Processor →
MQTT Publish → LIC Device → Acknowledgment → Status Update
```

**Typical Sequence**:

1. User updates property configuration in web interface
2. Database trigger detects change and calls `fn_send_lic_config_update()`
3. Function queues MQTT message for all associated LIC devices
4. Background processor builds ConfigPackage message
5. Message is encoded with CRC and published to MQTT
6. LIC device receives, validates, and applies configuration
7. LIC sends AckPackage confirming receipt
8. LIC sends updated SystemStatusPackage reflecting new configuration

### 4.2 Error Handling and Retry Logic

**Message Retry Strategy**:

- Failed messages are retried up to 3 times with exponential backoff
- CRC validation failures trigger immediate retry
- Device offline scenarios queue messages for later delivery
- Critical configuration errors generate alerts

**Error Recovery**:

```typescript
async function handleMessageError(messageId: number, error: Error) {
  const message = await db.query(
    `
    SELECT error_count, message_type, device_identifier
    FROM mqtt_message_queue
    WHERE id = $1
  `,
    [messageId]
  );

  if (message.error_count >= 3) {
    // Mark as failed and alert
    await db.query(
      `
      UPDATE mqtt_message_queue
      SET status = 'failed', failed_at = NOW()
      WHERE id = $1
    `,
      [messageId]
    );

    await sendAlert(
      `Message delivery failed for device ${message.device_identifier}`
    );
  } else {
    // Schedule retry with exponential backoff
    const retryDelay = Math.pow(2, message.error_count) * 60000; // 1min, 2min, 4min
    await db.query(
      `
      UPDATE mqtt_message_queue
      SET retry_at = NOW() + INTERVAL '${retryDelay} milliseconds',
          error_count = error_count + 1
      WHERE id = $1
    `,
      [messageId]
    );
  }
}
```

### 4.3 Device Status Monitoring

**Health Check Implementation**:

```typescript
async function performDeviceHealthCheck() {
  // Find devices that haven't reported status in 10 minutes
  const staleDevices = await db.query(`
    SELECT device_id, last_seen
    FROM device_status
    WHERE timestamp < NOW() - INTERVAL '10 minutes'
  `);

  for (const device of staleDevices) {
    // Request status update
    const requestInfo = codec.in.request_info.RequestInfoPackage.create({
      type: 0, // System status
    });

    const packet = codec.in.IncomingPacket.create({
      id: BigInt(Date.now()),
      payload: { case: "requestInfo", value: requestInfo },
    });

    const encoded = codec.in.IncomingPacket.encode(packet).finish();
    const message = encodeMessage(encoded);

    await mqttClient.publish(`/codec/${device.device_id}/downlink`, message);
  }
}

// Run health check every 5 minutes
setInterval(performDeviceHealthCheck, 300000);
```

## 5. INTEGRATION WITH EXISTING SYSTEM

### 5.1 Database Schema Extensions

**Required Tables**:

```sql
-- Message queue for MQTT communication
CREATE TABLE mqtt_message_queue (
  id SERIAL PRIMARY KEY,
  device_identifier VARCHAR(255) NOT NULL,
  message_type VARCHAR(50) NOT NULL,
  property_id UUID,
  project_id UUID,
  created_at TIMESTAMP DEFAULT NOW(),
  processed_at TIMESTAMP,
  retry_at TIMESTAMP,
  error_count INTEGER DEFAULT 0,
  last_error TEXT,
  status VARCHAR(20) DEFAULT 'pending'
);

-- Device status tracking
CREATE TABLE device_status (
  device_id VARCHAR(255) PRIMARY KEY,
  resets INTEGER,
  scheduling_running INTEGER,
  scheduling_paused INTEGER,
  raining INTEGER,
  rainfall INTEGER,
  sync_bitmask TEXT,
  on_bitmask TEXT,
  input_bitmask TEXT,
  failed_bitmask INTEGER,
  timestamp TIMESTAMP DEFAULT NOW()
);

-- Irrigation execution history
CREATE TABLE irrigation_execution_log (
  id SERIAL PRIMARY KEY,
  device_id VARCHAR(255),
  scheduling_idx INTEGER,
  start_time TIMESTAMP,
  end_time TIMESTAMP,
  status INTEGER,
  sector_bitmask1 TEXT,
  sector_bitmask2 TEXT,
  ferti_bitmask1 TEXT,
  ferti_bitmask2 TEXT,
  waterpump_status INTEGER,
  backwash_status INTEGER,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 5.2 API Integration Points

**REST API Endpoints**:

```typescript
// Manual configuration push
app.post("/api/devices/:deviceId/config/push", async (req, res) => {
  const { deviceId } = req.params;
  const { configType } = req.body;

  try {
    await queueConfigurationMessage(deviceId, configType);
    res.json({ success: true, message: "Configuration queued for delivery" });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Device status endpoint
app.get("/api/devices/:deviceId/status", async (req, res) => {
  const { deviceId } = req.params;

  const status = await db.query(
    `
    SELECT * FROM device_status WHERE device_id = $1
  `,
    [deviceId]
  );

  res.json(status);
});

// Irrigation history endpoint
app.get("/api/devices/:deviceId/irrigation-history", async (req, res) => {
  const { deviceId } = req.params;
  const { startDate, endDate } = req.query;

  const history = await db.query(
    `
    SELECT * FROM irrigation_execution_log
    WHERE device_id = $1
      AND start_time BETWEEN $2 AND $3
    ORDER BY start_time DESC
  `,
    [deviceId, startDate, endDate]
  );

  res.json(history);
});
```

## 6. TESTING AND VALIDATION

### 6.1 Message Validation Tests

**Unit Tests**:

```typescript
describe("Protobuf Message Encoding", () => {
  test("ConfigPackage encoding with CRC", () => {
    const config = codec.in.config.ConfigPackage.create({
      backwashCycle: 24,
      backwashDuration: 30,
      raingaugeEnabled: true,
      rainfallLimit: 10,
    });

    const packet = codec.in.IncomingPacket.create({
      id: BigInt(123456789),
      payload: { case: "config", value: config },
    });

    const encoded = codec.in.IncomingPacket.encode(packet).finish();
    const withCRC = encodeMessage(encoded);

    // Validate CRC
    const validation = validateMessage(withCRC);
    expect(validation.valid).toBe(true);

    // Validate decoded message
    const decoded = codec.in.IncomingPacket.decode(validation.message);
    expect(decoded.payload.case).toBe("config");
    expect(decoded.payload.value.backwashCycle).toBe(24);
  });
});
```

### 6.2 Integration Tests

**MQTT Communication Tests**:

```typescript
describe("MQTT Integration", () => {
  test("End-to-end configuration update", async () => {
    // Update property configuration
    await db.query(
      `
      UPDATE property
      SET backwash_duration_minutes = 45
      WHERE id = $1
    `,
      [testPropertyId]
    );

    // Wait for message processing
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Verify message was queued
    const queuedMessage = await db.query(
      `
      SELECT * FROM mqtt_message_queue
      WHERE property_id = $1 AND message_type = 'config'
      ORDER BY created_at DESC LIMIT 1
    `,
      [testPropertyId]
    );

    expect(queuedMessage).toBeDefined();
    expect(queuedMessage.processed_at).toBeDefined();
  });
});
```

## 7. CONCLUSION

This comprehensive protobuf MQTT integration guide provides:

1. **Complete Message Catalog** - All downlink and uplink message types with detailed field descriptions
2. **Implementation Guidance** - CRC validation, database triggers, and message processing
3. **Android App Insights** - Proven patterns from the working Android configurator
4. **Database Integration** - Schema extensions and trigger implementations
5. **Error Handling** - Robust retry logic and health monitoring
6. **Testing Framework** - Unit and integration test examples

The implementation combines the comprehensive data model of the full irriga+ system with the proven communication patterns from the Android app, providing a robust foundation for LIC device configuration and monitoring via MQTT.

**Key Success Factors**:

- CRC validation ensures message integrity
- Timestamp-based change detection provides efficiency
- Message queuing enables reliable delivery
- Background processing prevents UI blocking
- Comprehensive error handling ensures system reliability

This approach will enable seamless integration between the irriga+ system and LIC devices while maintaining the flexibility and reliability demonstrated by the Android configurator app.
