# LIC Simulator Implementation Plan

## Executive Summary

This document provides a comprehensive implementation plan for a LIC (Localized Irrigation Controller) simulator that behaves exactly like a real ESP32-based LIC device. The simulator will enable testing of the MQTT integration without requiring physical hardware, supporting development, testing, and debugging of the Irriga Mais system.

## Supporting Analysis Documents

This implementation plan is based on detailed analysis documented in the following supporting documents:

### 1. MQTT Communication Analysis

**Document**: `tasks/analysis/MQTT_COMMUNICATION_ANALYSIS.md`

Provides comprehensive analysis of:

- MQTT broker configuration and connection parameters
- Topic structure and device identification patterns (`/codec/{device_id}/report` and `/codec/{device_id}/downlink`)
- Message flow patterns for device registration, configuration sync, real-time control, status monitoring, and firmware updates
- ESP32 firmware MQTT implementation details including connection setup, message handling, and processing logic
- Error handling, resilience mechanisms, and performance considerations
- Integration service MQTT implementation for comparison and compatibility

### 2. Protobuf Message Analysis

**Document**: `tasks/analysis/PROTOBUF_MESSAGE_ANALYSIS.md`

Documents all protobuf message types and processing logic:

- Complete `IncomingPacket` message types (ConfigPackage, DevicesPackage, SchedulingPackage, etc.)
- Complete `OutgoingPacket` message types (InfoPackage, SystemStatusPackage, SchedulingReportPackage, etc.)
- ESP32 firmware processing logic for each message type with exact state changes
- Expected responses and acknowledgment patterns
- Message processing pipeline including CRC validation and error handling
- Simulator implementation requirements for exact behavior replication

### 3. Device State Management and Persistence Design

**Document**: `tasks/analysis/DEVICE_STATE_PERSISTENCE_DESIGN.md`

Defines state management and persistence architecture:

- ESP32 firmware state structures and global arrays analysis
- NVS (Non-Volatile Storage) persistence mechanism used by ESP32
- TypeScript state interfaces that mirror ESP32 structures
- File-based JSON persistence design for simulator state storage
- State synchronization strategy using timestamp-based configuration tracking
- Persistence triggers and state consistency mechanisms

## Project Overview

### Objectives

1. **Exact Behavior Replication**: Simulate all LIC device behaviors including message processing, state management, and timing
2. **MQTT Integration Testing**: Enable comprehensive testing of the MQTT communication layer
3. **Development Support**: Provide a reliable testing environment for system development
4. **Debugging Capabilities**: Offer enhanced debugging and monitoring capabilities
5. **Scalability**: Support multiple simultaneous LIC device simulations

### Key Requirements

- **Protocol Compliance**: Handle all protobuf message types exactly as ESP32 firmware
- **State Persistence**: Maintain state across simulator restarts using file-based storage
- **Timing Accuracy**: Simulate realistic response times and scheduling execution
- **Configuration Management**: Support all LIC configuration options and synchronization
- **Error Simulation**: Simulate various error conditions and edge cases

## Architecture Overview

### Technology Stack

- **Runtime**: Bun (JavaScript runtime optimized for performance)
- **Language**: TypeScript (type safety and development experience)
- **MQTT Client**: mqtt.js (mature MQTT client library)
- **Protobuf**: protobufjs (JavaScript protobuf implementation)
- **Persistence**: JSON files (human-readable state storage)
- **Logging**: pino (high-performance logging)
- **Testing**: Bun test (built-in testing framework)

### Module Structure

The LIC simulator will be implemented as a new package in the existing monorepo structure:

```
packages/
├── lic-simulator/                   # New LIC simulator package
│   ├── src/
│   │   ├── core/
│   │   │   ├── LICSimulator.ts           # Main simulator class
│   │   │   ├── MessageProcessor.ts       # Protobuf message processing
│   │   │   ├── StateManager.ts           # Device state management
│   │   │   └── SchedulingEngine.ts       # Irrigation scheduling logic
│   │   ├── mqtt/
│   │   │   ├── MQTTClient.ts            # MQTT connection and messaging
│   │   │   ├── MessageHandler.ts        # MQTT message routing
│   │   │   └── TopicManager.ts          # Topic subscription management
│   │   ├── protobuf/
│   │   │   ├── MessageBuilder.ts        # Protobuf message construction
│   │   │   ├── MessageParser.ts         # Protobuf message parsing
│   │   │   └── CRCValidator.ts          # CRC16 validation (copied from mqtt-integration)
│   │   ├── persistence/
│   │   │   ├── StatePersistence.ts      # File-based state persistence
│   │   │   ├── ConfigurationManager.ts  # Configuration management
│   │   │   └── BackupManager.ts         # State backup and recovery
│   │   ├── scheduling/
│   │   │   ├── ScheduleExecutor.ts      # Schedule execution engine
│   │   │   ├── DeviceController.ts      # Device control logic
│   │   │   ├── AutomationEngine.ts      # Automation rule processing
│   │   │   └── TimingManager.ts         # Timing and delay management
│   │   ├── simulation/
│   │   │   ├── DeviceSimulator.ts       # Individual device simulation
│   │   │   ├── SensorSimulator.ts       # Sensor data simulation
│   │   │   ├── ErrorSimulator.ts        # Error condition simulation
│   │   │   └── NetworkSimulator.ts      # Network condition simulation
│   │   ├── utils/
│   │   │   ├── Logger.ts                # Logging utilities
│   │   │   ├── TimeUtils.ts             # Time and date utilities
│   │   │   ├── ValidationUtils.ts       # Data validation utilities
│   │   │   └── ConfigUtils.ts           # Configuration utilities
│   │   └── types/
│   │       ├── SimulatorTypes.ts        # Core simulator types
│   │       ├── StateTypes.ts            # State management types
│   │       └── ConfigTypes.ts           # Configuration types
│   ├── config/
│   │   ├── default.json                 # Default configuration
│   │   ├── development.json             # Development configuration
│   │   └── production.json              # Production configuration
│   ├── tests/
│   │   ├── unit/                        # Unit tests
│   │   ├── integration/                 # Integration tests
│   │   └── fixtures/                    # Test fixtures and data
│   ├── docs/
│   │   ├── API.md                       # API documentation
│   │   ├── Configuration.md             # Configuration guide
│   │   └── Troubleshooting.md           # Troubleshooting guide
│   ├── scripts/
│   │   ├── start.ts                     # Startup script
│   │   ├── test.ts                      # Test runner
│   │   └── build.ts                     # Build script
│   ├── package.json                     # Project dependencies
│   ├── tsconfig.json                    # TypeScript configuration
│   └── README.md                        # Package documentation
├── protobuf/                        # Existing protobuf package (dependency)
├── mqtt-integration/                # Existing MQTT integration package (reference)
└── ...                              # Other existing packages
```

### Package Dependencies

The `packages/lic-simulator` package will depend on:

1. **`packages/protobuf`**: For all protobuf message parsing and encoding operations

   - Import generated TypeScript types from `packages/protobuf/generated`
   - Use protobuf encoding/decoding functions
   - Maintain consistency with existing protobuf usage patterns

2. **External Dependencies**:
   - `mqtt`: MQTT client library
   - `pino`: High-performance logging
   - Standard Node.js/Bun runtime libraries

### CRC Implementation Strategy

The simulator must use identical CRC16 validation logic to ensure message compatibility:

1. **Copy CRC Implementation**: Copy the CRC16 validation code from `packages/mqtt-integration/src/utils/crc.ts` to `packages/lic-simulator/src/protobuf/CRCValidator.ts`

2. **Maintain Identical Behavior**: The CRC calculation must produce identical results to the ESP32 firmware and existing MQTT integration

3. **Implementation Details**:

   ```typescript
   // packages/lic-simulator/src/protobuf/CRCValidator.ts
   export class CRCValidator {
     static calculate(data: Buffer): number {
       // Copy exact implementation from packages/mqtt-integration
       // to ensure identical CRC calculation behavior
     }

     static validate(payload: Buffer): boolean {
       if (payload.length < 2) return false;

       const receivedCRC = payload.readUInt16BE(payload.length - 2);
       const calculatedCRC = this.calculate(payload.slice(0, -2));

       return receivedCRC === calculatedCRC;
     }

     static addCRC(data: Buffer): Buffer {
       const crc = this.calculate(data);
       const crcBuffer = Buffer.allocUnsafe(2);
       crcBuffer.writeUInt16BE(crc, 0);
       return Buffer.concat([data, crcBuffer]);
     }
   }
   ```

### Protobuf Integration Strategy

The simulator will leverage the existing protobuf infrastructure:

1. **Import Generated Types**: Use TypeScript types from `packages/protobuf/generated`

   ```typescript
   import {
     IncomingPacket,
     OutgoingPacket,
     ConfigPackage,
     DevicesPackage,
     InfoPackage,
     SystemStatusPackage,
     // ... all other message types
   } from "@packages/protobuf/generated";
   ```

2. **Message Parsing**: Use existing protobuf encoding/decoding functions

   ```typescript
   // Parsing incoming messages
   const message = IncomingPacket.decode(protobufData);

   // Encoding outgoing messages
   const encoded = OutgoingPacket.encode(responseMessage).finish();
   ```

3. **Consistency**: Ensure the simulator uses the same protobuf definitions as the real system for perfect compatibility

## Core Components

### 1. LICSimulator (Main Class)

The central orchestrator that coordinates all simulator components:

```typescript
class LICSimulator {
  private codecId: string;
  private mqttClient: MQTTClient;
  private messageProcessor: MessageProcessor;
  private stateManager: StateManager;
  private schedulingEngine: SchedulingEngine;
  private persistence: StatePersistence;
  private logger: Logger;

  constructor(config: SimulatorConfig) {
    this.codecId = config.codecId;
    this.initializeComponents(config);
  }

  async start(): Promise<void> {
    // Load persisted state
    await this.persistence.loadState();

    // Initialize MQTT connection
    await this.mqttClient.connect();

    // Start scheduling engine
    this.schedulingEngine.start();

    // Begin status reporting
    this.startStatusReporting();

    this.logger.info("LIC Simulator started", { codecId: this.codecId });
  }

  async stop(): Promise<void> {
    // Save current state
    await this.persistence.saveState();

    // Stop all components
    this.schedulingEngine.stop();
    await this.mqttClient.disconnect();

    this.logger.info("LIC Simulator stopped", { codecId: this.codecId });
  }

  // Message handling
  private async handleIncomingMessage(
    topic: string,
    payload: Buffer
  ): Promise<void> {
    try {
      const message = await this.messageProcessor.parseIncomingMessage(payload);
      const response = await this.messageProcessor.processMessage(message);

      if (response) {
        await this.mqttClient.publishResponse(response);
      }

      // Save state after configuration changes
      if (this.messageProcessor.isConfigurationMessage(message)) {
        await this.persistence.saveConfiguration();
      }
    } catch (error) {
      this.logger.error("Failed to process message", { error, topic });
    }
  }

  // Status reporting
  private startStatusReporting(): void {
    setInterval(async () => {
      const statusMessage = this.messageProcessor.buildStatusMessage();
      await this.mqttClient.publishStatus(statusMessage);
    }, 30000); // Every 30 seconds
  }
}
```

### 2. MessageProcessor

Handles all protobuf message processing with exact ESP32 firmware behavior:

```typescript
import {
  IncomingPacket,
  OutgoingPacket,
  InfoPackage,
} from "@packages/protobuf/generated";
import { CRCValidator } from "../protobuf/CRCValidator";

class MessageProcessor {
  private stateManager: StateManager;
  private schedulingEngine: SchedulingEngine;
  private logger: Logger;

  async parseIncomingMessage(payload: Buffer): Promise<IncomingPacket> {
    // Validate CRC using copied implementation from mqtt-integration
    const crcValid = CRCValidator.validate(payload);
    if (!crcValid) {
      throw new Error("CRC validation failed");
    }

    // Remove CRC bytes and parse protobuf using existing protobuf package
    const protobufData = payload.slice(0, -2);
    return IncomingPacket.decode(protobufData);
  }

  async processMessage(
    message: IncomingPacket
  ): Promise<OutgoingPacket | null> {
    const messageId = message.id;

    switch (message.payload.oneofKind) {
      case "config":
        return this.processConfigPackage(message.payload.config, messageId);

      case "devices":
        return this.processDevicesPackage(message.payload.devices, messageId);

      case "scheduling":
        return this.processSchedulingPackage(
          message.payload.scheduling,
          messageId
        );

      case "devScheduling":
        return this.processDeviceSchedulingPackage(
          message.payload.devScheduling,
          messageId
        );

      case "automation":
        return this.processAutomationPackage(
          message.payload.automation,
          messageId
        );

      case "control":
        return this.processControlPackage(message.payload.control, messageId);

      case "pause":
        return this.processPausePackage(message.payload.pause, messageId);

      case "requestInfo":
        return this.processRequestInfoPackage(
          message.payload.requestInfo,
          messageId
        );

      case "firmwareUpdate":
        return this.processFirmwareUpdatePackage(
          message.payload.firmwareUpdate,
          messageId
        );

      default:
        this.logger.warn("Unknown message type", { messageId });
        return null;
    }
  }

  private async processConfigPackage(
    config: ConfigPackage,
    messageId: bigint
  ): Promise<OutgoingPacket> {
    // Update system configuration
    this.stateManager.updateSystemConfig({
      backwashCycle: config.backwashCycle,
      backwashDuration: config.backwashDuration,
      backwashDelay: config.backwashDelay,
      raingaugeEnabled: config.raingaugeEnabled,
      raingaugeFactor: config.raingaugeFactor,
      rainfallLimit: config.rainfallLimit,
      rainfallPauseDuration: config.rainfallPauseDuration,
      publishRawData: config.publishRawData,
      debug: config.debug,
      enableScheduleResumption: config.enableScheduleResumption,
      enableFertiResumption: config.enableFertiResumption,
      maxResumptionAttempts: config.maxResumptionAttempts,
    });

    // Update configuration timestamp
    this.stateManager.updateConfigTimestamp();

    // Return InfoPackage with updated timestamps
    return this.buildInfoPackage();
  }

  buildInfoPackage(): OutgoingPacket {
    const state = this.stateManager.getState();

    const infoPackage: InfoPackage = {
      codecId: state.codecId,
      firmwareEsp: state.firmwareEsp,
      firmwareMesh: state.firmwareMesh,
      hardwareVersion: state.hardwareVersion,
      resets: state.resets,
      schedulingRunning: state.schedulingRunning,
      schedulingPaused: state.schedulingPaused ? 1 : 0,
      devicesId: state.devicesId,
      schedulingId: state.schedulingId,
      devSchedulingId: state.devSchedulingId,
      automationId: state.automationId,
      configId: state.configId,
    };

    return {
      id: BigInt(Math.floor(Date.now() / 1000)),
      payload: {
        oneofKind: "info",
        info: infoPackage,
      },
    };
  }
}
```

### 3. MQTTClient

Manages MQTT connection and message publishing with exact protocol compliance:

```typescript
import { OutgoingPacket } from "@packages/protobuf/generated";
import { CRCValidator } from "../protobuf/CRCValidator";

class MQTTClient {
  private client: mqtt.MqttClient;
  private codecId: string;
  private reportTopic: string;
  private downlinkTopic: string;
  private messageHandler: (topic: string, payload: Buffer) => Promise<void>;
  private logger: Logger;

  constructor(config: MQTTConfig, codecId: string) {
    this.codecId = codecId;
    this.reportTopic = `/codec/${codecId}/report`;
    this.downlinkTopic = `/codec/${codecId}/downlink`;
    this.initializeClient(config);
  }

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.client.on("connect", () => {
        this.logger.info("MQTT connected", { codecId: this.codecId });

        // Subscribe to downlink topic
        this.client.subscribe(this.downlinkTopic, { qos: 0 }, (err) => {
          if (err) {
            reject(err);
          } else {
            this.logger.info("Subscribed to downlink topic", {
              topic: this.downlinkTopic,
            });
            resolve();
          }
        });
      });

      this.client.on("error", reject);
      this.client.connect();
    });
  }

  async publishResponse(message: OutgoingPacket): Promise<void> {
    const serialized = this.serializeMessage(message);
    const payload = this.addCRC(serialized);

    return new Promise((resolve, reject) => {
      this.client.publish(this.reportTopic, payload, { qos: 0 }, (err) => {
        if (err) {
          reject(err);
        } else {
          this.logger.debug("Published response", {
            topic: this.reportTopic,
            messageType: message.payload.oneofKind,
          });
          resolve();
        }
      });
    });
  }

  private serializeMessage(message: OutgoingPacket): Buffer {
    return Buffer.from(OutgoingPacket.encode(message).finish());
  }

  private addCRC(data: Buffer): Buffer {
    const crc = CRCValidator.calculate(data);
    const crcBuffer = Buffer.allocUnsafe(2);
    crcBuffer.writeUInt16BE(crc, 0);
    return Buffer.concat([data, crcBuffer]);
  }
}
```

## Implementation Phases

### Phase 1: Core Infrastructure (Week 1-2)

**Deliverables:**

- `packages/lic-simulator` package setup with Bun and TypeScript
- Basic MQTT client implementation
- Protobuf message parsing using existing `packages/protobuf` dependency
- CRC validation copied from `packages/mqtt-integration`
- File-based state persistence
- Basic logging and configuration

**Tasks:**

1. Initialize `packages/lic-simulator` package structure and dependencies
2. Set up dependency on `packages/protobuf` for message types
3. Copy CRC16 implementation from `packages/mqtt-integration` to ensure identical behavior
4. Implement MQTTClient with connection management
5. Create protobuf message parsing using existing protobuf package
6. Create StatePersistence with JSON file storage
7. Set up logging and configuration management

### Phase 2: Message Processing (Week 3-4)

**Deliverables:**

- Complete MessageProcessor implementation
- All protobuf message type handlers
- InfoPackage and StatusPackage generation
- Configuration synchronization logic

**Tasks:**

1. Implement all IncomingPacket message handlers
2. Create OutgoingPacket message builders
3. Implement timestamp-based synchronization
4. Add message validation and error handling
5. Create comprehensive unit tests

### Phase 3: Device Simulation (Week 5-6)

**Deliverables:**

- DeviceController with state simulation
- Device timing and control logic
- Realistic device behavior simulation
- Device state persistence

**Tasks:**

1. Implement device on/off control with timers
2. Create device state tracking and updates
3. Simulate device response times and behaviors
4. Add device failure and recovery simulation
5. Implement device state persistence

### Phase 4: Scheduling Engine (Week 7-8)

**Deliverables:**

- Complete SchedulingEngine implementation
- Schedule execution with proper timing
- Device coordination and sequencing
- Scheduling reports and status updates

**Tasks:**

1. Implement schedule parsing and validation
2. Create schedule execution engine with timing
3. Add device coordination and sequencing
4. Implement scheduling reports and status
5. Add schedule pause/resume functionality

### Phase 5: Automation and Advanced Features (Week 9-10)

**Deliverables:**

- AutomationEngine implementation
- Sensor simulation and automation triggers
- Error simulation and edge cases
- Performance optimization

**Tasks:**

1. Implement automation rule processing
2. Create sensor simulation and triggers
3. Add error simulation and edge cases
4. Optimize performance and memory usage
5. Add monitoring and debugging features

### Phase 6: Testing and Documentation (Week 11-12)

**Deliverables:**

- Comprehensive test suite
- Integration tests with real MQTT broker
- Performance benchmarks
- Complete documentation

**Tasks:**

1. Create comprehensive unit test suite
2. Implement integration tests
3. Performance testing and optimization
4. Create user documentation and guides
5. Final testing and bug fixes

## Configuration and Deployment

### Configuration Structure

```json
{
  "simulator": {
    "codecId": "ECC9FF468E64",
    "stateDirectory": "./simulator-state",
    "logLevel": "info",
    "enableDebugMode": false
  },
  "mqtt": {
    "brokerUrl": "mqtt://mosquitto-codec.saas.byagro.dev.br:8003",
    "username": "codec",
    "password": "Y29kZWM=",
    "clientId": "LICSimulator-{codecId}",
    "keepalive": 60,
    "reconnectPeriod": 1000,
    "connectTimeout": 30000
  },
  "timing": {
    "statusReportInterval": 30000,
    "stateBackupInterval": 300000,
    "scheduleCheckInterval": 60000,
    "deviceResponseDelay": 100
  },
  "simulation": {
    "enableErrorSimulation": false,
    "errorProbability": 0.01,
    "networkLatencyMin": 50,
    "networkLatencyMax": 200,
    "deviceFailureRate": 0.001
  }
}
```

### Deployment Options

#### 1. Single Device Simulation

```bash
# From project root
cd packages/lic-simulator
bun run start --config config/single-device.json

# Or from project root using workspace commands
bun run --filter lic-simulator start --config config/single-device.json
```

#### 2. Multiple Device Simulation

```bash
# From packages/lic-simulator directory
bun run start-multiple --count 5 --base-config config/multi-device.json

# Or from project root
bun run --filter lic-simulator start-multiple --count 5 --base-config config/multi-device.json
```

#### 3. Docker Deployment

```dockerfile
FROM oven/bun:1.0
WORKDIR /app

# Copy root package files for workspace setup
COPY package.json bun.lockb ./
COPY packages/protobuf/package.json ./packages/protobuf/
COPY packages/lic-simulator/package.json ./packages/lic-simulator/

# Install dependencies
RUN bun install

# Copy source code
COPY packages/protobuf ./packages/protobuf/
COPY packages/lic-simulator ./packages/lic-simulator/

# Build the simulator
WORKDIR /app/packages/lic-simulator
RUN bun run build

CMD ["bun", "run", "start"]
```

## Testing Strategy

### Unit Tests

- Message parsing and validation
- State management operations
- Device control logic
- Scheduling algorithms
- CRC validation
- Configuration management

### Integration Tests

- MQTT broker communication
- End-to-end message flow
- State persistence across restarts
- Multi-device coordination
- Error handling and recovery

### Performance Tests

- Message throughput
- Memory usage under load
- State persistence performance
- Concurrent device simulation
- Long-running stability

### Compatibility Tests

- Protocol compliance with ESP32 firmware
- Message format validation
- Timing accuracy verification
- State synchronization correctness

## Success Criteria

### Functional Requirements

1. **Message Compatibility**: 100% compatibility with ESP32 protobuf messages
2. **State Persistence**: Maintain state across restarts with <1% data loss
3. **Timing Accuracy**: Schedule execution within ±30 seconds of target time
4. **Response Time**: MQTT message responses within 500ms average
5. **Reliability**: 99.9% uptime during continuous operation

### Performance Requirements

1. **Throughput**: Handle 100+ messages per second per device
2. **Memory Usage**: <100MB RAM per simulated device
3. **CPU Usage**: <5% CPU per simulated device on modern hardware
4. **Scalability**: Support 50+ concurrent device simulations
5. **Storage**: <10MB disk space per device for state storage

### Quality Requirements

1. **Test Coverage**: >90% code coverage
2. **Documentation**: Complete API and user documentation
3. **Error Handling**: Graceful handling of all error conditions
4. **Logging**: Comprehensive logging for debugging and monitoring
5. **Maintainability**: Clean, well-documented, modular code

## Risk Mitigation

### Technical Risks

1. **Protocol Changes**: Monitor ESP32 firmware updates for protocol changes
2. **Performance Issues**: Regular performance testing and optimization
3. **State Corruption**: Implement backup and recovery mechanisms
4. **MQTT Reliability**: Handle connection failures and message loss
5. **Memory Leaks**: Regular memory profiling and leak detection

### Operational Risks

1. **Configuration Errors**: Validate configuration on startup
2. **Resource Exhaustion**: Monitor and limit resource usage
3. **Network Issues**: Implement retry logic and fallback mechanisms
4. **Data Loss**: Regular state backups and recovery procedures
5. **Security**: Secure MQTT credentials and access controls

## Conclusion

This implementation plan provides a comprehensive roadmap for developing a LIC simulator that exactly replicates ESP32 firmware behavior. The modular architecture, phased development approach, and thorough testing strategy ensure a robust and reliable simulator that will significantly enhance the development and testing capabilities of the Irriga Mais system.

The simulator will enable:

- **Faster Development**: Test MQTT integration without physical hardware
- **Better Testing**: Comprehensive testing of edge cases and error conditions
- **Easier Debugging**: Enhanced logging and state inspection capabilities
- **Scalable Testing**: Simulate multiple devices for load testing
- **Continuous Integration**: Automated testing in CI/CD pipelines

By following this plan, the development team will have a powerful tool for testing and validating the MQTT integration layer, leading to more robust and reliable irrigation control systems.
