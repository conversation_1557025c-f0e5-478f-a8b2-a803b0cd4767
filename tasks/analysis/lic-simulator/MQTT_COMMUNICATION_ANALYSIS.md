# MQTT Communication and Message Flow Analysis

## Overview

This document analyzes the MQTT communication patterns, topic structure, and message flow between the Irriga Mais system and LIC (Localized Irrigation Controller) devices. This analysis serves as the foundation for implementing a LIC simulator that behaves exactly like a real device.

## MQTT Broker Configuration

### Connection Details
- **Broker Host**: `mosquitto-codec.saas.byagro.dev.br`
- **Port**: 8003
- **Protocol**: MQTT v3.1.1 and v5.0 support
- **Authentication**: Username/password (`codec` / `Y29kZWM=` - base64 encoded)
- **Client ID Pattern**: `IrrigaNet-{UUID}` for Android app, configurable for other clients

### Connection Parameters
- **Protocol Version**: 5 (with fallback to 3.1.1)
- **Clean Session**: true
- **Reconnect Period**: 1000ms
- **Connect Timeout**: 30000ms
- **Keep Alive**: 60 seconds
- **Session Expiry**: 3600 seconds (1 hour)
- **QoS Level**: 0 for most messages, 1 for critical messages

## Topic Structure

### Topic Pattern
```
/codec/{device_identifier}/report    # Uplink messages (FROM LIC device)
/codec/{device_identifier}/downlink  # Downlink messages (TO LIC device)
```

### Device Identifier Format
- **ESP32 MAC Address**: 12-character hexadecimal string (e.g., `ECC9FF468E64`)
- **Case**: Uppercase
- **Format**: Derived from `esp_efuse_mac_get_default()` on ESP32

### Topic Examples
```
/codec/ECC9FF468E64/report     # Device ECC9FF468E64 sending status
/codec/ECC9FF468E64/downlink   # Commands sent to device ECC9FF468E64
```

## Message Flow Patterns

### 1. Device Registration and Discovery

**Flow**: System → LIC Device
1. System subscribes to `/codec/+/report` (wildcard subscription)
2. System sends `RequestInfoPackage` to `/codec/{device_id}/downlink`
3. LIC responds with `InfoPackage` on `/codec/{device_id}/report`
4. System registers device and establishes communication

### 2. Configuration Synchronization

**Flow**: System → LIC Device
1. System detects configuration changes (timestamp-based)
2. System sends configuration packages in sequence:
   - `ConfigPackage` (system settings)
   - `DevicesPackage` (mesh device configuration)
   - `SchedulingPackage` (irrigation schedules)
   - `DeviceSchedulingPackage` (detailed timing)
   - `AutomationPackage` (automation rules)
3. LIC processes each package and sends `AckPackage` responses
4. LIC updates internal state and sends `InfoPackage` with new timestamps

### 3. Real-time Control

**Flow**: System ↔ LIC Device
1. System sends `ControlPackage` for immediate device control
2. LIC executes command and responds with `AckPackage`
3. LIC sends `SystemStatusPackage` with updated device states
4. System updates UI to reflect current device status

### 4. Status Monitoring

**Flow**: LIC Device → System
1. LIC periodically sends `SystemStatusPackage` (status reports)
2. LIC sends `SchedulingReportPackage` after irrigation execution
3. LIC sends `AutomationReportPackage` when automation triggers
4. System processes reports and updates database/UI

### 5. Firmware Updates

**Flow**: System → LIC Device
1. System sends `FirmwareUpdatePackage` with update parameters
2. LIC stops MQTT client and begins OTA process
3. LIC downloads firmware from specified URL
4. LIC restarts with new firmware
5. LIC reconnects and sends `InfoPackage` with new version

## Message Processing Pipeline

### Outbound Message Processing (System → LIC)

1. **Message Construction**:
   - Query database for current configuration
   - Build protobuf message with appropriate payload
   - Set message ID (timestamp)
   - Serialize to binary format

2. **CRC Validation**:
   - Calculate CRC16 checksum of protobuf payload
   - Append 2-byte CRC to message
   - Final payload: `[protobuf_data][crc_high][crc_low]`

3. **MQTT Transmission**:
   - Publish to `/codec/{device_id}/downlink`
   - Use QoS 0 for most messages
   - Handle publish errors and retry if needed

### Inbound Message Processing (LIC → System)

1. **MQTT Reception**:
   - Receive message on `/codec/{device_id}/report`
   - Extract device ID from topic using regex
   - Parse message payload

2. **CRC Validation**:
   - Extract last 2 bytes as CRC
   - Calculate CRC16 of remaining payload
   - Validate CRC matches, discard if invalid

3. **Protobuf Deserialization**:
   - Parse binary payload as `OutgoingPacket`
   - Extract specific payload type (info, status, report, etc.)
   - Handle parsing errors gracefully

4. **Message Processing**:
   - Update device state in database/memory
   - Trigger UI updates
   - Log message for debugging
   - Send acknowledgments if required

## ESP32 Firmware MQTT Implementation

### Connection Setup
```c
// Topic generation from MAC address
uint8_t mac[6];
esp_efuse_mac_get_default(mac);

snprintf(publish_topic_report, sizeof(publish_topic_report),
         "/codec/%02X%02X%02X%02X%02X%02X/report",
         mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

snprintf(subscribe_topic, sizeof(subscribe_topic),
         "/codec/%02X%02X%02X%02X%02X%02X/downlink",
         mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
```

### Message Handling
```c
// ESP32 subscribes to its downlink topic on connection
esp_mqtt_client_subscribe(mqtt_client, subscribe_topic, 0);

// Message reception with buffer management
if (event->current_data_offset == 0) {
    // Start of new message - acquire buffer
    xSemaphoreTake(data_mutex, 0);
    protocol.len = 0;
    memset(protocol.data, 0, MAX_DATA_SIZE);
}

// Accumulate message data
memcpy(protocol.data + protocol.len, event->data, event->data_len);
protocol.len += event->data_len;

// Complete message received
if (protocol.len == event->total_data_len) {
    protocol.origin = P_MQTT;
    xTaskNotifyGive(data_task_handle);  // Notify processing task
}
```

### Message Processing
```c
// CRC validation
uint16_t crc_sent = (protocol.data[protocol.len-2] << 8) | 
                    (protocol.data[protocol.len-1] & 0xFF);
uint16_t crc_calculated = crc16(protocol.data, protocol.len - 2);

if(crc_sent != crc_calculated) {
    ESP_LOGE(TAG_PROT, "CRC mismatch");
    return ESP_FAIL;
}

// Protobuf deserialization
Codec__In__IncomingPacket *packet = 
    codec__in__incoming_packet__unpack(NULL, protocol.len-2, protocol.data);
```

## Integration Service MQTT Implementation

### Client Configuration
```typescript
const client = connect(brokerUrl, {
  username: config.mqtt.username,
  password: config.mqtt.password,
  protocolVersion: 5,
  clean: true,
  reconnectPeriod: 1000,
  connectTimeout: 30000,
  keepalive: 60,
  will: {
    topic: "client/disconnected",
    payload: JSON.stringify({
      message: "Client disconnected unexpectedly",
      timestamp: new Date().toISOString(),
    }),
    qos: 1,
    retain: false,
  }
});
```

### Topic Subscription and Routing
```typescript
// Subscribe to all device reports
client.subscribe("/codec/+/report");

// Message routing based on topic pattern
const reportTopicRegex = /^\/codec\/([^\/]+)\/report$/;
const downlinkTopicRegex = /^\/codec\/([^\/]+)\/downlink$/;

function parseTopic(topic: string): MQTTTopicInfo | null {
  let match = reportTopicRegex.exec(topic);
  if (match && match[1]) {
    return { deviceId: match[1], topicType: "report" };
  }
  // Handle downlink and unknown topics
}
```

### Message Processing
```typescript
client.on("message", (topic, payload, packet) => {
  const message = handleMessage(topic, payload, packet);
  const referenceDate = new Date();
  
  if (message) {
    const callbacks = this.listeners.get(message.deviceId);
    if (callbacks && callbacks.length > 0) {
      callbacks.forEach((callback) =>
        callback(message.topicType, message.payload, referenceDate)
      );
    } else {
      this.onUnhandledMessage(
        message.deviceId,
        message.topicType,
        message.payload,
        referenceDate
      );
    }
  }
});
```

## Error Handling and Resilience

### Connection Management
- **Automatic Reconnection**: 1-second intervals with exponential backoff
- **Connection Monitoring**: Heartbeat and keepalive mechanisms
- **Graceful Shutdown**: Proper client disconnection on system shutdown

### Message Reliability
- **CRC Validation**: All messages include CRC16 checksum
- **Message Deduplication**: Timestamp-based message IDs
- **Buffer Management**: Proper memory allocation and cleanup
- **Timeout Handling**: Message processing timeouts with cleanup

### Error Recovery
- **Invalid Messages**: Log and discard malformed messages
- **Connection Loss**: Queue messages during disconnection
- **Processing Errors**: Graceful degradation without system crash
- **Resource Exhaustion**: Proper cleanup and resource management

## Performance Considerations

### Message Optimization
- **Batch Operations**: Group related configuration updates
- **Change Detection**: Only send modified configurations
- **Compression**: Protobuf provides efficient binary serialization
- **QoS Selection**: Use appropriate QoS levels for message types

### Resource Management
- **Memory Usage**: Efficient protobuf object lifecycle
- **CPU Usage**: Optimized message parsing and processing
- **Network Usage**: Minimize unnecessary message transmission
- **Battery Usage**: Optimized keepalive intervals for mobile clients

## Security Considerations

### Authentication
- **Broker Authentication**: Username/password authentication
- **Client Identification**: Unique client IDs prevent conflicts
- **Session Management**: Clean sessions prevent state leakage

### Message Security
- **Transport Security**: TLS encryption for MQTT connections
- **Message Integrity**: CRC validation prevents corruption
- **Access Control**: Topic-based access restrictions
- **Mesh Encryption**: Separate encryption for mesh network communication

## Simulator Implementation Requirements

Based on this analysis, a LIC simulator must implement:

1. **MQTT Client**: Connect to broker with proper credentials and topics
2. **Message Handling**: Process all IncomingPacket message types
3. **State Management**: Maintain device state and configuration
4. **Response Generation**: Send appropriate OutgoingPacket responses
5. **Timing Simulation**: Realistic response times and scheduling execution
6. **Error Simulation**: Handle edge cases and error conditions
7. **Persistence**: Save state to file for consistency across restarts

The simulator should behave identically to a real LIC device from the system's perspective, enabling comprehensive testing of the MQTT integration without requiring physical hardware.
