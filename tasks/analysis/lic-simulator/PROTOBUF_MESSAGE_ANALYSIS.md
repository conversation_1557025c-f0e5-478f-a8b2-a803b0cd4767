# Protobuf Message Types and Processing Logic Analysis

## Overview

This document provides a comprehensive analysis of all protobuf message types used in the Irriga Mais system, their structure, processing logic in the ESP32 firmware, and expected responses. This analysis is essential for implementing a LIC simulator that behaves exactly like a real device.

## Message Container Structure

### IncomingPacket (Downlink Messages - TO LIC)

All messages sent to LIC devices are wrapped in the `IncomingPacket` container:

```protobuf
message IncomingPacket {
  uint64 id = 1;                                    // Message timestamp/ID for tracking
  oneof payload {
    codec.in.config.ConfigPackage config = 2;
    codec.in.devices.DevicesPackage devices = 3;
    codec.in.scheduling.SchedulingPackage scheduling = 4;
    codec.in.device_scheduling.DeviceSchedulingPackage dev_scheduling = 5;
    codec.in.automation.AutomationPackage automation = 6;
    codec.in.control.ControlPackage control = 7;
    codec.in.command.CommandPackage command = 8;
    codec.in.request_info.RequestInfoPackage request_info = 9;
    codec.in.firmware_update.FirmwareUpdatePackage firmware_update = 10;
  }
}
```

**Note**: The ESP32 firmware uses `pause` instead of `command` in field 8.

### OutgoingPacket (Uplink Messages - FROM LIC)

All messages sent from LIC devices are wrapped in the `OutgoingPacket` container:

```protobuf
message OutgoingPacket {
  uint64 id = 1;                                    // Message timestamp/ID
  oneof payload {
    codec.out.info.InfoPackage info = 2;
    codec.out.status.SystemStatusPackage status = 3;
    codec.out.scheduling_report.SchedulingReportPackage scheduling_report = 4;
    codec.out.automation_report.AutomationReportPackage automation_report = 5;
    codec.out.ack.AckPackage ack = 6;
    codec.out.raw.RawPackage raw = 7;
  }
}
```

## Downlink Message Types (TO LIC)

### 1. ConfigPackage - System Configuration

**Purpose**: Configure LIC device system settings including WiFi, mesh networking, and irrigation parameters.

**Message Structure**:

```protobuf
message ConfigPackage {
  int32 backwash_cycle = 1;                 // número do ciclo de retrolavagem
  int32 backwash_duration = 2;              // duração da retrolavagem
  int32 backwash_delay = 3;                 // atraso antes da retrolavagem
  bool  raingauge_enabled = 4;              // sensor de chuva ativado
  int32 raingauge_factor = 5;               // resolução do pluviômetro
  int32 rainfall_limit = 6;                 // limite de chuva
  int32 rainfall_pause_duration = 7;        // tempo de pausa após chuva
  WifiConfig wifi = 8;                      // configuração do Wi-Fi
  MeshConfig mesh = 9;                      // configuração da rede Mesh
  bool  publish_raw_data = 10;              // enviar pacotes brutos para o mqtt
  bool  debug = 11;                         // habilita modo de investigação de problemas
  bool  enable_schedule_resumption = 12;    // habilita retomada automática de agendamentos
  bool  enable_ferti_resumption = 13;       // habilita retomada automática do setor quando a ferti falhar
  int32 max_resumption_attempts = 14;       // número máximo de tentativas de retomada (0 = indefinido)
}

message WifiConfig {
  string ssid = 1;                          // Wi-Fi SSID
  string password = 2;                      // Wi-Fi password
}

message MeshConfig {
  bytes key = 1;                            // Chave da criptografia da rede Mesh
  uint32 channel = 2;                       // Canal da rede Mesh
}
```

**ESP32 Processing Logic**:

```c
case CODEC__IN__INCOMING_PACKET__PAYLOAD_CONFIG: {
    if (packet->config) {
        Codec__In__Config__ConfigPackage *cfg = packet->config;
        pumplink_config.backwash_cycle_time      = cfg->backwash_cycle;
        pumplink_config.backwash_delay           = cfg->backwash_delay;
        pumplink_config.backwash_duration        = cfg->backwash_duration;
        pumplink_config.raingauge_enabled        = cfg->raingauge_enabled;
        pumplink_config.raingauge_factor         = cfg->raingauge_factor;
        pumplink_config.rainfall_limit           = cfg->rainfall_limit;
        pumplink_config.rainfall_pause_duration  = cfg->rainfall_pause_duration;

        // Save configuration to memory
        memory_save_config();

        // Respond with InfoPackage
        *len = report_info((uint8_t **)result, S_REP_INFO_BASE);
    }
    break;
}
```

**State Changes**:

- Updates global `pumplink_config` structure
- Saves configuration to persistent memory
- Updates rain gauge configuration if changed
- Triggers WiFi reconnection if credentials changed

**Expected Response**: `InfoPackage` with updated `config_id`

### 2. DevicesPackage - Mesh Device Configuration

**Purpose**: Configure mesh network devices and their operational parameters.

**Message Structure**:

```protobuf
message DevicesPackage {
  repeated DevicesData data = 1;            // List of device configurations
}

message DevicesData {
  int32 idx = 1;                           // index in the array (device slot)
  int32 mesh_id = 2;                       // mesh ID
  int32 device_id = 3;                     // device ID
  int32 device_type = 4;                   // tipo de dispositivo
  int32 out1 = 5;                          // saída 1
  int32 out2 = 6;                          // saída 2
  int32 input = 7;                         // entrada
  int32 mode = 8;                          // modo de operação
  int32 sector = 9;                        // setor
  int32 group_idx = 10;                    // grupo
  int32 power = 11;                        // potência se estiver usando inversor
  int32 equipment = 12;                    // tipo de equipamento
}
```

**ESP32 Processing Logic**:

```c
case CODEC__IN__INCOMING_PACKET__PAYLOAD_DEVICES: {
    if (packet->devices) {
        Codec__In__Devices__DevicesPackage *devs = packet->devices;
        int devices_size = devs->n_data;
        if (devices_size > MAX_MESH_DEVICES) {
            devices_size = MAX_MESH_DEVICES;
        }

        for (int i = 0; i < devices_size; i++) {
            Codec__In__Devices__DevicesData *item = devs->data[i];
            pumplink_device[i].idx          = item->idx;
            pumplink_device[i].mesh_id      = item->mesh_id;
            pumplink_device[i].device_id    = item->device_id;
            pumplink_device[i].device_type  = item->device_type;
            pumplink_device[i].out1         = item->out1;
            pumplink_device[i].out2         = item->out2;
            pumplink_device[i].input        = item->input;
            pumplink_device[i].mode         = item->mode;
            pumplink_device[i].sector       = item->sector;
            pumplink_device[i].group_idx    = item->group_idx;
            pumplink_device[i].power        = item->power;
            pumplink_device[i].equipment    = item->equipment;
        }

        // Save devices to memory
        device_count = devices_size;
        memory_save_devices();

        // Respond with InfoPackage
        *len = report_info((uint8_t **)result, S_REP_INFO_BASE);
    }
    break;
}
```

**State Changes**:

- Updates global `pumplink_device[]` array
- Sets `device_count` to number of devices
- Saves device configuration to persistent memory
- Updates `last_devs_update` timestamp

**Expected Response**: `InfoPackage` with updated `devices_id`

### 3. SchedulingPackage - Irrigation Scheduling

**Purpose**: Define irrigation schedules with timing and device coordination.

**Message Structure**:

```protobuf
message SchedulingPackage {
  MsgType type = 1;
  repeated Scheduling scheduling_data = 2;      // Lista de agendamentos
  repeated DeviceScheduling device_scheduling_data = 3;   // Lista de agendamentos por dispositivo
}

message Scheduling {
  int32 idx = 1;                     // Índice do agendamento
  int32 start_time = 2;              // Horário de início (em minutos desde a meia-noite)
  int32 days_of_week = 3;            // Dias da semana em que o agendamento é válido (bitmask: 0b01111111)
  int32 number_of_steps = 4;         // Número total de etapas a executar no agendamento
  int32 waterpump_idx = 5;           // Índice da bomba de água usada
  int32 waterpump_working_time = 6;  // Tempo de funcionamento da bomba de água (em minutos)
  bool  allow_ferti = 7;             // Permite aplicação de fertilizante (1 = sim, 0 = não)
  int32 ferti_idx = 8;               // Índice da ferti
  bool  allow_backwash = 9;          // Permite retrolavagem (1 = sim, 0 = não)
  int32 backwash_idx = 10;           // Índice da retrolavagem
  int32 group = 11;                  // Grupo ao qual o agendamento pertence
  bool  once = 12;                   // Não repetir o agendamento
}

enum MsgType {
  MSG_NONE = 0;
  MSG_SCHEDULING_ONLY = 1;
  MSG_DEV_SCHEDULING_ONLY = 2;
  MSG_SCHEDULING_ALL = 3;
}
```

**State Changes**:

- Updates global `pumplink_scheduling[]` array
- Sets `schedule_count` to number of schedules
- Saves scheduling configuration to persistent memory
- Updates `last_sched_update` timestamp

**Expected Response**: `InfoPackage` with updated `scheduling_id`

### 4. DeviceSchedulingPackage - Device-Level Scheduling

**Purpose**: Define detailed device-specific scheduling steps with precise timing.

**Message Structure**:

```protobuf
message DeviceSchedulingPackage {
  repeated DeviceScheduling data = 1; // Lista de agendamentos de dispositivos
}

message DeviceScheduling {
  int32 idx = 1;                  // Índice do agendamento do dispositivo
  int32 scheduling_idx = 2;       // Índice do agendamento principal
  int32 device_idx = 3;           // Índice do dispositivo
  int32 order = 4;                // Ordem de execução no agendamento
  int32 sector_working_time = 5;  // Tempo de início relativo (minutos após início do agendamento)
  int32 ferti_working_time = 6;   // Tempo de funcionamento para fertilização (em minutos)
  int32 ferti_delay = 7;          // Atraso antes da fertilização (em minutos)
}
```

**State Changes**:

- Updates global `pumplink_device_scheduling[]` array
- Sets `schedules_per_device` to number of device schedules
- Saves device scheduling configuration to persistent memory
- Updates `last_dev_sched_update` timestamp

**Expected Response**: `InfoPackage` with updated `dev_scheduling_id`

### 5. AutomationPackage - Automation Rules

**Purpose**: Configure automation rules for pumps and level sensors.

**Message Structure**:

```protobuf
message AutomationPackage {
  repeated AutomationData data = 1;        // Lista de acionamentos
}

message AutomationData {
  int32 level_idx = 1;     // level input (sensor de nível)
  int32 pump_idx = 2;      // pump index
  int32 mask = 3;          // mascara
  int32 value = 4;         // valor de acionamento
  int32 working_time = 5;  // working time (tempo de acionamento)
}
```

**State Changes**:

- Updates global `pumplink_automation[]` array
- Sets automation rules as enabled
- Updates `last_auto_update` timestamp

**Expected Response**: `InfoPackage` with updated `automation_id`

### 6. ControlPackage - Real-Time Device Control

**Purpose**: Send immediate control commands to specific devices.

**Message Structure**:

```protobuf
message ControlPackage {
  int32 idx = 1;             // Índice do dispositivo
  MsgAction action = 2;      // Código da ação (ex: 1 = ligar, 2 = desligar)
  int32 value = 3;           // Tempo de funcionamento (em minutos)
  bytes payload = 4;         // Payload
  int32 activation_code = 5; // Código de ativação
}

enum MsgAction {
  MSG_NONE = 0;
  MSG_TURN_ON = 1;
  MSG_TURN_OFF = 2;
  MSG_PACKAGE = 3;
}
```

**ESP32 Processing Logic**:

```c
case CODEC__IN__INCOMING_PACKET__PAYLOAD_CONTROL: {
    if (packet->control) {
        Codec__In__Control__ControlPackage *ctrl = packet->control;

        time_t now;
        time(&now);

        struct s_pumplink_control new_control;
        new_control.dev_idx       = ctrl->idx;
        new_control.action        = ctrl->action;
        new_control.working_time  = ctrl->working_time;
        new_control.enabled       = true;
        new_control.start_time    = now;
        new_control.timeout       = TIMER_3MIN;

        if (new_control.action == 1 || new_control.action == 2) {
            mesh_insert_pumplink_control(new_control);
        }
    }
    break;
}
```

**State Changes**:

- Adds control command to mesh control queue
- Sets device state based on action (on/off)
- Starts timer for timed operations

**Expected Response**: No immediate response, but device state changes reflected in next `SystemStatusPackage`

### 7. PauseSchedulingPackage - Schedule Pause Control

**Purpose**: Pause or resume irrigation scheduling system-wide.

**Message Structure**:

```protobuf
message PauseSchedulingPackage {
  int32 state = 1;      // Estado da pausa: 1 = pausar, 0 = retomar
  int32 duration = 2;   // Duração da pausa (em minutos)
}
```

**ESP32 Processing Logic**:

```c
case CODEC__IN__INCOMING_PACKET__PAYLOAD_PAUSE: {
    if (packet->pause) {
        Codec__In__Pause__PauseSchedulingPackage *pause = packet->pause;

        pause_scheduling = pause->state;

        if (pause_scheduling == 1) {
            time_t now;
            time(&now);
            pause_scheduling_timer = pause->duration * 60;
            pause_scheduling_time = now;
        }

        if (protocol.origin == P_MQTT) {
            *len = report_status((uint8_t **)result);
        }
    }
    break;
}
```

**State Changes**:

- Sets global `pause_scheduling` flag
- Sets pause timer and start time if pausing
- Stops all active scheduling if pausing

**Expected Response**: `SystemStatusPackage` with updated pause status

### 8. RequestInfoPackage - Information Request

**Purpose**: Request specific information from LIC device.

**Message Structure**:

```protobuf
message RequestInfoPackage {
  int32 type = 1;      // Tipo de dados a ser retornados
}
```

**ESP32 Processing Logic**:

```c
case CODEC__IN__INCOMING_PACKET__PAYLOAD_REQUEST_INFO: {
    if (packet->request_info) {
        Codec__In__RequestInfo__RequestInfoPackage *request_info = packet->request_info;

        int type = request_info->type;
        *len = report_info((uint8_t **)result, S_REP_INFO_BASE);
    }
    break;
}
```

**Information Types**:

- `0`: System status information
- `1`: Device status information
- `2`: Scheduling status information
- `3`: Automation status information

**Expected Response**: `InfoPackage` with requested information

### 9. FirmwareUpdatePackage - Firmware Update Control

**Purpose**: Initiate firmware update process on LIC device.

**Message Structure**:

```protobuf
message FirmwareUpdatePackage {
  MsgType type = 1;          // Tipo de atualização
  MsgProtocol protocol = 2;  // Tipo de protocolo
  int32 activation_code = 3; // Código individual para ativação do UpDate
  int32 version = 4;         // Nova versão do firmware
}

enum MsgType {
  MSG_ESP = 0;
  MSG_STM = 1;
}

enum MsgProtocol {
  MSG_HTTPS = 0;
  MSG_HTTP = 1;
}
```

**ESP32 Processing Logic**:

```c
case CODEC__IN__INCOMING_PACKET__PAYLOAD_FIRMWARE_UPDATE: {
    if (packet->firmware_update) {
        Codec__In__FirmwareUpdate__FirmwareUpdatePackage *firmware_update = packet->firmware_update;

        ota_fw_update.type = firmware_update->type;
        ota_fw_update.proto = firmware_update->protocol;
        ota_fw_update.version = firmware_update->version;

        if (ota_handle == NULL) {
            xTaskCreate(&ota_task, "ota_task", 8192, NULL, 5, &ota_handle);
        }
    }
    break;
}
```

**State Changes**:

- Stops MQTT client
- Downloads firmware from URL
- Restarts device with new firmware
- Reconnects with updated version info

**Expected Response**: Device disconnects, then reconnects with new firmware version in `InfoPackage`

## Uplink Message Types (FROM LIC)

### 1. InfoPackage - Device Information

**Purpose**: Report device identification, firmware versions, and synchronization status.

**Message Structure**:

```protobuf
message InfoPackage {
  string codec_id = 1;            // Número de série do Codec
  uint32 firmware_esp = 2;        // Versão do firmware ESP
  uint32 firmware_mesh = 3;       // Versão do firmware do mesh
  uint32 hardware_version = 4;    // Versão do hardware
  uint32 resets = 5;              // Número de resets
  uint32 scheduling_running = 6;  // Agendamento em execução
  uint32 scheduling_paused = 7;   // Agendamento pausado
  uint32 devices_id = 8;          // Última atualização dos dispositivos
  uint32 scheduling_id = 9;       // Última atualização dos agendamentos
  uint32 dev_scheduling_id = 10;  // Última atualização dos agendamentos por dispositivo
  uint32 automation_id = 11;      // Última atualização das automações
  uint32 config_id = 12;          // Última atualização da configuração
}
```

**ESP32 Generation Logic**:

```c
size_t report_info(uint8_t **buffer, uint8_t type) {
    char mac_string[18];
    sprintf(mac_string, "%02X%02X%02X%02X%02X%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

    Codec__Out__Info__InfoPackage info = CODEC__OUT__INFO__INFO_PACKAGE__INIT;
    if(type == S_REP_INFO_ALL){
        info.codec_id = mac_string;
    }
    info.firmware_esp = FIRMWARE_VERSION;
    info.firmware_mesh = MESH_FIRMWARE_VERSION;
    info.hardware_version = HARDWARE_VERSION;
    info.resets = reset_count;
    info.scheduling_running = scheduling_running_count;
    info.scheduling_paused = pause_scheduling;
    info.devices_id = last_devs_update;
    info.scheduling_id = last_sched_update;
    info.dev_scheduling_id = last_dev_sched_update;
    info.automation_id = last_auto_update;
    info.config_id = last_config_update;

    // Wrap in OutgoingPacket
    Codec__Out__OutgoingPacket packet = CODEC__OUT__OUTGOING_PACKET__INIT;
    packet.id = (uint64_t)time(NULL);
    packet.payload_case = CODEC__OUT__OUTGOING_PACKET__PAYLOAD_INFO;
    packet.info = &info;

    size_t len = codec__out__outgoing_packet__get_packed_size(&packet);
    *buffer = malloc(len);
    codec__out__outgoing_packet__pack(&packet, *buffer);
    return len;
}
```

**Field Descriptions**:

- `codec_id`: Device MAC address as hex string (e.g., "ECC9FF468E64")
- `firmware_esp/firmware_mesh`: Version numbers for firmware components
- `resets`: System stability indicator (increments on each restart)
- `*_id` fields: Configuration version tracking for synchronization

**When Sent**:

- Response to `RequestInfoPackage`
- Response to configuration updates (ConfigPackage, DevicesPackage, etc.)
- Periodic status updates

### 2. SystemStatusPackage - Operational Status

**Purpose**: Report current system operational state and sensor readings.

**Message Structure**:

```protobuf
message SystemStatusPackage {
  uint32 resets = 1;              // Número de resets
  uint32 scheduling_running = 2;  // Agendamento em execução
  uint32 scheduling_paused = 3;   // Pausa de agendamento ativa

  oneof has_paused_time {
    uint32 paused_time = 4;       // Minutos desde que a pausa foi ativada
  }

  oneof has_raining {
    uint32 raining = 5;           // Está chovendo (1 = sim)
  }

  oneof has_rainfall {
    uint32 rainfall = 6;          // Chuva acumulada nas últimas 24h
  }

  uint64 sync_bitmask = 7;        // Máscara de bits de dispositivos sincronizados
  uint64 on_bitmask = 8;          // Máscara de bits de dispositivos ligados
  uint64 input_bitmask = 9;       // Máscara de bits de dispositivos com entrada 1
  uint32 failed_bitmask = 12;     // Máscara de bits de falhas do sistema
}
```

**ESP32 Generation Logic**:

```c
size_t report_status(uint8_t **buffer) {
    time_t now;
    time(&now);

    uint64_t sy_bitmask = 0;
    uint64_t on_bitmask = 0;
    uint64_t in_bitmask = 0;

    int limit = (device_count < MAX_MESH_DEVICES) ? device_count : MAX_MESH_DEVICES;
    for (int l = 0; l < limit; ++l) {
        if (difftime(now, pumplink_device_state[l].last_sync) < TIMER_20MIN) {
            sy_bitmask |= ((uint64_t)1 << l);
        }
        if (pumplink_device_state[l].device_status != 0) {
            on_bitmask |= ((uint64_t)1 << l);
        }
        if (pumplink_device_state[l].inputs & (1 << 1)) {
            in_bitmask |= ((uint64_t)1 << l);
        }
    }

    Codec__Out__Status__SystemStatusPackage status = CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__INIT;
    status.resets = reset_count;
    status.scheduling_running = scheduling_running_count;
    status.scheduling_paused = pause_scheduling;
    status.sync_bitmask = sy_bitmask;
    status.on_bitmask = on_bitmask;
    status.input_bitmask = in_bitmask;
    status.failed_bitmask = system_failed_bitmask;

    if (pause_scheduling == 1) {
        status.has_paused_time_case = CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_PAUSED_TIME_PAUSED_TIME;
        status.paused_time = (uint32_t)(difftime(now, pause_scheduling_time) / 60);
    }

    if (pumplink_config.raingauge_enabled) {
        status.has_raining_case = CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINING_RAINING;
        status.raining = raining_status;
        status.has_rainfall_case = CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINFALL_RAINFALL;
        status.rainfall = rainfall_last24h;
    }

    // Wrap in OutgoingPacket and serialize
    // ... similar to InfoPackage
}
```

**Field Descriptions**:

- `scheduling_running`: Number of currently executing schedules
- `scheduling_paused`: 1 if scheduling is paused, 0 if active
- `sync_bitmask`: Bitmask showing which devices are synchronized (responded in last 20 minutes)
- `on_bitmask`: Bitmask showing which devices are currently active
- `input_bitmask`: Bitmask showing device input states
- `failed_bitmask`: Bitmask indicating system failures

**When Sent**:

- Response to `PauseSchedulingPackage`
- Periodic status updates
- After significant state changes

### 3. SchedulingReportPackage - Irrigation Execution Report

**Purpose**: Report completion of irrigation scheduling execution.

**Message Structure**:

```protobuf
message SchedulingReportPackage {
  uint32 scheduling_idx = 1;      // Índice do agendamento executado
  uint32 device_idx = 2;          // Índice do dispositivo
  uint32 start_time = 3;          // Horário de início da execução
  uint32 end_time = 4;            // Horário de fim da execução
  uint32 working_time = 5;        // Tempo de funcionamento (em minutos)
  uint32 status = 6;              // Status da execução (0 = sucesso, 1 = falha)
}
```

**When Sent**:

- After completing each irrigation step in a schedule
- When irrigation is interrupted or fails
- Contains timing and status information for audit trail

### 4. AutomationReportPackage - Automation Execution Report

**Purpose**: Report automation rule execution and results.

**Message Structure**:

```protobuf
message AutomationReportPackage {
  uint32 level_idx = 1;           // Índice do sensor de nível
  uint32 pump_idx = 2;            // Índice da bomba
  uint32 start_time = 3;          // Horário de início
  uint32 end_time = 4;            // Horário de fim
  uint32 working_time = 5;        // Tempo de funcionamento
  uint32 status = 6;              // Status da execução
}
```

**When Sent**:

- When automation rules trigger pump activation
- When automation-controlled pumps stop
- Provides audit trail for automated operations

### 5. AckPackage - Acknowledgment Response

**Purpose**: Acknowledge receipt and processing of commands.

**Message Structure**:

```protobuf
message AckPackage {
  uint64 id = 1;                  // ID da mensagem sendo confirmada
  uint32 status = 2;              // Status da execução (0 = sucesso, outros = erro)
}
```

**ESP32 Generation Logic**:

```c
size_t report_ack(uint8_t **buffer, uint64_t msg_id, uint32_t status) {
    Codec__Out__Ack__AckPackage ack = CODEC__OUT__ACK__ACK_PACKAGE__INIT;
    ack.id = msg_id;
    ack.status = status;

    Codec__Out__OutgoingPacket packet = CODEC__OUT__OUTGOING_PACKET__INIT;
    packet.id = (uint64_t)time(NULL);
    packet.payload_case = CODEC__OUT__OUTGOING_PACKET__PAYLOAD_ACK;
    packet.ack = &ack;

    size_t len = codec__out__outgoing_packet__get_packed_size(&packet);
    *buffer = malloc(len);
    codec__out__outgoing_packet__pack(&packet, *buffer);
    return len;
}
```

**Status Codes**:

- `0`: Success - command processed successfully
- `1`: Error - general processing error
- `2`: Invalid - command format or parameters invalid
- `3`: Timeout - command processing timed out

**When Sent**:

- Response to `ControlPackage` commands
- Response to configuration updates when requested
- Error responses for invalid commands

### 6. RawPackage - Raw Mesh Data

**Purpose**: Forward raw mesh network data for debugging and monitoring.

**Message Structure**:

```protobuf
message RawPackage {
  bytes data = 1;                 // Dados brutos do mesh
}
```

**When Sent**:

- Only when `publish_raw_data` is enabled in configuration
- Contains unprocessed mesh network packets
- Used for debugging mesh communication issues

## Message Processing Summary

### Downlink Processing Flow (TO LIC)

1. **MQTT Reception**: Receive message on `/codec/{device_id}/downlink`
2. **CRC Validation**: Verify message integrity
3. **Protobuf Parsing**: Deserialize `IncomingPacket`
4. **Payload Processing**: Handle specific message type
5. **State Update**: Update device configuration/state
6. **Response Generation**: Send appropriate response message
7. **Persistence**: Save changes to memory if needed

### Uplink Processing Flow (FROM LIC)

1. **Event Trigger**: System event or periodic timer
2. **Data Collection**: Gather current system state
3. **Message Construction**: Build appropriate protobuf message
4. **Serialization**: Pack message with CRC
5. **MQTT Transmission**: Publish to `/codec/{device_id}/report`

### Configuration Synchronization

The system uses timestamp-based synchronization:

1. **System Side**: Tracks last modification time for each configuration type
2. **LIC Side**: Maintains `*_id` fields with last received timestamps
3. **Comparison**: System compares timestamps to detect changes
4. **Update**: Only sends configurations that have changed
5. **Confirmation**: LIC responds with `InfoPackage` containing new timestamps

### Error Handling

1. **CRC Mismatch**: Discard message and log error
2. **Protobuf Parse Error**: Discard message and log error
3. **Invalid Parameters**: Send `AckPackage` with error status
4. **Processing Timeout**: Send `AckPackage` with timeout status
5. **Memory Allocation**: Graceful degradation and cleanup

## Simulator Implementation Requirements

Based on this analysis, the LIC simulator must:

### Message Handling

- Parse all `IncomingPacket` message types correctly
- Generate appropriate `OutgoingPacket` responses
- Implement CRC16 validation for all messages
- Handle message timing and sequencing

### State Management

- Maintain device configuration state
- Track scheduling and automation state
- Implement timestamp-based synchronization
- Persist state across simulator restarts

### Response Logic

- Send `InfoPackage` after configuration updates
- Send `SystemStatusPackage` for status requests
- Send `AckPackage` for control commands
- Send reports for scheduling and automation execution

### Timing Simulation

- Realistic response delays
- Proper scheduling execution timing
- Automation trigger simulation
- Periodic status reporting

The simulator should behave identically to a real LIC device, enabling comprehensive testing of the MQTT integration and message processing logic.
