# Device State Management and Persistence Design

## Overview

This document analyzes how the ESP32 firmware manages device state and persistence, and designs a file-based persistence mechanism for the LIC simulator. The simulator must maintain state consistency across restarts and behave identically to a real LIC device.

## ESP32 Firmware State Management

### Core State Structures

The ESP32 firmware maintains several key state structures in memory:

#### 1. Device Configuration (`s_pumplink_devices`)

```c
struct s_pumplink_devices {
    unsigned int meshid;        // Mesh network ID
    unsigned char device_id;    // Device ID within mesh
    unsigned char type;         // Device type (valve, pump, etc.)
    unsigned char out1;         // Output 1 configuration
    unsigned char out2;         // Output 2 configuration
    unsigned char input;        // Input configuration
    unsigned char mode;         // Operating mode
    unsigned char group;        // Group/sector assignment
    unsigned char sector;       // Sector number
    unsigned char eqpt_ver;     // Equipment version
};
```

#### 2. Device Runtime State (`s_pumplink_device_state`)

```c
struct s_pumplink_device_state {
    unsigned short int id;              // Device mesh ID
    unsigned short int m;               // Additional mesh info
    unsigned char device_status;        // Current on/off status
    unsigned char outputs;              // Output states
    unsigned char inputs;               // Input states
    unsigned char flags;                // Status flags
    unsigned char device_id;            // Device ID
    unsigned char time_left;            // Remaining operation time
    unsigned int last_sync;             // Last synchronization timestamp
    unsigned int last_cmd;              // Last command timestamp
    unsigned short int last_appoint;    // Last appointment/schedule
    unsigned int working_time_secs;     // Working time in seconds
    unsigned char triggered;            // Trigger flag
    unsigned int triggered_time;        // Trigger timestamp
};
```

#### 3. System Configuration (`s_pumplink_config`)

```c
struct s_pumplink_config {
    unsigned short int backwash_cycle_time;     // Backwash cycle interval
    unsigned short int backwash_duration;       // Backwash duration
    unsigned short int backwash_delay;          // Backwash delay
    unsigned char raingauge_enabled;            // Rain gauge enabled
    unsigned short int raingauge_factor;        // Rain gauge resolution
    unsigned short int rainfall_limit;          // Rainfall limit
    unsigned short int rainfall_pause_duration; // Rain pause duration
};
```

#### 4. Scheduling Configuration (`s_pumplink_scheduling`)

```c
struct s_pumplink_scheduling {
    unsigned int start_time;                    // Start time (minutes from midnight)
    unsigned char days_of_week;                 // Days of week bitmask
    unsigned char type;                         // Schedule type
    unsigned char number_of_steps;              // Number of steps
    unsigned short int waterpump_dev_idx;       // Water pump device index
    unsigned short int waterpump_working_time;  // Water pump working time
    unsigned char allow_backwash;               // Allow backwash
    unsigned short int backwash_dev_idx;        // Backwash device index
    unsigned char allow_ferti;                  // Allow fertilizer
    unsigned short int ferti_dev_idx;           // Fertilizer device index
    unsigned short int group;                   // Group assignment
};
```

#### 5. Device Scheduling (`s_pumplink_device_scheduling`)

```c
struct s_pumplink_device_scheduling {
    unsigned short int shc_idx;         // Schedule index
    unsigned short int dev_idx;         // Device index
    unsigned char step;                 // Step in sequence
    unsigned int sector_working_time;   // Sector working time
    unsigned int ferti_working_time;    // Fertilizer working time
    unsigned int ferti_delay;           // Fertilizer delay
};
```

#### 6. Automation Rules (`s_pumplink_automation`)

```c
struct s_pumplink_automation {
    unsigned short int level_dev_idx;   // Level sensor device index
    unsigned short int pump_dev_idx;    // Pump device index
    unsigned char mask;                 // Input mask
    unsigned char value;                // Trigger value
    unsigned char enabled;              // Rule enabled
    unsigned short int working_time;    // Working time
};
```

### Global State Arrays

The firmware maintains arrays of these structures:

```c
struct s_pumplink_devices pumplink_devices[MAX_MESH_DEVICES];                    // 64 devices max
struct s_pumplink_device_state pumplink_device_state[MAX_MESH_DEVICES];         // Device states
struct s_pumplink_scheduling pumplink_scheduling[MAX_MESH_SCHEDULING];          // 32 schedules max
struct s_pumplink_device_scheduling pumplink_device_scheduling[MAX_MESH_DEVICE_SCHEDULING]; // 256 device schedules max
struct s_pumplink_automation pumplink_automation[MAX_MESH_AUTOMATION];          // 32 automation rules max
struct s_pumplink_config pumplink_config;                                       // Single config instance
```

### State Counters and Timestamps

The firmware tracks configuration versions and counts:

```c
// Configuration timestamps (for synchronization)
time_t last_devs_update;           // Last device configuration update
time_t last_sched_update;          // Last scheduling update
time_t last_dev_sched_update;      // Last device scheduling update
time_t last_auto_update;           // Last automation update
time_t last_config_update;         // Last system config update

// Item counts
uint16_t device_count;              // Number of configured devices
uint16_t schedule_count;            // Number of schedules
uint16_t schedules_per_device;      // Number of device schedules
uint16_t automation_count;          // Number of automation rules

// System state
uint32_t reset_count;               // Number of system resets
uint32_t scheduling_running_count;  // Number of running schedules
uint8_t pause_scheduling;           // Scheduling paused flag
time_t pause_scheduling_time;       // Pause start time
uint32_t pause_scheduling_timer;    // Pause duration
```

## ESP32 Persistence Mechanism

### NVS (Non-Volatile Storage) Usage

The ESP32 firmware uses NVS to persist configuration across reboots:

#### 1. System Information

```c
esp_err_t memory_save_system_info() {
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("system", NVS_READWRITE, &nvs_handle);
    err = nvs_set_blob(nvs_handle, "sysinfo", &system_info, sizeof(system_info));
    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}
```

#### 2. Device Configuration

```c
esp_err_t memory_save_devices() {
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("devs", NVS_READWRITE, &nvs_handle);

    // Save timestamp
    err = nvs_set_u32(nvs_handle, "devs_t", last_devs_update);

    // Save device count
    err = nvs_set_u16(nvs_handle, "devs_c", device_count);

    // Save device array
    err = nvs_set_blob(nvs_handle, "devs_l", pumplink_devices, sizeof(pumplink_devices));

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}
```

#### 3. Scheduling Configuration

```c
esp_err_t memory_save_scheduling() {
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("sched", NVS_READWRITE, &nvs_handle);

    err = nvs_set_u32(nvs_handle, "sched_t", last_sched_update);
    err = nvs_set_u16(nvs_handle, "sched_c", schedule_count);
    err = nvs_set_blob(nvs_handle, "sched_l", pumplink_scheduling, sizeof(pumplink_scheduling));

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}
```

#### 4. Device Scheduling

```c
esp_err_t memory_save_device_scheduling() {
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("dev_sched", NVS_READWRITE, &nvs_handle);

    err = nvs_set_u32(nvs_handle, "dev_sched_t", last_dev_sched_update);
    err = nvs_set_u16(nvs_handle, "dev_sched_c", schedules_per_device);
    err = nvs_set_blob(nvs_handle, "dev_sched_l", pumplink_device_scheduling, sizeof(pumplink_device_scheduling));

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}
```

#### 5. Automation Configuration

```c
esp_err_t memory_save_automation() {
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("auto", NVS_READWRITE, &nvs_handle);

    err = nvs_set_u32(nvs_handle, "auto_t", last_auto_update);
    err = nvs_set_u16(nvs_handle, "auto_c", automation_count);
    err = nvs_set_blob(nvs_handle, "auto_l", pumplink_automation, sizeof(pumplink_automation));

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}
```

#### 6. System Configuration

```c
esp_err_t memory_save_config() {
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("config", NVS_READWRITE, &nvs_handle);

    err = nvs_set_u32(nvs_handle, "time", last_config_update);
    err = nvs_set_blob(nvs_handle, "others", &pumplink_config, sizeof(pumplink_config));

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}
```

### Initialization and Loading

On startup, the firmware loads all persisted configuration:

```c
void app_main(void) {
    // Initialize NVS
    memory_nvs_init();

    // Load system info (increments reset count)
    memory_load_system_info();

    // Load all configurations
    memory_load_wifi_credentials(sta_ssid, MAX_STA_SSID_SIZE, sta_pass, MAX_STA_PASS_SIZE);
    memory_load_devices();
    memory_load_scheduling();
    memory_load_device_scheduling();
    memory_load_automation();
    memory_load_config();

    // Continue with system initialization...
}
```

## LIC Simulator State Management Design

### TypeScript State Interfaces

The simulator will use TypeScript interfaces that mirror the ESP32 structures:

#### 1. Device Configuration

```typescript
interface DeviceConfig {
  idx: number; // Array index
  meshId: number; // Mesh network ID
  deviceId: number; // Device ID within mesh
  deviceType: number; // Device type
  out1: number; // Output 1 configuration
  out2: number; // Output 2 configuration
  input: number; // Input configuration
  mode: number; // Operating mode
  sector: number; // Sector number
  groupIdx: number; // Group index
  power: number; // Power setting
  equipment: number; // Equipment type
}
```

#### 2. Device Runtime State

```typescript
interface DeviceState {
  id: number; // Device mesh ID
  deviceStatus: number; // Current on/off status (bitmask)
  outputs: number; // Output states
  inputs: number; // Input states
  flags: number; // Status flags
  deviceId: number; // Device ID
  timeLeft: number; // Remaining operation time
  lastSync: number; // Last synchronization timestamp
  lastCmd: number; // Last command timestamp
  lastAppoint: number; // Last appointment/schedule
  workingTimeSecs: number; // Working time in seconds
  triggered: boolean; // Trigger flag
  triggeredTime: number; // Trigger timestamp
}
```

#### 3. System Configuration

```typescript
interface SystemConfig {
  backwashCycle: number; // Backwash cycle interval
  backwashDuration: number; // Backwash duration
  backwashDelay: number; // Backwash delay
  raingaugeEnabled: boolean; // Rain gauge enabled
  raingaugeFactor: number; // Rain gauge resolution
  rainfallLimit: number; // Rainfall limit
  rainfallPauseDuration: number; // Rain pause duration
  publishRawData: boolean; // Publish raw mesh data
  debug: boolean; // Debug mode
  enableScheduleResumption: boolean; // Enable schedule resumption
  enableFertiResumption: boolean; // Enable fertilizer resumption
  maxResumptionAttempts: number; // Max resumption attempts
}
```

#### 4. Scheduling Configuration

```typescript
interface ScheduleConfig {
  idx: number; // Schedule index
  startTime: number; // Start time (minutes from midnight)
  daysOfWeek: number; // Days of week bitmask
  numberOfSteps: number; // Number of steps
  waterpumpIdx: number; // Water pump device index
  waterpumpWorkingTime: number; // Water pump working time
  allowFerti: boolean; // Allow fertilizer
  fertiIdx: number; // Fertilizer device index
  allowBackwash: boolean; // Allow backwash
  backwashIdx: number; // Backwash device index
  group: number; // Group assignment
  once: boolean; // Execute only once
}
```

#### 5. Device Scheduling

```typescript
interface DeviceScheduleConfig {
  idx: number; // Device schedule index
  schedulingIdx: number; // Parent schedule index
  deviceIdx: number; // Device index
  order: number; // Execution order
  sectorWorkingTime: number; // Sector working time
  fertiWorkingTime: number; // Fertilizer working time
  fertiDelay: number; // Fertilizer delay
}
```

#### 6. Automation Rules

```typescript
interface AutomationConfig {
  levelIdx: number; // Level sensor device index
  pumpIdx: number; // Pump device index
  mask: number; // Input mask
  value: number; // Trigger value
  workingTime: number; // Working time
}
```

### Simulator State Container

```typescript
interface LICSimulatorState {
  // Device identification
  codecId: string; // MAC address as hex string
  firmwareEsp: number; // ESP firmware version
  firmwareMesh: number; // Mesh firmware version
  hardwareVersion: number; // Hardware version

  // System state
  resets: number; // Reset count
  schedulingRunning: number; // Running schedules count
  schedulingPaused: boolean; // Scheduling paused
  pausedTime?: number; // Minutes since pause
  raining?: boolean; // Currently raining
  rainfall?: number; // 24h rainfall accumulation

  // Configuration timestamps (for synchronization)
  configId: number; // Last config update
  devicesId: number; // Last devices update
  schedulingId: number; // Last scheduling update
  devSchedulingId: number; // Last device scheduling update
  automationId: number; // Last automation update

  // Configuration arrays
  devices: DeviceConfig[]; // Device configurations
  deviceStates: DeviceState[]; // Device runtime states
  config: SystemConfig; // System configuration
  schedules: ScheduleConfig[]; // Scheduling configurations
  deviceSchedules: DeviceScheduleConfig[]; // Device scheduling
  automation: AutomationConfig[]; // Automation rules

  // Runtime state
  pauseSchedulingTime?: Date; // Pause start time
  pauseSchedulingTimer?: number; // Pause duration
  systemFailedBitmask: number; // System failure bitmask

  // Limits (matching ESP32 firmware)
  maxDevices: number; // MAX_MESH_DEVICES = 64
  maxSchedules: number; // MAX_MESH_SCHEDULING = 32
  maxDeviceSchedules: number; // MAX_MESH_DEVICE_SCHEDULING = 256
  maxAutomation: number; // MAX_MESH_AUTOMATION = 32
}
```

## File-Based Persistence Design

### File Structure

The simulator will use JSON files for persistence, organized by configuration type:

```
simulator-state/
├── system.json              # System info and metadata
├── config.json              # System configuration
├── devices.json             # Device configurations
├── device-states.json       # Device runtime states
├── schedules.json           # Scheduling configurations
├── device-schedules.json    # Device scheduling details
├── automation.json          # Automation rules
└── runtime.json             # Runtime state and flags
```

### File Formats

#### 1. system.json

```json
{
  "codecId": "ECC9FF468E64",
  "firmwareEsp": 1001,
  "firmwareMesh": 2001,
  "hardwareVersion": 1,
  "resets": 5,
  "lastSaved": "2024-08-25T10:30:00.000Z",
  "version": "1.0.0"
}
```

#### 2. config.json

```json
{
  "configId": 1724580600,
  "config": {
    "backwashCycle": 10,
    "backwashDuration": 5,
    "backwashDelay": 2,
    "raingaugeEnabled": true,
    "raingaugeFactor": 100,
    "rainfallLimit": 10,
    "rainfallPauseDuration": 60,
    "publishRawData": false,
    "debug": false,
    "enableScheduleResumption": true,
    "enableFertiResumption": true,
    "maxResumptionAttempts": 3
  }
}
```

#### 3. devices.json

```json
{
  "devicesId": 1724580600,
  "deviceCount": 4,
  "devices": [
    {
      "idx": 0,
      "meshId": 123456,
      "deviceId": 1,
      "deviceType": 0,
      "out1": 1,
      "out2": 0,
      "input": 0,
      "mode": 0,
      "sector": 1,
      "groupIdx": 0,
      "power": 0,
      "equipment": 0
    }
  ]
}
```

#### 4. device-states.json

```json
{
  "lastUpdated": "2024-08-25T10:30:00.000Z",
  "deviceStates": [
    {
      "id": 123456,
      "deviceStatus": 0,
      "outputs": 0,
      "inputs": 0,
      "flags": 0,
      "deviceId": 1,
      "timeLeft": 0,
      "lastSync": 1724580600,
      "lastCmd": 0,
      "lastAppoint": 0,
      "workingTimeSecs": 0,
      "triggered": false,
      "triggeredTime": 0
    }
  ]
}
```

#### 5. runtime.json

```json
{
  "schedulingRunning": 0,
  "schedulingPaused": false,
  "pauseSchedulingTime": null,
  "pauseSchedulingTimer": null,
  "systemFailedBitmask": 0,
  "raining": false,
  "rainfall": 0,
  "lastStatusUpdate": "2024-08-25T10:30:00.000Z"
}
```

### Persistence Manager Implementation

```typescript
class LICStatePersistence {
  private stateDir: string;
  private state: LICSimulatorState;

  constructor(stateDir: string, codecId: string) {
    this.stateDir = stateDir;
    this.state = this.initializeDefaultState(codecId);
  }

  /**
   * Initialize default state matching ESP32 firmware defaults
   */
  private initializeDefaultState(codecId: string): LICSimulatorState {
    return {
      // Device identification
      codecId,
      firmwareEsp: 1001,
      firmwareMesh: 2001,
      hardwareVersion: 1,

      // System state
      resets: 0,
      schedulingRunning: 0,
      schedulingPaused: false,

      // Configuration timestamps (start with current time)
      configId: Math.floor(Date.now() / 1000),
      devicesId: Math.floor(Date.now() / 1000),
      schedulingId: Math.floor(Date.now() / 1000),
      devSchedulingId: Math.floor(Date.now() / 1000),
      automationId: Math.floor(Date.now() / 1000),

      // Empty configuration arrays
      devices: [],
      deviceStates: [],
      config: this.getDefaultSystemConfig(),
      schedules: [],
      deviceSchedules: [],
      automation: [],

      // Runtime state
      systemFailedBitmask: 0,

      // Limits matching ESP32 firmware
      maxDevices: 64,
      maxSchedules: 32,
      maxDeviceSchedules: 256,
      maxAutomation: 32,
    };
  }

  /**
   * Load state from files or create default state
   */
  async loadState(): Promise<void> {
    try {
      await this.ensureStateDirectory();

      // Load each configuration file
      await this.loadSystemInfo();
      await this.loadConfig();
      await this.loadDevices();
      await this.loadDeviceStates();
      await this.loadSchedules();
      await this.loadDeviceSchedules();
      await this.loadAutomation();
      await this.loadRuntime();

      // Increment reset count (like ESP32 firmware)
      this.state.resets++;
      await this.saveSystemInfo();
    } catch (error) {
      console.warn("Failed to load state, using defaults:", error);
      // Continue with default state
    }
  }

  /**
   * Save complete state to files
   */
  async saveState(): Promise<void> {
    await this.ensureStateDirectory();

    await Promise.all([
      this.saveSystemInfo(),
      this.saveConfig(),
      this.saveDevices(),
      this.saveDeviceStates(),
      this.saveSchedules(),
      this.saveDeviceSchedules(),
      this.saveAutomation(),
      this.saveRuntime(),
    ]);
  }

  /**
   * Save only configuration (called after config updates)
   */
  async saveConfiguration(): Promise<void> {
    await Promise.all([
      this.saveConfig(),
      this.saveDevices(),
      this.saveSchedules(),
      this.saveDeviceSchedules(),
      this.saveAutomation(),
    ]);
  }

  /**
   * Save only runtime state (called periodically)
   */
  async saveRuntimeState(): Promise<void> {
    await Promise.all([this.saveDeviceStates(), this.saveRuntime()]);
  }

  // File I/O methods
  private async loadSystemInfo(): Promise<void> {
    const data = await this.readJsonFile("system.json");
    if (data) {
      this.state.codecId = data.codecId || this.state.codecId;
      this.state.firmwareEsp = data.firmwareEsp || this.state.firmwareEsp;
      this.state.firmwareMesh = data.firmwareMesh || this.state.firmwareMesh;
      this.state.hardwareVersion =
        data.hardwareVersion || this.state.hardwareVersion;
      this.state.resets = data.resets || 0;
    }
  }

  private async saveSystemInfo(): Promise<void> {
    const data = {
      codecId: this.state.codecId,
      firmwareEsp: this.state.firmwareEsp,
      firmwareMesh: this.state.firmwareMesh,
      hardwareVersion: this.state.hardwareVersion,
      resets: this.state.resets,
      lastSaved: new Date().toISOString(),
      version: "1.0.0",
    };
    await this.writeJsonFile("system.json", data);
  }

  private async loadConfig(): Promise<void> {
    const data = await this.readJsonFile("config.json");
    if (data) {
      this.state.configId = data.configId || this.state.configId;
      this.state.config = { ...this.state.config, ...data.config };
    }
  }

  private async saveConfig(): Promise<void> {
    const data = {
      configId: this.state.configId,
      config: this.state.config,
    };
    await this.writeJsonFile("config.json", data);
  }

  private async loadDevices(): Promise<void> {
    const data = await this.readJsonFile("devices.json");
    if (data) {
      this.state.devicesId = data.devicesId || this.state.devicesId;
      this.state.devices = data.devices || [];
    }
  }

  private async saveDevices(): Promise<void> {
    const data = {
      devicesId: this.state.devicesId,
      deviceCount: this.state.devices.length,
      devices: this.state.devices,
    };
    await this.writeJsonFile("devices.json", data);
  }

  // Utility methods
  private async ensureStateDirectory(): Promise<void> {
    const fs = await import("fs/promises");
    try {
      await fs.access(this.stateDir);
    } catch {
      await fs.mkdir(this.stateDir, { recursive: true });
    }
  }

  private async readJsonFile(filename: string): Promise<any> {
    const fs = await import("fs/promises");
    const path = await import("path");
    try {
      const filePath = path.join(this.stateDir, filename);
      const content = await fs.readFile(filePath, "utf-8");
      return JSON.parse(content);
    } catch {
      return null;
    }
  }

  private async writeJsonFile(filename: string, data: any): Promise<void> {
    const fs = await import("fs/promises");
    const path = await import("path");
    const filePath = path.join(this.stateDir, filename);
    await fs.writeFile(filePath, JSON.stringify(data, null, 2));
  }

  private getDefaultSystemConfig(): SystemConfig {
    return {
      backwashCycle: 10,
      backwashDuration: 5,
      backwashDelay: 2,
      raingaugeEnabled: false,
      raingaugeFactor: 100,
      rainfallLimit: 10,
      rainfallPauseDuration: 60,
      publishRawData: false,
      debug: false,
      enableScheduleResumption: true,
      enableFertiResumption: true,
      maxResumptionAttempts: 3,
    };
  }

  // State access methods
  getState(): LICSimulatorState {
    return this.state;
  }

  updateConfig(config: Partial<SystemConfig>): void {
    this.state.config = { ...this.state.config, ...config };
    this.state.configId = Math.floor(Date.now() / 1000);
  }

  updateDevices(devices: DeviceConfig[]): void {
    this.state.devices = devices;
    this.state.devicesId = Math.floor(Date.now() / 1000);

    // Initialize device states for new devices
    this.initializeDeviceStates();
  }

  updateSchedules(schedules: ScheduleConfig[]): void {
    this.state.schedules = schedules;
    this.state.schedulingId = Math.floor(Date.now() / 1000);
  }

  updateDeviceSchedules(deviceSchedules: DeviceScheduleConfig[]): void {
    this.state.deviceSchedules = deviceSchedules;
    this.state.devSchedulingId = Math.floor(Date.now() / 1000);
  }

  updateAutomation(automation: AutomationConfig[]): void {
    this.state.automation = automation;
    this.state.automationId = Math.floor(Date.now() / 1000);
  }

  private initializeDeviceStates(): void {
    // Ensure device states array matches devices array
    const newStates: DeviceState[] = [];

    for (let i = 0; i < this.state.devices.length; i++) {
      const device = this.state.devices[i];
      const existingState = this.state.deviceStates.find(
        (s) => s.id === device.meshId && s.deviceId === device.deviceId
      );

      if (existingState) {
        newStates.push(existingState);
      } else {
        // Create new device state
        newStates.push({
          id: device.meshId,
          deviceStatus: 0,
          outputs: 0,
          inputs: 0,
          flags: 0,
          deviceId: device.deviceId,
          timeLeft: 0,
          lastSync: Math.floor(Date.now() / 1000),
          lastCmd: 0,
          lastAppoint: 0,
          workingTimeSecs: 0,
          triggered: false,
          triggeredTime: 0,
        });
      }
    }

    this.state.deviceStates = newStates;
  }
}
```

## State Synchronization Strategy

### Timestamp-Based Synchronization

The simulator implements the same timestamp-based synchronization as the ESP32 firmware:

1. **Configuration Updates**: When receiving configuration messages, update the corresponding `*_id` timestamp
2. **InfoPackage Generation**: Include current timestamps in InfoPackage responses
3. **Change Detection**: System compares timestamps to detect configuration changes
4. **Selective Updates**: Only send configurations that have newer timestamps

### Persistence Triggers

The simulator saves state at strategic points:

1. **Configuration Changes**: Save immediately after processing configuration messages
2. **Periodic Saves**: Save runtime state every 30 seconds
3. **Graceful Shutdown**: Save complete state on SIGTERM/SIGINT
4. **Error Recovery**: Save state before processing potentially risky operations

### State Consistency

To ensure state consistency:

1. **Atomic Updates**: Use temporary files and atomic rename operations
2. **Validation**: Validate loaded state against schema before use
3. **Fallback**: Use default state if loaded state is corrupted
4. **Backup**: Keep previous state file as backup during updates

## Implementation Benefits

### Advantages over ESP32 NVS

1. **Human Readable**: JSON files can be inspected and modified manually
2. **Version Control**: State files can be tracked in git for testing
3. **Debugging**: Easy to examine state during development
4. **Portability**: Works across different operating systems
5. **Backup**: Simple file-based backup and restore

### Performance Considerations

1. **Lazy Loading**: Load state files only when needed
2. **Selective Saving**: Save only changed configurations
3. **Async I/O**: Use non-blocking file operations
4. **Memory Efficiency**: Keep only active state in memory

The file-based persistence design ensures the LIC simulator maintains state consistency across restarts while providing better debugging and development capabilities than the ESP32's NVS system.
