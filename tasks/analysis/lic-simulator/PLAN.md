# CODEK Simulator Architecture Plan

## Overview

The CODEK simulator will be a TypeScript/Node.js application that mimics the behavior of a physical CODEK (LIC - Localized Irrigation Controller) device. It will communicate via MQTT using protocol buffer messages and simulate the complete irrigation control system including mesh device management, scheduling, and automation.

## Core Features

### 1. **Protocol Buffer Communication**
- **MQTT Client**: Connect to MQTT broker with device-specific topics (`/codec/{MAC}/uplink`, `/codec/{MAC}/downlink`)
- **Message Handling**: Process all incoming message types (ConfigPackage, DevicesPackage, SchedulingPackage, etc.)
- **Message Publishing**: Send outgoing messages (InfoPackage, StatusPackage, Reports, Acknowledgments)
- **Protocol Buffer Integration**: Use existing `proto` workspace package for message serialization

### 2. **Virtual Device Management**
- **Device Registry**: Store and manage virtual devices (pumps, valves, sensors) with their mesh network mappings
- **Device State Simulation**: Track device states (on/off, synchronized, input states, failures)
- **Mesh Network Simulation**: Simulate communication with PumpLink, ValveLink, and Reservoir Monitor devices
- **Device Type Support**: Support all device types (Valve=0, IrrigationPump=1, Ferti=2, Backwash=3, ServicePump=4, Level=5)

### 3. **Irrigation Scheduling Engine**
- **Schedule Execution**: Execute irrigation schedules with proper timing and device coordination
- **Multi-step Scheduling**: Support complex schedules with multiple sectors, fertigation, and backwash
- **Schedule State Management**: Track running, paused, and completed schedules
- **Time-based Triggers**: Implement day-of-week and time-based schedule execution

### 4. **Automation System**
- **Reservoir Level Automation**: Simulate water level monitoring and pump control
- **Rule Engine**: Execute automation rules based on sensor inputs
- **Safety Mechanisms**: Implement working time limits and failure handling

### 5. **Environmental Simulation**
- **Rain Gauge Simulation**: Simulate rainfall detection and irrigation pause logic
- **Sensor Data Generation**: Generate realistic sensor readings (water levels, flow rates)
- **System Status Tracking**: Maintain system health, reset counts, and failure states

### 6. **Configuration Management**
- **Dynamic Configuration**: Accept and apply configuration updates via MQTT
- **State Persistence**: Maintain configuration state between restarts
- **Validation**: Validate incoming configurations and report errors

## Implementation Architecture

### **Core Components**

```typescript
// Main simulator class
class CodekSimulator {
  private mqttClient: MqttClient
  private deviceManager: DeviceManager
  private scheduleEngine: ScheduleEngine
  private automationEngine: AutomationEngine
  private statusReporter: StatusReporter
  private configManager: ConfigManager
}

// Device management
class DeviceManager {
  private devices: Map<number, VirtualDevice>
  private meshNetworks: Map<number, MeshDevice>
  
  async turnDeviceOn(idx: number, workingTime: number): Promise<void>
  async turnDeviceOff(idx: number): Promise<void>
  getDeviceStatus(idx: number): DeviceStatus
}

// Schedule execution
class ScheduleEngine {
  private activeSchedules: Map<number, RunningSchedule>
  private scheduledTasks: Map<number, NodeJS.Timeout>
  
  async executeSchedule(schedule: Scheduling): Promise<void>
  pauseAllSchedules(): void
  resumeSchedules(): void
}

// Automation control
class AutomationEngine {
  private automations: Map<number, AutomationRule>
  
  async processAutomation(levelIdx: number, pumpIdx: number): Promise<void>
  checkAutomationTriggers(): void
}
```

### **Message Flow Architecture**

```typescript
// Incoming message handlers
interface MessageHandlers {
  handleConfig(config: ConfigPackage): Promise<void>
  handleDevices(devices: DevicesPackage): Promise<void>
  handleScheduling(scheduling: SchedulingPackage): Promise<void>
  handleControl(control: ControlPackage): Promise<void>
  handleCommand(command: CommandPackage): Promise<void>
}

// Outgoing message publishers
interface MessagePublishers {
  publishInfo(): Promise<void>
  publishStatus(): Promise<void>
  publishSchedulingReport(report: SchedulingReportData): Promise<void>
  publishAutomationReport(report: AutomationReportData): Promise<void>
  publishAck(packageType: number, value: number): Promise<void>
}
```

### **State Management**

```typescript
// Device state
interface DeviceState {
  idx: number
  meshId: number
  deviceId: number
  deviceType: number
  isOn: boolean
  isSynchronized: boolean
  inputState: boolean
  hasFailed: boolean
  lastActivation: Date
  workingTimeRemaining: number
}

// Schedule state
interface ScheduleState {
  idx: number
  isRunning: boolean
  isPaused: boolean
  currentStep: number
  startTime: Date
  sectorsExecuted: number[]
  fertiApplied: boolean
  backwashExecuted: boolean
}
```

## Technical Implementation Details

### **1. MQTT Integration**
- Use `mqtt` package from existing dependencies
- Generate device MAC address for topic construction
- Implement connection management with reconnection logic
- Handle message acknowledgments and delivery guarantees

### **2. Protocol Buffer Integration**
- Leverage existing `proto` workspace package
- Implement message serialization/deserialization helpers
- Handle message versioning and compatibility

### **3. Time Management**
- Use system time for schedule execution
- Implement time zone handling for irrigation schedules
- Support manual time injection for testing

### **4. Configuration Management**
- Store configuration in memory with optional persistence
- Support hot-reloading of configurations
- Validate configuration consistency

### **5. Logging and Monitoring**
- Structured logging with different levels (INFO, WARN, ERROR)
- Performance metrics and system health monitoring
- Debug mode for detailed operation logging

## Simulator-Specific Features

### **1. Testing and Development Support**
- **Mock Mode**: Run without MQTT connection for local testing
- **Time Acceleration**: Speed up time for rapid testing of schedules
- **Scenario Injection**: Inject specific scenarios (device failures, network issues)
- **State Inspection**: REST API endpoints for inspecting internal state

### **2. Web Interface (Optional)**
- Simple web dashboard showing device states, schedules, and logs
- Real-time updates via WebSocket
- Manual device control for testing

### **3. Configuration File Support**
- Load initial configuration from JSON/YAML files
- Support multiple device profiles and scenarios
- Environment-based configuration overrides

## Project Structure

```
packages/codec-simulator/
├── src/
│   ├── simulator/
│   │   ├── CodekSimulator.ts          # Main simulator class
│   │   ├── DeviceManager.ts           # Device state management
│   │   ├── ScheduleEngine.ts          # Schedule execution
│   │   ├── AutomationEngine.ts        # Automation rules
│   │   └── StatusReporter.ts          # Status reporting
│   ├── mqtt/
│   │   ├── MqttClient.ts             # MQTT connection management
│   │   ├── MessageHandler.ts         # Incoming message processing
│   │   └── MessagePublisher.ts       # Outgoing message publishing
│   ├── models/
│   │   ├── Device.ts                 # Device models and interfaces
│   │   ├── Schedule.ts               # Schedule models
│   │   └── Config.ts                 # Configuration models
│   ├── utils/
│   │   ├── TimeManager.ts            # Time utilities
│   │   ├── Logger.ts                 # Logging utilities
│   │   └── Validation.ts             # Configuration validation
│   └── index.ts                      # Entry point
├── config/
│   ├── default.json                  # Default configuration
│   └── scenarios/                    # Test scenarios
├── tests/                           # Unit and integration tests
└── package.json
```

## Next Steps

1. **Initialize Basic Structure**: Set up the main simulator class and core components
2. **Implement MQTT Communication**: Establish MQTT client with protocol buffer message handling
3. **Build Device Management**: Create virtual device registry and state management
4. **Add Schedule Engine**: Implement irrigation schedule execution logic
5. **Create Status Reporting**: Build comprehensive status reporting system
6. **Add Testing Framework**: Create comprehensive test suite with mock scenarios
7. **Documentation**: Create usage documentation and examples

This architecture provides a comprehensive, testable, and extensible foundation for simulating CODEK irrigation controllers, enabling thorough testing of the irrigation management system without requiring physical hardware.