- Permitir criar conta de usuário / validar email
- Permitir alterar senha
- Permitir resetar senha via email
- Convidar usuário para conta
- Remover usuário de conta
- Mudar papel de usuário na conta
- Comunicar diretamente com o LIC via wifi / http
- "Remover" dispositivo: Finalize property_device e mesh_device_mapping
- "Trocar" dispositivo: Remove antigo e adiciona novo - Atualiza mesh network
- Arquivar projeto
- Arquivar agendamento
- Arquivar propriedade
- Arquivar reservatório

  [11:32, 8/29/2025] Igor: Enviar Device de bomba que não é de serviço e não está em projeto
  [12:04, 8/29/2025] Igor: Mostrar última comunicação codec

IDEIA: No cadastro de dispositivo, permitir associar um elemento a cada saída.
Por exemplo:

- Dispositivo VaveLink (Controlador de Valvula):
  - Saída 1: Setor 1
  - Saída 2: Setor 2
  - Saída 3: Setor 3
  - Saída 4: Setor 4
- Dispositivo PumpLink 10 (Controlador de Bomba):
  - Saída 1: Bomba 1
  - Saída 2: Bomba 2
  - Saída 3: Retrolavagem

IDEIA: - Adicionar toggle "Retrolavagem" na bomba: "Indica se o controlador da bomba comanda a retrolavagem"
