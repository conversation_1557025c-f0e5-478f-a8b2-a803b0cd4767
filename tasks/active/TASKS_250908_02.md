# Task list info:

- name: 250908_02
- base_branch: develop

---

# Task list context:

When excluding a device from a property, we must check if the device is being used in any project. If it is, we must not allow the exclusion.
Right now we are not checking for this and allowing the exclusion, which is resulting in errors when the user tries to access the project.
The project (see docs/DDL.md) has localized_irrigation_controller NOT NULL, which is a foreign key to the device table. This poses a problem when the device is excluded from the property, because the project still references the device. We must allow the localized_irrigation_controller to be set to null.
Same goes for sectors, which have a foreign key to the device table `valve_controller uuid NOT NULL` and `valve_controller_output smallint NOT NULL`.
We must find allow null in both fields.
We also must find any other place where we have a foreign key to the device table and are NOT NULL, so we can analyze if we must allow null or not.

Then, when we remove the device from the property (by finalizing the property_device relation, by setting end_date), we must set any reference to the device to null. This will vary depending on the device model. This is a not exhaustive list of device references by other entities, depending on the device model:

- LIC devices are referenced by project,
- valve controllers are referenced by sectors,
- water pumps controllers are referenced by projects and reservoirs.
- reservoir monitors are referenced by reservoirs.

We must find any other reference to a device and make sure we can handle it properly.

**Device References Analysis Results:**

Based on the database schema analysis, here are ALL device foreign key references found:

### Direct device table references (NOT NULL):

1. **project.localized_irrigation_controller** - NOT NULL FK to device (LIC devices)
2. **sector.valve_controller** - NOT NULL FK to device (valve controller devices)
3. **property_device.device** - NOT NULL FK to device (device-property associations)
4. **current_lic_packet.device** - NOT NULL FK to device (LIC packet data)
5. **lic_packet.device** - NOT NULL FK to device (LIC packet hypertable)
6. **device_message_request.device** - NOT NULL FK to device (device commands)
7. **lic_state.device** - NOT NULL FK to device (LIC state data)
8. **lic_state_history.device** - NOT NULL FK to device (LIC state history)

### Direct device table references (NULLABLE):

1. **reservoir.reservoir_monitor** - NULLABLE FK to device (reservoir monitor devices)
2. **water_pump.water_pump_controller** - NULLABLE FK to device (water pump controllers)

### Indirect device references through water_pump table:

1. **project.irrigation_water_pump** - NOT NULL FK to water_pump (which may have device FK)
2. **project.fertigation_water_pump** - NULLABLE FK to water_pump (which may have device FK)
3. **reservoir.water_pump** - NULLABLE FK to water_pump (which may have device FK)

### Key findings:

- All packet/state/command tables have NOT NULL device FKs (should remain NOT NULL)
- property_device.device should remain NOT NULL (core association table)
- Main issues are in project and sector tables as originally identified
- Water pump associations are indirect and water_pump.water_pump_controller is already NULLABLE

---

# Tasks

## Task 1. Analyze database schema for device foreign key references

**Description**
Examine the current database schema to identify all tables that have foreign key references to the device table, especially those marked as NOT NULL

**Target directories**

- docs/ (DDL.md)
- packages/directus/migrations/

**Status:** Done

## Task 2. Allow NULL for localized_irrigation_controller in projects

**Description**
Create migration to modify the projects table to allow NULL values for the localized_irrigation_controller field.
Also update Directus config to reflect the change.

**Target directories**

- packages/directus/migrations/

**Status:** Done

## Task 3. Allow NULL for valve controller fields in sectors

**Description**
Create migration to modify the sectors table to allow NULL values for valve_controller and valve_controller_output fields.
Also update Directus config to reflect the change.

**Target directories**

- packages/directus/migrations/

**Status:** Done

## Task 4. Review and fix other device foreign key constraints

**Description**
Identify any other NOT NULL foreign key references to devices and determine which should allow NULL values, then create appropriate migrations.
If none is identified, mark the task as done.

**Target directories**

- packages/directus/migrations/
- docs/

**Status:** Done

## Task 5. Implement device reference cleanup logic in frontend

**Description**
Create utility function to set device references to NULL when a device is removed from a property (when property_device.end_date is set).
When ending a property_device relation, we must also end the mesh_device_mapping relation for the selected device. If its a LIC, we must also end the mesh_device_mapping relation for all other devices mapped to it.

confirmExclude at packages/app/src/pages/main/components/DeviceDetailModal.tsx logic should be moved to the new utility function.
So when finalizing the property_device relation for the selected device, confirmExclude will call the utility function instead of ending the property_device relation and mesh mappings directly.

The utility function must be called in the frontend when the user removes a device from a property and will receive:

- The device being removed
- The property the device is being removed from
- the AUTProperty (see packages/app/src/api/queries/account.ts) which has all necessary related data

**Target directories**

- packages/app

**Status:** Done

### Subtask 5.1. Create device removal utility function

**Description**
Create centralized utility function to handle device removal logic, including ending property_device relations and mesh mappings based on device type.

**Target directories**

- packages/app

**Status:** Done

### Subtask 5.2. Implement mesh device mapping cleanup logic

**Description**
Add logic to end mesh_device_mapping relations for removed devices, including LIC-specific logic to end mappings for all mapped devices.

**Target directories**

- packages/app

**Status:** Done

### Subtask 5.3. Refactor DeviceDetailModal to use utility function

**Description**
Move confirmExclude logic from DeviceDetailModal.tsx to use the new utility function.

**Target directories**

- packages/app

**Status:** Done

### Subtask 5.4. Add error handling and user feedback

**Description**
Implement proper error handling, loading states, and user feedback for device removal operations.

**Target directories**

- packages/app

**Status:** Done

## Task 6. Add device removal validation

**Description**
Implement validation logic to check if device removal would break critical system functionality. This must report what other entities would be affected by the removal. This report will be presented to the user before allowing the removal.

**Target directories**

- packages/app

**Status:** Done

### Subtask 6.1. Create device removal impact analysis function

**Description**
Implement function to analyze and report what entities would be affected by device removal (projects, sectors, etc.).

**Target directories**

- packages/app

**Status:** Done

### Subtask 6.2. Create impact report UI component

**Description**
Build UI component to display device removal impact report to users in a clear, understandable format.

**Target directories**

- packages/app

**Status:** Done

### Subtask 6.3. Integrate validation with removal flow

**Description**
Integrate impact analysis and reporting into the device removal confirmation flow.

**Target directories**

- packages/app

**Status:** Done

## Task 7. Implement device removal in frontend

**Description**
Implement the device removal process in the frontend.
Right now it is just ending the property_device relation for the selected device and if the device is a LIC device, it will also end the mesh_device_mapping relation for the selected device: see confirmExclude at packages/app/src/pages/main/components/DeviceDetailModal.tsx.

- Check the effects of ending the property_device relation for the selected device. If there is any other relation affected, tell the user what entities will also be affected and ask the user for confirmation before proceeding.
- IF the user confirms, call the utility function to end the property_device relation for the selected device and clean up any other references to the device.

**Target directories**

- packages/app

**Status:** Done

### Subtask 7.1. Implement pre-removal impact check

**Description**
Add logic to check device removal impacts before showing confirmation dialog.

**Target directories**

- packages/app

**Status:** Done

### Subtask 7.2. Enhanced confirmation dialog with impact display

**Description**
Update confirmation flow to show impact report and get user confirmation for affected entities.

**Target directories**

- packages/app

**Status:** Done

### Subtask 7.3. Execute removal with cleanup on confirmation

**Description**
Call the utility function from Task 5 to execute device removal and cleanup after user confirms.

**Target directories**

- packages/app

**Status:** Done

## Task 8. Update documentation

**Description**
Update DDL.md and other relevant documentation to reflect the schema changes and new device removal process

**Target directories**

- docs/

**Status:** Done

# Task 9. Project's irrigation water pump nullability analysis and updates

**Description**
Since the project's irrigation_water_pump must have a controller that is in the project's localized_irrigation_controller network, when localized_irrigation_controller is null, the irrigation_water_pump must be set to null as well.
For this, we need to:

- Create migration to modify the projects table to allow NULL values for the irrigation_water_pump field. Also update Directus config to reflect the change.
- update packages/app/src/utils/device-removal.ts to align with the new behavior
- Update the relevant types in packages/app/src/api/model and packages/app/src/api/queries/account.ts
- Perform an analysis on the impact of this change on the frontend and update as necessary. This is a non exhaustive list of files that may use the affected fields:
  - packages/app/src/pages/main/components/ProjectConfigPanel.tsx
  - packages/app/src/pages/main/components/ProjectCard.tsx
  - packages/app/src/pages/main/ProjectDetailPage.tsx
  - packages/app/src/pages/main/ProjectConfigPage.tsx
  - packages/app/src/pages/main/ProjectsPage.tsx
  - packages/app/src/components/ProjectStateCard.tsx
- Document the changes in DDL.md , 001-ENTITIES.md and 002-ENTITY_DIAGRAMS.md

**Target directories**

- packages/app
- packages/directus
- docs

**Status:** Done
