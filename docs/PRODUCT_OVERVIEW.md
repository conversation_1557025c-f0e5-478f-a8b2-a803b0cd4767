# Context

My electric engineering team has developed a product capable of manage localized irrigation.

It consists of one main board that must be connected to the internet using WiFi or ethernet cable and other remote boards that control water pumps and valves. The remote boards communicate with the main board using LoRa Mesh.

There are 5 kinds of boards:

LIC - Localized Irrigation Controller: This is the main board connected to the internet. It has a programmable  ESP32 and persistent storage. This is where all configuration is stored and all business rules logic happens. It will command the other boards when to turn on and off.

VC - Valve Controller: This is a board that can control up to 4 electric irrigation valves

WPC-PL10 - Water Pump Controller - PL10 Variant: This is the board that controls water pumps with PL10 specifications.

WPC-PL50 - Water Pump Controller - PL50 Variant: This is the board that controls water pumps with PL50 specifications.

RM - Reservoir Monitor: This is the board that monitors water reservoir systems.

---

## Target Audience

The target audience for this project is small rural property owners who may not have extensive technical knowledge. The app must be simple, intuitive, and user-friendly to accommodate their needs.

## How it works

The system consists of the following components:

- **LIC**: The main board that connects to the internet and manages the irrigation system.

- **VC**: A board that controls up to 4 electric irrigation valves.

- **WPC-PL10**: A board that controls water pumps with PL10 specifications.

- **WPC-PL50**: A board that controls water pumps with PL50 specifications.

- **RM**: A board that monitors water reservoir systems.

A LIC is capable of managing the localized irrigation of a property by controlling water pumps and valves.

Each water pump is connected to a Water Pump Controller (WPC-PL10 or WPC-PL50), which can control the water pump's operation remotely.

Each valve is connected to a VC output, which can control the valve's operation remotely.

It is important to note that a valve will "direct" the water flow to a specific sector of the property, while the Water Pump Controller will control the water pump that provides water to that sector. In this scenario, the water can only flow to one sector at a time, as the Water Pump Controller can only control one water pump at a time and a water pump can only provide water enough for one sector at a time.

In terms of naming, a water pump provides water to a PLOT, which is a set of SECTORS. Thus, a water pump controller is linked to a plot, and a VC is linked to up to 4 sectors of that plot through its outputs.

For this to happen, the LIC must be configured with the serial number of the Water Pump Controller and the serial numbers of the VC's that are connected to it.

The plot will be linked to a Water Pump Controller, and the sectors will be linked to the VC's outputs.

At this point, the LIC know how to control the water pump and the valves, but it does not know when to do it.

The LIC will be configured with a plot's "PLAN". A plan belongs to plot, and it is composed of:

- A start time

- The order of the sectors to be irrigated

- The duration of each sector's irrigation

- The delay between each sector's irrigation

- The days of the week when the plan will be executed

- The time zone of the property

LIC will execute an irrigation plan. It will turn on the valves connected to the output of a VC sequentially, starting at a pre configured time and for a pre configured duration for each VC output, waiting a pre configured delay between each valve.

LIC must know what water pump controller provides water to what VC valves, because it will turn the water pump on before starting a plan. It will know this because the LIC knows the Water Pump Controller that is associated to the plot.

A LIC can have many plots, thus, it can control many sets of water pump controller + VC's.

Two plans of distinct plots can be executed concurrently.

It is required that each element of the system to have, besides its serial number, a label to better identify it.

### Plan Execution Flow

1. The LIC will check the current time and day of the week.

2. If the current time and day match the plan's schedule, the LIC will start the irrigation process by:

- Turning on the water pump associated with the plan's plot.

- Sequentially activating each valve connected to the VC outputs according to the plan's sector order.

- Wait for the specified duration for each sector before moving to the next one.

- Wait for the specified delay between each sector's irrigation.

**Flow Diagram**

```mermaid

flowchart TD

    A[Start] --> B{Is it time to irrigate?}

    B -- Yes --> C[Turn on water pump]

    C --> D[Activate valves in order]

    D --> E[Wait for duration]

    E --> F[Wait for delay]

    F --> D

    B -- No --> G[Check again later]

    G --> B

```
