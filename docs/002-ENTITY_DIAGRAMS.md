# Entity Diagrams

This document provides the Entity-Relationship (ER) and Class diagrams for the irrigation management system. The diagrams illustrate the relationships between different entities, their attributes, and how they interact within the system.

## ER Diagram

```mermaid
erDiagram
    DIRECTUS_USERS {
        UUID    id PK
        string  first_name
        string  last_name
        string  email
        string  avatar
        string  language
        string  status
    }

    ACCOUNT {
        UUID    id PK
        UUID    owner FK
        string  notes
        datetime date_created
        UUID    user_created
        datetime date_updated
        UUID    user_updated
        jsonb   metadata
    }

    PROPERTY {
        UUID     id PK
        UUID     account FK
        string   name
        Point    point
        string   timezone
        string   address_postal_code
        string   address_street_name
        string   address_street_number
        string   address_complement
        string   address_neighborhood
        string   address_city
        string   address_state
        string   address_country
        int      backwash_duration_minutes
        int      backwash_period_minutes
        int      backwash_delay_seconds
        boolean  rain_gauge_enabled
        float    rain_gauge_resolution_mm
        float    precipitation_volume_limit_mm
        float    precipitation_suspended_duration_hours
        string   notes
        datetime date_created
        UUID     user_created
        datetime date_updated
        UUID     user_updated
        jsonb    metadata
    }

    DEVICE {
        UUID    id PK
        string  model  "'LIC'|'WPC-PL10'|'WPC-PL50'|'VC'|'RM'"
        string  identifier
        string  notes
        datetime date_created
        UUID    user_created
        datetime date_updated
        UUID    user_updated
        jsonb   metadata
    }

    PROPERTY_DEVICE {
        UUID     id PK
        UUID     device   FK
        UUID     property FK
        datetime start_date
        datetime end_date
        string   notes
        datetime date_created
        UUID     user_created
        datetime date_updated
        UUID     user_updated
        jsonb    metadata
    }

    WATER_PUMP {
        UUID     id PK
        UUID     property FK
        UUID     water_pump_controller FK
        string   label
        string   identifier
        string   pump_type "'IRRIGATION'|'FERTIGATION'|'SERVICE'"
        string   pump_model
        boolean  monitor_operation
        string   mode "'PULSE'|'CONTINUOUS'"
        string   notes
        datetime date_created
        UUID     user_created
        datetime date_updated
        UUID     user_updated
        jsonb    metadata
    }

    PROJECT {
        UUID     id PK
        UUID     property                       FK
        UUID     irrigation_water_pump          FK "nullable"
        UUID     fertigation_water_pump         FK "nullable"
        UUID     localized_irrigation_controller FK "nullable"
        string   name
        string   description
        int      pipe_wash_time_seconds
        int      backwash_duration_seconds
        int      backwash_period_seconds
        date     start_date
        date     end_date
        string   notes
        datetime date_created
        UUID     user_created
        datetime date_updated
        UUID     user_updated
        jsonb    metadata
    }

    SECTOR {
        UUID     id PK
        UUID     project            FK
        UUID     valve_controller       FK
        int      valve_controller_output "1|2|3|4"
        string   name
        string   description
        number   area
        Polygon  polygon
        int      power "0-100"
        string   notes
        datetime date_created
        UUID     user_created
        datetime date_updated
        UUID     user_updated
        jsonb    metadata
    }

    IRRIGATION_PLAN {
        UUID     id PK
        UUID     project                     FK
        string   name
        string   description
        string   start_time                  "HH:MM:SS"
        string[] days_of_week                "'MON'|'TUE'|'WED'|'THU'|'FRI'|'SAT'|'SUN'"
        boolean  is_enabled
        boolean  fertigation_enabled
        boolean  backwash_enabled
        int      total_irrigation_duration
        date     start_date
        date     end_date
        string   notes
        datetime date_created
        UUID     user_created
        datetime date_updated
        UUID     user_updated
        jsonb    metadata
    }

    IRRIGATION_PLAN_STEP {
        UUID     id PK
        UUID     irrigation_plan    FK
        UUID     sector             FK
        string   description
        int      order
        int      duration_seconds
        int      fertigation_start_delay_seconds
        int      fertigation_duration_seconds
        string   notes
        datetime date_created
        UUID     user_created
        datetime date_updated
        UUID     user_updated
        jsonb    metadata
    }

    CURRENT_IRRIGATION_PLAN_STATE {
        UUID     id PK
        UUID     irrigation_plan       FK
        datetime date_created
        datetime date_updated
        datetime packet_date
        datetime start_time
        datetime end_time
        jsonb    activated_steps
        jsonb    activated_ferti_steps
        boolean  waterpump_working
        datetime backwash_start_time
        boolean  uses_waterpump
        boolean  uses_ferti
    }

    IRRIGATION_PLAN_STATE {
        int      id PK
        UUID     irrigation_plan       FK
        datetime date_created
        datetime packet_date
        datetime start_time
        datetime end_time
        jsonb    activated_steps
        jsonb    activated_ferti_steps
        boolean  waterpump_working
        datetime backwash_start_time
        boolean  uses_waterpump
        boolean  uses_ferti
    }

    CURRENT_RESERVOIR_STATE {
        UUID     id PK
        UUID     reservoir           FK
        datetime date_created
        datetime date_updated
        datetime packet_date
        datetime start_time
        datetime restart_time
        datetime end_time
    }

    RESERVOIR_STATE {
        int      id PK
        UUID     reservoir           FK
        datetime date_created
        datetime packet_date
        datetime start_time
        datetime restart_time
        datetime end_time
    }

    DEVICE_MESSAGE_REQUEST {
        UUID     id PK
        UUID     device                         FK
        UUID     property_device                FK
        bigint   packet_id
        string   payload_type                   "'config'|'devices'|'scheduling'|'dev_scheduling'|'automation'|'control'|'command'|'request_info'|'firmware_update'"
        jsonb    payload_data
        bytea    payload_bytes
        string   message_hash
        UUID     parent_message_id              FK
        UUID     correlation_id
        string   status                         "'pending'|'processing'|'sent'|'acknowledged'|'failed'|'expired'|'cancelled'"
        smallint priority
        datetime scheduled_at
        datetime expires_at
        datetime sent_at
        datetime acknowledged_at
        smallint attempts
        smallint max_attempts
        int      retry_delay_seconds
        text     last_error
        datetime date_created
        UUID     user_created
        datetime date_updated
        UUID     user_updated
        jsonb    metadata
        text     notes
    }

    CURRENT_LIC_PACKET {
        int      id PK
        UUID     device         FK
        datetime date_created
        datetime packet_date
        string   payload_type
        jsonb    payload_data
    }

    LIC_PACKET {
        int      id PK
        UUID     device         FK
        datetime date_created
        datetime packet_date
        string   payload_type
        jsonb    payload_data
    }

    CURRENT_PROJECT_STATE {
        UUID     id PK
        UUID     project                FK
        datetime date_created
        datetime date_updated
        datetime packet_date
        string   irrigation_status
        string   fertigation_status
        string   backwash_status
        jsonb    sectors
    }

    PROJECT_STATE {
        int      id PK
        UUID     project                FK
        datetime date_created
        datetime date_updated
        datetime packet_date
        string   irrigation_status
        string   fertigation_status
        string   backwash_status
        jsonb    sectors
    }

    LIC_STATE {
        UUID     device                    PK,FK
        jsonb    lic
        jsonb    groups
        jsonb    devices
        jsonb    mesh_devices
        jsonb    schedules
        jsonb    sector_schedules
        jsonb    device_schedules
        bigint   last_devices_request
        bigint   last_scheduling_request
        bigint   last_dev_scheduling_request
        bigint   last_automation_request
        bigint   last_config_request
        bigint   current_devices_timestamp
        bigint   current_scheduling_timestamp
        bigint   current_dev_scheduling_timestamp
        bigint   current_automation_timestamp
        bigint   current_config_timestamp
        datetime date_created
        UUID     user_created
        datetime date_updated
        UUID     user_updated
    }

    LIC_STATE_HISTORY {
        UUID     device                    PK,FK
        datetime state_date               PK
        jsonb    lic
        jsonb    groups
        jsonb    devices
        jsonb    mesh_devices
        jsonb    schedules
        jsonb    sector_schedules
        jsonb    device_schedules
        bigint   last_devices_request
        bigint   last_scheduling_request
        bigint   last_dev_scheduling_request
        bigint   last_automation_request
        bigint   last_config_request
        bigint   current_devices_timestamp
        bigint   current_scheduling_timestamp
        bigint   current_dev_scheduling_timestamp
        bigint   current_automation_timestamp
        bigint   current_config_timestamp
    }

    RESERVOIR {
        UUID     id PK
        UUID     property               FK
        string   name
        UUID     reservoir_monitor      FK
        UUID     water_pump             FK
        string   description
        numeric  capacity
        int      safety_time_minutes
        Point    location
        boolean  enabled
        string   notes
        datetime date_created
        UUID     user_created
        datetime date_updated
        UUID     user_updated
        jsonb    metadata
    }

    MESH_DEVICE_MAPPING {
        UUID     id PK
        UUID     mesh_property_device   FK
        UUID     lic_property_device    FK
        datetime start_date
        datetime end_date
        datetime date_created
        UUID     user_created
        datetime date_updated
        UUID     user_updated
    }

    ACCOUNT_USER {
        UUID     id PK
        UUID     account   FK
        UUID     user      FK
        string   role      "'admin'|'user'|'guest'"
        datetime start_date
        datetime end_date
        string   notes
        datetime date_created
        UUID     user_created
        datetime date_updated
        UUID     user_updated
        jsonb    metadata
    }

    %% Relationships
    DIRECTUS_USERS  ||--o{ ACCOUNT                   : "owns"
    ACCOUNT         ||--o{ PROPERTY                  : "owns"
    PROPERTY        ||--o{ PROPERTY_DEVICE           : "has"
    DEVICE          ||--o{ PROPERTY_DEVICE           : "linked at"
    PROPERTY        ||--o{ WATER_PUMP                : "contains"
    DEVICE          ||--o{ WATER_PUMP                : "controls as WPC"
    PROPERTY        ||--o{ RESERVOIR                 : "contains"
    DEVICE          ||--o{ RESERVOIR                 : "monitors as RM"
    WATER_PUMP      ||--o{ RESERVOIR                 : "refills"
    PROPERTY_DEVICE ||--o{ MESH_DEVICE_MAPPING       : "mesh device"
    PROPERTY_DEVICE ||--o{ MESH_DEVICE_MAPPING       : "lic device"
    PROPERTY        ||--o{ PROJECT                   : "contains"
    WATER_PUMP      ||--o{ PROJECT                   : "assigned as irrigation/fertigation pump"
    DEVICE          ||--o{ PROJECT                   : "assigned as LIC"
    PROJECT         ||--o{ SECTOR                    : "includes"
    DEVICE          ||--o{ SECTOR                    : "controls via VC"
    PROJECT         ||--o{ IRRIGATION_PLAN           : "has"
    IRRIGATION_PLAN ||--o{ IRRIGATION_PLAN_STEP      : "comprises"
    IRRIGATION_PLAN ||--|| CURRENT_IRRIGATION_PLAN_STATE : "has current state"
    IRRIGATION_PLAN ||--o{ IRRIGATION_PLAN_STATE        : "has historical states"
    SECTOR          ||--o{ IRRIGATION_PLAN_STEP      : "appears in"
    DEVICE          ||--o{ CURRENT_LIC_PACKET        : "sends packets"
    DEVICE          ||--o{ LIC_PACKET                : "sends packets"
    PROJECT         ||--|| CURRENT_PROJECT_STATE    : "has current state"
    PROJECT         ||--o{ PROJECT_STATE            : "has historical states"
    DEVICE          ||--|| LIC_STATE                : "has current state"
    DEVICE          ||--o{ LIC_STATE_HISTORY        : "has historical states"
    DEVICE          ||--o{ DEVICE_MESSAGE_REQUEST    : "receives messages"
    PROPERTY_DEVICE ||--o{ DEVICE_MESSAGE_REQUEST    : "routes via"
    DEVICE_MESSAGE_REQUEST ||--o{ DEVICE_MESSAGE_REQUEST : "parent of"
    ACCOUNT         ||--o{ ACCOUNT_USER              : "assigns"
    DIRECTUS_USERS  ||--o{ ACCOUNT_USER              : "participates"

```

## Class Diagram

```mermaid
classDiagram
    %% =========================
    %% Base class
    %% =========================
    class BaseEntity {
        +UUID id
        +Date date_created
        +UUID user_created
        +Date date_updated
        +UUID user_updated
        +object metadata
        +string notes
    }

    %% =========================
    %% Concrete entities
    %% =========================
    class DirectusUser {
        +UUID id
        +string first_name
        +string last_name
        +string email
        +string avatar
        +string language
        +string status
    }
    class Account {
        +UUID owner
        +string notes
    }
    class Property {
        +UUID account
        +string name
        +Point point
        +string timezone
        +string address_postal_code
        +string address_street_name
        +string address_street_number
        +string address_complement
        +string address_neighborhood
        +string address_city
        +string address_state
        +string address_country
        +int backwash_duration_minutes
        +int backwash_period_minutes
        +int backwash_delay_seconds
        +boolean rain_gauge_enabled
        +float rain_gauge_resolution_mm
        +float precipitation_volume_limit_mm
        +float precipitation_suspended_duration_hours
        +string notes
    }
    class Device {
        +'LIC'|'WPC-PL10'|'WPC-PL50'|'VC'|'RM' model
        +string identifier
        +string notes
    }
    class PropertyDevice {
        +UUID device
        +UUID property
        +Date start_date
        +Date end_date
        +string notes
    }
    class WaterPump {
        +UUID property
        +UUID water_pump_controller
        +string label
        +string identifier
        +string pump_type
        +string pump_model
        +boolean monitor_operation
        +string notes
    }
    class Project {
        +string name
        +string description
        +UUID property
        +UUID irrigation_water_pump
        +UUID fertigation_water_pump
        +UUID localized_irrigation_controller
        +int pipe_wash_time_seconds
        +int backwash_duration_seconds
        +int backwash_period_seconds
        +Date start_date
        +Date end_date
        +string notes
    }
    class Sector {
        +string name
        +string description
        +UUID project
        +UUID valve_controller
        +int valve_controller_output (1|2|3|4)
        +number area
        +Polygon polygon
        +string notes
    }
    class IrrigationPlan {
        +string name
        +string description
        +UUID project
        +string start_time
        +string[] days_of_week ('MON'|'TUE'|'WED'|'THU'|'FRI'|'SAT'|'SUN')
        +boolean is_enabled
        +boolean fertigation_enabled
        +int total_irrigation_duration
        +Date start_date
        +Date end_date
        +string notes
    }
    class IrrigationPlanStep {
        +UUID irrigation_plan
        +UUID sector
        +string description
        +int order
        +int duration_seconds
        +int fertigation_start_delay_seconds
        +int fertigation_duration_seconds
        +string notes
    }
    class DeviceMessageRequest {
        +UUID device
        +UUID property_device
        +bigint packet_id
        +string payload_type ('config'|'devices'|'scheduling'|'dev_scheduling'|'automation'|'control'|'command'|'request_info'|'firmware_update')
        +jsonb payload_data
        +bytea payload_bytes
        +string message_hash
        +UUID parent_message_id
        +UUID correlation_id
        +string status ('pending'|'processing'|'sent'|'acknowledged'|'failed'|'expired'|'cancelled')
        +smallint priority
        +datetime scheduled_at
        +datetime expires_at
        +datetime sent_at
        +datetime acknowledged_at
        +smallint attempts
        +smallint max_attempts
        +int retry_delay_seconds
        +text last_error
    }
    class CurrentLicPacket {
        +UUID device
        +datetime packet_date
        +string payload_type
        +jsonb payload_data
    }
    class LicPacket {
        +UUID device
        +datetime packet_date
        +string payload_type
        +jsonb payload_data
    }
    class CurrentIrrigationPlanState {
        +UUID irrigation_plan
        +datetime packet_date
        +datetime start_time
        +datetime end_time
        +jsonb activated_steps
        +jsonb activated_ferti_steps
        +boolean waterpump_working
        +datetime backwash_start_time
        +boolean uses_waterpump
        +boolean uses_ferti
    }
    class IrrigationPlanState {
        +UUID irrigation_plan
        +datetime packet_date
        +datetime start_time
        +datetime end_time
        +jsonb activated_steps
        +jsonb activated_ferti_steps
        +boolean waterpump_working
        +datetime backwash_start_time
        +boolean uses_waterpump
        +boolean uses_ferti
    }
    class Reservoir {
        +UUID property
        +string name
        +UUID reservoir_monitor
        +UUID water_pump
        +string description
        +numeric capacity
        +int safety_time_minutes
        +Point location
        +boolean enabled
    }
    class MeshDeviceMapping {
        +UUID mesh_property_device
        +UUID lic_property_device
        +datetime start_date
        +datetime end_date
    }
    class AccountUser {
        +UUID account
        +UUID user
        +string role ('admin'|'user'|'guest')
        +Date start_date
        +Date end_date
        +string notes
    }

    %% =========================
    %% Inheritance
    %% =========================
    BaseEntity <|-- Account
    BaseEntity <|-- Property
    BaseEntity <|-- Device
    BaseEntity <|-- PropertyDevice
    BaseEntity <|-- WaterPump
    BaseEntity <|-- Reservoir
    BaseEntity <|-- MeshDeviceMapping
    BaseEntity <|-- Project
    BaseEntity <|-- Sector
    BaseEntity <|-- IrrigationPlan
    BaseEntity <|-- IrrigationPlanStep
    BaseEntity <|-- CurrentLicPacket
    BaseEntity <|-- LicPacket
    BaseEntity <|-- CurrentIrrigationPlanState
    BaseEntity <|-- IrrigationPlanState
    BaseEntity <|-- CURRENT_RESERVOIR_STATE
    BaseEntity <|-- RESERVOIR_STATE
    BaseEntity <|-- DeviceMessageRequest
    BaseEntity <|-- AccountUser

    %% =========================
    %% Associations & multiplicities
    %% =========================
    Account  "1"       --> "0..*" Property               : owns
    Property "1"       --> "0..*" PropertyDevice         : has
    Device   "1"       --> "0..*" PropertyDevice         : linked at
    Property "1"       --> "0..*" WaterPump              : contains
    Device   "1"       --> "0..*" WaterPump              : controls as WPC
    Property "1"       --> "0..*" Reservoir              : contains
    Device   "1"       --> "0..*" Reservoir              : monitors as RM
    WaterPump "1"      --> "0..*" Reservoir              : refills
    PropertyDevice "1" --> "0..*" MeshDeviceMapping      : mesh device
    PropertyDevice "1" --> "0..*" MeshDeviceMapping      : lic device
    Property "1"       --> "0..*" Project                : contains
    WaterPump "1"      --> "0..*" Project                : assigned as irrigation/fertigation pump
    Device   "1"       --> "0..*" Project                : assigned as LIC
    Project  "1"       --> "0..*" Sector                 : includes
    Device   "1"       --> "0..*" Sector                 : controls via VC
    Project  "1"       --> "0..*" IrrigationPlan         : has
    IrrigationPlan "1" --> "0..*" IrrigationPlanStep     : comprises
    IrrigationPlan "1" --> "1"    CurrentIrrigationPlanState : has current state
    IrrigationPlan "1" --> "0..*" IrrigationPlanState    : has historical states
    Reservoir     "1"  --> "1"    CURRENT_RESERVOIR_STATE : has current state
    Reservoir     "1"  --> "0..*" RESERVOIR_STATE        : has historical states
    Sector   "1"       --> "0..*" IrrigationPlanStep     : appears in
    Device   "1"       --> "0..*" CurrentLicPacket       : sends packets
    Device   "1"       --> "0..*" LicPacket              : sends packets
    Device   "1"       --> "0..*" DeviceMessageRequest   : receives messages
    PropertyDevice "1" --> "0..*" DeviceMessageRequest   : routes via
    DeviceMessageRequest "1" --> "0..*" DeviceMessageRequest : parent of
    Account  "1"       --> "0..*" AccountUser            : assigns
```
