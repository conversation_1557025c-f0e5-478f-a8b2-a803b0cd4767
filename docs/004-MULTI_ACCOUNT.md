# Multi-Account Functionality

## Introduction

This document describes the importance of the user account (account collection) and the functioning of the system for accessing multiple user accounts by a single user.

### User Account (account collection)

Each user profile in the system can have an account (`account` collection in directus, `Account` interface in docs/models.ts).
The user's account is where all the necessary information for managing the user's properties is derived from.
For example, a property (property collection) belongs to an account (account collection).
Data model:

```mermaid
erDiagram
    directus_user ||--|| account : "has"
    account ||--o{ property : "contains"
    property ||--o{ project : "contains"
    project ||--o{ sector : "contains"

    directus_user {
        string id
        string email
        string first_name
        string last_name
    }

    account {
        string id
        string user
        string name
        datetime created_at
    }

    property {
        string id
        string account
        string name
        string location
    }

    project {
        string id
        string property
        string name
        string description
    }

    sector {
        string id
        string project
        string name
        string type
    }
```

### User account access consent

The `account_user` collection in directus is used to store user consent to access a specific account.
Each user can have access to multiple accounts, and each account can have multiple associated users. Consent is required for the user to access the account information.
Therefore, user <PERSON> can access account Y if there is a record in the `account_user` collection that associates user <PERSON> with account Y.
The `AccountUser` interface in `docs/models.ts` represents this relationship.

Some of the main fields of this collection are:

- `id`: Unique identifier of the consent.
- `user`: Reference to the user who gave the consent.
- `account`: Reference to the account for which the consent was given.
- `role`: User role in the account: "admin" | "user" | "guest"
- `start_date`: Consent start date.
- `end_date`: Consent end date (optional).
- `created_at`: Consent creation date.

## Application Flow

1. The user logs in.
2. The system obtains the list of accounts associated with the user.
3. The app checks if the user has only one associated account:
   - If yes, this account is automatically selected as the active account.
   - If no, the app checks if the user already has an active account in localStorage.
     - If yes, this account is used as the active account.
     - If no, the app displays a list of available accounts for the user to choose from. When selecting an account, the system stores this account as the active account in localStorage.

**Selected Account**

- The selected account is used for all subsequent operations in the application, such as accessing properties, projects, sectors, etc.
- The account is stored in a pinia store and in localStorage for persistence between sessions.
- When starting the application, the system checks if there is an active account stored in localStorage and uses it as the current account.
- When an account is selected, the system updates the pinia store and localStorage with the new active account and then loads data related to this account through specific stores (such as `propertyStore`, `projectStore`, etc.) that use the Directus client for REST API calls.

**Account Switching**

The user can change the active account at any time, and the system will update the interface to reflect the selected account.

## Considerations

- Account switching does not require re-authentication, as long as the user is authenticated.
- The system must ensure that all operations are performed on the active account.
