# Database Structure Changes

## Overview

This document outlines the guidelines for making changes to the database structure.

## Naming Conventions

- Table names should be singular and descriptive and use underscores to separate words.
- Column names should be descriptive and use underscores to separate words.
- Primary keys should be named `id`.
- Foreign keys should be named `table_name` (no id suffix).
- Column names should be the most descriptive possible. For example:
  - If it represents a value which has a unit, the unit should be part of the column name (usually at the end). Example: `backwash_duration_minutes`.
  - If it represents a boolean value, the name should be a question. Example: `rain_gauge_enabled` or `is_active` or `has_frequency_inverter`.

## Database documenting using `comment`

Whenever possible, use the `comment` attribute to document the purpose of the table, column, or constraint.

## Migrations

Migration guidelines are defined in /docs/guidelines/backend/migrations-and-seed.md.
Usually additional DDL migrates are needed in order to configure Directus:

- A migration to reflect the database changes, manipulating data in directus system tables, like `directus_collections`, `directus_fields`, `directus_relations`, etc.
- A migration to configure the Directus permissions, manipulating data in `directus_permissions` for the Account Admin role.

## Documentation updates

Database structure changes must be documented. Main documentation files are:

- /docs/001-ENTITIES.md
- /docs/002-ENTITY_DIAGRAMS.md
- /docs/DDL.md
- /docs/000-DESCRIPTION.md and /docs/003-BUSINESS_RULES_AND_CONSTRAINTS.md may also be relevant.

## Seed data updates

Seed data must be updated to align with the new database structure.
Seed data guidelines are defined in /docs/guidelines/backend/migrations-and-seed.md.

## Testing

Depending on the change, unit tests may be affected and may need to be added or updated. Look for coverage in `/directus/tests` and add/update as needed.
If the change involves business logic changes, like new constraints and triggers, a unit test must be added.
Testing guidelines are defined in /docs/guidelines/backend/directus-backend-testing.md.

## Git Commit

All changes must be committed in the same PR.

## Checklists

- Documentation updated for all structural changes.
- Directus field and/or collection migration files generated and committed if applicable.
- Directus read, create, update permissions migrations updated if applicable.
- Seed data updated if applicable.
- Unit tests added / updated if applicable.
