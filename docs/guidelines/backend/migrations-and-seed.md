# Migrations and Seed Data

## 1. Overview

### 1.1. Migration

Migration files are used to migrate the database from one state to another. They are located in the `directus/migrations` directory. They are executed in order, based on the filename. The filename must follow the pattern `YYYYMMDDA-migration-name.js`, where `YYYYMMDD` is the date of creation and `A` is a letter indicating the order of execution. The first migration file must have the letter `A`, the second `B`, and so on.
Two migrations can not have the same combination of date and letter. You must check it and ensure the keys `YYYYMMDDA` do not conflict with each other. The letter should be the next in the alphabet, based on the existing migrations.

### 1.2. Seed

The seed script is used to populate the database with initial data for development purposes. It is located in the `directus/src/seed/index.ts` file.
Any changes to the database structure must be accompanied by a change to the seed script to ensure the data is consistent.
If a table is dropped, the corresponding data must be removed from the seed script.
If a table is created, the seed script must be updated to include the new table.
If a table is altered, the seed script must be updated to reflect the changes.

## 2. Principles

- Deterministic, ordered migrations.
- Seed data synced with schema changes.

## 3. Conventions

### 3.1. Migrations

- Directory: /directus/migrations
- Filename pattern: YYYYMMDD[A]-migration-name.js
- Letter suffix A, B, C… indicates order within the same date.
- No two migrations share the same date-letter key.

### 3.2. Seed

- File: /directus/src/seed/index.ts
- Any schema change must be reflected in the seed:
  - Dropped table -> remove seed data.
  - Created table -> add seed data.
  - Altered table -> update seed data.

## 4. Patterns

- Before adding a migration, inspect existing date-letter keys.
- Update seed in the same PR as migration changes.

## 5. Examples

- Create 20250731D-new-feature.js when C is the latest for 20250731.

## 6. Do/Don’t

### 6.1. Do

- Verify ordering keys and avoid collisions.
- Keep seed aligned with schema.

### 6.2. Don’t

- Merge migrations without seed updates.

## 7. Checklists

- Migration key unique and sequential.
- Seed updated for all structural changes.
- Docs updated .

## 8. References

Documentation updates required on schema changes:

- /docs/001-ENTITIES.md
- /docs/002-ENTITY_DIAGRAMS.md
- /docs/DDL.md
