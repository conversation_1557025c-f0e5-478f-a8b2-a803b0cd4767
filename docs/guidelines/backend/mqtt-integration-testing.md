# MQTT Integration Testing Guidelines

This document provides comprehensive guidelines for writing tests in the `packages/mqtt-integration/` package. Tests are written in TypeScript using <PERSON><PERSON>'s test runner with database transaction-based isolation.

## Table of Contents

- [Test Structure](#test-structure)
- [Database Testing Pattern](#database-testing-pattern)
- [Test File Organization](#test-file-organization)
- [Fixture Creation](#fixture-creation)
- [Testing Patterns](#testing-patterns)
- [Best Practices](#best-practices)
- [Example Test](#example-test)

## Test Structure

All tests must follow this basic structure:

```typescript
import { describe, it, expect } from "bun:test";
import { runInTransaction } from "./helpers/db";
import {
  insertUser,
  insertAccount,
  insertProperty,
  // ... other fixture functions
} from "./helpers/fixtures";

describe("feature-name", () => {
  it("should describe what the test does", async () => {
    await runInTransaction(async (trx) => {
      // Setup test data
      // Execute function under test
      // Assert results
    });
  });
});
```

## Database Testing Pattern

### Transaction Management

- **All tests MUST run within a transaction** that is automatically rolled back
- Use the `runInTransaction` helper function for all tests
- Each test gets a clean database state and is completely isolated

```typescript
await runInTransaction(async (trx) => {
  // Your test code here
  // Transaction is automatically rolled back at the end
});
```

### Database Connection

- Use the centralized database connection from `tests/helpers/db.ts`
- The connection is configured for testing environment with proper pooling
- Never create direct database connections in test files

## Test File Organization

### Naming Convention
- Test files should be named `*.test.ts`
- Place test files in `packages/mqtt-integration/tests/`
- Mirror the source structure when testing specific modules

### Helper Files
- **Database helpers**: `tests/helpers/db.ts` - connection and transaction management
- **Fixture helpers**: `tests/helpers/fixtures.ts` - entity creation functions
- Add new fixture functions to `fixtures.ts` when needed

## Fixture Creation

### Entity Interfaces
All database entities have TypeScript interfaces defined in `fixtures.ts`. These provide type safety for test data creation.

### Fixture Functions
Use the provided fixture functions to create test data:

```typescript
// Create basic entities
const user = await insertUser(trx, { email: "<EMAIL>" });
const account = await insertAccount(trx, user.id);
const property = await insertProperty(trx, account.id, "Property Name");

// Create devices
const licDevice = await insertDevice(trx, "LIC", "LIC-001");
const valveController = await insertDevice(trx, "VC", "VC-001");
const waterPumpController = await insertDevice(trx, "WPC-PL10", "WPC-001");

// Create associations
const propertyDevice = await insertPropertyDevice(
  trx,
  device.id,
  property.id,
  startDate,
  endDate
);

// Create mesh mappings
const meshMapping = await insertMeshMapping(
  trx,
  meshPropertyDeviceId,
  licPropertyDeviceId,
  startDate,
  endDate
);
```

### Custom Test Data
- Use the `overrides` parameter in fixture functions to customize data
- Generate unique identifiers using `Math.random().toString(36).slice(2)`
- Use realistic but obvious test data (e.g., "Test Property", "LIC-001")

## Testing Patterns

### Testing Date Ranges
Many functions work with date ranges. Test these scenarios:

```typescript
// Active association at reference date
await insertPropertyDevice(trx, deviceId, propertyId, startDate, endDate);
const result = await functionUnderTest(trx, "DEVICE-001", referenceDate);

// Ongoing association (null end date)
await insertPropertyDevice(trx, deviceId, propertyId, startDate, null);

// Expired association
const result = await functionUnderTest(trx, "DEVICE-001", dateAfterEnd);
expect(result).toBeUndefined(); // or toEqual([])
```

### Testing Query Results
Structure assertions for database query results:

```typescript
// Test existence and type
expect(result).toBeDefined();
expect(Array.isArray(result)).toBe(true);
expect(result.length).toBeGreaterThan(0);

// Test specific properties
expect(result[0]?.id).toBe(expectedId);
expect(result[0]?.name).toBe("Expected Name");

// Test nested objects
expect(result[0]?.nested_object).toBeDefined();
expect(result[0]?.nested_object.property).toBe("value");
```

### Testing Edge Cases
Always test these scenarios:

```typescript
// Non-existent entities
const result = await functionUnderTest(trx, "NONEXISTENT", new Date());
expect(result).toBeUndefined(); // or toEqual([])

// Null/undefined handling
expect(result?.optional_field).toBeNull();
expect(result?.missing_field).toBeUndefined();

// Date boundary conditions
// Test dates exactly on start/end boundaries
// Test dates before and after valid ranges
```

### Multiple Entity Testing
When testing functions that return multiple results:

```typescript
// Create multiple test entities with different attributes
const entity1 = await createEntity(trx, "Name 1");
const entity2 = await createEntity(trx, "Name 2");

const results = await functionUnderTest(trx, criteria);

expect(results.length).toBe(2);
expect(results.map(r => r.name).sort()).toEqual(["Name 1", "Name 2"]);
```

## Best Practices

### Test Isolation
- Each test creates its own test data
- Never rely on data from other tests
- Use unique identifiers to avoid conflicts
- All test data is automatically cleaned up via transaction rollback

### Descriptive Test Names
```typescript
// Good
it("should return projects for LIC device associated and active at reference date")
it("should return empty array when device is not associated at reference date")
it("should handle null end dates for ongoing associations")

// Avoid
it("should work")
it("test query function")
```

### Assertion Patterns
```typescript
// Check for existence before accessing properties
expect(result).toBeDefined();
expect(result?.property).toBe(expectedValue);

// Use appropriate matchers
expect(array).toEqual([]);           // Empty array
expect(value).toBeNull();           // Explicit null
expect(value).toBeUndefined();      // Undefined/missing
expect(array.length).toBeGreaterThan(0); // Non-empty array
```

### Error Handling
```typescript
// Test that fixture creation succeeds
const device = await insertDevice(trx, "LIC", "TEST-001");
expect(device).toBeDefined();
expect(device.id).toBeDefined();

// Let database constraint violations bubble up as test failures
// This helps catch data modeling issues
```

### Date Testing
```typescript
// Use consistent date patterns
const startDate = new Date("2024-01-01");
const endDate = new Date("2024-12-31");
const referenceDate = new Date("2024-06-01"); // Middle of range

// Test boundary conditions
const beforeStart = new Date("2023-12-31");
const afterEnd = new Date("2025-01-01");
```

## Example Test

Here's a complete example following these guidelines:

```typescript
import { describe, it, expect } from "bun:test";
import { getCurrentPropertyForDevice } from "../src/db/queries/property-device-queries";
import { runInTransaction } from "./helpers/db";
import {
  insertUser,
  insertAccount,
  insertProperty,
  insertDevice,
  insertPropertyDevice,
} from "./helpers/fixtures";

describe("property-device-queries", () => {
  describe("getCurrentPropertyForDevice", () => {
    it("should return property when device is associated and active at reference date", async () => {
      await runInTransaction(async (trx) => {
        // Setup test data
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "TEST-001");
        
        const startDate = new Date("2024-01-01");
        const endDate = new Date("2024-12-31");
        const referenceDate = new Date("2024-06-01");
        
        await insertPropertyDevice(trx, device.id, property.id, startDate, endDate);

        // Execute function under test
        const result = await getCurrentPropertyForDevice(
          trx,
          "TEST-001",
          "LIC",
          referenceDate
        );

        // Assert results
        expect(result).toBeDefined();
        expect(result.id).toBe(property.id);
        expect(result.name).toBe("Test Property");
      });
    });

    it("should return undefined when device is not associated at reference date", async () => {
      await runInTransaction(async (trx) => {
        const result = await getCurrentPropertyForDevice(
          trx,
          "NONEXISTENT-001",
          "LIC",
          new Date()
        );

        expect(result).toBeUndefined();
      });
    });

    it("should handle ongoing associations with null end dates", async () => {
      await runInTransaction(async (trx) => {
        const user = await insertUser(trx);
        const account = await insertAccount(trx, user.id);
        const property = await insertProperty(trx, account.id, "Test Property");
        const device = await insertDevice(trx, "LIC", "TEST-002");
        
        const startDate = new Date("2024-01-01");
        const futureDate = new Date("2025-01-01");
        
        await insertPropertyDevice(trx, device.id, property.id, startDate, null);

        const result = await getCurrentPropertyForDevice(
          trx,
          "TEST-002",
          "LIC",
          futureDate
        );

        expect(result).toBeDefined();
        expect(result.id).toBe(property.id);
      });
    });
  });
});
```

## Running Tests

```bash
# Run all tests
bun test ./tests

# Run specific test file  
bun test ./tests/project-queries.test.ts

# Run tests matching pattern
bun test ./tests --grep "should return projects"

# Run with verbose output
bun test ./tests --verbose
```

Following these guidelines ensures consistent, maintainable, and reliable tests for the MQTT integration package.