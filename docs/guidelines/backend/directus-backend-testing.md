# Directus Backend Testing Guidelines

## Overview

This document outlines the testing standards, patterns, and best practices for the Directus backend project. Our testing strategy emphasizes database integration testing with proper transaction isolation, comprehensive fixture management, and meaningful test coverage over raw percentages.

## Testing Framework and Tools

### Primary Framework

- **Bun Test**: Primary test runner (`bun test`)
- **Knex.js**: Database query builder and transaction management
- **PostgreSQL**: Database engine with transaction support

### Key Testing Utilities

- `createKnex()`: Creates isolated test database connections
- `begin()`: Starts database transactions for test isolation
- `rollbackAndDestroy()`: Cleans up transactions after tests
- `expectRejectWithinSavepoint()`: Tests error scenarios without affecting main transaction

## Test Structure and Organization

### File Structure

```
directus/tests/
├── helpers/
│   ├── db.ts              # Database connection and transaction utilities
│   └── fixtures.ts        # Test data creation helpers
├── *.test.ts              # Individual test files
└── README.md              # Testing documentation
```

### Naming Conventions

- Test files: `*.test.ts` (e.g., `mesh_device_mapping.test.ts`)
- Helper files: Descriptive names in `helpers/` directory
- Test descriptions: Use clear, behavior-focused descriptions

### Test File Structure

```typescript
import { describe, it, beforeEach, afterEach, expect } from "bun:test";
import { Knex } from "knex";
import { createKnex, begin, rollbackAndDestroy } from "./helpers/db";
import /* fixtures */ "./helpers/fixtures";

describe("Feature Name", () => {
  let knex: Knex;
  let trx: Knex.Transaction;

  beforeEach(async () => {
    knex = createKnex();
    trx = await begin(knex);
  });

  afterEach(async () => {
    await rollbackAndDestroy(trx);
  });

  // Test cases here
});
```

## Database Testing Patterns

### Transaction Isolation

Every test runs in its own transaction that is rolled back after completion:

```typescript
beforeEach(async () => {
  knex = createKnex();
  trx = await begin(knex);
});

afterEach(async () => {
  await rollbackAndDestroy(trx);
});
```

### Test Data Setup

Use fixture functions for consistent test data creation:

```typescript
// Setup helper function
async function setupPropertyWithDevices() {
  const userId = await insertUser(trx);
  const accountId = await insertAccount(trx, userId);
  const propertyId = await insertProperty(trx, accountId);

  const licDev = await insertDevice(trx, "LIC");
  const meshDev1 = await insertDevice(trx, "WPC-PL10");

  return { propertyId, licDev, meshDev1 };
}
```

### Error Testing with Savepoints

Test constraint violations and error scenarios without breaking the main transaction:

```typescript
await expectRejectWithinSavepoint(trx, async () => {
  await insertMeshMapping(trx, meshPd, licPd, daysAgo(1), null);
});
```

## Test Categories

### Integration Tests

- **Primary focus**: Test database operations, constraints, and business logic
- **Scope**: Multiple components working together
- **Examples**: Mesh device mapping constraints, project creation workflows

### Database Constraint Tests

- **Purpose**: Verify database-level business rules and constraints
- **Pattern**: Use `expectRejectWithinSavepoint()` for constraint violations
- **Examples**: Foreign key constraints, custom check constraints

### Seed/Migration Tests

- **Purpose**: Verify data seeding and migration operations
- **Scope**: End-to-end database population and schema changes
- **Examples**: `seedDatabase` function testing

## Best Practices

### Assertion Patterns

```typescript
// Verify record creation
expect(result).toBeDefined();
expect(result).toBeString(); // For UUIDs

// Verify collections
expect(users).toHaveLength(3);
expect(users.map(u => u.email).sort()).toEqual([...]);

// Verify specific values
expect(user?.first_name).toBe("Expected Name");
```

### Date Handling

```typescript
// Use helper functions for consistent date manipulation
const start = daysAgo(1);
const future = plusSeconds(start, 3600);

// Test date proximity for time-sensitive operations
function closeMs(a: Date, b: Date, toleranceMs = 10) {
  return Math.abs(a.getTime() - b.getTime()) <= toleranceMs;
}
```

### Test Organization

- Group related tests in `describe` blocks
- Use descriptive test names that explain the expected behavior
- Create setup helper functions for complex test scenarios
- Keep tests focused on single behaviors or constraints

## Setup and Configuration

### Environment Variables

Required test database environment variables:

```bash
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_USER=test_user
TEST_DB_PASSWORD=test_password
TEST_DB_DATABASE=test_db
```

### Database Setup

**Important**: Before running tests, you must set up the test database:

```bash
# Recreate test database (from package.json)
bun run docker:database:recreate-test-db
```

This command will:

1. Drop the existing `test_db` database (if it exists)
2. Create a new `test_db` database
3. Install the PostGIS extension
4. Bootstrap the database with the Directus schema

**Note**: This setup is required because tests run against a real PostgreSQL database, not an in-memory database. This ensures that database constraints, triggers, and PostgreSQL-specific features work correctly in tests.

### Dependencies

```json
{
  "devDependencies": {
    "@types/bun": "latest",
    "bun-types": "^1.2.14",
    "knex": "^3.1.0",
    "pg": "^8.16.0"
  }
}
```

## Running Tests

### Basic Commands

```bash
# Run all tests
bun test

# Run specific test file
bun test tests/mesh_device_mapping.test.ts

# Run tests with verbose output
bun test --verbose

# Run tests in watch mode
bun test --watch
```

### Test Database Management

```bash
# Recreate test database
bun run docker:database:recreate-test-db

# Access test database directly
docker compose exec -u postgres database psql -d test_db
```

## Common Patterns

### Fixture Creation Pattern

```typescript
export async function insertUser(trx: Knex, overrides: Partial<any> = {}) {
  const id = overrides.id ?? randomUUID();
  const [row] = await trx("directus_users")
    .insert({
      id,
      first_name: overrides.first_name ?? "Test",
      last_name: overrides.last_name ?? "User",
      email:
        overrides.email ??
        `test_${Math.random().toString(36).slice(2)}@example.com`,
      // ... other defaults
    })
    .returning(["id"]);
  return row.id ?? id;
}
```

### Complex Setup Pattern

```typescript
async function setupMeshNetworks() {
  const userId = await insertUser(trx);
  const accountId = await insertAccount(trx, userId);
  const propertyId = await insertProperty(trx, accountId, "Test Property");

  // Create devices
  const lic1Device = await insertDevice(trx, "LIC", "LIC-1");
  const rm1Device = await insertDevice(trx, "RM", "RM-1");

  // Create property devices with relationships
  const lic1PropDevice = await insertPropertyDevice(
    trx,
    lic1Device,
    propertyId,
    activeStart,
    null
  );

  return { propertyId, lic1Device, lic1PropDevice /* ... */ };
}
```

### Constraint Testing Pattern

```typescript
it("should reject invalid operation", async () => {
  const setup = await setupTestData();

  await expectRejectWithinSavepoint(trx, async () => {
    await performInvalidOperation(setup);
  });
});
```

### Date Testing Utilities

```typescript
// Helper functions from existing codebase
function daysAgo(n: number): Date {
  const d = new Date();
  d.setDate(d.getDate() - n);
  return d;
}

function plusSeconds(date: Date, seconds: number): Date {
  return new Date(date.getTime() + seconds * 1000);
}

function closeMs(a: Date, b: Date, toleranceMs = 10): boolean {
  return Math.abs(a.getTime() - b.getTime()) <= toleranceMs;
}
```

## Examples

### Basic Integration Test

```typescript
it("creates mapping and updates current mapping (happy path)", async () => {
  const { licPd, meshPd1 } = await setupPropertyWithDevices();

  const start = daysAgo(1);
  const id = await insertMeshMapping(trx, meshPd1, licPd, start, null);
  expect(id).toBeDefined();

  // Verify trigger updated current mapping
  const currentId = await getCurrentMeshMappingId(trx, meshPd1);
  expect(currentId).toEqual(id);
});
```

### Constraint Violation Test

```typescript
it("rejects mapping if mesh and LIC properties differ", async () => {
  const userId = await insertUser(trx);
  const accountId = await insertAccount(trx, userId);
  const propA = await insertProperty(trx, accountId, "PropA");
  const propB = await insertProperty(trx, accountId, "PropB");

  const licPd = await insertPropertyDevice(
    trx,
    licDev,
    propA,
    daysAgo(5),
    null
  );
  const meshPd = await insertPropertyDevice(
    trx,
    meshDev,
    propB,
    daysAgo(5),
    null
  );

  await expectRejectWithinSavepoint(trx, async () => {
    await insertMeshMapping(trx, meshPd, licPd, daysAgo(1), null);
  });
});
```

### Comprehensive Seed Test

```typescript
it("should successfully seed the database with all required data", async () => {
  await seedDatabase(trx);

  // Verify users were created
  const users = await trx("directus_users")
    .whereIn("email", [
      "<EMAIL>",
      "<EMAIL>",
    ])
    .select("*");

  expect(users).toHaveLength(2);
  expect(users.map((u) => u.email).sort()).toEqual([
    "<EMAIL>",
    "<EMAIL>",
  ]);

  // Verify accounts and properties
  const accounts = await trx("account").select("*");
  expect(accounts).toHaveLength(2);

  const properties = await trx("property").select("*");
  expect(properties.length).toBeGreaterThan(0);
});
```

### Error Handling Test

```typescript
it("should handle duplicate user creation gracefully", async () => {
  await seedDatabase(trx);

  // Attempt to seed again should handle duplicates
  await expect(seedDatabase(trx)).resolves.not.toThrow();

  // Verify no duplicate users were created
  const users = await trx("directus_users")
    .whereIn("email", [
      "<EMAIL>",
      "<EMAIL>",
    ])
    .select("*");
  expect(users).toHaveLength(2);
});
```

## Do's and Don'ts

### Do

- ✅ Use transactions for test isolation
- ✅ Create reusable fixture functions
- ✅ Test business constraints and rules
- ✅ Use descriptive test names
- ✅ Group related tests logically
- ✅ Test both success and failure scenarios
- ✅ Use savepoints for error testing
- ✅ Clean up resources in afterEach hooks
- ✅ Use helper functions for date manipulation
- ✅ Test database triggers and constraints

### Don't

- ❌ Share state between tests
- ❌ Test implementation details over behavior
- ❌ Create overly complex test setups
- ❌ Ignore database constraints in tests
- ❌ Use hardcoded IDs or timestamps
- ❌ Skip cleanup in afterEach hooks
- ❌ Test multiple unrelated behaviors in one test
- ❌ Rely on test execution order

## Troubleshooting

### Common Issues

#### Transaction Deadlocks

```typescript
// Problem: Tests hanging due to transaction conflicts
// Solution: Ensure proper cleanup and avoid nested transactions
afterEach(async () => {
  await rollbackAndDestroy(trx); // Always clean up
});
```

#### Constraint Violations

```typescript
// Problem: Tests failing due to foreign key constraints
// Solution: Create dependencies in correct order
const userId = await insertUser(trx);
const accountId = await insertAccount(trx, userId); // userId must exist first
```

#### Date Comparison Issues

```typescript
// Problem: Flaky tests due to timestamp precision
// Solution: Use tolerance-based comparison
expect(closeMs(actualDate, expectedDate, 100)).toBe(true);
```

#### Database Connection Issues

```bash
# Problem: "database test_db does not exist" error
# Solution: Set up the test database first
bun run docker:database:recreate-test-db

# Problem: Connection refused or authentication failed
# Solution: Check Docker containers are running and environment variables
docker compose ps
echo $TEST_DB_HOST $TEST_DB_USER $TEST_DB_DATABASE
```

## Performance Considerations

### Efficient Test Data Creation

- Use batch inserts when creating multiple records
- Reuse common setup data within test suites
- Minimize database round trips in fixtures

### Test Isolation vs Speed

- Prioritize isolation over speed
- Use transactions for cleanup (faster than DELETE operations)
- Consider test database optimization for CI/CD

## References

- [Migrations and Seed Guidelines](/docs/guidelines/backend/migrations-and-seed.md)
- [Bun Test Documentation](https://bun.sh/docs/cli/test)
- [Knex.js Documentation](https://knexjs.org/)

## Conflicts & Resolutions

- **Transaction vs Real Database**: Use transactions for isolation, but be aware that some database features (like certain triggers) may behave differently
- **Test Speed vs Isolation**: Prioritize isolation over speed; use fixtures efficiently to minimize setup time
- **Mocking vs Integration**: Prefer integration tests for database operations; use mocks sparingly for external dependencies
