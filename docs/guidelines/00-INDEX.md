# Irriga Mais Guidelines Index

Overview
This index links to consolidated guidelines extracted from:

- /app/.augment/rules/code.md
- /app/.github/instructions/coding.instructions.md
- /app/.github/instructions/design.instructions.md
- /app/.github/instructions/react.instructions.md
- /app/.github/instructions/tech-stack.instructions.md
- /app/docs/design.json
- /tasks/INSTRUCTIONS.md

Sections

- Foundations
  - Tech Stack: /docs/guidelines/foundations/tech-stack.md
  - Architecture: /docs/guidelines/foundations/architecture.md
  - Design System: /docs/guidelines/foundations/design-system.md
  - Coding Conventions: /docs/guidelines/foundations/coding-conventions.md
- Frontend
  - API & Services: /docs/guidelines/backend/api-and-services.md
  - React: /docs/guidelines/frontend/react.md
  - State (Jotai): /docs/guidelines/frontend/state-management-jotai.md
  - TailwindCSS: /docs/guidelines/frontend/tailwindcss.md
  - UX/UI: /docs/guidelines/frontend/ux-ui-guidelines.md
  - PWA & Performance: /docs/guidelines/frontend/pwa-and-performance.md
- Backend
  - Migrations & Seed: /docs/guidelines/backend/migrations-and-seed.md
  - Directus Testing: /docs/guidelines/backend/directus-backend-testing.md
  - MQTT Integration Testing: /docs/guidelines/backend/mqtt-integration-testing.md
  - Database Structure Changes: /docs/guidelines/backend/ddl-changes.md
- Workflows
  - Tasks File Usage: /docs/guidelines/workflows/tasks-file-usage.md
  - Git & Commit Policy: /docs/guidelines/workflows/git-and-commit-policy.md
- References
  - Design Tokens (from design.json): /docs/guidelines/references/design-tokens.md
  - Cross-References: /docs/guidelines/references/cross-references.md
