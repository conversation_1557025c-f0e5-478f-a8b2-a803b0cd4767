# Task File Expanding Workflow

The user provided the task file and task id.

Please analyze Task id in the task file to assess its complexity and scope for breakdown into manageable subtasks.

## Analysis Steps

1. First, read and parse the task file to locate and understand Task id
2. Use codebase retrieval to gather relevant code snippets, documentation, and context related to the task requirements
3. Investigate any dependencies, constraints, or prerequisites that may affect task completion
4. Consider the current codebase architecture and existing patterns that may influence the implementation approach
5. If you have access to websearch, search for relevant information and context related to the task requirements if needed

## Required Analysis Output

1. **Task Overview**: Summarize the task's main objective and current description
2. **Component Breakdown**: Identify the main components, phases, or logical steps involved in completing the task
3. **Subtask Recommendation**: Provide a specific number of recommended subtasks (typically 2-6) with clear justification for why this number is appropriate
4. **Subtask Specifications**: For each proposed subtask, provide:
   - A clear, actionable title
   - A brief description of the work involved
   - Estimated complexity level (Low/Medium/High)
   - Any dependencies on other subtasks
5. **Effort Estimation**: Ensure each subtask represents approximately 15-25 minutes of focused development work for a professional developer

## Useful Documents

1. /tasks/INSTRUCTIONS.md - Tasks file format and workflow
2. /docs/guidelines/00-INDEX.md - General guidelines for the project organized by topic

## Decision Criteria

- Tasks requiring less than 30 minutes total should generally not be broken down
- Each subtask must be independently testable or verifiable
- Subtasks should follow logical development sequence
- Consider code review and testing overhead in time estimates

## User Response Options

After providing your analysis, present these options to the user:

1. **Accept and update task file**: "The breakdown looks good - please update the task file with these subtasks" - The tasks file specification can be found in the file @tasks/INSTRUCTIONS.md.
2. **Refine further**: "The breakdown needs adjustment - let's discuss specific changes"
3. **Keep as single task**: "The task doesn't need breakdown - keep it as is"
4. **Request more context**: "I need additional information about [specific aspect] before deciding"

If the task is already appropriately scoped (estimated at 15-25 minutes total), explain specifically why it doesn't require breakdown and recommend keeping it as a single unit of work.
