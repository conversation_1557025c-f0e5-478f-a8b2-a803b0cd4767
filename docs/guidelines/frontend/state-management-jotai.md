# State Management (Jotai)

Overview
Source: /app/.github/instructions/coding.instructions.md (Jotai section)

Principles

- Atomic, minimal, composable state units.
- Derived state for computation; async via write-only atoms.
- Domain organization in src/store.

Conventions

- Place atoms in src/store by domain: auth.ts, data.ts, crud.ts.
- Name atoms with Atom suffix: user<PERSON>tom, isAuthenticatedAtom.
- Hook usage:
  - use<PERSON>tom for read/write.
  - useAtomValue for read-only.
  - useSet<PERSON>tom for write-only.

Patterns

- Derived atoms for memoized computations.
- Write-only atoms encapsulate side effects (API calls).

Examples
// Derived atom
const fullNameAtom = atom((get) => `${get(firstNameAtom)} ${get(lastNameAtom)}`);

// Write-only atom
const refreshDataAtom = atom(null, async (get, set) => {
// call API and set atoms
});

Do/Don’t
Do

- Split atoms into granular, domain-focused units.
- Prefer derived atoms over manual memoization.

Don’t

- Use one giant atom for unrelated concerns.

Checklists

- Naming with Atom suffix.
- Domain file placement.
- Correct hooks per access pattern.

References

- /docs/guidelines/backend/api-and-services.md
- /docs/guidelines/frontend/foundations/coding-conventions.md

Conflicts & Resolutions

- None.
