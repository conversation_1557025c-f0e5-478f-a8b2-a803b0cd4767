# Architecture

Overview
Sources:

- /app/.github/instructions/tech-stack.instructions.md (Frontend architecture layout)
- /app/.github/instructions/tech-stack.instructions.md (API architecture overview)

Principles

- Modular, scalable structure with clear separation: pages, components, store, api.
- Centralized API service; typed models; domain-specific methods.
- Design system integration at all layers.

Conventions
Frontend Structure
src/

- App.tsx: Root component
- frontend.tsx: Client entry
- index.tsx: Server/entry with API routes
- Routes.tsx: Routing
- store/: Global state (Jotai)
  - index.ts, auth.ts, data.ts, crud.ts, Provider.tsx
- components/: Reusable UI
- pages/: Screens

API Architecture

- Use @directus/sdk client in src/api/client.ts
- Central service in src/api/service.ts
- Typed collections and domain-specific methods
- Request/response interceptors; error handling
- Env-based configuration; auth token mgmt

Patterns

- Singleton API service instance.
- Generic CRUD factory.
- Account/user tree queries via SDK.
- Asset URL resolution utility.

Examples
See /docs/guidelines/frontend/api-and-services.md for Directus client/service details.

Do/Don’t
Do

- Keep atoms, services, and components small and domain-organized.
- Enforce typed boundaries between layers.

Don’t

- Bypass centralized API/service for ad-hoc fetches.

Checklists

- Routes declared in Routes.tsx.
- API functions routed via service.ts.
- State colocated by domain under store/.

References

- /docs/guidelines/frontend/foundations/tech-stack.md
- /docs/guidelines/frontend/react.md
- /docs/guidelines/frontend/api-and-services.md

Conflicts & Resolutions

- None.
