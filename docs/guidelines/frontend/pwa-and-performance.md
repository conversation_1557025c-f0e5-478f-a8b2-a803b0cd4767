# PWA & Performance

Overview
Source: /app/.github/instructions/tech-stack.instructions.md

Principles

- PWA-ready with offline considerations.
- Optimize build/runtime performance.

Conventions

- Tailwind purge; code splitting; tree shaking; minify in prod.
- Hot reloading only in development.
- Lazy load where appropriate.

Patterns

- Use Bun build options for minify, splitting, sourcemaps.
- React 19 performance features; memoization in components.

Examples
Production build
bun run build.ts --minify --splitting --source-map external

Do/Don’t
Do

- Keep bundles clean of secrets.
- Enable code splitting and lazy loading.

Don’t

- Ship dev-only features to production.

Checklists

- Source maps disabled or external in prod.
- Asset sizes checked.
- PWA offline path validated (if applicable).

References

- /docs/guidelines/frontend/foundations/tech-stack.md

Conflicts & Resolutions

- None.
