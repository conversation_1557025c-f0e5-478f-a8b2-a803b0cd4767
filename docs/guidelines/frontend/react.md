# React Guidelines

Overview
Sources:

/home/<USER>/geocontrol/repositorios/agro/irriga-mais/app/.github/instructions/coding.instructions.md
/home/<USER>/geocontrol/repositorios/agro/irriga-mais/app/.github/instructions/react.instructions.md
/home/<USER>/geocontrol/repositorios/agro/irriga-mais/app/.github/instructions/design.instructions.md
Before creating computed variables, check if there is not already a jotai atom that provides the value in /home/<USER>/geocontrol/repositorios/agro/irriga-mais/app/src/store/data.ts.
Before creating any method that deals with jotai atoms, check if there is not already a method that does what you need in /home/<USER>/geocontrol/repositorios/agro/irriga-mais/app/src/store/data.ts. For example: propertyByIdAtom is a method atom that returns a property by ID.
/home/<USER>/geocontrol/repositorios/agro/irriga-mais/doc/guidelines/foundations/design-system.md
/home/<USER>/geocontrol/repositorios/agro/irriga-mais/doc/guidelines/frontend/tailwindcss.md
/home/<USER>/geocontrol/repositorios/agro/irriga-mais/doc/guidelines/frontend/state-management-jotai.md

- Strong typing for props and state.

Conventions

- Interfaces for props; named exports; small, focused components.
- useCallback/useMemo to control re-renders.
- Context for global/shared state; avoid prop drilling.
- Keys on lists; avoid inline lambdas in JSX when feasible.
- Align with design system tokens and patterns.

Patterns

- UI props mapped to DS variants/sizes.
- Use domain-organized components under src/components and src/pages.

Examples
Props typing aligned to DS
interface ButtonProps {
variant: "primary" | "secondary" | "ghost";
size: "sm" | "md" | "lg";
}

Do/Don’t
Do

- Follow design.json and tokens.
- Keep accessibility and performance in mind.

Don’t

- Break DS consistency.
- Leave side effects in render.

Checklists

- Props typed; component pure.
- DS adherence verified; states covered.
- Before creating computed variables, check if there is not already a jotai atom that provides the value in /app/src/store/data.ts.
- Before creating any method that deals with jotai atoms, check if there is not already a method that does what you need in /app/src/store/data.ts. For example: propertyByIdAtom is a method atom that returns a property by ID.

References

- /doc/guidelines/foundations/design-system.md
- /doc/guidelines/frontend/tailwindcss.md
- /doc/guidelines/frontend/state-management-jotai.md

Conflicts & Resolutions

- None.
