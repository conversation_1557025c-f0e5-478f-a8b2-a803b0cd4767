# API and Services

Overview
Source: /app/.github/instructions/tech-stack.instructions.md (API Architecture)

Directus SDK is the backbone for API access. Use a centralized client and service with typed collections, authentication, and error handling.

Principles

- Single Directus client initialized in src/api/client.ts.
- Central service layer in src/api/service.ts with domain methods.
- Type-safe collection interfaces and schema validation.

Conventions
Client (src/api/client.ts)

- Configure base URL, auth token management.
- Request/response interceptors for errors.
- Env-based configuration with BUN*PUBLIC*\* for client.

Service (src/api/service.ts)

- Singleton pattern for consistent access.
- Generic CRUD factory for collections.
- Domain-specific methods (auth, accounts, assets).
- Account/user tree queries; asset URL resolution.
- Error handling and type conversion utils.

Patterns

- Centralized auth flow: login, logout, user fetch.
- Typed SDK initialization with collection types.

Examples

- See src/api/service.ts usages in pages and components for CRUD.

Do/Don’t
Do

- Route all API usage through service.ts.
- Maintain type coverage for requests/responses.

Don’t

- Use fetch directly in components.

Checklists

- Client configured once; service singleton exported.
- CRUD and domain methods documented and typed.

References

- /home/<USER>/geocontrol/repositorios/agro/irriga-mais/docs/foundations/architecture.md
- /home/<USER>/geocontrol/repositorios/agro/irriga-mais/docs/workflows/git-and-commit-policy.md

Conflicts & Resolutions

- None.
