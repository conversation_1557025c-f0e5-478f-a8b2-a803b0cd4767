# TailwindCSS Guidelines

Overview
Sources:

- /app/.github/instructions/coding.instructions.md (Tailwind section)
- /app/.github/instructions/design.instructions.md

Principles

- Utility-first with DS tokens.
- Readable, minimal className strings.
- Mobile-first and accessible.

Conventions

- Use utilities directly; @apply only for repeated patterns.
- Use responsive/state variants (sm:, hover:, etc.).
- Avoid arbitrary values unless necessary.
- Use bg-black/50 style for opacity, not bg-opacity-[number].

Patterns

- Map component variants to token classes.
- Keep utility sequences consistent across similar components.

Examples
<button class="min-h-[48px] rounded-lg bg-primary-500 px-lg py-md text-white hover:bg-primary-600 active:bg-primary-700 disabled:bg-neutral-300 disabled:text-neutral-500">
...
</button>

Do/Don’t
Do

- Ensure tokens match design.json for color/spacing/typography.
- Keep class order consistent.

Don’t

- Write global overrides unnecessarily.
- Use unsupported bg-opacity-[number].

Checklists

- Token mapping verified.
- Responsive and state variants implemented.
- @apply limited to repeated patterns.

References

- /docs/guidelines/frontend/foundations/design-system.md
- /docs/guidelines/references/design-tokens.md

Conflicts & Resolutions

- None.
