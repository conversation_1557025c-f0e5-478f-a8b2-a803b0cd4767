# Cross-References

Overview
Consolidated pointers across guidelines.

Frontdoor Files

- Coding Conventions: /docs/guidelines/frontend/foundations/coding-conventions.md
- Design System: /docs/guidelines/frontend/foundations/design-system.md
- Tech Stack: /docs/guidelines/frontend/foundations/tech-stack.md
- Architecture: /docs/guidelines/frontend/foundations/architecture.md
- React: /docs/guidelines/frontend/react.md
- Jotai: /docs/guidelines/frontend/state-management-jotai.md
- Tailwind: /docs/guidelines/frontend/tailwindcss.md
- UX/UI: /docs/guidelines/frontend/ux-ui-guidelines.md
- PWA & Performance: /docs/guidelines/frontend/pwa-and-performance.md
- API & Services: /docs/guidelines/frontend/api-and-services.md
- Migrations & Seed: /docs/guidelines/backend/migrations-and-seed.md
- Tasks Workflow: /docs/guidelines/workflows/tasks-file-usage.md
- Git & Commits: /docs/guidelines/workflows/git-and-commit-policy.md
- Design Tokens: /docs/guidelines/references/design-tokens.md

Related Links

- React ↔ <PERSON><PERSON>: /docs/guidelines/frontend/react.md ↔ /docs/guidelines/frontend/state-management-jotai.md
- Design System ↔ Tailwind: /docs/guidelines/frontend/foundations/design-system.md ↔ /docs/guidelines/frontend/tailwindcss.md
- Tasks Workflow ↔ Git Policy: /docs/guidelines/workflows/tasks-file-usage.md ↔ /docs/guidelines/workflows/git-and-commit-policy.md

Conflicts & Resolutions

- None.
