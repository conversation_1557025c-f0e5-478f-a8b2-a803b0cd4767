# AccountSelector Component - Architecture Plan

## Overview

The AccountSelector is a comprehensive, standalone component designed for enhanced account switching workflows. It significantly improves upon the existing UserInfoCard menu implementation with better UX, accessibility, and flexibility for multiple contexts.

## Key Features

- **Enhanced UX**: Search/filter capabilities, detailed account information display
- **Better Accessibility**: Full keyboard navigation, ARIA labels, screen reader support
- **Flexible Context Usage**: Works in dialogs, pages, dropdowns, and standalone modes
- **Comprehensive Error Handling**: Network error recovery, loading states, empty states
- **Advanced Features**: Account details preview, role indicators, owner information

## Component Architecture

```mermaid
graph TD
    A[AccountSelector Component] --> B[Props Interface]
    A --> C[Emits Interface]
    A --> D[Internal State]
    A --> E[Computed Properties]
    A --> F[Methods]
    A --> G[Template Sections]

    B --> B1[mode: 'dialog' | 'page' | 'dropdown']
    B --> B2[showSearch: boolean]
    B --> B3[showAccountDetails: boolean]
    B --> B4[maxHeight: string]
    B --> B5[allowEmpty: boolean]

    C --> C1[@account-selected]
    C --> C2[@account-switching]
    C --> C3[@error]
    C --> C4[@close]

    D --> D1[searchQuery: string]
    D --> D2[isLocalLoading: boolean]
    D --> D3[localError: string]
    D --> D4[selectedIndex: number]

    E --> E1[filteredAccounts]
    E --> E2[hasAccounts]
    E --> E3[isLoading]
    E --> E4[displayError]

    F --> F1[handleAccountSelect]
    F --> F2[handleKeyNavigation]
    F --> F3[filterAccounts]
    F --> F4[handleRetry]
    F --> F5[resetState]

    G --> G1[Search Input]
    G --> G2[Account List]
    G --> G3[Loading States]
    G --> G4[Error States]
    G --> G5[Empty States]
```

## Component Interface Design

### Props Interface

```typescript
interface AccountSelectorProps {
  mode?: 'dialog' | 'page' | 'dropdown';
  showSearch?: boolean;
  showAccountDetails?: boolean;
  maxHeight?: string;
  allowEmpty?: boolean;
  autoFocus?: boolean;
  dense?: boolean;
}
```

### Emits Interface

```typescript
interface AccountSelectorEmits {
  'account-selected': [accountUserId: string, account: AccountUserWithAccount];
  'account-switching': [fromAccountId: string | null, toAccountId: string];
  error: [error: string];
  close: [];
}
```

### Account Display Data

```typescript
interface AccountDisplayData {
  id: string;
  displayName: string;
  ownerName: string;
  ownerEmail: string;
  role: string;
  isActive: boolean;
  hasExpired: boolean;
  startDate?: string;
  endDate?: string;
}
```

## Key Features & Functionality

### Enhanced UX Features

- **Search & Filter**: Real-time search through account names, owner names, and emails
- **Detailed Account Cards**: Rich information display with owner details, roles, and dates
- **Visual Active Indicator**: Clear highlighting of currently active account
- **Responsive Design**: Adapts to different screen sizes and container contexts

### Accessibility Features

- **Full Keyboard Navigation**: Arrow keys, Enter/Space, Escape handling
- **Screen Reader Support**: Comprehensive ARIA labels and announcements
- **Focus Management**: Proper focus trapping and restoration
- **High Contrast Support**: Compatible with accessibility themes

### Error Handling & Edge Cases

- **Network Error Recovery**: Retry mechanisms with exponential backoff
- **Empty State Handling**: Graceful display when no accounts available
- **Loading State Management**: Skeleton loading, progressive disclosure
- **Account Switching Errors**: Rollback to previous account on failure

### Multi-Context Support

- **Dialog Mode**: Modal overlay with backdrop, escape handling
- **Page Mode**: Full-page component for dedicated account management
- **Dropdown Mode**: Compact selector for toolbar/navigation usage

## Component Structure

```mermaid
graph LR
    A[AccountSelector.vue] --> B[Template]
    A --> C[Script Setup]
    A --> D[Styles]

    B --> B1[Search Section]
    B --> B2[Account List Section]
    B --> B3[Loading Section]
    B --> B4[Error Section]
    B --> B5[Empty Section]

    B1 --> B1a[q-input with search icon]
    B1 --> B1b[Clear search button]

    B2 --> B2a[q-virtual-scroll for performance]
    B2 --> B2b[AccountCard components]

    B2b --> B2b1[Account Avatar/Icon]
    B2b --> B2b2[Account Name & Details]
    B2b --> B2b3[Role Badge]
    B2b --> B2b4[Active Indicator]
    B2b --> B2b5[Actions Menu]

    C --> C1[Reactive State]
    C --> C2[Computed Properties]
    C --> C3[Event Handlers]
    C --> C4[Lifecycle Hooks]
    C --> C5[Composables]
```

## State Management Integration

The component integrates seamlessly with the existing account store:

```typescript
// Store Integration Pattern
const accountStore = useAccountStore();

// Reactive dependencies
const {
  list: accounts,
  current: currentAccount,
  isLoading: storeLoading,
  error: storeError,
  setCurrent,
  getAccountDisplayName,
} = accountStore;

// Enhanced error handling
const handleAccountSwitch = async (accountUserId: string) => {
  const previousAccount = currentAccount?.id;

  try {
    emit('account-switching', previousAccount, accountUserId);
    await setCurrent(accountUserId);
    emit('account-selected', accountUserId, accounts.find((a) => a.id === accountUserId)!);
  } catch (error) {
    emit('error', error.message);
    // Rollback logic if needed
  }
};
```

## Template Structure Overview

```vue
<template>
  <div class="account-selector" :class="modeClasses">
    <!-- Search Section (if enabled) -->
    <div v-if="showSearch" class="account-selector__search">
      <q-input
        v-model="searchQuery"
        placeholder="Search accounts..."
        dense
        outlined
        clearable
        @keydown="handleSearchKeydown"
      >
        <template #prepend>
          <q-icon name="search" />
        </template>
      </q-input>
    </div>

    <!-- Account List -->
    <div class="account-selector__list" :style="{ maxHeight }">
      <q-virtual-scroll
        v-if="hasAccounts"
        :items="filteredAccounts"
        separator
        v-slot="{ item, index }"
      >
        <AccountSelectorItem
          :account="item"
          :is-active="item.id === currentAccount?.id"
          :is-selected="selectedIndex === index"
          @click="handleAccountSelect(item)"
          @keydown="handleItemKeydown($event, index)"
        />
      </q-virtual-scroll>

      <!-- Loading State -->
      <div v-else-if="isLoading" class="account-selector__loading">
        <q-skeleton-loader />
      </div>

      <!-- Error State -->
      <div v-else-if="displayError" class="account-selector__error">
        <q-banner type="negative">
          {{ displayError }}
          <template #action>
            <q-btn flat label="Retry" @click="handleRetry" />
          </template>
        </q-banner>
      </div>

      <!-- Empty State -->
      <div v-else class="account-selector__empty">
        <q-empty-state icon="account_circle" message="No accounts available" />
      </div>
    </div>
  </div>
</template>
```

## Accessibility Implementation

```typescript
// Keyboard Navigation
const handleKeyNavigation = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      moveSelection(1);
      break;
    case 'ArrowUp':
      event.preventDefault();
      moveSelection(-1);
      break;
    case 'Enter':
    case ' ':
      event.preventDefault();
      selectCurrentAccount();
      break;
    case 'Escape':
      emit('close');
      break;
  }
};

// ARIA Support
const accountListId = `account-list-${componentId}`;
const selectedAccountId = computed(() =>
  selectedIndex.value >= 0 ? `account-${filteredAccounts.value[selectedIndex.value]?.id}` : null,
);
```

## Performance Optimizations

- **Virtual Scrolling**: Handle large account lists efficiently
- **Debounced Search**: Prevent excessive filtering on rapid typing
- **Memoized Computations**: Cache expensive operations
- **Lazy Loading**: Load account details on demand

## Testing Strategy

```typescript
// Test Coverage Areas
describe('AccountSelector', () => {
  // Basic functionality
  test('displays account list correctly');
  test('filters accounts by search query');
  test('selects account on click');

  // Accessibility
  test('supports keyboard navigation');
  test('has proper ARIA attributes');
  test('announces selection changes');

  // Error handling
  test('displays error states');
  test('handles network failures');
  test('recovers from errors');

  // Edge cases
  test('handles empty account list');
  test('handles single account');
  test('handles account switching failures');
});
```

## Implementation Files

The component will consist of:

1. **`src/components/AccountSelector.vue`** - Main component
2. **`src/components/AccountSelectorItem.vue`** - Individual account item component
3. **`src/types/account-selector.ts`** - TypeScript interfaces and types
4. **`src/composables/useAccountSelector.ts`** - Reusable composition function (optional)

## Integration Examples

### Dialog Usage

```vue
<q-dialog v-model="showAccountSelector">
  <AccountSelector
    mode="dialog"
    show-search
    show-account-details
    @account-selected="onAccountSelected"
    @close="showAccountSelector = false"
  />
</q-dialog>
```

### Page Usage

```vue
<AccountSelector
  mode="page"
  show-search
  show-account-details
  max-height="60vh"
  @account-selected="onAccountSelected"
/>
```

### Dropdown Usage

```vue
<q-btn-dropdown>
  <AccountSelector
    mode="dropdown"
    dense
    @account-selected="onAccountSelected"
  />
</q-btn-dropdown>
```
