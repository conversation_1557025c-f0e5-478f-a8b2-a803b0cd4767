# Account Management Implementation

## Overview

This document describes the implementation of account loading and switching functionality after user authentication.

## Implementation Details

### 1. Account Store (`src/stores/account.ts`)

- **Purpose**: Manages the list of accounts the user has access to and tracks the current selected account
- **Key Features**:
  - Automatically loads accounts when user becomes authenticated
  - Persists current account selection in localStorage
  - Auto-selects single account when user has only one
  - Provides account switching capabilities when multiple accounts exist

### 2. Updated UserInfoCard (`src/components/UserInfoCard.vue`)

- **Enhanced Features**:
  - Shows current account information
  - Displays account switching button when multiple accounts are available
  - Provides dropdown menu for account selection
  - Hides switching functionality when only one account exists

## Flow

1. **Authentication**: User logs in and `authStore.isUserAuthenticated` becomes `true`
2. **Account Loading**: Account store watches authentication state and automatically calls `fetchAccounts()`
3. **Account Selection**:
   - If only 1 account: Automatically set as current, no switching UI shown
   - If multiple accounts: Restore from localStorage or default to first account
4. **Account Switching**: User can click account button to see dropdown and switch accounts
5. **Persistence**: Selected account ID is stored in localStorage for future sessions

## Key Components

### Account Store State

```typescript
interface State {
  list: AccountUserWithAccount[]; // All available accounts
  current: AccountUserWithAccount; // Currently selected account
  isLoading: boolean; // Loading state
  error: string | null; // Error handling
}
```

### Account Store Actions

- `fetchAccounts()`: Loads account_user items with related account data
- `setCurrent(id)`: Sets current account and updates localStorage
- `clearCurrent()`: Clears current account (used on logout)

### Account Store Getters

- `currentAccount`: The account object of current selection
- `canSwitchAccounts`: Boolean indicating if switching UI should show
- `accountDisplayName`: Display name for current account

## Notes

- Account names currently use account IDs since the Account interface doesn't include a `name` field
- The implementation follows the approved architectural design
- Error handling is included for failed account fetching
- Automatic cleanup occurs on user logout
