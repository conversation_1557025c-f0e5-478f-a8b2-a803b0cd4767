# AccountSelector Component - Usage Guide

## Overview

The AccountSelector is a comprehensive, accessible, and feature-rich component for selecting and switching between user accounts. It provides enhanced UX, full keyboard navigation, search capabilities, and flexible integration options.

## Installation

The component is already integrated into the project. Import it as needed:

```vue
<script setup lang="ts">
import AccountSelector from '@/components/AccountSelector.vue';
</script>
```

## Basic Usage

### Dialog Mode (Recommended for account switching)

```vue
<template>
  <div>
    <q-btn @click="showAccountSelector = true"> Switch Account </q-btn>

    <q-dialog v-model="showAccountSelector" persistent>
      <q-card style="min-width: 400px;">
        <AccountSelector
          mode="dialog"
          show-search
          show-account-details
          auto-focus
          @account-selected="onAccountSelected"
          @close="showAccountSelector = false"
        />
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const showAccountSelector = ref(false);

const onAccountSelected = (accountUserId: string, account: any) => {
  console.log('Account selected:', account);
  showAccountSelector.value = false;
};
</script>
```

### Dropdown Mode (For toolbars and compact spaces)

```vue
<template>
  <q-btn-dropdown color="primary" :label="currentAccountLabel" icon="account_circle" auto-close>
    <AccountSelector
      mode="dropdown"
      :show-search="false"
      dense
      @account-selected="onAccountSelected"
    />
  </q-btn-dropdown>
</template>
```

### Page Mode (For dedicated account management)

```vue
<template>
  <div class="account-management-page">
    <AccountSelector
      mode="page"
      show-search
      show-account-details
      max-height="60vh"
      @account-selected="onAccountSelected"
      @account-switching="onAccountSwitching"
      @error="onError"
    />
  </div>
</template>
```

## Props

| Prop                 | Type                               | Default    | Description                       |
| -------------------- | ---------------------------------- | ---------- | --------------------------------- |
| `mode`               | `'dialog' \| 'page' \| 'dropdown'` | `'dialog'` | Display mode of the component     |
| `showSearch`         | `boolean`                          | `true`     | Show search input                 |
| `showAccountDetails` | `boolean`                          | `true`     | Show detailed account information |
| `maxHeight`          | `string`                           | `'400px'`  | Maximum height of the selector    |
| `allowEmpty`         | `boolean`                          | `false`    | Allow no account selection        |
| `autoFocus`          | `boolean`                          | `true`     | Auto-focus search input           |
| `dense`              | `boolean`                          | `false`    | Compact display mode              |

## Events

| Event               | Payload                                                    | Description                               |
| ------------------- | ---------------------------------------------------------- | ----------------------------------------- |
| `account-selected`  | `(accountUserId: string, account: AccountUserWithAccount)` | Fired when an account is selected         |
| `account-switching` | `(fromAccountId: string \| null, toAccountId: string)`     | Fired before account switch               |
| `error`             | `(error: string)`                                          | Fired when an error occurs                |
| `close`             | `()`                                                       | Fired when the component should be closed |

## Features

### Search and Filtering

The component provides real-time search across:

- Account display names
- Owner names
- Owner emails
- User roles

```vue
<AccountSelector show-search @account-selected="onAccountSelected" />
```

### Keyboard Navigation

Full keyboard support includes:

- **Arrow Keys**: Navigate between accounts
- **Enter/Space**: Select highlighted account
- **Escape**: Close the selector
- **Tab**: Move between search and list

### Accessibility

- ARIA labels and roles
- Screen reader announcements
- High contrast support
- Focus management
- Keyboard navigation

### Error Handling

The component handles various error scenarios:

- Network failures during account loading
- Account switching errors
- Expired account access
- Empty account lists

```vue
<AccountSelector @error="handleError" @account-switching="handleSwitching" />

<script setup lang="ts">
const handleError = (error: string) => {
  console.error('Account selector error:', error);
  // Show user notification
};

const handleSwitching = (fromId: string | null, toId: string) => {
  console.log(`Switching from ${fromId} to ${toId}`);
  // Show loading state
};
</script>
```

## Advanced Usage

### Using the Composable

For complex scenarios, use the `useAccountSelector` composable:

```vue
<script setup lang="ts">
import { useAccountSelector } from '@/composables/useAccountSelector';

const {
  // State
  searchQuery,
  selectedIndex,
  localError,

  // Data
  filteredAccounts,
  hasAccounts,
  isLoading,

  // Methods
  switchAccount,
  resetState,
  setInitialSelection,
} = useAccountSelector();

// Custom account switching logic
const handleCustomSwitch = (accountId: string) => {
  const success = switchAccount(accountId);
  if (success) {
    // Handle success
  } else {
    // Handle error
  }
};
</script>
```

### Custom Styling

The component uses CSS custom properties for theming:

```scss
.account-selector {
  --account-selector-primary-color: #your-color;
  --account-selector-border-radius: 12px;
  --account-selector-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

// Override specific elements
.account-selector-item {
  &--active {
    background: linear-gradient(45deg, #your-gradient);
  }
}
```

### Integration with Existing UserInfoCard

The AccountSelector can replace or enhance the existing account switching in UserInfoCard:

```vue
<!-- In UserInfoCard.vue -->
<template>
  <div>
    <!-- Replace the simple menu with AccountSelector -->
    <q-btn v-if="accountStore.canSwitchAccounts" flat dense @click="showAdvancedSelector = true">
      Switch Account (Advanced)
    </q-btn>

    <q-dialog v-model="showAdvancedSelector">
      <AccountSelector
        mode="dialog"
        @account-selected="onAccountSelected"
        @close="showAdvancedSelector = false"
      />
    </q-dialog>
  </div>
</template>
```

## Performance Considerations

### Virtual Scrolling

For large account lists, the component uses virtual scrolling automatically:

```vue
<AccountSelector
  mode="page"
  max-height="400px"
  <!-- Virtual scrolling kicks in automatically -->
/>
```

### Search Debouncing

Search input is debounced by default (300ms) to prevent excessive filtering.

### Memoization

The component memoizes expensive computations for optimal performance.

## Testing

### Unit Tests

```typescript
import { mount } from '@vue/test-utils';
import AccountSelector from '@/components/AccountSelector.vue';

describe('AccountSelector', () => {
  it('displays accounts correctly', () => {
    const wrapper = mount(AccountSelector, {
      props: { mode: 'dialog' },
    });

    expect(wrapper.find('.account-selector').exists()).toBe(true);
  });

  it('filters accounts by search query', async () => {
    const wrapper = mount(AccountSelector, {
      props: { showSearch: true },
    });

    const searchInput = wrapper.find('input');
    await searchInput.setValue('test');

    // Assert filtered results
  });
});
```

### E2E Tests

```typescript
// cypress/integration/account-selector.spec.ts
describe('Account Selector', () => {
  it('should allow account switching', () => {
    cy.visit('/demo/account-selector');
    cy.get('[data-cy=open-dialog]').click();
    cy.get('[data-cy=account-item]').first().click();
    cy.get('[data-cy=current-account]').should('contain', 'New Account');
  });
});
```

## Demo

Visit `/demo/account-selector` in your application to see the component in action with all modes and features demonstrated.

## Troubleshooting

### Common Issues

1. **Accounts not loading**: Check network connectivity and authentication
2. **Search not working**: Ensure `showSearch` prop is `true`
3. **Keyboard navigation issues**: Check focus management and ARIA attributes
4. **Styling issues**: Verify CSS custom properties and Quasar theme

### Debug Mode

Enable debug logging:

```typescript
// In development
const accountSelector = useAccountSelector();
console.log('Filtered accounts:', accountSelector.filteredAccounts.value);
```

## Browser Support

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## Accessibility Compliance

- WCAG 2.1 AA compliant
- Section 508 compliant
- Keyboard accessible
- Screen reader compatible
