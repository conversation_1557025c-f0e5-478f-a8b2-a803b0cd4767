# Device Message Request System

## Overview

The Device Message Request system provides a robust queue-based mechanism for sending protobuf messages to LIC (Localized Irrigation Controller) devices via MQTT. This system manages the complete lifecycle of outgoing messages, from creation to delivery confirmation, with support for retry mechanisms, message correlation, and sequential operations.

## Architecture

### Core Components

1. **Message Queue Table**: `device_message_request` - Central storage for all outgoing messages
2. **Protobuf Integration**: Support for `IncomingPacket` message structure with all payload types
3. **MQTT Transport Layer**: Delivery mechanism using MQTT protocol
4. **Queue Processor**: Service that monitors and processes pending messages
5. **Correlation System**: Message tracking and grouping capabilities

### Message Flow

```
User/System → device_message_request → Queue Processor → MQTT → LIC Device
                     ↓
            [Status Tracking & Retry Logic]
                     ↓
              [Acknowledgment Handling]
```

## Table Structure

### Primary Table: device_message_request

The `device_message_request` table serves as the central queue for all outgoing device messages. It follows the existing database patterns established in the irrigation management system.

## Key Field Explanations

### packet_id Field

**Purpose**: The `packet_id` field corresponds directly to the `id` field (uint64) in the protobuf `IncomingPacket` message structure.

**Key Characteristics**:

- **Unique per device**: Must be unique for each target device to avoid confusion in device responses
- **Protocol-level correlation**: Used by devices to acknowledge or respond to specific messages
- **Sequential numbering**: Commonly implemented as 1, 2, 3... but not strictly required
- **Response matching**: Enables request-response correlation at the protocol level

**Example Usage**:

```sql
-- Device LIC-001 gets packet_id 1, 2, 3...
INSERT INTO device_message_request (device, packet_id, ...) VALUES ('lic-001', 1, ...);
INSERT INTO device_message_request (device, packet_id, ...) VALUES ('lic-001', 2, ...);

-- Device LIC-002 gets its own sequence: 1, 2, 3...
INSERT INTO device_message_request (device, packet_id, ...) VALUES ('lic-002', 1, ...);
```

### Payload Storage Strategy

The system uses a dual-storage approach for message payloads:

1. **payload_data (jsonb)**: Human-readable JSON representation

   - Used for debugging, logging, and administrative interfaces
   - Allows easy querying and inspection of message content
   - Facilitates troubleshooting and audit trails

2. **payload_bytes (bytea)**: Binary protobuf data
   - Ready-to-transmit binary payload without recompilation
   - Exact bytes that will be sent to the device
   - Performance-optimized for queue processing
   - Ensures integrity - what's stored is what gets transmitted

### Message Relationships

#### parent_message_id: Sequential Operations

Creates **message chains** for multi-step operations that must execute in sequence.

**Use Cases**:

- **Sequential operations** that must complete in order
- **Conditional messaging** (send B only if A succeeds)
- **Message dependency tracking** for troubleshooting
- **Cancellation cascading** (cancel children if parent fails)

**Example: Configuration Update Chain**

```sql
-- Step 1: Send configuration
INSERT INTO device_message_request (id, device, payload_type, packet_id, ...)
VALUES ('msg-1', 'device-123', 'config', 1, ...);

-- Step 2: Request info to verify config applied (child of msg-1)
INSERT INTO device_message_request (id, device, payload_type, packet_id, parent_message_id, ...)
VALUES ('msg-2', 'device-123', 'request_info', 2, 'msg-1', ...);

-- Step 3: Send scheduling after config confirmed (child of msg-2)
INSERT INTO device_message_request (id, device, payload_type, packet_id, parent_message_id, ...)
VALUES ('msg-3', 'device-123', 'scheduling', 3, 'msg-2', ...);
```

#### correlation_id: Cross-Device Operations

Groups **related messages across different devices** or **logical operations**.

**Use Cases**:

- **Bulk operations** across multiple devices
- **Session tracking** for irrigation cycles
- **System-wide updates** (mesh config, firmware)
- **Operational grouping** for monitoring/reporting
- **Cross-device transaction tracking**

**Example: Project-Wide Irrigation Start**

```sql
-- Generate correlation ID for this irrigation session
SET @correlation := gen_random_uuid();

-- Send control messages to multiple devices for same irrigation session
INSERT INTO device_message_request (device, payload_type, correlation_id, ...) VALUES
('pump-controller', 'control', @correlation, ...), -- Start pump
('valve-controller-1', 'control', @correlation, ...), -- Open valve sector 1
('valve-controller-2', 'control', @correlation, ...), -- Open valve sector 2
('lic-device', 'automation', @correlation, ...); -- Start automation sequence
```

### Delivery Timing

The `scheduled_at` field controls when messages are processed:

- **Immediate delivery**: `scheduled_at = now()`
- **Scheduled delivery**: `scheduled_at = future timestamp`
- **Batch processing**: Handled by queue processor logic
- **Delivery prioritization**: Combined with `priority` field (1=highest, 10=lowest)

### Implementation Details Storage

The `metadata` jsonb column stores transport-specific details that shouldn't be in dedicated columns:

```json
{
  "mqtt_topic": "device/LIC123/incoming",
  "mqtt_qos": 1,
  "mqtt_retain": false,
  "transport_protocol": "mqtt",
  "delivery_attempt_timestamps": [
    "2024-01-01T10:00:00Z",
    "2024-01-01T10:01:00Z"
  ],
  "custom_routing": {
    "mesh_gateway": "LIC-MAIN-001"
  }
}
```

## Payload Types

The system supports all `IncomingPacket` protobuf payload types:

| Payload Type      | Purpose                      | Example Use Case                                        |
| ----------------- | ---------------------------- | ------------------------------------------------------- |
| `config`          | System configuration updates | WiFi settings, mesh network config, backwash parameters |
| `devices`         | Device management operations | Device registration, mesh network setup                 |
| `scheduling`      | Irrigation scheduling        | Daily irrigation plans, schedule updates                |
| `dev_scheduling`  | Device-specific scheduling   | Individual device timing configurations                 |
| `automation`      | Automation rules             | Conditional irrigation logic, sensor-based triggers     |
| `control`         | Real-time control commands   | Manual valve control, pump operations                   |
| `command`         | System commands              | Pause/resume operations, emergency stops                |
| `request_info`    | Information requests         | Status queries, configuration verification              |
| `firmware_update` | Firmware update operations   | OTA update management                                   |

## Message Lifecycle

### Status States

| Status         | Description                                      | Next States                          |
| -------------- | ------------------------------------------------ | ------------------------------------ |
| `pending`      | Message queued, waiting to be processed          | `processing`, `expired`, `cancelled` |
| `processing`   | Message currently being sent to device           | `sent`, `failed`                     |
| `sent`         | Message successfully transmitted via MQTT        | `acknowledged`, `expired`            |
| `acknowledged` | Device confirmed receipt (for reliable delivery) | _(final state)_                      |
| `failed`       | Message failed after max retry attempts          | _(final state)_                      |
| `expired`      | Message expired before delivery                  | _(final state)_                      |
| `cancelled`    | Message manually cancelled                       | _(final state)_                      |

### Retry Logic

- **Automatic retries**: Failed messages are retried up to `max_attempts`
- **Retry delays**: Configurable delay between attempts via `retry_delay_seconds`
- **Exponential backoff**: Can be implemented in queue processor
- **Error tracking**: `last_error` field captures failure reasons

## Database Schema

```sql
-- ============================================================================
-- Table: device_message_request
-- ============================================================================
-- Purpose: Queue system for outgoing messages to LIC devices via MQTT
--
-- This table manages the lifecycle of messages sent to LIC (Localized
-- Irrigation Controller) devices. It supports message tracking, correlation,
-- retry mechanisms, and stores both JSON and binary protobuf payloads.
-- ============================================================================

CREATE TABLE IF NOT EXISTS public.device_message_request
(
    -- ========================================================================
    -- Primary Identification
    -- ========================================================================
    id uuid NOT NULL DEFAULT gen_random_uuid(),

    -- ========================================================================
    -- Device Relationships
    -- ========================================================================
    device uuid NOT NULL,                    -- Target device (must be LIC model)
    property_device uuid,                    -- Optional: specific property_device association for mesh-aware routing

    -- ========================================================================
    -- Message Content (IncomingPacket structure)
    -- ========================================================================
    packet_id bigint NOT NULL,               -- Unique packet identifier for protobuf IncomingPacket.id field (must be unique per device)
    payload_type character varying(50) NOT NULL,  -- Type of payload: config, devices, scheduling, dev_scheduling, automation, control, command, request_info, firmware_update
    payload_data jsonb NOT NULL,             -- Serialized protobuf payload data as JSON (human-readable, for debugging)
    payload_bytes bytea,            -- Compiled protobuf binary data ready for transmission
    message_hash character varying(64),      -- SHA-256 hash for deduplication (optional)

    -- ========================================================================
    -- Message Relationships & Correlation
    -- ========================================================================
    parent_message_id uuid,                  -- Reference to parent message for sequential operations (creates message chains)
    correlation_id uuid,                     -- Correlation identifier for grouping related messages across devices/operations

    -- ========================================================================
    -- Delivery Control
    -- ========================================================================
    status character varying(20) NOT NULL DEFAULT 'pending',     -- Message lifecycle status
    priority smallint NOT NULL DEFAULT 5,                        -- Priority (1=highest, 10=lowest)

    -- ========================================================================
    -- Scheduling & Timing
    -- ========================================================================
    scheduled_at timestamp with time zone NOT NULL DEFAULT now(),     -- When to process this message (now() = immediate, future = scheduled)
    expires_at timestamp with time zone,                              -- Message expiration (NULL = never expires)
    sent_at timestamp with time zone,                                 -- When message was successfully sent
    acknowledged_at timestamp with time zone,                         -- When device acknowledged receipt (if applicable)

    -- ========================================================================
    -- Retry & Error Handling
    -- ========================================================================
    attempts smallint NOT NULL DEFAULT 0,         -- Number of delivery attempts made
    max_attempts smallint NOT NULL DEFAULT 3,     -- Maximum retry attempts before marking as failed
    retry_delay_seconds integer DEFAULT 30,       -- Seconds to wait between retry attempts
    last_error text,                               -- Last error message encountered

    -- ========================================================================
    -- Standard Audit Fields (following existing table patterns)
    -- ========================================================================
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,                       -- User who created this message request
    date_updated timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_updated uuid,                       -- User who last updated this request
    metadata jsonb,                          -- Transport details (mqtt_topic, qos, etc.) and flexible metadata storage
    notes text,                              -- Human-readable notes

    -- ========================================================================
    -- Constraints
    -- ========================================================================
    CONSTRAINT device_message_request_pkey PRIMARY KEY (id),

    -- Foreign Key Constraints
    CONSTRAINT device_message_request_device_foreign
        FOREIGN KEY (device) REFERENCES public.device (id)
        ON UPDATE RESTRICT ON DELETE RESTRICT,

    CONSTRAINT device_message_request_property_device_foreign
        FOREIGN KEY (property_device) REFERENCES public.property_device (id)
        ON UPDATE RESTRICT ON DELETE RESTRICT,

    CONSTRAINT device_message_request_parent_foreign
        FOREIGN KEY (parent_message_id) REFERENCES public.device_message_request (id)
        ON UPDATE RESTRICT ON DELETE SET NULL,

    CONSTRAINT device_message_request_user_created_foreign
        FOREIGN KEY (user_created) REFERENCES public.directus_users (id)
        ON UPDATE RESTRICT ON DELETE RESTRICT,

    CONSTRAINT device_message_request_user_updated_foreign
        FOREIGN KEY (user_updated) REFERENCES public.directus_users (id)
        ON UPDATE RESTRICT ON DELETE RESTRICT,

    -- Business Logic Constraints
    CONSTRAINT device_message_request_status_check
        CHECK (status IN ('pending', 'processing', 'sent', 'acknowledged', 'failed', 'expired', 'cancelled')),

    CONSTRAINT device_message_request_priority_check
        CHECK (priority >= 1 AND priority <= 10),

    CONSTRAINT device_message_request_payload_type_check
        CHECK (payload_type IN ('config', 'devices', 'scheduling', 'dev_scheduling', 'automation', 'control', 'command', 'request_info', 'firmware_update')),

    CONSTRAINT device_message_request_attempts_check
        CHECK (attempts >= 0 AND attempts <= max_attempts),

    CONSTRAINT device_message_request_max_attempts_check
        CHECK (max_attempts >= 1 AND max_attempts <= 100),

    CONSTRAINT device_message_request_retry_delay_check
        CHECK (retry_delay_seconds >= 0),

    -- Timing Logic Constraints
    CONSTRAINT device_message_request_timing_check
        CHECK (expires_at IS NULL OR expires_at > scheduled_at),

    CONSTRAINT device_message_request_sent_after_scheduled_check
        CHECK (sent_at IS NULL OR sent_at >= scheduled_at),

    CONSTRAINT device_message_request_ack_after_sent_check
        CHECK (acknowledged_at IS NULL OR (sent_at IS NOT NULL AND acknowledged_at >= sent_at)),

    -- packet_id must be unique per device (to avoid confusion in device responses)
    CONSTRAINT device_message_request_device_packet_id_unique
        UNIQUE (device, packet_id)
);

-- ============================================================================
-- Indexes for Performance
-- ============================================================================

-- Primary queue processing index (most important)
CREATE INDEX IF NOT EXISTS device_message_request_queue_processing_idx
    ON public.device_message_request USING btree
    (status, scheduled_at, priority)
    WHERE status IN ('pending', 'processing');

-- Device-specific message lookup
CREATE INDEX IF NOT EXISTS device_message_request_device_status_idx
    ON public.device_message_request USING btree
    (device, status, date_created DESC);

-- Property device association lookup
CREATE INDEX IF NOT EXISTS device_message_request_property_device_idx
    ON public.device_message_request USING btree
    (property_device)
    WHERE property_device IS NOT NULL;

-- Scheduled messages lookup
CREATE INDEX IF NOT EXISTS device_message_request_scheduled_idx
    ON public.device_message_request USING btree
    (scheduled_at)
    WHERE status = 'pending';

-- Deduplication index (sparse - only when hash exists)
CREATE UNIQUE INDEX IF NOT EXISTS device_message_request_dedup_idx
    ON public.device_message_request USING btree
    (message_hash)
    WHERE message_hash IS NOT NULL;

-- Message correlation and chains
CREATE INDEX IF NOT EXISTS device_message_request_correlation_idx
    ON public.device_message_request USING btree
    (correlation_id)
    WHERE correlation_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS device_message_request_parent_idx
    ON public.device_message_request USING btree
    (parent_message_id)
    WHERE parent_message_id IS NOT NULL;

-- Failed/retry messages lookup
CREATE INDEX IF NOT EXISTS device_message_request_retry_idx
    ON public.device_message_request USING btree
    (status, attempts, scheduled_at)
    WHERE status = 'failed' AND attempts < max_attempts;

-- Cleanup/archival index
CREATE INDEX IF NOT EXISTS device_message_request_cleanup_idx
    ON public.device_message_request USING btree
    (status, date_created)
    WHERE status IN ('sent', 'acknowledged', 'failed', 'expired', 'cancelled');

-- ============================================================================
-- Triggers
-- ============================================================================

-- Standard timestamp update trigger (following existing pattern)
CREATE OR REPLACE TRIGGER set_device_message_request_date_updated
    BEFORE UPDATE
    ON public.device_message_request
    FOR EACH ROW
    EXECUTE FUNCTION public.update_timestamp_column();
```

## Usage Examples

### Basic Message Sending

```sql
-- Send immediate configuration update
INSERT INTO device_message_request (
    device,
    packet_id,
    payload_type,
    payload_data,
    user_created
) VALUES (
    'device-uuid-123',
    1,
    'config',
    '{"backwash_cycle": 24, "backwash_duration": 300}'::jsonb,
    'user-uuid-456'
);
```

### Scheduled Message

```sql
-- Schedule message for future delivery
INSERT INTO device_message_request (
    device,
    packet_id,
    payload_type,
    payload_data,
    scheduled_at,
    expires_at,
    user_created
) VALUES (
    'device-uuid-123',
    2,
    'scheduling',
    '{"irrigation_plan": "daily-morning"}'::jsonb,
    '2024-01-01 06:00:00+00',
    '2024-01-01 23:59:59+00',
    'user-uuid-456'
);
```

### Message Chain

```sql
-- Create message chain for complex operation
INSERT INTO device_message_request (id, device, packet_id, payload_type, ...)
VALUES ('msg-1', 'device-123', 1, 'config', ...);

INSERT INTO device_message_request (device, packet_id, payload_type, parent_message_id, ...)
VALUES ('device-123', 2, 'request_info', 'msg-1', ...);
```

### Bulk Operation with Correlation

```sql
-- Bulk update with correlation tracking
WITH correlation AS (SELECT gen_random_uuid() as id)
INSERT INTO device_message_request (device, packet_id, payload_type, correlation_id, ...)
SELECT d.id, row_number() OVER (ORDER BY d.identifier), 'config', correlation.id, ...
FROM device d, correlation
WHERE d.model = 'LIC'
AND d.id IN (SELECT device FROM property_device WHERE property = 'property-123');
```

## Queue Processing

The queue processor should:

1. **Poll for pending messages**: Query by status and scheduled_at
2. **Process by priority**: Higher priority (lower number) first
3. **Handle message chains**: Process parent before children
4. **Implement retry logic**: Respect retry delays and max attempts
5. **Update status**: Track processing states and timestamps
6. **Handle acknowledgments**: Update acknowledged_at when devices respond

### Recommended Query for Processing

```sql
-- Get next messages to process
SELECT * FROM device_message_request
WHERE status = 'pending'
  AND scheduled_at <= now()
  AND (parent_message_id IS NULL OR
       parent_message_id IN (
         SELECT id FROM device_message_request WHERE status IN ('sent', 'acknowledged')
       ))
ORDER BY priority ASC, scheduled_at ASC, date_created ASC
LIMIT 100;
```

## Monitoring and Maintenance

### Key Metrics to Monitor

- **Queue depth**: Number of pending messages
- **Processing rate**: Messages per minute/hour
- **Failure rate**: Percentage of messages failing
- **Retry frequency**: Messages requiring multiple attempts
- **Delivery latency**: Time from creation to delivery

### Cleanup Recommendations

- **Archive old messages**: Move completed messages to archive after retention period
- **Purge expired messages**: Remove expired messages that were never sent
- **Clean up orphaned chains**: Handle incomplete message chains
- **Monitor correlation groups**: Track completion of bulk operations

## Integration Points

### With MQTT Integration Service

The `mqtt-integration` package should:

- Monitor the `device_message_request` table
- Process messages according to priority and schedule
- Update status fields as messages are processed
- Store transport details in metadata field

### With Frontend Applications

Frontend can:

- Create message requests through API
- Monitor message status and progress
- Display correlation groups for bulk operations
- Provide retry/cancel functionality for failed messages

### With Device Management

Integration with existing device/property hierarchy:

- Validate target devices exist and are active
- Support mesh-network routing via property_device
- Enforce device model constraints (LIC only)
- Leverage existing audit and user tracking patterns

## Security Considerations

- **Input validation**: Validate payload_data structure before storage
- **Size limits**: Implement reasonable limits on payload_bytes size
- **Rate limiting**: Prevent message queue flooding per user/device
- **Access control**: Ensure users can only send messages to their devices
- **Audit logging**: Track all message creation and status changes

## Future Enhancements

- **Message templates**: Pre-defined message templates for common operations
- **Bulk operations UI**: Frontend interface for correlation-based bulk operations
- **Message scheduling UI**: Calendar-based message scheduling interface
- **Advanced retry policies**: Exponential backoff, dead letter queues
- **Delivery confirmations**: Enhanced acknowledgment tracking
- **Performance analytics**: Message delivery performance dashboards
