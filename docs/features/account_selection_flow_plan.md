# Account Selection Flow Implementation Plan

This document outlines the plan to implement an account selection flow after user authentication.

## Phase 1: Information Gathering & Clarification (Completed)

- Reviewed `src/stores/auth.ts`
- Reviewed `src/stores/account.ts`
- Reviewed `src/router/index.ts` and `src/router/routes.ts`
- Gathered details for "Account Selection Page" and "No Accounts Page".

## Phase 2: Planning & Implementation Steps

**1. Create New Vue Pages:**

- **Account Selection Page:**

  - **File:** `src/pages/SelectAccountPage.vue`
  - **Purpose:** This page will use the existing `AccountSelector.vue` component to allow users to select an account if multiple are available and none are currently active.
  - **Content Sketch:**

    ```vue
    <template>
      <q-page class="flex flex-center q-pa-md">
        <div class="q-pa-md" style="width: 100%; max-width: 500px;">
          <h4 class="q-mb-lg text-center">Select an Account</h4>
          <AccountSelector mode="page" @account-selected="onAccountSelected" @close="handleClose" />
        </div>
      </q-page>
    </template>

    <script setup lang="ts">
    import AccountSelector from 'components/AccountSelector.vue';
    import { useRouter } from 'vue-router';
    import { useAccountStore } from 'stores/account';

    const router = useRouter();
    const accountStore = useAccountStore();

    const onAccountSelected = () => {
      router.push({ name: 'home' });
    };

    const handleClose = () => {
      if (accountStore.current) {
        router.push({ name: 'home' });
      }
      // If no account is current, user should select one.
      // Consider behavior if they attempt to "cancel".
    };
    </script>
    ```

- **No Accounts Page:**

  - **File:** `src/pages/NoAccountPage.vue`
  - **Purpose:** This page will inform the user they have no accounts and provide a placeholder button to "Create Account".
  - **Content Sketch:**

    ```vue
    <template>
      <q-page class="flex flex-center q-pa-md text-center">
        <div>
          <q-icon name="warning" size="xl" color="warning" class="q-mb-md" />
          <h5 class="q-mb-sm">No Accounts Found</h5>
          <p class="text-body1 text-grey-8 q-mb-lg">
            You are authenticated, but it seems you don't have access to any accounts yet.
          </p>
          <q-btn
            label="Create Account (Placeholder)"
            color="primary"
            @click="handleCreateAccount"
            unelevated
          />
          <q-btn flat label="Logout" color="grey" @click="handleLogout" class="q-ml-sm" />
        </div>
      </q-page>
    </template>

    <script setup lang="ts">
    import { useAuthStore } from 'stores/auth';
    import { useRouter } from 'vue-router';

    const authStore = useAuthStore();
    const router = useRouter();

    const handleCreateAccount = () => {
      alert('Account creation functionality is not yet implemented.');
    };

    const handleLogout = async () => {
      await authStore.logout();
      router.push({ name: 'login' });
    };
    </script>
    ```

**2. Update Router (`src/router/routes.ts`):**

- Add new routes for the "Account Selection Page" and "No Accounts Page".
  - **Account Selection Route:**
    - `path: '/select-account'`
    - `name: 'selectAccount'`
    - `component: () => import('pages/SelectAccountPage.vue')`
    - `meta: { requiresAuth: true, hideLayoutDrawer: true }` (suggested)
  - **No Account Route:**
    - `path: '/no-account'`
    - `name: 'noAccount'`
    - `component: () => import('pages/NoAccountPage.vue')`
    - `meta: { requiresAuth: true, hideLayoutDrawer: true }` (suggested)

**3. Implement Redirection Logic (Navigation Guard):**

- This logic will reside in a navigation guard, preferably in `src/boot/auth.ts`.
- **Guard Logic:**
  1.  Check if the user is authenticated (`authStore.isUserAuthenticated`).
  2.  If trying to access a protected route (`to.meta.requiresAuth`):
      - Ensure `authStore.initializeAuth()` has been attempted if a token exists but user state isn't loaded.
      - If not authenticated, redirect to `/login`.
      - If authenticated:
        - Ensure account data has been fetched or fetch attempt has been made (check `accountStore.isLoading` and potentially `accountStore.list.length` or a dedicated "loaded" flag if added to store).
        - If `!accountStore.current` (no account selected/active):
          - If `accountStore.list.length === 0` (no accounts available after fetch): Redirect to `/no-account` (unless already there).
          - If `accountStore.list.length > 0` (accounts available but none selected): Redirect to `/select-account` (unless already there).
        - If `accountStore.current` is set (account selected/active), allow navigation.
  3.  If authenticated and trying to access `/login`, redirect to `/home`.
  4.  Otherwise, allow navigation.

**4. Ensure `fetchAccounts` is Called Appropriately:**

- The `watch` in `src/stores/account.ts` (line 98) calls `fetchAccounts` when `authStore.isUserAuthenticated` becomes `true`. This handles the initial fetch.
- The `fetchAccounts` function in `src/stores/account.ts` (line 51) already contains logic to:
  - Auto-set `current` account if `list.value.length === 1`.
  - Attempt to restore `current` account from `localStorage` if `list.value.length > 1`.
  - This auto-selection mechanism is key to bypassing the `/select-account` page when only one account exists.

**5. (Optional) `MainLayout.vue` Adjustments:**

- If `hideLayoutDrawer: true` meta field is used for the new routes, `MainLayout.vue` can be updated to check `this.$route.meta.hideLayoutDrawer` and conditionally render its drawer/sidebar for a cleaner UI on these specific pages.

## Mermaid Diagram of the Flow

```mermaid
graph TD
    A[User Action: Login] --> B{Auth Attempt};
    B -- Success --> C{authStore.isUserAuthenticated = true};
    C --> D{accountStore Watcher: Triggers fetchAccounts};
    D --> E{fetchAccounts};
    E -- Fetches Accounts --> F{Account Data Loaded};
    F --> G{accountStore.list Populated};
    G --> H{Auto-select if 1 account OR Restore from localStorage};
    H --> I{accountStore.current Updated?};

    J[Router Navigation Guard: beforeEach];
    J --> K{Is Authenticated?};
    K -- No --> L[Redirect to /login];
    K -- Yes --> M{Requires Auth?};
    M -- No --> N[Allow Navigation];
    M -- Yes --> O{accountStore.current Populated?};
    O -- Yes --> N;
    O -- No --> P{accountStore.list.length > 0? (after fetch attempt)};
    P -- No (No accounts at all) --> Q[Redirect to /no-account];
    P -- Yes (Accounts exist, but none selected) --> R[Redirect to /select-account];

    S[User on /select-account Page];
    S --> T{AccountSelector Component};
    T -- Account Selected --> U{accountStore.setCurrent()};
    U --> V[Navigate to Home/Target];

    W[User on /no-account Page];
    W --> X[Display "No Accounts" Message + Create Button];

    subgraph Store Logic
        C
        D
        E
        F
        G
        H
        I
        U
    end

    subgraph Router Logic
        J
        K
        L
        M
        N
        O
        P
        Q
        R
    end

    subgraph UI Pages
        S
        T
        V
        W
        X
    end
```

This plan provides a comprehensive approach to implementing the desired account selection workflow.
