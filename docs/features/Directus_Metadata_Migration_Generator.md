# Directus Metadata Migration Generator

## Description

I am working on some projects that involve Directus v11 Headless CMS, and I need to manage database migrations effectively.
Currently, I am using Knex.js for database migrations, and I want to automate the generation of migration files that reflect changes in the Directus system tables.
DDL commands like `CREATE TABLE`, `ALTER TABLE`, and `DROP TABLE` are already handled by the knex migrations, the only thing left is to handle the data changes in the system tables.
The Directus system tables include `directus_collections`, `directus_fields`, `directus_permissions`, `directus_policies`, `directus_access`, `directus_roles`, `directus_relations`, `directus_translations`, `directus_flows`, `directus_operations`, and others that store metadata about collections, fields, relations, permissions, etc.

### Workflow

The Directus Metadata configuration workflow is the following:

- We start directus docker container.
- A migration is applied (if needed) containing:
  - the creation of a view called `directus_data_config_json` that reflects the current state of the system tables as JSONB.
  - a table called `directus_migration.directus_config` that stores the current state of the system tables as JSONB. This table is populated with the current state of the system tables using the view through a trigger that runs after each insert or delete of directus_migrations tables. Thus the table is always up-to-date with the current state of the system tables for the current database version.
  - a trigger that will perform the necessary actions to keep the JSONB table in sync with the system tables.
- We access the Directus Admin App.
- We modify collections, fields, permissions, roles, etc. through the Admin App.
- When we are done with the modifications, I want to generate a new migration file that reflects the changes made in the system tables. This is the main purpose of this task and is explained in detail below.

## The Task

When we are done with the modifications, I want to generate a new migration file that reflects the changes made in the system tables. This can be done by comparing the current state of the JSONB (`directus_data_config_json` view) table with latest state of the system tables stored in `directus_migration.directus_config` table. This can be done by comparing the JSONB data in the view, which reflects the current state of the system tables, and the JSONB data in the most recent record of `directus_migration.directus_config` table, which contains the last known state of the system tables, generating the necessary DDL commands to apply the changes using knex migration. Thus, we will have a migration file that can be applied to the database to reflect the changes made in the Directus Admin App.

To achieve this, we can create a Bun Typescript script that will:

1. Connect to the Directus database using Knex.js.
2. Fetch the current state of the `directus_data_config_json` view.
3. Fetch the last known state of the `directus_migration.directus_config` table.
4. Compare the two states to identify changes.
5. Generate the necessary DDL commands based on the differences.

### Script Output

The output of the script will be a Knex migration file content that contains the necessary SQL commands to apply the changes made in the Directus Admin App. The migration file will include:

- Creation of new collections, fields, permissions, roles, etc.
- Alteration of existing collections, fields, permissions, roles, etc.
- Deletion of collections, fields, permissions, roles, etc. that were removed in the Directus Admin App.
  When generating the migration content, the script has access to the current current and previous state of the system tables, so it can generate the necessary SQL commands to apply the changes and revert them if needed.
  The migration content will be structured as follows:

```javascript
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Here we will generate the necessary SQL commands based on the differences.
    // For example:
    // Add new_collection - It didn't exist in the previous state but exists in the current state.
    await tx("directus_collections").insert({
      collection: "new_collection",
      icon: "new_icon",
      note: "This is a new collection",
      hidden: false,
      singleton: false,
      translations: null,
      archive_field: null,
      archive_app_filter: true,
      archive_value: null,
      unarchive_value: null,
      sort_field: null,
      accountability: "all",
      color: "#FFFFFF",
      item_duplication_fields: null,
      sort: 0,
      group: null,
      collapse: "open",
      preview_url: null,
      versioning: false,
    });
    // Update existing_collection - It existed in the previous state and exists in the current state and has been modified.
    await tx("directus_collections")
      .update({
        note: "Updated collection note",
      })
      .where("collection", "existing_collection");
    // Remove old_collection - It existed in the previous state but not in the current state.
    await tx("directus_collections")
      .where("collection", "old_collection")
      .del();
    // Add more SQL commands as needed based on the differences.
  });
}

/**
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Here we will generate the necessary SQL commands to revert the changes made in the up function.
    // Undo old_collection deletion - The contents of the insert comes from the previous state of the system tables.
    await tx("directus_collections").insert({
      collection: "old_collection",
      icon: "old_icon",
      note: "This is an old collection",
      hidden: false,
      singleton: false,
      translations: null,
      archive_field: null,
      archive_app_filter: true,
      archive_value: null,
      unarchive_value: null,
      sort_field: null,
      accountability: "all",
      color: "#FFFFFF",
      item_duplication_fields: null,
      sort: 0,
      group: null,
      collapse: "open",
      preview_url: null,
      versioning: false,
    });
    // Undo existing_collection update - The contents of the update comes from the previous state of the system tables.
    await tx("directus_collections")
      .update({
        note: "Previous collection note",
      })
      .where("collection", "existing_collection");
    // Undo new_collection addition - The contents of the insert comes from the previous state of the system tables.
    await tx("directus_collections")
      .where("collection", "new_collection")
      .del();
    // Add more SQL commands as needed to revert the changes made in the up function.
  });
}
```

### Implementation:

- We need a function that receives a Knex instance and generate a intermediate diff object that contains the differences between the current state of the system tables and the previous state stored in the `directus_migration.directus_config` table.
- We need a function that receives the diff object and produces a string with the necessary knex migration javascript code.
- We need a script that will read environment variables to connect to the Directus database using knex, call the previous functions and write the generated migration code to a file, if specified via arguments, or to the console.

### Nuances

Most directus system tables have uuid id fields (PK), but, saving some exceptions, the id fields cant be used to uniquely identify a record because they are not the same across different database instances. Thus, we need to rely on other fields to identify records when generating migrations.
This is a table of the tables with its description, the fields that can be used to uniquely identify records in each table and the foreign keys that reference other tables:

| Table                 | Description                                    | Unique Identifier Fields    | References (Foreign Keys)                         |
| --------------------- | ---------------------------------------------- | --------------------------- | ------------------------------------------------- |
| directus_access       | Associates a policy to a user or to a role     | policy, role, user          | directus_roles, directus_users, directus_policies |
| directus_collections  | Stores collection (table) definitions          | collection                  |                                                   |
| directus_fields       | Stores field definitions for collections       | collection, field           |                                                   |
| directus_flows        | Stores automation flows                        | id                          | directus_operations                               |
| directus_operations   | Stores operations within flows                 | id                          | directus_operations, directus_flows               |
| directus_permissions  | Stores permissions for roles and collections   | collection, action, policy  | directus_policies                                 |
| directus_policies     | Stores policies for collections and roles      | id                          |                                                   |
| directus_relations    | Stores relationships between collections       | many_collection, many_field |                                                   |
| directus_roles        | Stores role definitions                        | id                          | directus_roles                                    |
| directus_translations | Stores translations for collections and fields | language, key               |                                                   |

Also it is necessary a way to identify if a record was added, modified or deleted in the Directus Admin App. This can be done by comparing the current state of the system tables with the previous state stored in the `directus_migration.directus_config` table. The script will need to handle the following cases:

- **Added Records**: If a record exists in the current state but not in the previous state, it is considered added.
- **Modified Records**: If a record exists in both states but has different values, it is considered modified: We need a algorithm to compare the fields of the record in both states and generate the necessary SQL commands to update the record.
- **Deleted Records**: If a record exists in the previous state but not in the current state, it is considered deleted.

## Code

### Knex Migration of `directus_migration.directus_config` Table, `directus_data_config_json` View and Trigger

```javascript
// file: migrations/20250530A-directus-config-management.js
/**
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.from("directus_settings").then(async (r) => {
      if (!r || r.length === 0) {
        await tx
          .insert({
            project_name: "Irrigação Localizada",
          })
          .into("directus_settings");
      }
    });

    await tx.schema.createView("directus_data_config_json", (builder) => {
      builder.columns(["config"]);
      builder.as(
        tx.select({
          config: tx.raw(`json_build_object(
	'collections', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_collections c), '[]'::json),
	'fields', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_fields c), '[]'::json),
	'permissions', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_permissions c), '[]'::json),
	'policies', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_policies c), '[]'::json),
	'access', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_access c), '[]'::json),
	'roles', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_roles c WHERE c.name::text <> 'Administrator'::text), '[]'::json),
	'relations', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_relations c), '[]'::json),
	'translations', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_translations c), '[]'::json),
	'flows', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_flows c), '[]'::json),
	'operations', COALESCE(( SELECT json_agg(to_json(c.*)) AS json_agg FROM directus_operations c), '[]'::json)
)`),
        })
      );
    });

    await tx.schema.createSchema("directus_migration");
    await tx.schema.createTable(
      "directus_migration.directus_config",
      (builder) => {
        builder.increments("id", { primaryKey: true });
        builder
          .timestamp("date_created", { useTz: true })
          .defaultTo(tx.fn.now());
        builder.json("config").nullable();
        builder.text("name").nullable();
      }
    );
    await tx.schema
      .raw(`CREATE OR REPLACE FUNCTION public.insert_directus_config()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
BEGIN
    INSERT INTO directus_migration.directus_config(config, name)
    SELECT config, NEW.version FROM directus_data_config_json;
    RETURN NEW;
END;
$BODY$;`);
    await tx.schema.raw(`CREATE TRIGGER tg_insert_directus_config
    AFTER INSERT
    ON public.directus_migrations
    FOR EACH ROW
    EXECUTE FUNCTION public.insert_directus_config();`);

    await tx.schema
      .raw(`CREATE OR REPLACE FUNCTION public.delete_directus_config()
    RETURNS trigger
    LANGUAGE 'plpgsql'
    COST 100
    VOLATILE NOT LEAKPROOF
AS $BODY$
BEGIN
    RAISE LOG 'REMOVING migration: %', OLD.version;
    DELETE FROM directus_migration.directus_config WHERE name = OLD.version;
    RAISE LOG 'LAST MIGRATION: %', (SELECT version FROM directus_migrations order by timestamp desc limit 1);
    RETURN NEW;
END;
$BODY$;`);
    await tx.schema.raw(`CREATE TRIGGER tg_delete_directus_config
    AFTER DELETE
    ON public.directus_migrations
    FOR EACH ROW
    EXECUTE FUNCTION public.delete_directus_config();`);
  });
}

/**
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.raw(
      "DROP TRIGGER tg_delete_directus_config ON public.directus_migrations"
    );
    await tx.schema.raw(
      "DROP FUNCTION IF EXISTS public.delete_directus_config()"
    );

    await tx.schema.raw(
      "DROP TRIGGER tg_insert_directus_config ON public.directus_migrations"
    );
    await tx.schema.raw(
      "DROP FUNCTION IF EXISTS public.insert_directus_config()"
    );
    await tx.schema.dropTable("directus_migration.directus_config");
    await tx.schema.dropSchema("directus_migration");
    await tx.schema.dropView("directus_data_config_json");
  });
}
```

### SQL DDL of directus system tables

```sql
-- SQL DDL for Directus system tables
CREATE TABLE IF NOT EXISTS public.directus_access
(
    id uuid NOT NULL,
    role uuid,
    "user" uuid,
    policy uuid NOT NULL,
    sort integer,
    CONSTRAINT directus_access_pkey PRIMARY KEY (id),
    CONSTRAINT directus_access_policy_foreign FOREIGN KEY (policy)
        REFERENCES public.directus_policies (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE CASCADE,
    CONSTRAINT directus_access_role_foreign FOREIGN KEY (role)
        REFERENCES public.directus_roles (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE CASCADE,
    CONSTRAINT directus_access_user_foreign FOREIGN KEY ("user")
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS public.directus_collections
(
    collection character varying(64) COLLATE pg_catalog."default" NOT NULL,
    icon character varying(64) COLLATE pg_catalog."default",
    note text COLLATE pg_catalog."default",
    display_template character varying(255) COLLATE pg_catalog."default",
    hidden boolean NOT NULL DEFAULT false,
    singleton boolean NOT NULL DEFAULT false,
    translations json,
    archive_field character varying(64) COLLATE pg_catalog."default",
    archive_app_filter boolean NOT NULL DEFAULT true,
    archive_value character varying(255) COLLATE pg_catalog."default",
    unarchive_value character varying(255) COLLATE pg_catalog."default",
    sort_field character varying(64) COLLATE pg_catalog."default",
    accountability character varying(255) COLLATE pg_catalog."default" DEFAULT 'all'::character varying,
    color character varying(255) COLLATE pg_catalog."default",
    item_duplication_fields json,
    sort integer,
    "group" character varying(64) COLLATE pg_catalog."default",
    collapse character varying(255) COLLATE pg_catalog."default" NOT NULL DEFAULT 'open'::character varying,
    preview_url character varying(255) COLLATE pg_catalog."default",
    versioning boolean NOT NULL DEFAULT false,
    CONSTRAINT directus_collections_pkey PRIMARY KEY (collection),
    CONSTRAINT directus_collections_group_foreign FOREIGN KEY ("group")
        REFERENCES public.directus_collections (collection) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

CREATE TABLE IF NOT EXISTS public.directus_fields
(
    id integer NOT NULL DEFAULT nextval('directus_fields_id_seq'::regclass),
    collection character varying(64) COLLATE pg_catalog."default" NOT NULL,
    field character varying(64) COLLATE pg_catalog."default" NOT NULL,
    special character varying(64) COLLATE pg_catalog."default",
    interface character varying(64) COLLATE pg_catalog."default",
    options json,
    display character varying(64) COLLATE pg_catalog."default",
    display_options json,
    readonly boolean NOT NULL DEFAULT false,
    hidden boolean NOT NULL DEFAULT false,
    sort integer,
    width character varying(30) COLLATE pg_catalog."default" DEFAULT 'full'::character varying,
    translations json,
    note text COLLATE pg_catalog."default",
    conditions json,
    required boolean DEFAULT false,
    "group" character varying(64) COLLATE pg_catalog."default",
    validation json,
    validation_message text COLLATE pg_catalog."default",
    CONSTRAINT directus_fields_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.directus_flows
(
    id uuid NOT NULL,
    name character varying(255) COLLATE pg_catalog."default" NOT NULL,
    icon character varying(64) COLLATE pg_catalog."default",
    color character varying(255) COLLATE pg_catalog."default",
    description text COLLATE pg_catalog."default",
    status character varying(255) COLLATE pg_catalog."default" NOT NULL DEFAULT 'active'::character varying,
    trigger character varying(255) COLLATE pg_catalog."default",
    accountability character varying(255) COLLATE pg_catalog."default" DEFAULT 'all'::character varying,
    options json,
    operation uuid,
    date_created timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    CONSTRAINT directus_flows_pkey PRIMARY KEY (id),
    CONSTRAINT directus_flows_operation_unique UNIQUE (operation),
    CONSTRAINT directus_flows_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE SET NULL
);

CREATE TABLE IF NOT EXISTS public.directus_operations
(
    id uuid NOT NULL,
    name character varying(255) COLLATE pg_catalog."default",
    key character varying(255) COLLATE pg_catalog."default" NOT NULL,
    type character varying(255) COLLATE pg_catalog."default" NOT NULL,
    position_x integer NOT NULL,
    position_y integer NOT NULL,
    options json,
    resolve uuid,
    reject uuid,
    flow uuid NOT NULL,
    date_created timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    CONSTRAINT directus_operations_pkey PRIMARY KEY (id),
    CONSTRAINT directus_operations_reject_unique UNIQUE (reject),
    CONSTRAINT directus_operations_resolve_unique UNIQUE (resolve),
    CONSTRAINT directus_operations_flow_foreign FOREIGN KEY (flow)
        REFERENCES public.directus_flows (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE CASCADE,
    CONSTRAINT directus_operations_reject_foreign FOREIGN KEY (reject)
        REFERENCES public.directus_operations (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT directus_operations_resolve_foreign FOREIGN KEY (resolve)
        REFERENCES public.directus_operations (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION,
    CONSTRAINT directus_operations_user_created_foreign FOREIGN KEY (user_created)
        REFERENCES public.directus_users (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE SET NULL
);

CREATE TABLE IF NOT EXISTS public.directus_permissions
(
    id integer NOT NULL DEFAULT nextval('directus_permissions_id_seq'::regclass),
    collection character varying(64) COLLATE pg_catalog."default" NOT NULL,
    action character varying(10) COLLATE pg_catalog."default" NOT NULL,
    permissions json,
    validation json,
    presets json,
    fields text COLLATE pg_catalog."default",
    policy uuid NOT NULL,
    CONSTRAINT directus_permissions_pkey PRIMARY KEY (id),
    CONSTRAINT directus_permissions_policy_foreign FOREIGN KEY (policy)
        REFERENCES public.directus_policies (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS public.directus_policies
(
    id uuid NOT NULL,
    name character varying(100) COLLATE pg_catalog."default" NOT NULL,
    icon character varying(64) COLLATE pg_catalog."default" NOT NULL DEFAULT 'badge'::character varying,
    description text COLLATE pg_catalog."default",
    ip_access text COLLATE pg_catalog."default",
    enforce_tfa boolean NOT NULL DEFAULT false,
    admin_access boolean NOT NULL DEFAULT false,
    app_access boolean NOT NULL DEFAULT false,
    CONSTRAINT directus_policies_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.directus_relations
(
    id integer NOT NULL DEFAULT nextval('directus_relations_id_seq'::regclass),
    many_collection character varying(64) COLLATE pg_catalog."default" NOT NULL,
    many_field character varying(64) COLLATE pg_catalog."default" NOT NULL,
    one_collection character varying(64) COLLATE pg_catalog."default",
    one_field character varying(64) COLLATE pg_catalog."default",
    one_collection_field character varying(64) COLLATE pg_catalog."default",
    one_allowed_collections text COLLATE pg_catalog."default",
    junction_field character varying(64) COLLATE pg_catalog."default",
    sort_field character varying(64) COLLATE pg_catalog."default",
    one_deselect_action character varying(255) COLLATE pg_catalog."default" NOT NULL DEFAULT 'nullify'::character varying,
    CONSTRAINT directus_relations_pkey PRIMARY KEY (id)
);

CREATE TABLE IF NOT EXISTS public.directus_roles
(
    id uuid NOT NULL,
    name character varying(100) COLLATE pg_catalog."default" NOT NULL,
    icon character varying(64) COLLATE pg_catalog."default" NOT NULL DEFAULT 'supervised_user_circle'::character varying,
    description text COLLATE pg_catalog."default",
    parent uuid,
    CONSTRAINT directus_roles_pkey PRIMARY KEY (id),
    CONSTRAINT directus_roles_parent_foreign FOREIGN KEY (parent)
        REFERENCES public.directus_roles (id) MATCH SIMPLE
        ON UPDATE NO ACTION
        ON DELETE NO ACTION
);

CREATE TABLE IF NOT EXISTS public.directus_translations
(
    id uuid NOT NULL,
    language character varying(255) COLLATE pg_catalog."default" NOT NULL,
    key character varying(255) COLLATE pg_catalog."default" NOT NULL,
    value text COLLATE pg_catalog."default" NOT NULL,
    CONSTRAINT directus_translations_pkey PRIMARY KEY (id)
);
```
