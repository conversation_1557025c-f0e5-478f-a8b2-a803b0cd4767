# Water Pump Flow Rate Feature

## Overview

This document describes the implementation of the water pump flow rate feature, which allows users to specify and store the flow rate of water pumps in liters per hour (L/h).

## Changes Made

### 1. Database Schema Changes

**Migration File**: `directus/migrations/20250729A-add-water-pump-flow-rate.js`

- Added `flow_rate_lh` column to the `water_pump` table
- Column type: `DECIMAL(10,2)` - allows for precise flow rate values up to 99,999,999.99 L/h
- Column is nullable to support existing pumps without flow rate data
- Added descriptive comment: "Flow rate in liters per hour (L/h)"

**Migration Commands**:
```bash
# Apply migration
npm run docker:directus:cli:db:migrate:latest

# Rollback migration (if needed)
npm run docker:directus:cli:db:migrate:down
```

### 2. Frontend Model Updates

**File**: `app/src/api/model/water-pump.ts`

- Added `flow_rate_lh: number | null` to the `WaterPump` interface
- This field represents the pump's flow rate in liters per hour

### 3. API Query Updates

**File**: `app/src/api/queries/account.ts`

- Added `flow_rate_lh` to the water pump fields selection in the account query
- This ensures the flow rate data is fetched when retrieving water pump information

### 4. UI Component Updates

**File**: `app/src/pages/main/components/PumpDetailModal.tsx`

#### Form Data Structure
- Added `flow_rate_lh: null as number | null` to the form data state
- Updated form initialization in `useEffect` to handle the new field for both create and edit modes

#### Input Field
- Enhanced the existing "Vazão" (Flow Rate) input field:
  - Changed from `type="text"` to `type="number"` for better validation
  - Connected to form data with proper value binding
  - Added `onChange` handler with number parsing
  - Added `min="0"` and `step="0.1"` attributes for validation
  - Updated placeholder to show "1000" as example
  - Added helper text: "Taxa de fluxo da bomba em litros por hora"

#### Data Handling
- The `handleSave` function automatically includes the `flow_rate_lh` field when creating or updating pumps
- Proper null handling for empty values

## Usage

### For Users

1. **Creating a New Pump**:
   - Open the "Nova Bomba" (New Pump) modal
   - Fill in the required fields (Label, Type, etc.)
   - Optionally enter the flow rate in the "Vazão (L/h)" field
   - Click "Salvar" to save the pump

2. **Editing an Existing Pump**:
   - Open the pump detail modal for an existing pump
   - The flow rate field will show the current value (if any)
   - Update the flow rate as needed
   - Click "Salvar" to save changes

### For Developers

#### Accessing Flow Rate Data

```typescript
// In components that receive WaterPump data
const pump: WaterPump = // ... get pump data
const flowRate = pump.flow_rate_lh; // number | null

// Check if flow rate is available
if (flowRate !== null) {
  console.log(`Pump flow rate: ${flowRate} L/h`);
} else {
  console.log('Flow rate not specified');
}
```

#### Form Validation

The current implementation allows null values for flow rate, making it an optional field. If validation is needed in the future:

```typescript
// Example validation logic
const validateFlowRate = (value: number | null): string | null => {
  if (value !== null && value <= 0) {
    return "Flow rate must be greater than 0";
  }
  return null;
};
```

## Database Schema

### water_pump Table Structure (Updated)

| Column | Type | Nullable | Description |
|--------|------|----------|-------------|
| id | UUID | No | Primary key |
| property | UUID | No | Foreign key to property |
| water_pump_controller | UUID | Yes | Foreign key to device |
| label | VARCHAR | No | Pump label/name |
| identifier | VARCHAR | No | Pump identifier |
| pump_type | ENUM | No | Type: IRRIGATION, FERTIGATION, SERVICE |
| pump_model | VARCHAR | No | Pump model |
| has_frequency_inverter | BOOLEAN | No | Whether pump has frequency inverter |
| monitor_operation | BOOLEAN | No | Whether to monitor pump operation |
| **flow_rate_lh** | **DECIMAL(10,2)** | **Yes** | **Flow rate in L/h** |
| notes | TEXT | Yes | Additional notes |
| metadata | JSON | Yes | Additional metadata |
| created_at | TIMESTAMP | No | Creation timestamp |
| updated_at | TIMESTAMP | No | Last update timestamp |

## Future Enhancements

1. **Unit Conversion**: Add support for different flow rate units (m³/h, GPM, etc.)
2. **Validation**: Add minimum/maximum flow rate validation based on pump type
3. **Calculations**: Use flow rate data for irrigation time calculations
4. **Reporting**: Include flow rate in pump performance reports
5. **Import/Export**: Support flow rate data in bulk import/export operations

## Testing

### Manual Testing Checklist

- [ ] Create new pump with flow rate
- [ ] Create new pump without flow rate
- [ ] Edit existing pump to add flow rate
- [ ] Edit existing pump to change flow rate
- [ ] Edit existing pump to remove flow rate (set to empty)
- [ ] Verify data persistence after page refresh
- [ ] Test with various flow rate values (integers, decimals)
- [ ] Test with edge cases (0, very large numbers)

### API Testing

```bash
# Test creating pump with flow rate
curl -X POST /api/water_pump \
  -H "Content-Type: application/json" \
  -d '{
    "label": "Test Pump",
    "pump_type": "IRRIGATION",
    "flow_rate_lh": 1500.5,
    ...
  }'

# Test updating pump flow rate
curl -X PATCH /api/water_pump/{id} \
  -H "Content-Type: application/json" \
  -d '{"flow_rate_lh": 2000.0}'
```

## Migration Notes

- **Backward Compatibility**: Existing pumps will have `flow_rate_lh` set to `NULL`
- **Data Migration**: If historical flow rate data exists elsewhere, a data migration script may be needed
- **API Compatibility**: The API remains backward compatible as the new field is optional

## Troubleshooting

### Common Issues

1. **Migration Fails**: Ensure database connection is available and user has ALTER TABLE permissions
2. **Field Not Showing**: Clear browser cache and restart development server
3. **Data Not Saving**: Check browser console for JavaScript errors and verify API endpoint

### Rollback Procedure

If the feature needs to be rolled back:

1. Run the down migration: `npm run docker:directus:cli:db:migrate:down`
2. Revert the code changes in the affected files
3. Restart the application

---

**Implementation Date**: 2025-07-29  
**Version**: 1.0.0  
**Author**: Development Team
