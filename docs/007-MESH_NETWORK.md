# 007 — Mesh Network and Device Association (LIC-centric)

This document describes how mesh devices are associated with a Localized Irrigation Controller (LIC) within the Irriga+ system. It consolidates system behavior, database model, constraints, business rules, backend triggers, scheduler logic, and frontend handling, so that nothing is left out.

Contents

1. Overview
2. Roles and Device Types
3. Association Model and Lifecycle
4. Database Structures
   4.1 property_device
   4.2 mesh_device_mapping
   4.3 “current_mesh_device_mapping” on mesh property_device
   4.4 Triggers and updaters
5. Constraints
6. Business Rules and Overlap Accommodation
7. Operational Flow
   7.1 Creating/updating mappings
   7.2 Current mapping calculation
   7.3 Command routing example (e.g., turning on a pump)
8. Frontend Integration
   8.1 API Models and Client
   8.2 Queries
   8.3 UI/UX (HardwarePage and Modals)
   8.4 Jotai stores
9. Examples and Scenarios
10. Notes, Future Work, and Gotchas

---

## 1. Overview

Irriga+ uses a mesh network where the Localized Irrigation Controller (LIC) works as both coordinator and gateway for property-level field devices. Mesh devices (WPC, VC, RM) communicate via LoRa Mesh through the LIC. The system must always know which mesh device is associated with which LIC to route commands and collect telemetry correctly.

Associations are time-bound and stored in a dedicated mapping table. The “current” active association for each mesh device is tracked on the mesh device’s `property_device` record via a foreign key to the mapping, enabling fast reads for UI and control decisions.

---

## 2. Roles and Device Types

- LIC (Localized Irrigation Controller)

  - Coordinator and gateway of the mesh network.
  - Mediates communication between the backend and mesh devices.
  - A single LIC can serve multiple mesh devices concurrently.

- Mesh Devices

  - WPC (Water Pump Controller)
  - VC (Valve Controller)
  - RM (Remote Module or other in-field device types)
  - Each mesh device is associated with exactly one LIC at any given time (but may change over time).

- Properties
  - Both LIC and mesh devices belong to a given property.
  - Associations are only valid within the same property.

---

## 3. Association Model and Lifecycle

- Associations are represented by records in `mesh_device_mapping`.
- Each record references exactly one mesh device (via its `property_device`) and one LIC (via its `property_device`).
- Associations are time-bound using `start_date` and `end_date` (end is nullable to indicate open-ended).
- At any “reference date” (typically now()), there must be at most one active mapping for each mesh device.
- The system maintains a “current” association pointer on the mesh device’s `property_device` to the mapping record deemed active at the chosen reference date.

---

## 4. Database Structures

### 4.1 property_device

- Represents the relationship between a property and a device.
- Devices include LIC and mesh types (WPC, VC, RM).
- A new nullable column `current_mesh_device_mapping` (UUID FK to `mesh_device_mapping`) is present only on mesh property_device rows. If set, it indicates the current active association mapping at a reference moment (generally now()).

Key points:

- `property_device` is the primary place to quickly retrieve “current association” for a mesh device via `current_mesh_device_mapping`.
- LIC `property_device` rows do not set `current_mesh_device_mapping` themselves (they can be associated with many mesh devices).

### 4.2 mesh_device_mapping

- Table to record the association between:

  - mesh_property_device (UUID FK to property_device)
  - lic_property_device (UUID FK to property_device)

- Core columns:
  - id: UUID (PK)
  - mesh_property_device: UUID (FK to property_device — must be a mesh device)
  - lic_property_device: UUID (FK to property_device — must be LIC)
  - start_date: timestamptz (inclusive start)
  - end_date: timestamptz (inclusive end; nullable means open-ended)
  - date_created, user_created, date_updated, user_updated (auditing)
- Trigger:
  - A trigger updates `date_updated` on UPDATE using pre-existing `update_timestamp_column()`.

### 4.3 “current_mesh_device_mapping” on mesh property_device

- Nullable FK to `mesh_device_mapping`.
- Should reflect the active mapping for the device at a given reference time (usually `now()`).
- Is maintained by a PLSQL function `im_update_current_mesh_device_mapping(mesh_pd_ids[], reference_date?)`.
  - If multiple IDs are passed, it updates each mesh device’s current pointer based on active mapping at `reference_date` (defaults to now()).
  - If no active mapping exists at the reference date, sets the pointer to null.

Rationale:

- Fast reads for “who is my LIC right now?” from a device row.
- Allows real-time control routing without repeated time-window scans.

### 4.4 Triggers and updaters

- A trigger on `mesh_device_mapping` ensures:

  - Validation of constraints (same property, devices active at association time, etc.).
  - Overlap handling (see “Business Rules”).
  - After CREATE or date changes, calls `im_update_current_mesh_device_mapping` for the affected mesh device with `now()` as `reference_date`.

- `im_update_current_mesh_device_mapping(mesh_pd_ids[], reference_date?)`:
  - For each ID:
    - Finds mapping where `start_date <= reference_date <= end_date` (treat null end_date as infinity).
    - Updates `property_device.current_mesh_device_mapping` to that mapping’s id, or null if none.

---

## 5. Constraints

Enforced by DB-side validations and/or triggers:

1. Single active LIC per mesh device at a time

   - For a given mesh device, at any reference moment there must be at most one mapping active.
   - Result: No overlapping time windows for the same mesh device.

2. Same property

   - The mesh device and LIC must belong to the same property.
   - Associations across properties are invalid.

3. Both devices active within the association period

   - The mesh device and the LIC must be active at the time the association applies.
   - Specifically at creation/update: `start_date <= now() <= end_date` for both devices (or consistent with how device active windows are modeled in your system). If device active periods are represented elsewhere, trigger checks must validate this consistently.

4. No overlapping `mesh_device_mapping` for the same mesh device
   - Enforced by the overlap accommodation logic described below.

5. **Mesh Network Invariants (Business Logic Enforcement)**

   The following additional constraints ensure devices are properly grouped within the same mesh network:

   - **Reservoir Constraint**: If both Reservoir Monitor (RM) and Service Water Pump are configured for a reservoir, their controllers must be mapped to the same LIC.
   - **Project Constraint**: Irrigation and fertigation pumps must have controllers mapped to the same LIC as the project's Localized Irrigation Controller.
   - **Sector Constraint**: Valve controllers must be mapped to the same LIC as their project's Localized Irrigation Controller.

   These constraints are enforced at the database level by dedicated triggers that run before INSERT/UPDATE operations on the respective tables.

---

## 6. Business Rules and Overlap Accommodation

When a mapping is created or when `start_date`/`end_date` is changed, the system checks for overlap with existing mappings for the same `mesh_property_device`. If overlaps exist, the system must adjust the existing records according to these rules:

Definitions

- New mapping: N = [N.start, N.end]
- Existing mapping: E = [E.start, E.end]
- All intervals are inclusive at both ends by convention here. Database implementation uses timestamptz; keep inclusivity consistent.

Rules

1. If E.start < N.start and periods overlap at the beginning:

   - Truncate E’s end to N.start (E.end = N.start).
   - This ensures E ends right before N begins if inclusivity would otherwise overlap. If you need a 1-second offset to avoid inclusive overlap, apply E.end = N.start - 1s (or conversely, set N.start = E.end + 1s); the implementation must be consistent. The source spec uses “end_date set to the new record’s start_date” and sometimes “start_date to end_date + 1 second”; pick one consistent approach in code so no overlap occurs.

2. If E.end > N.end and periods overlap at the end:

   - Shift E’s start forward to N.end + 1 second (E.start = N.end + 1s).
   - This ensures E starts right after N ends, eliminating overlap.

3. If E is completely contained within N:

   - Error: “There is an association already active in the requested period.”
   - This prevents N from superseding E fully (no total overshadowing).

4. If E fully contains N:
   - Split E into two records around N:
     - Left portion: E.left = [E.start, N.start] (or N.start - 1s depending on inclusivity handling)
     - Right portion: E.right = [N.end, E.end] (or N.end + 1s)
   - Update E to become the left portion, and CREATE a new record for the right portion.
   - Important: References to E (foreign keys) that depend on time windows may need to be re-pointed depending on the period; add TODO comment as per spec. This FK reassignment is out of scope for now.

After any adjustment:

- Call `im_update_current_mesh_device_mapping([mesh_property_device_id], now())` to refresh current pointer.

Note on inclusive boundaries:

- The task spec mixes two styles (setting end to the new start, and setting start to old end + 1 second). Implementation should choose a uniform, collision-free interpretation. Common pattern:
  - Earlier window end = N.start - 1s
  - Later window start = N.end + 1s
- The spec's intent is "no overlap." Use precise arithmetic in SQL to enforce that.

---

## 6.1 Database Enforcement of Mesh Network Invariants

The mesh network invariants described in constraint #5 are enforced through a set of database functions and triggers:

### Helper Function: get_lic_for_device

```sql
CREATE OR REPLACE FUNCTION get_lic_for_device(p_device_id UUID)
RETURNS UUID
```

This function retrieves the LIC property_device ID for a given device by:
1. Finding the active property_device record for the device (`end_date IS NULL`)
2. Looking up the active mesh_device_mapping for that property_device
3. Returning the `lic_property_device` from the mapping, or NULL if no active mapping exists

### Trigger Functions

Three trigger functions enforce mesh network constraints:

1. **check_reservoir_mesh()**: Validates that if both `reservoir_monitor` and `water_pump` are set on a reservoir, their controllers are mapped to the same LIC.

2. **check_project_mesh()**: Validates that irrigation and fertigation pumps in a project have controllers mapped to the same LIC as the project's `localized_irrigation_controller`.

3. **check_sector_mesh()**: Validates that the valve controller in a sector is mapped to the same LIC as the sector's project LIC.

### Trigger Installation

Each validation function is installed as BEFORE INSERT and BEFORE UPDATE triggers on their respective tables:
- `trigger_check_reservoir_mesh_before_insert/update` on `reservoir`
- `trigger_check_project_mesh_before_insert/update` on `project`  
- `trigger_check_sector_mesh_before_insert/update` on `sector`

### Error Handling

When constraints are violated, the triggers raise exceptions with descriptive messages:
- "Reservoir Monitor and Service Water Pump must be in the same mesh network."
- "Irrigation pump must be in the same mesh network as the project LIC."
- "Fertigation pump must be in the same mesh network as the project LIC."
- "Valve Controller must be in the same mesh network as the project LIC."

These constraints ensure data integrity at the database level, complementing the frontend filtering implemented in the UI forms.

---

## 7. Operational Flow

### 7.1 Creating/updating mappings

1. User or backend operation attempts to insert/update a `mesh_device_mapping`.
2. Trigger runs:
   - Validates:
     - Mesh device and LIC belong to the same property.
     - Both devices are active for the relevant period (at least at creation time per spec).
     - No overlap; runs accommodation logic to adjust existing records.
   - On success, calls:
     - `im_update_current_mesh_device_mapping([mesh_pd_id], now())`.

### 7.2 Current mapping calculation

- `im_update_current_mesh_device_mapping(mesh_pd_ids[], reference_date?)`:
  - For each mesh device ID:
    - Finds mapping where start_date <= reference_date <= end_date (with null end treated as open-ended).
    - Updates mesh device’s `property_device.current_mesh_device_mapping` to this mapping id or null if none.

This function may be scheduled (job scheduler) to ensure consistency if, e.g., large batches of time updates occur or to re-evaluate at new “now()”.

### 7.3 Command routing example (turning on a pump)

- User chooses to turn on a service water pump (WPC) in the app.
- Backend resolves the target device’s `property_device` and reads `current_mesh_device_mapping`.
- From that mapping, the backend gets `lic_property_device`.
- Command is then sent to the LIC, which forwards it over LoRa Mesh to the WPC.
- If no current mapping exists, the backend returns a controlled error (e.g., “Device not mapped to an LIC”).

---

## 8. Frontend Integration

The following pieces are already implemented per tasks, but this section describes the intended structure and behavior.

### 8.1 API Models and Client

- Model

  - `app/src/models/mesh-device-mapping.ts`:
    - Defines MeshDeviceMapping entity (id, mesh_property_device, lic_property_device, start_date, end_date, audit columns).
  - `app/src/models/property-device.ts`:
    - Adds `current_mesh_device_mapping` relation field.

- Directus client and service
  - `app/src/api/client.ts`:
    - Augmented `AppSchema` to include `mesh_device_mapping`.
  - `app/src/api/service.ts`:
    - CRUD service for meshDeviceMapping.

### 8.2 Queries

- `app/src/api/queries/account.ts`:
  - `getAccountUserTree` updated to include `mesh_device_mapping` collection and `current_mesh_device_mapping` on property_device.
  - Enables UI to know current mappings in a single tree query.

### 8.3 UI/UX (HardwarePage and Modals)

- Managing mappings lives under Hardware → Devices tab.
- Final Recommendation: “Enhanced List” (Solution 3) implemented per prototype:
  - Shows devices and clearly indicates mesh type and current LIC mapping.
  - Allows opening Manage Network and Assign to LIC modals.
  - Actions:
    - “Adicionar Dispositivo” in ManageNetworkModal opens a selector to add a device to an LIC.
    - AssignToLICModal has “Mapear” to create a new mapping (time-bounded), automatically handling “current” via backend trigger.
  - DeviceDetailModal:
    - Displays device info and mapping state.
    - Provides “Mapear” to change association.

Key UX Constraints:

- `current_mesh_device_mapping` is read-only; it is set by backend logic based on time-windowed mappings and scheduler updates.
- UI creates new mapping records to effect changes; it does not directly set the “current” field.

### 8.4 Jotai stores

- Jotai atoms exist for mesh_device_mapping data and CRUD flows:
  - Atoms keep local state in sync with server operations.
  - Upon create/update/delete, UI re-queries or patches state to reflect the newly active mapping.

---

## 9. Examples and Scenarios

Scenario A: First-time mapping

- A new WPC is installed in Property X.
- User selects WPC in UI → “Mapear” → chooses LIC-A (same Property X).
- UI creates `mesh_device_mapping` with start=now(), end=null.
- Trigger validates constraints, sets current on WPC’s `property_device`.
- Result: WPC appears under LIC-A in device list; commands route via LIC-A.

Scenario B: Reassign during season

- WPC currently mapped to LIC-A with [2025-04-01, null].
- User wants to move it to LIC-B effective today.
- UI creates new mapping N: [2025-08-01T12:00:00Z, null] to LIC-B.
- Trigger detects overlap with E: [2025-04-01, null].
  - Adjusts E.end to N.start - 1s (or sets E.end=N.start if using inclusive-end policy).
- Updates current pointer to N.
- Result: From now on, commands route via LIC-B; history preserved.

Scenario C: Backdated correction

- An engineer realizes WPC was actually operating under LIC-B from [2025-07-15, 2025-07-20].
- Current mapping E: [2025-04-01, null] to LIC-A.
- New backdated N: [2025-07-15, 2025-07-20] to LIC-B.
- Trigger sees E fully contains N. Splits E into:
  - E.left: [2025-04-01, 2025-07-14 23:59:59]
  - E.right: [2025-07-20 00:00:01, null]
  - Leaves N as [2025-07-15, 2025-07-20].
  - Notes a TODO about foreign key time-dependent references to E.
- Current pointer (now()) likely still E.right (LIC-A), unless now() is within N’s window.

Scenario D: Invalid cross-property attempt

- Mesh device belongs to Property X; LIC belongs to Property Y.
- Attempted mapping is rejected by trigger with validation error.

---

## 10. Notes, Future Work, and Gotchas

- Inclusivity policy:

  - The spec mixes “set end to new start” and “set start to old end + 1s.” Implementation must be internally consistent to guarantee no overlaps. Decide on:
    - Inclusive intervals [start, end], and:
      - Truncation rule: earlier.end = later.start - 1s
      - Shift rule: later.start = earlier.end + 1s
  - Ensure all code paths and tests follow the same convention.

- Device active windows:

  - The constraint requires devices to be active at association time. If device activity is modeled elsewhere (e.g., device.enabled or an availability table), ensure the trigger checks the correct source of truth.

- Foreign keys during splits:

  - When splitting an existing mapping due to a backdated insert, any downstream FKs that point to the original mapping and are time-sensitive might need reassignment. The spec says not implemented now; add a clear TODO in the migration/trigger code.

- Performance:

  - Index mapping by mesh_property_device and time columns for efficient overlap queries.
  - `current_mesh_device_mapping` avoids repeated time-window scans in hot paths.

- Scheduler usage:

  - Even though triggers update “current” on change, a scheduled job calling `im_update_current_mesh_device_mapping` can refresh pointers periodically (especially if future-dated windows are common).

- Frontend safeguards:

  - UI should prevent selecting an LIC from another property.
  - When showing candidates in “Adicionar Dispositivo,” include the device’s current mapping to signal possible reassignment.

- Error messaging:

  - Overlap conflicts that result in “existing contained within new” must produce a clear error message to users.

- Auditing:
  - `date_created`, `user_created`, `date_updated`, `user_updated` must be kept for traceability of mapping decisions.

---

Appendix: High-level Diagram (ASCII)

Property
|
+-- LICs (property_device: LIC)
| +-- LIC-A (pd_id=licA)
| +-- LIC-B (pd_id=licB)
|
+-- Mesh Devices (property_device: WPC/VC/RM)
+-- WPC-1 (pd_id=wpc1) ---- mesh_device_mapping ----> licA
| [2025-04-01 .. 2025-07-31 11:59:59]
| ---- mesh_device_mapping ----> licB
| [2025-07-31 12:00:00 .. null]
|
+-- VC-2 (pd_id=vc2) ---- mesh_device_mapping ----> licA
[2025-05-10 .. null]

At any time, current_mesh_device_mapping for wpc1 and vc2 points to the active row.

---

This document defines the complete behavior of the mesh device association system around the LIC coordinator model, covering schema, constraints, triggers, scheduler function, frontend integration, and operational flows end-to-end.
