<mxfile host="65bd71144e">
    <diagram id="Z5oasqo4Wv3JWkX0wABZ" name="Page-1">
        <mxGraphModel dx="1041" dy="827" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="2" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="130" y="180" width="300" height="700" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="140" y="190" width="280" height="130" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="" style="html=1;verticalLabelPosition=bottom;align=center;labelBackgroundColor=#ffffff;verticalAlign=top;strokeWidth=2;strokeColor=#0080F0;shadow=0;dashed=0;shape=mxgraph.ios7.icons.user;" vertex="1" parent="1">
                    <mxGeometry x="240" y="200" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="&lt;b style=&quot;line-height: 170%;&quot;&gt;Fulano da Silva&lt;/b&gt;&lt;br&gt;<EMAIL>" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="140" y="280" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;b style=&quot;&quot;&gt;Conta&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="140" y="350" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="140" y="380" width="280" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="&lt;b style=&quot;line-height: 170%;&quot;&gt;Fulano da Silva&lt;/b&gt;&lt;br&gt;<EMAIL>" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="150" y="387.5" width="230" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="" style="shape=ellipse;dashed=0;strokeColor=none;shadow=1;fontSize=13;align=center;verticalAlign=top;labelPosition=center;verticalLabelPosition=bottom;html=1;aspect=fixed;" vertex="1" parent="1">
                    <mxGeometry x="380" y="387.5" width="30" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="" style="dashed=0;html=1;shape=mxgraph.gmdl.edit;strokeColor=none;fillColor=#737373;shadow=0;sketch=0;html=1;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;" vertex="1" parent="13">
                    <mxGeometry x="9.642857142857142" y="9.642857142857142" width="10.714285714285715" height="10.714285714285715" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;&lt;b style=&quot;&quot;&gt;Propriedade&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="140" y="450" width="280" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="140" y="480" width="280" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="&lt;b&gt;Sítio Bom Jesus&lt;/b&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="150" y="487.5" width="230" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="" style="shape=ellipse;dashed=0;strokeColor=none;shadow=1;fontSize=13;align=center;verticalAlign=top;labelPosition=center;verticalLabelPosition=bottom;html=1;aspect=fixed;" vertex="1" parent="1">
                    <mxGeometry x="380" y="487.5" width="30" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="" style="dashed=0;html=1;shape=mxgraph.gmdl.edit;strokeColor=none;fillColor=#737373;shadow=0;sketch=0;html=1;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;" vertex="1" parent="18">
                    <mxGeometry x="9.642857142857142" y="9.642857142857142" width="10.714285714285715" height="10.714285714285715" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="LOGOUT" style="whiteSpace=wrap;html=1;dashed=0;align=center;fontSize=12;shape=rect;fillColor=#f8cecc;strokeColor=#b85450;fontStyle=1;shadow=1" vertex="1" parent="1">
                    <mxGeometry x="140" y="830" width="280" height="36" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="" style="shape=mxgraph.signs.tech.eject;html=1;pointerEvents=1;fillColor=#000000;strokeColor=none;verticalLabelPosition=bottom;verticalAlign=top;align=center;direction=south;" vertex="1" parent="1">
                    <mxGeometry x="310" y="840.75" width="20" height="14.5" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="shape=ellipse;dashed=0;strokeColor=none;shadow=1;fontSize=13;align=center;verticalAlign=top;labelPosition=center;verticalLabelPosition=bottom;html=1;aspect=fixed;" vertex="1" parent="1">
                    <mxGeometry x="380" y="200" width="30" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="" style="dashed=0;html=1;shape=mxgraph.gmdl.edit;strokeColor=none;fillColor=#737373;shadow=0;sketch=0;html=1;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;" vertex="1" parent="22">
                    <mxGeometry x="9.642857142857142" y="9.642857142857142" width="10.714285714285715" height="10.714285714285715" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>