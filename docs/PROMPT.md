# Context

My electric engineering team has developed a product capable of manage localized irrigation.

It consists of one main board that must be connected to the internet using WiFi or ethernet cable and other remote boards that control water pumps and valves. The remote boards communicate with the main board using LoRa Mesh.

There are 5 kinds of boards:

LIC - Localized Irrigation Controller: This is the main board connected to the internet. It has a programmable  ESP32 and persistent storage. This is where all configuration is stored and all business rules logic happens. It will command the other boards when to turn on and off.

VC - Valve Controller: This is a board that can control up to 4 electric irrigation valves

WPC-PL10 - Water Pump Controller - PL10 Variant: This is the board that controls water pumps with PL10 specifications.

WPC-PL50 - Water Pump Controller - PL50 Variant: This is the board that controls water pumps with PL50 specifications.

RM - Reservoir Monitor: This is the board that monitors water reservoir systems.

---

## Target Audience

The target audience for this project is small rural property owners who may not have extensive technical knowledge. The app must be simple, intuitive, and user-friendly to accommodate their needs.

## How it works

The system consists of the following components:

- **LIC**: The main board that connects to the internet and manages the irrigation system.

- **VC**: A board that controls up to 4 electric irrigation valves.

- **WPC-PL10**: A board that controls water pumps with PL10 specifications.

- **WPC-PL50**: A board that controls water pumps with PL50 specifications.

- **RM**: A board that monitors water reservoir systems.

A LIC is capable of managing the localized irrigation of a property by controlling water pumps and valves.

Each water pump is connected to a Water Pump Controller (WPC-PL10 or WPC-PL50), which can control the water pump's operation remotely.

Each valve is connected to a VC output, which can control the valve's operation remotely.

It is important to note that a valve will "direct" the water flow to a specific sector of the property, while the Water Pump Controller will control the water pump that provides water to that sector. In this scenario, the water can only flow to one sector at a time, as the Water Pump Controller can only control one water pump at a time and a water pump can only provide water enough for one sector at a time.

In terms of naming, a water pump provides water to a PLOT, which is a set of SECTORS. Thus, a water pump controller is linked to a plot, and a VC is linked to up to 4 sectors of that plot through its outputs.

For this to happen, the LIC must be configured with the serial number of the Water Pump Controller and the serial numbers of the VC's that are connected to it.

The plot will be linked to a Water Pump Controller, and the sectors will be linked to the VC's outputs.

At this point, the LIC know how to control the water pump and the valves, but it does not know when to do it.

The LIC will be configured with a plot's "PLAN". A plan belongs to plot, and it is composed of:

- A start time

- The order of the sectors to be irrigated

- The duration of each sector's irrigation

- The delay between each sector's irrigation

- The days of the week when the plan will be executed

- The time zone of the property

LIC will execute an irrigation plan. It will turn on the valves connected to the output of a VC sequentially, starting at a pre configured time and for a pre configured duration for each VC output, waiting a pre configured delay between each valve.

LIC must know what water pump controller provides water to what VC valves, because it will turn the water pump on before starting a plan. It will know this because the LIC knows the Water Pump Controller that is associated to the plot.

A LIC can have many plots, thus, it can control many sets of water pump controller + VC's.

Two plans of distinct plots can be executed concurrently.

It is required that each element of the system to have, besides its serial number, a label to better identify it.

### Plan Execution Flow

1. The LIC will check the current time and day of the week.

2. If the current time and day match the plan's schedule, the LIC will start the irrigation process by:

- Turning on the water pump associated with the plan's plot.

- Sequentially activating each valve connected to the VC outputs according to the plan's sector order.

- Wait for the specified duration for each sector before moving to the next one.

- Wait for the specified delay between each sector's irrigation.

**Flow Diagram**

```mermaid

flowchart TD

    A[Start] --> B{Is it time to irrigate?}

    B -- Yes --> C[Turn on water pump]

    C --> D[Activate valves in order]

    D --> E[Wait for duration]

    E --> F[Wait for delay]

    F --> D

    B -- No --> G[Check again later]

    G --> B

```

## User Profile

The user profile will contain the following fields:

- `property_name`: A string representing the name of the property or farm.

- `property_location`: An optional object that can contain:

- `lat`: A number representing the latitude of the property.

- `lon`: A number representing the longitude of the property.

- `address`: A string representing the address of the property.

- `email`: An optional string representing the user's email.

- `phone`: An optional string representing the user's phone number.

- `cpf`: An optional string representing the user's CPF (Brazilian individual taxpayer registry ID).

- `owner_name`: The owner of the property, which is a string representing the name of the owner.

## Data Modeling

### Entities and Fields

- **Device**

- `serial_number`: string (unique)

- `label`: string

- `model`: string literally one of `LIC`, `VC`, `WPC-PL10`, `WPC-PL50`, or `RM`

- **Plot**

- `id`: integer (primary key)

- `label`: string

- `water_pump_controller_id`: integer (foreign key → Water Pump Controller)

- `lic_id`: integer (foreign key → Device)

- **Sector**

- `id`: integer (primary key)

- `label`: string

- `plot_id`: integer (foreign key → Plot)

- `vc_id`: integer (foreign key → Device)

- `vc_output`: integer (1–4)

- **Plan**

- `id`: integer (primary key)

- `plot_id`: integer (foreign key → Plot)

- `start_time`: datetime

- `sector_order`: array of integers (sector IDs)

- `duration_seconds`: integer

- `delay_seconds`: integer

- `days_of_week`: array of strings

- `timezone`: string

### Relationships and Constraints

- One **LIC** can manage multiple **Plots** (1 → N).

- One **Plot** links one **Water Pump Controller** and one **LIC**.

- One **Plot** contains multiple **Sectors** (1 → N).

- One **VC** can control multiple **Sectors** (1 → N).

- One **Plot** can have multiple **Plans** (1 → N).

- Unique constraint on `serial_number` for LIC, Water Pump Controllers, VC, and RM.

- Foreign-key definitions enforce referential integrity between entities.

---

What are the main tasks users need to perform in the app?

(For example: register devices, create plots, define irrigation plans, monitor execution, etc. Feel free to describe it in your own words.)

register devices, create plots, define irrigation plans, monitor execution, see reports

---

Will this app run only on mobile , or should it also support desktop and tablet screens with responsive design?

mobile

---

Will users manually register devices (by typing the serial number), or do you want support for QR code scanning or device auto-discovery as well?

manually register devices

---

How will users create sectors within a plot?

Will they select VC outputs and assign them manually to sectors?

Or will they draw/select sectors on a map with GPS coordinates and polygon shapes?

(Or maybe both?)

Will they select VC outputs and assign them manually to sectors

---

Should the app display real-time execution status? For example: “Pump ON”, “Sector 2 irrigating”, timer countdowns, etc.

yes

---

Should the user be able to view historical reports of past irrigation events, such as:

What sector was irrigated

When it started and ended

Any errors or anomalies

yes

---

Will the app have any form of authentication (login, user profile, PIN code), or will it be entirely open and offline with no login at all?

entirely open and offline with no login at all

---

Should users be able to customize the labels for each component (e.g., rename “Sector 1” to “Tomatoes”) to help them identify parts of their system more easily?

yes

---

Will the app be used only in Portuguese, or should it support multiple languages (with Portuguese as default)?

multiple languages - Portuguese as default

---

Absolutely! Here's a full **UI design prompt** in the same style and structure, tailored for your **Localized Irrigation App**:

---

# Prompt: 💧 IrrigEasy – Localized Irrigation Manager (Mobile UI Design)

Design a _clean, friendly, and offline-first mobile app_ titled **IrrigEasy – Localized Irrigation Manager**, built for small farm owners managing remote-controlled irrigation systems. The app allows users to register devices, organize their land into plots and sectors, create irrigation plans, and monitor execution — all without requiring internet access.

-

## 1. Status Bar (Top)

- **Style**: Standard Android/iOS
- **Safe Area**: Fully respected
- **Theme switcher**: Accessible via side panel or Home page top-right icon

-

## 2. Header Section (on all top-level pages)

- **Left**: Page title (e.g., _Plots_, _Devices_)
  - Font: Nunito – SemiBold – 16 pt
  - Color: Gray-900 (light) / Gray-100 (dark)
- **Right**: Action icon (context-sensitive, e.g., ➕ for "Add Plot")

  - Size: 32×32 px
  - Style: Icon-only, outlined
  - Padding: 12 px

-

## 3. Home (Dashboard)

- **Section 1: Current Status**

  - “No active plan” or “Plot: _Tomato Field_ – Sector 2 running (4m left)”
  - Icon: 🚿 animated ripple effect (optional)
  - Card: rounded (`16px`), with shadow

- **Section 2: Next Plans**

  - Title: “Next Scheduled Plans”
  - Vertical list of cards:
    - Plot name, first sector, start time
    - Background: soft neutral with green accent bar on left

- **Manual Control CTA** (optional)

  - Icon-only button: "⚙ Manual Irrigation"

-

## 4. Plots

- **Plot List**

  - Card per plot: Label, #sectors, Water Pump Controller icon
  - Tap opens Plot Detail
  - Add button (top-right): ➕

- **Plot Detail**

  - Title: Plot name
  - Sector list (with VC output shown)
  - Action: “Create Plan” (primary CTA)

- **Plan Creation Wizard**

  - Step 1: Time & days of week (pill buttons for Mon–Sun)
  - Step 2: Sector order (drag-n-drop list with icons)
  - Step 3: Duration per sector + delay
  - Step 4: Confirm overview
  - Submit: Rounded green CTA button

-

## 5. Devices

- **LICs, VCs, Water Pump Controllers, Reservoir Monitors** – grouped lists

  - Device card:
    - Icon: 🧠 for LIC, 🌊 for Water Pump Controllers, 🌱 for VC, 🏗️ for RM
    - Label, Serial Number, Connected Plots
  - Add Device: Modal form with type, serial number, label

-

## 6. Bottom Navigation Bar

| Tab     | Icon    | State                         |
| ------- | ------- | ----------------------------- |
| Home    | 🏠 Home | Active – filled, green accent |
| Plots   | 🌱 Tree | Inactive – outlined icon      |
| Devices | ⚙ Chip  | Inactive – outlined icon      |

- **Padding**: `safe-area-inset-bottom` respected
- **Icon Style**: Lucide – outlined, stroke-friendly
- **Icon size**: 24×24 px

-

## 🎨 Visual Style Guidelines

| Element            | Style / Hex                                     |
| ------------------ | ----------------------------------------------- |
| Primary Green      | `#4CAF50` (Standard Ag Green)                   |
| Background (light) | `#F9FAFB`                                       |
| Background (dark)  | `#0D1117`                                       |
| Surface            | White / Dark Gray (`#1E1E1E`)                   |
| Accent             | `#22C55E` (Green 400)                           |
| Font               | **Nunito**, fallback: Inter                     |
| Corner Radius      | `16px` (`rounded-xl`)                           |
| Shadow             | `shadow-md` with soft elevation                 |
| Icons              | Lucide, outlined style, consistent stroke width |
| Button Style       | Icon-only where possible, with large tap area   |
| Animations         | Optional: ripple on execution icon, soft fades  |

-

## 🤝 UX Principles

- Built for **offline-first**, **touch-friendly**, and **low-tech** users
- Every screen avoids clutter — **single-focus pages**, **clear CTAs**
- **Progress feedback** on multi-step flows (Plan Creation)
- Manual device registration with **error handling and guidance**

-

## 📐 Component Examples

- **Device Card**

  - Icon | Label | Type | Serial
  - Status dot (optional)
  - Tap to view connections

- **Sector Item**

  - Label + VC Output number
  - Badge-style design (`badge-green-100`)
  - Drag handle (when ordering)

- **Floating Action Button**

  - Only when useful (e.g., add device)
  - Style: `rounded-full`, shadow-lg, green accent

-
