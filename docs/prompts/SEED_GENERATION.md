# Seed Data Generation for Directus Database

Generate seed data and seed data cleanup for the following tables:

- directus_users
- account
- device
- account_device
- account_user
- plan
- plot
- project
- property
- sector
- sector_irrigation_step

## Documentation

Check @/docs/DESCRICAO.md, @/docs/MULTI_ACCOUNT.md and @/docs/DDL.md for having information about how the system works and how to generate the data. It is important to understand the relationships between the tables and generate data that makes sense and observe the order of creation.

## Data requirements:

- Two `directus_users` with first_name, last_name, email, password, role (USER_ROLE_ID), status (active):
  - first user: first_name: "<PERSON><PERSON><PERSON><PERSON>", last_name: "<PERSON>", email: "<EMAIL>", password: "password123"
  - second user: first_name: "<PERSON>", last_name: "<PERSON>", email: "<EMAIL>", password: "password123"
- One `account` for each created user as owner
- 1 property for <PERSON><PERSON><PERSON><PERSON> and 2 for Maria:
  - You generate the properties data
- Each property must have at least 2 projects
- Each project must have a distinct irrigation_pump_controller
- At least one project must have a fertigation_pump_controller
- Each project must have at least 2 plots
- Each plot must have at least 2 sectors
- Each project will have a distinct plan
- Each plan will have step for every plan's plot sectors

## Existing data check

Before generating the seed data, check if any of the users already exist in the `directus_users` table. If they do, abort seeding.

## Script modes

The seed script will operate in two distinct modes:

1. Generate new seed data.
2. Clean up existing seed data.

### Data Cleanup

In the cleanup mode, the script will remove all data generated by the seed script, ensuring a fresh start for the next seeding operation. It will not delete any data that was not created by the seed script, preserving any existing data in the database. And it will not run the data generation if the cleanup mode is enabled.
The deletion of data will be done by deleting any record related directly or indirectly to the two users created in the `directus_users` table. This includes all records in the `account`, `device`, `account_device`, `account_user`, `plan`, `plot`, `project`, `property`, `sector`, and `sector_irrigation_step` tables that are associated with these users. It must be done in the correct order to avoid foreign key constraint violations.

## File

Implement the task in file @/src/seed/index.ts
