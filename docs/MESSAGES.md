# Protocol Buffer Messages

## Incoming Messages (From Server TO LIC Devices)

**Container Message**:

```protobuf
message IncomingPacket {
  uint64 id = 1;
  oneof payload {
    codec.in.config.ConfigPackage config = 2;
    codec.in.devices.DevicesPackage devices = 3;
    codec.in.scheduling.SchedulingPackage scheduling = 4;
    codec.in.device_scheduling.DeviceSchedulingPackage dev_scheduling = 5;
    codec.in.automation.AutomationPackage automation = 6;
    codec.in.control.ControlPackage control = 7;
    codec.in.command.CommandPackage command = 8;
    codec.in.request_info.RequestInfoPackage request_info = 9;
    codec.in.firmware_update.FirmwareUpdatePackage firmware_update = 10;
  }
}
```

## Outgoing Messages (From LIC Devices To Server)

**Container Message**:

```protobuf
message OutgoingPacket {
  uint64 id = 1;
  oneof payload {
    codec.out.info.InfoPackage info = 2;
    codec.out.status.SystemStatusPackage status = 3;
    codec.out.scheduling_report.SchedulingReportPackage scheduling_report = 4;
    codec.out.automation_report.AutomationReportPackage automation_report = 5;
    codec.out.ack.AckPackage ack = 6;
    codec.out.raw.RawPackage raw = 7;
  }
}
```

## Message Exchange Mechanisms

1. **Configuration Updates**: App sends device/scheduling configurations
2. **Status Monitoring**: Devices report operational status and sensor data
3. **Control Commands**: Real-time device control (start/stop irrigation)
4. **Firmware Updates**: OTA firmware update coordination
5. **Acknowledgments**: Confirmation of received and processed messages

---

## Complete Protocol Buffer Message Documentation

### Message Flow Direction

- **Downlink Messages (TO LIC)**: Sent from Server to irrigation devices via MQTT
- **Uplink Messages (FROM LIC)**: Sent from irrigation devices to Server via MQTT

### Downlink Messages (IncomingPacket Container)

All downlink messages are wrapped in the `IncomingPacket` container:

```protobuf
message IncomingPacket {
  uint64 id = 1;                                    // Message timestamp/ID for tracking
  oneof payload {
    codec.in.config.ConfigPackage config = 2;
    codec.in.devices.DevicesPackage devices = 3;
    codec.in.scheduling.SchedulingPackage scheduling = 4;
    codec.in.device_scheduling.DeviceSchedulingPackage dev_scheduling = 5;
    codec.in.automation.AutomationPackage automation = 6;
    codec.in.control.ControlPackage control = 7;
    codec.in.command.CommandPackage command = 8;
    codec.in.request_info.RequestInfoPackage request_info = 9;
    codec.in.firmware_update.FirmwareUpdatePackage firmware_update = 10;
  }
}
```

#### 1. ConfigPackage - System Configuration

**Purpose**: Configure LIC device system settings including WiFi, mesh networking, and irrigation parameters.

```protobuf
message ConfigPackage {
    /**
    * Period, in minutes, between automatic backwash operations.
    */
    int32 backwash_cycle = 1;
    /**
    * Duration, in minutes, of automatic backwash operations.
    */
  int32 backwash_duration = 2;
  /**
  * Delay, in seconds, before automatic backwash operations.
  */
  int32 backwash_delay = 3;
  /**
  * Enable rain gauge sensor for automatic irrigation pause.
  */
  bool raingauge_enabled = 4;
  /**
  * Conversion factor for rain gauge pulses to millimeters.
  */
  int32 raingauge_factor = 5;
  /**
  * Minimum rainfall, in millimeters, to trigger irrigation pause.
  */
  int32 rainfall_limit = 6;
  /**
  * Duration, in minutes, to pause irrigation after rainfall exceeds the limit.
  */
  int32 rainfall_pause_duration = 7;
  /**
  * WiFi network configuration for device connectivity.
  */
  WifiConfig wifi = 8;
  /**
  * Mesh network configuration for device-to-device communication. The server does not include
  * this information in the message. It is set by the factory and burned into the device's
  * firmware.
  */
  MeshConfig mesh = 9;
  /**
  * If true, the device publishes raw packets to MQTT for diagnostics.
  */
  bool publish_raw_data = 10;
  /**
  * Enables debug mode on the device (extra logging/behavior for investigation).
  */
  bool debug = 11;
  /**
  * Enables automatic schedule resumption after interruptions.
  */
  bool enable_schedule_resumption = 12;
  /**
  * Enables automatic sector resumption when fertigation fails.
  */
  bool enable_ferti_resumption = 13;
  /**
  * Maximum number of resumption attempts (0 = unlimited/unspecified).
  */
  int32 max_resumption_attempts = 14;
}

message WifiConfig {
    /**
    * WiFi network SSID for device connectivity.
    */
  string ssid = 1;
  /**
  * WiFi network password for device connectivity.
  */
  string password = 2;
}

message MeshConfig {
  bytes key = 1;                            // Mesh network encryption key
  uint32 channel = 2;                       // Mesh network channel
}
```

#### 2. DevicesPackage - Device Configuration

**Purpose**: Configure mesh network devices and their operational parameters.

```protobuf
message DevicesPackage {
    /**
    * List of device configurations to be applied.
    */
  repeated DevicesData data = 1;
}

/**
 * Configuration for an individual device within the mesh network.
 * Each DevicesData will correspond to a virtual device in an ending mesh device, like a PumpLink or a Reservoir Monitor.
 */
message DevicesData {
    /**
    * Virtual Device unique identifier. Start at 0 and increase by 1 for each new device.
    */
  int32 idx = 1;
  /**
  * Mesh network device ID. This ID identifies the physical device in the mesh network.
  */
  int32 mesh_id = 2;
  /**
  * Virtual device ID in the physical device (used by the PumpLink, for example). This ID identifies the virtual device within the physical device. It is unique within the physical device, but not necessarily unique in the LIC stored devices.
  */
  int32 device_id = 3;
  /**
  * The type of equipment being controlled and/or monitored.
  * Device type: 0=Valve, 1=IrrigationPump, 2=FertigationPump, 3=Backwash, 4=ServicePump, 5=LevelLink (reservoir monitor).
  */
  int32 device_type = 4;
  /*
  * What physical device output corresponds to the virtual device output 1.
  * Output 1 configuration. The meaning of this field depends on the device type.
  * For example, for a Valve, it represents the valve opening/closing; for a pump, it represents the pump ON/OFF. If in CONTINUOUS mode, it represents the output that will be turned on to turn the equipment on and off to turn the equipment off. If in PULSE mode, it represents the output that will be pulsed to turn the equipment on.
  */
  int32 out1 = 5;
  /**
  * What physical device output corresponds to the virtual device output 2.
  * Output 2 configuration. The meaning of this field depends on the device type.
  * For example, for a Valve, it represents the valve opening/closing; for a pump, it represents the pump ON/OFF. If in CONTINUOUS mode, it is not used (set to 0). If in PULSE mode, it represents the output that will be pulsed to turn the equipment off.
  */
  int32 out2 = 6;
  /**
  * What physical device input corresponds to the virtual device input.
  * Input configuration. The meaning of this field depends on the device type.
  * For example, for a Valve, it is not used; for a pump, it can be used to monitor if the pump is running properly. For a reservoir monitor, it is used to monitor the water level.
  */
  int32 input = 7;
  /**
  * Operating mode of the virtual device output. The meaning of this field depends on the device type.
  * For example, for a Valve, it is not used; for a pump, it represents the pump operating mode (CONTINUOUS or PULSE). For a reservoir monitor, it is not used.
  * 0: CONTINUOUS
  * 1: PULSE
  */
  int32 mode = 8;
  /**
  * Sector number for irrigation zones. The meaning of this field depends on the device type.
  * For example, for a Valve, it represents the sector that the valve controls; For other devices, it is not used. In fact it is merely informative and doesn't affect the device operation.
  */
  int32 sector = 9;
  /**
  * Group index for organization. It groups together all virtual devices of a irrigation project.
  */
  int32 group_idx = 10;
  /**
  * Power (e.g., rated power if using an inverter). Optional depending on device.
  */
  int32 power = 11;
  /**
  * Equipment type code (device-internal classification).
  */
  int32 equipment = 12;
}
```

```ts
/**
 * Device type constants for individual devices within mesh devices
 */
export const DEV_TYPES = {
  Valve: 0,
  IrrigationPump: 1,
  Ferti: 2,
  Backwash: 3,
  ServicePump: 4,
  Level: 5,
} as const;
```

##### Pump types:

- IRRIGATION: Water pump for irrigation
- FERTIGATION: Water pump for fertigation
- SERVICE: Water pump for services in general (cleaning, backwash, etc.)

#### 3. SchedulingPackage - Irrigation Scheduling

**Purpose**: Define irrigation schedules with timing and device coordination.

```protobuf
message SchedulingPackage {
  MsgType type = 1;                         // Message content type
  repeated Scheduling scheduling_data = 2;   // List of schedules
  repeated DeviceScheduling device_scheduling_data = 3; // Device-specific steps
}

message Scheduling {
    /**
    * Schedule index. Unique identifier for the schedule.  Start at 0 and increase by 1 for each new device.
    */
  int32 idx = 1;
  /**
  * Start time of the schedule in minutes since midnight.
  */
  int32 start_time = 2;
  /**
  * Days of the week when the schedule is active. Bitmask where bit 0=Sunday, bit 1=Monday, etc.
  */
  int32 days_of_week = 3;
  /**
  * Number of irrigation steps in the schedule, thus, the number of sectors involved.
  * In other words, it is the number of DeviceScheduling entries that have scheduling_idx = idx.
  */
  int32 number_of_steps = 4;
  /**
  * Water pump device index. It is the idx of the virtual device that controls the water pump.
  */
  int32 waterpump_idx = 5;
  /**
  * Working time of the water pump in minutes.
  */
  int32 waterpump_working_time = 6;
  /**
  * Whether fertigation is allowed in this schedule. If 1, ferti_idx must be a valid idx.
  */
  bool  allow_ferti = 7;
  /**
  * Fertigation device index. It is the idx of the virtual device that controls the fertigation pump.
  */
  int32 ferti_idx = 8;
  /**
  * Whether backwash is allowed in this schedule. If 1, backwash_idx must be a valid idx.
  */
  bool  allow_backwash = 9;
  /**
  * Backwash device index. It is the idx of the virtual device that controls the backwash pump.
  */
  int32 backwash_idx = 10;
  /**
  * The group_idx in the DevicesData message that corresponds to this schedule.
  * This is  how the LIC knows which virtual devices belong to this schedule.
  */
  int32 group = 11;
  /**
  * If true, do not repeat the schedule (one-shot schedule).
  */
  bool  once = 12;
}

enum MsgType {
  MSG_NONE = 0;                            // No specific type
  MSG_SCHEDULING_ONLY = 1;                 // Only schedule data
  MSG_DEV_SCHEDULING_ONLY = 2;             // Only device scheduling data
  MSG_SCHEDULING_ALL = 3;                  // Both schedule and device data
}
```

#### 4. DeviceSchedulingPackage - Device-Level Scheduling

**Purpose**: Define detailed device-specific scheduling steps with precise timing. Can be sent independently from the SchedulingPackage.

```protobuf
message DeviceSchedulingPackage {
  repeated DeviceScheduling data = 1;       // List of device scheduling steps
}

message DeviceScheduling {
    /**
    * Device scheduling index. Unique identifier for the device scheduling step. Start at 0 and increase by 1 for each new device.
    */
  int32 idx = 1;
  /**
  * Index of the parent schedule. It is the idx of the Scheduling entry that this device scheduling step belongs to.
  */
  int32 scheduling_idx = 2;
  /**
  * Index of the virtual device that this device scheduling step refers to. It is the idx of the DevicesData entry that this device scheduling step refers to. As of now, it is always a valve.
  */
  int32 device_idx = 3;
  /**
  * Order of execution of this device scheduling step within the parent schedule. The steps are executed in ascending order.
  */
  int32 order = 4;
  /**
  * Duration of the sector operation in minutes.
  * At the schedule start time, the first sector is activated for sector_working_time minutes.
  * Then, the next sector is activated for sector_working_time minutes, and so on.
  Thus, this is the duration that the valve will be open, not relative to the start time of the schedule.
  */
  int32 sector_working_time = 5;
  /**
  * Duration of fertigation in minutes. If 0, fertigation is not performed.
  */
  int32 ferti_working_time = 6;
  /**
  * Delay before starting fertigation in minutes. If 0, fertigation starts immediately after the sector operation.
  */
  int32 ferti_delay = 7;
}
```

#### 5. AutomationPackage - Automated Control Rules

**Purpose**: Configure automated operations of water pumps feeding water to the reservoir based on water level sensor inputs.

```protobuf
message AutomationPackage {
  repeated AutomationData data = 1;         // List of automation rules
}

message AutomationData {
    /**
    * Index of the water level sensor device. It is the idx of the DevicesData entry that represents the water level sensor (device_type 5 (Level)).
    */
  int32 level_idx = 1;
  /**
  * Index of the pump device to be controlled. It is the idx of the DevicesData entry that represents the pump (device_type 4 (ServicePump)).
  */
  int32 pump_idx = 2;
  /**
  * Its value is 6. It needs to be documented.
  */
  int32 mask = 3;
  /**
  * Its value is 0. It needs to be documented.
  */
  int32 value = 4;
  /**
  * Duration of the pump operation in minutes when the automation rule is triggered.
  This is a safety time to avoid the case the reservoir monitor fails to signal that the reservoir is full and the pump keeps running.
  */
  int32 working_time = 5;
}
```

#### 6. ControlPackage - Real-Time Device Control

**Purpose**: Send immediate control commands to specific devices.

```protobuf
message ControlPackage {
    /**
    * Index of the virtual device to control. It is the idx of the DevicesData entry that represents the device.
    */
  int32 idx = 1;
  /**
  * Action to perform on the device.
  */
  MsgAction action = 2;
  /**
  * Value associated with the action (e.g., duration in minutes for turn on/off).
  */
  int32 value = 3;
  /**
  * Optional payload used when sending a package to a device.
  */
  bytes payload = 4;
  /**
  * Activation code used for gated actions (e.g., protected operations).
  */
  int32 activation_code = 5;
}

enum MsgAction {
  MSG_NONE = 0;                            // No action
  MSG_TURN_ON = 1;                         // Turn device on
  MSG_TURN_OFF = 2;                        // Turn device off
  MSG_PACKAGE = 3;                         // Send raw package to device
}
```

#### 7. CommandPackage - System-Wide Commands

**Purpose**: Commands that affect the entire LIC system, such as pausing or resuming scheduling.

```protobuf
message CommandPackage {
    /**
    * Type of command to execute: 1 = pause, 0 = resume
    */
  MsgType type = 1;
  /**
  * Value to be used with the command. For example, if type = MSG_PAUSE, value is the duration of the pause in minutes.
  */
  int32 value = 2;
}

enum MsgType {
  MSG_RESUME = 0;
  MSG_PAUSE = 1;
}

```

#### 8. RequestInfoPackage - Information Request

**Purpose**: Request specific information from LIC device.

```protobuf
message RequestInfoPackage {
  int32 type = 1;                          // Information type requested
}
```

**Information Types**:

- `0`: System status information
- `1`: Device status information
- `2`: Scheduling status information
- `3`: Automation status information

#### 9. FirmwareUpdatePackage - Firmware Update Control

**Purpose**: Initiate firmware update process on LIC device.

```protobuf
message FirmwareUpdatePackage {
  MsgType type = 1;                        // Update target component
  MsgProtocol protocol = 2;                // Download protocol
  int32 activation_code = 3;               // Unique activation code
  int32 version = 4;                       // Target firmware version
}

enum MsgType {
  MSG_ESP = 0;                             // ESP32 firmware update
  MSG_STM = 1;                             // STM32 firmware update
}

enum MsgProtocol {
  MSG_HTTPS = 0;                           // HTTPS download
  MSG_HTTP = 1;                            // HTTP download
}
```

**Field Descriptions**:

- `type`: Target microcontroller for update
- `protocol`: Network protocol for firmware download
- `activation_code`: Security code to authorize update
- `version`: Target firmware version number

### Uplink Messages (OutgoingPacket Container)

All uplink messages are wrapped in the `OutgoingPacket` container:

```protobuf
message OutgoingPacket {
  uint64 id = 1;                                    // Message timestamp/ID
  oneof payload {
    codec.out.info.InfoPackage info = 2;
    codec.out.status.SystemStatusPackage status = 3;
    codec.out.scheduling_report.SchedulingReportPackage scheduling_report = 4;
    codec.out.automation_report.AutomationReportPackage automation_report = 5;
    codec.out.ack.AckPackage ack = 6;
    codec.out.raw.RawPackage raw = 7;
  }
}
```

#### 1. InfoPackage - Device Information

**Purpose**: Report device identification, firmware versions, and synchronization status.

```protobuf
message InfoPackage {
  string codec_id = 1;                     // Device serial number
  uint32 firmware_esp = 2;                 // ESP32 firmware version
  uint32 firmware_mesh = 3;                // Mesh firmware version
  uint32 hardware_version = 4;             // Hardware version
  uint32 resets = 5;                       // Number of system resets
  uint32 scheduling_running = 6;           // Active scheduling count
  uint32 scheduling_paused = 7;            // Scheduling pause state
  /**
  * It is the id of the last DevicesPackage message received by the device. Conveniently, The timestamp is used as the id of the message, thus, we can know the time of configuration.
  */
  uint32 devices_id = 8;                   // Last device configuration ID
  /**
  * It is the id of the last SchedulingPackage message received by the device. Conveniently, The timestamp is used as the id of the message, thus, we can know the time of configuration.
  */
  uint32 scheduling_id = 9;                // Last scheduling configuration ID
  /**
  * It is the id of the last DeviceSchedulingPackage message received by the device. Conveniently, The timestamp is used as the id of the message, thus, we can know the time of configuration.
  */
  uint32 dev_scheduling_id = 10;           // Last device scheduling ID
  /**
  * It is the id of the last AutomationPackage message received by the device. Conveniently, The timestamp is used as the id of the message, thus, we can know the time of configuration.
  */
  uint32 automation_id = 11;               // Last automation configuration ID
  /**
  * It is the id of the last ConfigPackage message received by the device. Conveniently, The timestamp is used as the id of the message, thus, we can know the time of configuration.
  */
  uint32 config_id = 12;                   // Last general configuration ID
  uint32 failed_bitmask = 13;              // System failure bitmask
}
```

#### 2. SystemStatusPackage - Operational Status

**Purpose**: Report current system operational state and sensor readings.

```protobuf
message SystemStatusPackage {
  uint32 resets = 1;              // Número de resets
  uint32 scheduling_running = 2;  // Agendamento em execução
  uint32 scheduling_paused = 3;   // Pausa de agendamento ativa

  oneof has_paused_time {
    uint32 paused_time = 4;       // Minutos desde que a pausa foi ativada
  }

  oneof has_raining {
    uint32 raining = 5;           // Está chovendo (1 = sim)
  }

  oneof has_rainfall {
    uint32 rainfall = 6;          // Chuva acumulada nas últimas 24h
  }
  /**
  * Bitmask showing which devices are synchronized, which means, what devices are communicating with the LIC. Each bit represents the idx of a virtual device as sent in the DevicesPackage message. If the first bit is 1, the device of idx=0 is synchronized, and so on.
  */
  uint64 sync_bitmask = 7;                 // Synchronized devices bitmask
  /**
  * Bitmask showing which devices are currently active. Each bit represents the idx of a virtual device as sent in the DevicesPackage message. If the first bit is 1, the device of idx=0 is active, and so on.
  */
  uint64 on_bitmask = 8;                   // Active devices bitmask
  /**
  * Bitmask showing the state of the input of each device. Each bit represents the idx of a virtual device as sent in the DevicesPackage message. If the first bit is 1, the input of the device of idx=0 is 1, and so on.
  */
  uint64 input_bitmask = 9;                // Device input states bitmask
  /**
  * Bitmask indicating system failures. Each bit represents a different failure. If the first bit is 1, there is a failure in the device of idx=0, and so on.
  */
  uint32 failed_bitmask = 12;              // System failure bitmask
}
```

**Field Descriptions**:

- `scheduling_running`: Number of currently executing schedules
- `scheduling_paused`: 1 if scheduling is paused, 0 if active
- `raining`: Current rain detection status
- `rainfall`: Accumulated rainfall in last 24 hours
- `sync_bitmask`: Bitmask showing which devices are synchronized
- `on_bitmask`: Bitmask showing which devices are currently active
- `input_bitmask`: Bitmask showing device input states
- `failed_bitmask`: Bitmask indicating system failures

#### 3. SchedulingReportPackage - Scheduling Execution Report

**Purpose**: Report irrigation schedule execution status and results.

```protobuf
message SchedulingReportData {
    /**
    * Index of the schedule that was executed. It is the idx of the Scheduling entry that was executed.
    */
  int32  scheduling_idx = 1;       // Índice do agendamento
  /**
  * Timestamp of the start of the schedule. It is the timestamp when the schedule was started.
  */
  uint64 start_time = 2;           // Timestamp do início do agendamento
  /**
  * Timestamp of the end of the schedule. It is the timestamp when the schedule was finished.
  */
  uint64 end_time = 3;             // Timestamp do fim do agendamento
  /**
   * Bitmask showing which sectors were activated. The first 64 bits represent the first 64 sectors, and the next 64 bits represent the next 64 sectors.
   * It is "cumulative", meaning that that once a sector is activated, it will remain activated.
   */
  uint64 sector_bitmask1 = 4;      // Bitmask de setores acionados, mascara com 64bits iniciais
  uint64 sector_bitmask2 = 5;      // Bitmask de setores acionados, mascara com mais 64bits
  /**
   * Bitmask showing which sectors received fertigation. The first 64 bits represent the first 64 sectors, and the next 64 bits represent the next 64 sectors.
   * It is "cumulative", meaning that that once a sector receives fertigation, it will remain activated.
   */
  uint64 ferti_bitmask1 = 6;       // Bitmask da ferti de setores acionados, mascara com 64bits iniciais
  uint64 ferti_bitmask2 = 7;       // Bitmask da ferti de setores acionados, mascara com mais 64bits
  /**
  * Status of the water pump during the schedule. If 1, the water pump was on. If 0, the water pump was off.
  */
  bool   waterpump = 8;            // Estado da bomba de água (1 = ligou, 0 = não ligou)
  /**
  * Timestamp of the start of the backwash, if it occurred. It is the timestamp when the backwash was started.
  */
  uint64 backwash_time = 9;        // Timestamp de início da retrolavagem, se teve retrolavagem
  int32  number_of_sectors = 10;   // Número de setores do agendamento
  /**
  * Whether this schedule makes use of the water pump. If 1, the water pump should be used. If 0, the water pump should not be used.
  */
  bool   had_waterpump = 11;       // Se usou a bomba de agua
  /**
  * Whether this schedule makes use of fertigation. If 1, fertigation should be used. If 0, fertigation should not be used.
  */
  bool   had_ferti = 12;           // Se teve aplicação de fertilizante (1 = sim, 0 = não)
  uint64 time_of_resumption = 13;  // Hora do início da retomada
  int32  resumption_attempts = 14; // Número de tentativas da retomada
  /**
  * Status of the schedule. It can be one of the following:
  * 1: Completed
  */
  int32  status = 15;              // Código de status do agendamento
}

message SchedulingReportPackage {
  repeated SchedulingReportData data = 1;     // Lista de relatórios
}
```

#### 4. AutomationReportPackage - Automation Execution Report

**Purpose**: Report automated control actions triggered by sensor inputs.

```protobuf

message AutomationReportData {
  int32 auto_idx = 1;          // Índice da automação
  uint64 start_time = 2;       // Horário do início
  uint64 restart_time = 3;     // Horário do reinicio, 0 se não ocorreu reinicio
  uint64 end_time = 4;         // Horário do término, 0 se não finalizou
  int32 status = 5;            // Status de execução
}

message AutomationReportPackage {
  repeated AutomationReportData data = 1;  // Relatório da automação
}
```

#### 5. AckPackage - Acknowledgment

**Purpose**: Acknowledge receipt and processing of downlink messages.

```protobuf
message AckPackage {
  uint32 package = 1;                      // Acknowledged message type
  uint32 value = 2;                        // Acknowledgment value/status
}
```

**Field Descriptions**:

- `package`: Type of message being acknowledged
- `value`: Acknowledgment status or result code

#### 6. RawPackage - Raw Data

Purpose: Send raw packets and telemetry from the LIC to the server for diagnostics or vendor-specific features.

```protobuf
message RawPackage {
  int32 idx = 1;             // Device index
  int32 equipment = 2;       // Equipment type (internal classification)
  int32 type = 3;            // Raw packet type
  bytes payload = 4;         // Raw payload bytes
}
```
