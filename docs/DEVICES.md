# The PumpLink Device

The PumpLink hardware electronic device is a flexible hardware solution designed to control various types of pumps and valves. It has 6 physical outputs that can be configured to control different types of equipments.

The PumpLink firmware works by managing "Virtual Devices". Virtual Device is an abstraction that represents one or two physical outputs of the PumpLink device. A Virtual Device can be configured to control a pump, a valve, or a sensor.

## PumpLink Virtual Device Operation

When the user wants to control a pump, a valve, or a sensor, they do it by starting a "Virtual Device". In the moment the PumpLink receives the packet to start a virtual device, it turns on the outputs and starts monitoring the input (if configured). If the input fails, the PumpLink turns off the outputs. If the input succeeds, the PumpLink keeps the outputs on for the configured amount of time.

These are the parameters that start a virtual device:

- Given:

```ts
type ByteNumber = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7;
type OutputNumber = 1 | 2 | 3 | 4 | 5 | 6;
type InputNumber = 1 | 2 | 3;
type OutputMode = "PULSE" | "CONTINUOUS";
```

- Starts a virtual device with the following parameters:

```ts
export interface PumpLinkTurnDeviceOnCommand {
  type: "turn_device_on";
  device_id: ByteNumber;
  outputs: {
    on: OutputNumber;
    off: OutputNumber;
  };
  monitored_input: InputNumber;
  mode: {
    turn_off_on_input_failure: boolean;
    output_mode: OutputMode;
  };
  working_time: number;
}
```

- Stops a virtual device by sending its `device_id`.

```ts
export interface PumpLinkTurnDeviceOffCommand {
  type: "turn_device_off";
  device_id: ByteNumber;
}
```

- Stops a output directly by sending its `output_number`.

```ts
export interface PumpLinkTurnOutputOffCommand {
  type: "turn_output_off";
  device_id: ByteNumber;
  output: OutputNumber;
  mode: OutputMode;
}
```

## OutputMode

The `OutputMode` parameter controls how the PumpLink will turn on/off the outputs. There are two modes: `PULSE` and `CONTINUOUS`.

- `PULSE`: The PumpLink will emit a electrical pulse in the output specified in the `outputs.on` parameter. This is useful to control solenoid valves. It will turn off the output after the `working_time` has elapsed by emitting a pulse in the `outputs.off` output.

- `CONTINUOUS`: The PumpLink will turn on the output specified in the `outputs.on` parameter keeping it on until the `working_time` has elapsed.

So, when operating in `PULSE` mode, the virtual device uses two outputs: one to turn on the equipment and another to turn it off. When operating in `CONTINUOUS` mode, the virtual device uses only one output.

## PumpLink as a mesh device

When operating as a mesh device, the PumpLink is controlled by the LIC (Localized Irrigation Controller), also know as CODEK. The LIC sends commands to the PumpLink to turn on and off the virtual devices.

# The CODEK

The CODEK is the main controller of the irrigation system. It is responsible for managing the PumpLink and other mesh devices and controlling the virtual devices.
It is capable of scheduling a set of devices in a group.
A group defines a set of devices used in an irrigation project.
It will be composed of:

- A pumplink virtual device to control the irrigation pump.
- A pumplink virtual device to control the fertigation pump (optional).
- A pumplink virtual device to control the backwash pump (optional).
- A set of ValveLink virtual devices (which in fact are PumpLink devices, it is just a marketing thing) to control the valves.

The CODEK is also capable of controlling devices that are not part of a group. These devices are called "service devices". They are not part of a irrigation project and are used for diverse tasks.

In fact, the CODEK is capable of controlling any device in the mesh network, as long as it is a mesh PumpLink virtual device.
One can send a command to turn on (or off) a virtual device in any PumpLink in the mesh network for a specific amount of time.

Besides irrigation projects, the CODEK is also capable of controlling Reservoir Levels. Internally, it is called an "Automation". An automation is composed of a "Reservoir Monitor" virtual device, which monitors the water level in a reservoir, and a "Service Pump" virtual device, which controls a pump that refills the reservoir.

Apart from controlling the devices, the CODEK is also capable of monitoring the status of the devices and reporting it back to the cloud via MQTT. It also receives commands and configuration updates from the cloud via MQTT.

The CODEK will report the following information:

- Which devices are on and off.
- Which schedules are running and their status.
- Which automations are running and their status.

We configure the CODEK by sending a set of device definitions. Each device definition will correspond to a virtual device in a PumpLink.

This is a example of a set of device definitions:

```ts
[
  {
    idx: 0,
    mode: 0,
    out1: 1,
    out2: 0,
    input: 0,
    power: 0,
    sector: 0,
    mesh_id: 1237,
    device_id: 0,
    equipment: 0,
    group_idx: 1,
    device_type: 1,
  },
  {
    idx: 1,
    mode: 0,
    out1: 2,
    out2: 0,
    input: 0,
    power: 0,
    sector: 0,
    mesh_id: 1237,
    device_id: 1,
    equipment: 0,
    group_idx: 1,
    device_type: 2,
  },
  {
    idx: 2,
    mode: 0,
    out1: 3,
    out2: 0,
    input: 0,
    power: 0,
    sector: 0,
    mesh_id: 1237,
    device_id: 2,
    equipment: 0,
    group_idx: 1,
    device_type: 3,
  },
  {
    idx: 3,
    mode: 1,
    out1: 1,
    out2: 2,
    input: 0,
    power: 0,
    sector: 1,
    mesh_id: 1252,
    device_id: 0,
    equipment: 0,
    group_idx: 1,
    device_type: 0,
  },
  {
    idx: 4,
    mode: 1,
    out1: 3,
    out2: 4,
    input: 0,
    power: 0,
    sector: 2,
    mesh_id: 1252,
    device_id: 1,
    equipment: 0,
    group_idx: 1,
    device_type: 0,
  },
  {
    idx: 5,
    mode: 1,
    out1: 1,
    out2: 2,
    input: 0,
    power: 0,
    sector: 1,
    mesh_id: 1247,
    device_id: 0,
    equipment: 0,
    group_idx: 1,
    device_type: 0,
  },
  {
    idx: 6,
    mode: 1,
    out1: 3,
    out2: 4,
    input: 0,
    power: 0,
    sector: 2,
    mesh_id: 1247,
    device_id: 1,
    equipment: 0,
    group_idx: 1,
    device_type: 0,
  },
  {
    idx: 7,
    mode: 3,
    out1: 1,
    out2: 2,
    input: 1,
    power: 0,
    sector: 0,
    mesh_id: 1261,
    device_id: 0,
    equipment: 0,
    group_idx: 2,
    device_type: 4,
  },
  {
    idx: 8,
    mode: 0,
    out1: 0,
    out2: 0,
    input: 0,
    power: 0,
    sector: 0,
    mesh_id: 1236,
    device_id: 0,
    equipment: 0,
    group_idx: 2,
    device_type: 5,
  },
];
```

These are the parameters that define a CODEK device:

- idx: A unique identifier for the device. It is used to identify the device in the LIC. It must be unique within the LIC.
- mesh_id: The mesh_id of the PumpLink device. It is used to identify the PumpLink device in the LIC.
- device_id: The device_id of the virtual device in the PumpLink. It is used to identify the virtual device in the PumpLink.
- device_type: The type of the virtual device. It is used to configure the virtual device in the PumpLink (more on this below).
- out1: The first output of the virtual device. It is the `outputs.on` parameter of the PumpLink virtual device.
- out2: The second output of the virtual device. It is the `outputs.off` parameter of the PumpLink virtual device. It will be 0 if the virtual device does not have a second output.
- input: The input of the virtual device. It is the `monitored_input` parameter of the PumpLink virtual device. It will be 0 if the virtual device does not monitor an input.
- mode: The mode of the virtual device. It is the `mode.output_mode` parameter of the PumpLink virtual device. It will be 0 for CONTINUOUS and 1 for PULSE.
- sector: The sector of the virtual device. It is not used by the PumpLink. And currently ignored by the LIC.
- group_idx: The group of the virtual device. It is not used by the PumpLink. The LIC uses it to group devices together.
- power: If the virtual device is controlling a equipment with a frequency inverter, this field is used to configure output power.
- equipment: Not used yet.

## The Device Type Field

The `device_type` field is used to configure the virtual device in the PumpLink. It is an enumeration that defines the type of the virtual device. The possible values are:

```ts
/**
 * Device type constants for individual devices within mesh devices
 */
export const DEV_TYPES = {
  Valve: 0,
  IrrigationPump: 1,
  Ferti: 2,
  Backwash: 3,
  ServicePump: 4,
  Level: 5,
} as const;
```

Value 5, Level, does not correspond to a PumpLink virtual device. It is used to let the LIC know that it is a Reservoir Monitor (RM) device, which does not control any outputs but monitors an input.
The other values are all PumpLink based virtual devices.

# The ValveLink

The ValveLink is a PumpLink device that is used to control valves. It is configured in the same way as a PumpLink device. It is a PumpLink with 8 outputs and operating in `PULSE` mode, thus, it has 4 virtual devices.

# Reservoir Monitor

The Reservoir Monitor is a device that monitors the water level in a reservoir. It is not a PumpLink device. It is a standalone device that communicates with the LIC to report the water level. It does not have any outputs. It has a single input that is connected to a water level sensor.
