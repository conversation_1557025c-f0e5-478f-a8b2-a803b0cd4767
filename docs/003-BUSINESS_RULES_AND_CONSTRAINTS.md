# Business Rules

## Device - Property Association (PropertyDevice in `docs/001-ENTITIES.md`)

- A device can be associated with only one property at a time.
- A property can have multiple devices associated with it.
- When a device is associated with a property, it must be disassociated from any previously associated property, i.e., it must be "available".
- When creating a device, this is the flow:

```code
if (device exists) {
    if (device is associated with a property) {
        throw error;
        // TODO: Check if this is the correct behavior. Maybe we should disassociate the device from the property and then associate it with the new property.
    } else {
        associate device with property;
    }
} else {
    create device;
    associate device with property;
}
```

### Update device data

It is not allowed to update the data (serial number and model) of a device. Even if the screen shows "Update Device", what actually happens is the creation of a new device and the association of this new device with the property. The old device is disassociated from the property and is no longer used.
