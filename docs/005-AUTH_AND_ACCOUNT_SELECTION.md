### **Authentication and Account Selection Flow Document**

**Date:** June 6, 2025
**Version:** 1.0
**Status:** Final

---

#### **1. Objective**

This document details the process flow for user authentication and account selection in an application. The flow describes the logic from the user's entry point into the system to the final loading of selected account data, ensuring that access is granted securely and contextually. The process was modeled based on the provided data flow diagram.

---

#### **2. Flow Overview**

The process begins when a user accesses the application. The system first checks if the user already has an authenticated session. If not, they are directed to a login page. After successful authentication, the system loads the accounts to which the user has permission. The logic then branches to handle different scenarios: absence of accounts, existence of a single account, or multiple accounts. Finally, after proper selection or assignment of the account, its specific data is loaded for use in the application.

---

#### **3. Flow Steps Details**

The process can be divided into the following main stages:

##### **3.1. Start and Authentication Verification**

1.  **Flow Start:** The process is triggered.
2.  **Authentication Verification (`AUTHENTICATED?`):** The system evaluates if there is an active and valid user session.
    - **IF YES:** The flow advances directly to Step 3.3 (Account Loading and Verification).
    - **IF NO:** The flow advances to Step 3.2 (Login Process).

##### **3.2. Login Process (For Non-Authenticated Users)**

1.  **Redirection:** The user is redirected to the authentication login route.
2.  **Login Page Interaction:** The user enters their credentials (email and password) on the `LoginPage`.
3.  **Login Validation (`LOGIN OK?`):** After submission, the system validates the credentials.
    - **IF YES (success):** The user is considered authenticated, and the flow advances to Step 3.3.
    - **IF NO (failure):** The system displays an error message (`SHOW ERROR`) and the user remains on the `LoginPage` for a new attempt.

##### **3.3. Account Loading and Verification**

1.  **Loading Allowed Accounts:** With the user authenticated, the system performs a query to obtain the list of all accounts to which they have access.
2.  **Permission Verification (`HAS ALLOWED ACCOUNT?`):** The system checks if the previous query returned at least one account.
    - **IF YES:** The flow advances to Step 3.5 to handle selection.
    - **IF NO:** The flow advances to Step 3.4 (Scenario: No Allowed Account).

##### **3.4. Scenario: No Allowed Account**

1.  **Redirection:** The user is directed to the "no account" route (`/no-account`).
2.  **`NoAccountPage` Interaction:** The page informs that no accounts are available and presents the "Create Account" option.
3.  **Account Creation Validation (`ACCOUNT CREATED?`):** If the user attempts to create an account, the system validates the result.
    - **IF YES (success):** The new account is created and the flow advances to Step 3.6.1 (Current Account Assignment).
    - **IF NO (failure):** An error message is displayed, and the user remains on the `NoAccountPage`.

##### **3.5. Scenario: Allowed Accounts and Selection**

1.  **Account Quantity Analysis (`ONLY ONE ACCOUNT?`):**
    - **IF YES:** Having only one account, there is no need for choice. The flow advances directly to Step 3.6.1 (Current Account Assignment).
    - **IF NO:** Having multiple accounts, the system performs an additional verification.
2.  **Previous Account Verification (`HAS PREVIOUS CURRENT ACCOUNT?`):** The system checks if the user had already selected an account in a previous session (stored locally, for example).
    - **IF YES:** The previously selected account is used. The flow advances to Step 3.6.1.
    - **IF NO:** The user needs to make a choice. They are redirected to the selection route (`/select-account`).
3.  **`AccountSelectPage` Interaction:** The page displays the list of available accounts. When clicking on one of them, the selection is recorded and the flow advances to Step 3.6.1.

##### **3.6. Flow Conclusion and Data Loading**

1.  **Current Account Assignment (`ASSIGN CURRENT ACCOUNT`):** At this point, an account has already been defined (either by being the only one, by previous selection, by user choice, or by recent creation). The system formalizes this account as the "active account" for the session.
2.  **Current Account Data Loading (`LOAD CURRENT ACCOUNT DATA`):** The system uses the active account identifier to load all relevant information, permissions, and data for the user interface.
3.  **Flow End:** The process is completed. The user is authenticated and with the context of a specific account loaded, ready to use the application.

---

#### **4. Interface Components (Pages)**

The flow references three main user interfaces:

- **`LoginPage`:** Standard login screen that requests `email` and `password`. Contains a button to submit authentication.
- **`NoAccountPage`:** Screen displayed when an authenticated user has no associated accounts. Presents an informative message and a button to start the account creation flow.
- **`AccountSelectPage`:** Screen displayed when a user has access to multiple accounts, but none was pre-selected. Presents a list of accounts for the user to choose from.

---

```mermaid
flowchart TD
%% --- Start and Authentication Verification ---
A(START) --> B{AUTHENTICATED?};
B -- NO --> C[REDIRECT /auth/login];
B -- YES --> G[LOAD ALLOWED ACCOUNTS];

    %% --- Login Subflow ---
    C --> LoginPage;
    subgraph LoginPage
        D[Input: email];
        E[Input: password];
        F((LOGIN));
    end

    LoginPage -- CLICK --> H{LOGIN OK?};
    H -- YES --> G;
    H -- NO --> I[SHOW ERROR];
    I -- REMAINS --> LoginPage;

    %% --- Main Flow after Authentication ---
    G --> J{HAS ALLOWED ACCOUNT?};
    J -- YES --> K{ONLY ONE ACCOUNT?};
    J -- NO --> L[REDIRECT /no-account];

    %% --- No Account / Creation Subflow ---
    L --> NoAccountPage;
    subgraph NoAccountPage
        M["Msg: No accounts available"];
        N((CREATE ACCOUNT));
    end

    NoAccountPage -- CLICK --> O{ACCOUNT CREATED?};
    O -- YES --> S[ASSIGN CURRENT ACCOUNT];
    O -- NO --> P[SHOW ERROR];
    P -- REMAINS --> NoAccountPage;

    %% --- Account Selection Flow ---
    K -- YES --> S;
    K -- NO --> Q{HAS PREVIOUS CURRENT ACCOUNT?};
    Q -- YES --> S;
    Q -- NO --> R[REDIRECT /select-account];

    R --> SelectAccountPage;
    subgraph AccountSelectPage
        direction LR
        T["SELECT AN ACCOUNT"];
        U((Account A));
        V((Account B));
    end
    SelectAccountPage -- CLICK ACCOUNT --> S;

    %% --- Final Steps ---
    S --> W[LOAD CURRENT ACCOUNT DATA];
    W --> Z((END));

    %% --- Styles (Optional, improves visualization) ---
    style LoginPage fill:#f9f,stroke:#333,stroke-width:2px
    style NoAccountPage fill:#ffc,stroke:#333,stroke-width:2px
    style SelectAccountPage fill:#cff,stroke:#333,stroke-width:2px
```
