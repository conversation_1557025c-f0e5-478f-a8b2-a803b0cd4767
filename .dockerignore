# Ignore everything by default
**

# Allow package.json and bun.lock anywhere
!package.json
!bun.lock


# Allow the packages folder and its contents
!packages
!packages/**


# But within packages, ignore these files/folders
packages/**/.env
packages/**/.env*
packages/**/dist
packages/**/dist/**
packages/**/out
packages/**/out/**
packages/**/bin
packages/**/bin/**
packages/**/*.log
packages/**/.repomix
packages/**/.crush
packages/**/.local
packages/**/node_modules
packages/**/augaugment-kv-store
packages/**/.env*
!packages/**/.env.example
