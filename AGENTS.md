# AGENTS.md

This document gives agents a high-level map of the repository: where things live, the core technology stacks, and where to find the authoritative guidelines and task flow. It is a quick reference, not a full specification—follow the links to the source docs when in doubt.

## Project Structure

- docs
  - General project docs. See guidelines below for mandatory engineering standards and starting points.
- packages/app
  - Web application built with <PERSON><PERSON> + React (TypeScript), Tailwind CSS v4, <PERSON><PERSON><PERSON> (routing), <PERSON><PERSON> (state), and Directus SDK for data access. Build/dev driven by Bun scripts.
- packages/directus
  - Backend powered by Directus (Docker Compose). Uses PostgreSQL + PostGIS, Knex migrations and TypeScript tooling executed with Bun. Contains database migrations, scripts, and test helpers.
- packages/mqtt-integration
  - MQTT worker/service written in TypeScript on Bun, using `mqtt` v5 and `postgres` (porsager/postgres) to process device messages. Depends on the shared `proto` package for message schemas.
- packages/protobuf
  - Protocol Buffers definitions compiled via `protobufjs-cli` into an ES module bundle with TypeScript declarations. Built with Bun; exported as the `proto` workspace package.
- packages/codec-http-integration
  - TypeScript library that requests codec device reports over HTTP and decodes responses with the shared `proto` bundle. Built with <PERSON>un for ESM and CJS consumption (browser and Node runtimes).
- tasks
  - Task workflow utilities and records.
  - `tasks/INSTRUCTIONS.md`: canonical instructions describing how tasks are authored, executed, and tracked (statuses, branching, naming, etc.).
  - `tasks/cli.ts`: Bun-based CLI to scaffold and manage task files (e.g., create `TASKS_YYMMDD_NN.md`, ensure unique names, and apply the standard template).
- engineering/irriganet
  - Android (Gradle) project for the IrrigaNet tooling/application (Kotlin/Java). Contains `app` module and Gradle configs.
- engineering/codec_wifi
  - ESP-IDF firmware project (CMake) for the Wi‑Fi codec device; includes `components/`, `main/`, partition table, and `sdkconfig`.

## Guidelines

The engineering guidelines are mandatory. Start at `docs/guidelines/00-INDEX.md` for the consolidated topics and links. Every contribution must follow these standards.

- Start here: `docs/guidelines/00-INDEX.md`
  - Overview and links to the project’s foundations, frontend, backend, workflows, and references.

Subfolders under `docs/guidelines`:

- backend
  - Standards for DB and integration work: migrations and seed processes, Directus backend testing, MQTT integration testing, and database DDL change procedures.
- frontend
  - Frontend engineering: React conventions, API/service layer, Jotai state patterns, TailwindCSS guidance, PWA/performance practices, and UX/UI guidelines. Also includes “foundations” docs (architecture, tech stack, design system, coding conventions).
- references
  - Canonical references such as design tokens extracted from `design.json` and cross-reference documents that map concepts and modules.
- workflows
  - How we work: tasks file usage, branching and commit policy, and related operational conventions.

Restated: the guidelines are mandatory for all changes. `docs/guidelines/00-INDEX.md` is the entry point and must be read before starting work.

## Tasks

- `tasks/INSTRUCTIONS.md`
  - Explains the task list format, statuses, daily naming conventions, branching rules, and the expected execution algorithm (how to pick the next task or subtask).
- `tasks/cli.ts`
  - A Bun-powered CLI that scaffolds and manages task files. It creates properly named daily files (e.g., `TASKS_YYMMDD_NN.md`), applies the standard template, and prevents collisions by incrementing counters.

---

Purpose: This file is a quick reference for agents. It offers an overview and pointers to the authoritative docs rather than duplicating them. Always defer to the linked documentation for details.

