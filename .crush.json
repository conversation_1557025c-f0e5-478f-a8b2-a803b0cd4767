{"$schema": "https://charm.land/crush.json", "permissions": {"allowed_tools": ["view", "ls", "grep", "edit", "mcp_context7_get-library-doc", "bash", "multiedit"]}, "lsp": {"typescript": {"command": "typescript-language-server", "args": ["--st<PERSON>"]}}, "mcp": {"fetch": {"disabled": false, "command": "node", "args": ["/home/<USER>/Documents/Cline/MCP/fetch-mcp/dist/index.js"], "env": {"HOME": "/home/<USER>"}, "type": "stdio"}, "context7": {"command": "docker", "args": ["run", "-i", "--rm", "mcp/context7"], "type": "stdio"}}}