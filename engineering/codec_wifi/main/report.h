#ifndef REPORT_H
#define REPORT_H

#include "defines.h"
#include "mesh.h"

#include <stdint.h>
#include <string.h>
#include <ctype.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include <unistd.h>
#include <time.h>
#include "mqtt_client.h"

#define MAX_JSON_SCHEDULINGS    4

typedef enum {
    S_REP_SCHED_START,
    S_REP_SCHED_END,
    S_REP_SCHED_PUMP_FAILED,
} report_scheduling_t;

typedef enum {
    S_REP_AUTO_START,
    S_REP_AUTO_END,
    S_REP_AUTO_FAILED,
} report_auto_t;

typedef enum {
    S_REP_INFO_ALL,
    S_REP_INFO_BASE,
} report_info_t;

void report_init();
void report_scheduling_start(int idx, time_t started_time, unsigned int n_sectors, bool had_waterpump, bool had_ferti);
void report_scheduling_print(int idx); 
void report_automation_start(int idx, time_t started_time);
void report_scheduling_retrive(int idx, time_t started_time);
void report_automation_end(int idx, time_t end_time);
size_t report_scheduling(uint8_t **buffer);
size_t report_automation(uint8_t **buffer);
size_t report_status(uint8_t **buffer);
size_t report_info(uint8_t **buffer, uint8_t type);

#endif // REPORT_H
