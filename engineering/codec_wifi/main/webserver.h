#ifndef WEBSERVER_H
#define WEBSERVER_H

#include "defines.h"
#include "mesh.h"
#include "memory.h"
#include "report.h"
#include "protocol.h"

#include <stdint.h>
#include <string.h>
#include <ctype.h>
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_netif.h"
#include "esp_http_server.h"
#include <lwip/sockets.h>
#include <lwip/netdb.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <time.h>
#include <inttypes.h>


httpd_handle_t webserver_init(void);
void set_cors_headers(httpd_req_t *req);
esp_err_t options_handler(httpd_req_t *req);
esp_err_t check_basic_auth(httpd_req_t *req);
esp_err_t report_post_handler(httpd_req_t *req);
esp_err_t data_post_handler(httpd_req_t *req);

#endif // WEBSERVER_H