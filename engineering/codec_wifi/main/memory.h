#ifndef MEMORY_H
#define MEMORY_H

#include "defines.h"
#include "mesh.h"

#include <stdint.h>
#include <string.h>
#include <ctype.h>
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_check.h"
#include "nvs_flash.h"
#include <driver/gpio.h> 
#include "driver/i2c_master.h"
#include "nvs.h"

#define EEPROM_ADDR        0x50        // 1010 000x (A2-A0 em GND)
#define EEPROM_SIZE        4096        // 32 kbit → 4 kB
#define EEPROM_PAGE_SIZE   32
#define EEPROM_TAG         "24LC32A"

esp_err_t memory_nvs_init();
void      memory_nvs_stats();
esp_err_t memory_save_system_info();
esp_err_t memory_load_system_info();
esp_err_t memory_save_wifi_credentials(char* ssid, char* password);
esp_err_t memory_load_wifi_credentials(char* ssid, size_t ssid_size, char* password, size_t password_size);
esp_err_t memory_save_devices();
esp_err_t memory_load_devices();
esp_err_t memory_save_scheduling();
esp_err_t memory_load_scheduling();
esp_err_t memory_save_device_scheduling();
esp_err_t memory_load_device_scheduling();
esp_err_t memory_save_automation();
esp_err_t memory_load_automation();
esp_err_t memory_save_config();
esp_err_t memory_load_config();
esp_err_t memory_save_scheduling_result();
esp_err_t memory_load_scheduling_result();
esp_err_t memory_eeprom_init(void);
esp_err_t memory_eeprom_wait_ready(uint32_t max_wait_ms);
esp_err_t memory_eeprom_write_page(uint16_t mem, const uint8_t *data, size_t len);
esp_err_t memory_eeprom_read(uint16_t mem, uint8_t *out, size_t len);
esp_err_t memory_eeprom_write(uint16_t mem, const uint8_t *d, size_t n);

#endif // MEMORY_H