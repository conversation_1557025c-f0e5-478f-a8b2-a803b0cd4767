#include "main.h"
#include "memory.h"
#include "mqtt.h"
#include "webserver.h"
#include "protocol.h"
#include "mesh.h"
#include "report.h"
#include "ds1339.h"
#include "esp_task_wdt.h"
#include "esp_timer.h"
#include "esp_check.h"
#include "driver/i2c_master.h"
#include "incoming_packet.pb-c.h"

static const char *TAG_M = "MAIN";

#define ANSI_COLOR_RED     "\x1b[31m"
#define ANSI_COLOR_GREEN   "\x1b[32m"
#define ANSI_COLOR_YELLOW  "\x1b[33m"
#define ANSI_COLOR_BLUE    "\x1b[34m"
#define ANSI_COLOR_MAGENTA "\x1b[35m"
#define ANSI_COLOR_CYAN    "\x1b[36m"
#define ANSI_COLOR_RESET   "\x1b[0m"
#define ESP_INTR_FLAG_DEFAULT 0

#define MEMORY_DEBUG true
#define REPORT_STAGE_DELAY 2
#define REPORT_CYCLE_PERIOD 60

uint8_t mac[6];
static const int UART_BUF_SIZE = 1024;
char sta_ssid[MAX_STA_SSID_SIZE] = {0};
char sta_pass[MAX_STA_PASS_SIZE] = {0};
int rainfall_count[24] = {0};
int current_rainfall_hour = 0;
int rainfall_last24h = 0;
bool its_raining = 0;
bool anticipate_report = false;
bool sched_anticipate_report = false;
time_t now, last_gpio_interruption = 0;
time_t last_its_raining = 0;

static QueueHandle_t uart_queue;
StaticSemaphore_t data_mutex_cb;
SemaphoreHandle_t data_mutex;
TaskHandle_t main_task_handle;

static QueueHandle_t gpio_evt_queue = NULL;
extern esp_mqtt_client_handle_t mqtt_client;
static esp_timer_handle_t reconnect_timer;
int wifi_retry_count = 0;

i2c_master_bus_handle_t i2c_bus = NULL;
static SemaphoreHandle_t i2c_mutex = NULL;
static QueueHandle_t rtc_evt_queue = NULL;

typedef enum {
    STAGE_SCHEDULING = 0,  /* primeiro: agendamentos   */
    STAGE_AUTOMATION       /* segundo: automações      */
  , STAGE_STATUS           /* terceiro: status geral   */
  , STAGE_DONE             /* ciclo concluído          */
} report_stage_t;

static void i2c_scan(void);
static void rtc_update_task(void *pvParameter);

static void IRAM_ATTR gpio_isr_handler(void* arg) {
    uint32_t gpio_num = (uint32_t) arg;
    int level = gpio_get_level(gpio_num);
    if (level == 0) {
        xQueueSendFromISR(gpio_evt_queue, &gpio_num, NULL);
    }
}

void reconnect_timer_callback(void* arg) {
    ESP_LOGI(TAG_M, "Timer expired, trying to reconnect...");
    esp_wifi_connect();
}

void start_reconnect_timer() {
    const esp_timer_create_args_t timer_args = {
        .callback = &reconnect_timer_callback,
        .name = "reconnect_timer"
    };
    esp_timer_create(&timer_args, &reconnect_timer);
    esp_timer_start_once(reconnect_timer, 60 * 1000000); // 60s
}

void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data) {
    if (event_base == WIFI_EVENT) {
        if (event_id == WIFI_EVENT_STA_START) {
            esp_wifi_connect();

        } else if (event_id == WIFI_EVENT_STA_DISCONNECTED) {
            if (mqtt_connected){
                mqtt_connected = false; 
                esp_mqtt_client_stop(mqtt_client);  // parar MQTT e tentar reconectar
            } 
            if (wifi_retry_count++ < MAX_RETRY) {
                esp_wifi_connect();
                ESP_LOGI(TAG_M, "Retry %d/%d", wifi_retry_count, MAX_RETRY);
            } else {
                ESP_LOGW(TAG_M, "Max retries reached. Waiting 60s.");
                wifi_retry_count = 0;
                start_reconnect_timer();
            }

        } else if (event_id == WIFI_EVENT_AP_START) {
            ESP_LOGI(TAG_M, "Soft-AP started");
        }
    } else if (event_base == IP_EVENT) {
        if (event_id == IP_EVENT_STA_GOT_IP) {
            ip_event_got_ip_t *evt = (ip_event_got_ip_t *)event_data;
            ESP_LOGI(TAG_M, "Got IP: " IPSTR, IP2STR(&evt->ip_info.ip));
            wifi_retry_count = 0;
            if (reconnect_timer) esp_timer_stop(reconnect_timer);
            if (!mqtt_connected) {
                esp_mqtt_client_start(mqtt_client);
            }
        } else if (event_id == IP_EVENT_STA_LOST_IP) {
            ESP_LOGW(TAG_M, "Lost IP lease");
            if (mqtt_connected){
                mqtt_connected = false; 
                esp_mqtt_client_stop(mqtt_client); // ainda ligado ao AP, mas sem DHCP, parar MQTT
            }
            esp_wifi_disconnect();
            esp_wifi_connect();
        }
    }
}

void wifi_init_softap_sta() {
    esp_netif_init();
    esp_event_loop_create_default();

    esp_netif_t *ap_netif = esp_netif_create_default_wifi_ap();
    
    esp_netif_ip_info_t ip_info;
    esp_netif_set_ip4_addr(&ip_info.ip, 192, 168, 4, 1);
    esp_netif_set_ip4_addr(&ip_info.gw, 192, 168, 4, 1);
    esp_netif_set_ip4_addr(&ip_info.netmask, 255, 255, 255, 0);
    esp_netif_dhcps_stop(ap_netif); // Stop DHCP server
    esp_netif_set_ip_info(ap_netif, &ip_info); // Assign static IP
    esp_netif_dhcps_start(ap_netif); // Restart DHCP server

    //esp_netif_create_default_wifi_ap();
    esp_netif_create_default_wifi_sta();
    
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    esp_wifi_init(&cfg);
    
    esp_event_handler_instance_t instance_any_id;
    esp_event_handler_instance_t instance_got_ip;
    esp_event_handler_instance_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL, &instance_any_id);
    esp_event_handler_instance_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &wifi_event_handler, NULL, &instance_got_ip);
    
    wifi_config_t wifi_config_sta = {
        .sta = {
            .ssid = {0},
            .password = {0}
        },
    };
    
    strncpy((char*)wifi_config_sta.sta.ssid, sta_ssid, sizeof(wifi_config_sta.sta.ssid));
    strncpy((char*)wifi_config_sta.sta.password, sta_pass, sizeof(wifi_config_sta.sta.password));

    char wifi_ap_ssid[20];
    sprintf(wifi_ap_ssid, "Codec_%02X%02X%02X%02X%02X%02X", mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

    wifi_config_t wifi_ap_config = {
        .ap = {
            .ssid_len = strlen(wifi_ap_ssid),
            .password = WIFI_AP_PASS,
            .max_connection = MAX_STA_CONN,
            .authmode = WIFI_AUTH_WPA2_PSK,
            .channel = 6 // Escolha um canal
        },
    };

    strncpy((char *)wifi_ap_config.ap.ssid, wifi_ap_ssid, sizeof(wifi_ap_config.ap.ssid));
    
    esp_wifi_set_mode(WIFI_MODE_APSTA);
    esp_wifi_set_config(WIFI_IF_STA, &wifi_config_sta);
    esp_wifi_set_config(WIFI_IF_AP, &wifi_ap_config);
    esp_wifi_start();
}

void uart_init(void)
{
    const uart_config_t uart_config = {
        .baud_rate = 115200,
        .data_bits = UART_DATA_8_BITS,
        .parity    = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE
    };
    ESP_ERROR_CHECK(uart_param_config(UART_NUM, &uart_config));
    ESP_ERROR_CHECK(uart_set_pin(UART_NUM, TXD_PIN, RXD_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE));
    ESP_ERROR_CHECK(uart_driver_install(UART_NUM, UART_BUF_SIZE, UART_BUF_SIZE, 10, &uart_queue, 0));

    uart_enable_pattern_det_baud_intr(UART_NUM, '}', 1, 9, 0, 0);
    uart_pattern_queue_reset(UART_NUM, 20);
}

static void sntp_time_sync_notification(struct timeval *tv)
{
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;

    xTaskNotifyFromISR(main_task_handle, EVENT_UPDATE_RTC, eSetBits, &xHigherPriorityTaskWoken);

    portYIELD_FROM_ISR( xHigherPriorityTaskWoken );
}

static inline void initialize_sntp(void) 
{
    sntp_setoperatingmode(SNTP_OPMODE_POLL);
    sntp_setservername(0, "pool.ntp.org");
    sntp_set_time_sync_notification_cb(sntp_time_sync_notification);
    sntp_init();

    setenv("TZ", "BRT3BRST,M10.3.0/0,M2.3.0/0", 1);
    tzset();
}

static inline void get_date_by_rtc(void)
{
    struct tm rtc_tm;

    if (ds1339_get_time(&rtc_tm) != ESP_OK) {
        ESP_LOGW(TAG_M, "Could not read RTC");
        return;
    }

    rtc_tm.tm_isdst = -1;
    time_t epoch = mktime(&rtc_tm);

    struct timeval tv = { .tv_sec = epoch, .tv_usec = 0 };
    settimeofday(&tv, NULL);

    ESP_LOGI(TAG_M, "Clock adjusted by RTC: %s", asctime(&rtc_tm));
}

struct s_mesh_device_scheduling *get_device_scheduling(int shc_idx, int current_step) {
    for (int j = 0; j < MAX_MESH_DEVICE_SCHEDULING; j++) {
        if (mesh_device_scheduling[j].shc_idx == shc_idx &&
            mesh_device_scheduling[j].step == current_step) {
            return &mesh_device_scheduling[j];
        }
    }
    return NULL; // Se não encontrar
}

static inline bool group_conflict(int sch_idx)
{
    if (mesh_scheduling_state[sch_idx].working)
        return false;

    uint8_t g = mesh_scheduling[sch_idx].group;

    for (int k = 0; k < schedule_count; k++) {
        if (k == sch_idx)   // ignora a própria entrada
            continue;

        if (mesh_scheduling_state[k].working && mesh_scheduling[k].group == g)
        {
            return true; // encontrou conflito
        }
    }
    return false; // nenhum conflito encontrado
}

static inline void mqtt_report()
{
    static time_t cycle_start = 0;
    static time_t stage_ts = 0;
    static uint8_t stage = 0;

    if (cycle_start == 0) {
        cycle_start = now;
        return;                        
    }

    if (difftime(now, cycle_start) >= REPORT_CYCLE_PERIOD || anticipate_report) {
        cycle_start = now;
        stage = STAGE_SCHEDULING;
        stage_ts = anticipate_report ? now : now - 2;  // se não for antecipar, força a primeira etapa imediatamente
        anticipate_report = false; // reseta o flag de antecipação
    }

    // reportar relatórios de agendamento
    if ((stage == 0  && difftime(now, stage_ts) >= REPORT_STAGE_DELAY) || sched_anticipate_report) {
        uint8_t *buffer = NULL;
        int len = report_scheduling(&buffer);
        if (buffer != NULL && len > 0) {
            if (mqtt_client != NULL) {
                ESP_LOGI(TAG_M, "MQTT Report scheduling len: %d", len);
                int res = esp_mqtt_client_enqueue(mqtt_client, publish_topic_report, (const char*)buffer, len, 1, false, true);
                if (res < 0) {
                    ESP_LOGW(TAG_M, "MQTT enqueue failed: %d", res);
                } else {
                    ESP_LOGI(TAG_M, "MQTT enqueued msg_id=%d", res);
                }

            }
        }

        stage = STAGE_AUTOMATION;
        stage_ts = now;
        sched_anticipate_report = false; // reseta o flag de antecipação
        free(buffer);
    }
    
    // reportar relatórios de automação
    if (stage == 1 && difftime(now, stage_ts) >= REPORT_STAGE_DELAY) {
        uint8_t *buffer = NULL;
        int len = report_automation(&buffer);
        if (buffer != NULL && len > 0) {
            if (mqtt_client != NULL) {
                ESP_LOGI(TAG_M, "MQTT Report automation len: %d", len);
                int res = esp_mqtt_client_enqueue(mqtt_client, publish_topic_report, (const char*)buffer, len, 1, false, true);
                if (res < 0) {
                    ESP_LOGW(TAG_M, "MQTT enqueue failed: %d", res);
                } else {
                    ESP_LOGI(TAG_M, "MQTT enqueued msg_id=%d", res);
                }
            }
        }
        stage = STAGE_STATUS;
        stage_ts = now;
        free(buffer);
    }
    
    // reportar status
    if (stage == 2 && difftime(now, stage_ts) >= REPORT_STAGE_DELAY) {
        uint8_t *buffer = NULL;
        int len = report_status(&buffer);
        if (buffer != NULL && len > 0) {
            if (mqtt_client != NULL) {
                ESP_LOGI(TAG_M, "MQTT Report status len: %d", len);
                int res = esp_mqtt_client_enqueue(mqtt_client, publish_topic_report, (const char*)buffer, len, 1, false, true);
                if (res < 0) {
                    ESP_LOGW(TAG_M, "MQTT enqueue failed: %d", res);
                } else {
                    ESP_LOGI(TAG_M, "MQTT enqueued msg_id=%d", res);
                }
            }
        }
        stage = STAGE_DONE;
        free(buffer);
    }
}

static inline void update_rainfall_last24h()
{
    rainfall_last24h = 0;
    for(int i=0; i<24; i++){
        rainfall_last24h += rainfall_count[i];
    }
    rainfall_last24h /= mesh_config.raingauge_factor;
}

static void reset_scheduling_flags(int shc_idx)
{
    mesh_scheduling_state[shc_idx].current_step               = 0;
    mesh_scheduling_state[shc_idx].total_working_time_secs    = 0;
    mesh_scheduling_state[shc_idx].init_info                  = false;
    mesh_scheduling_state[shc_idx].started                    = false;
    mesh_scheduling_state[shc_idx].working                    = false;
    mesh_scheduling_state[shc_idx].must_resumption            = false;
    mesh_scheduling_state[shc_idx].checked_whether_resumption = false;
    mesh_scheduling_state[shc_idx].resumption_overlap         = false;
    mesh_scheduling_state[shc_idx].initial_resumption_setup   = false;   
    mesh_scheduling_state[shc_idx].pump_working_time_if_resumption = 0;
    //mesh_scheduling_state[shc_idx].last_dev_idx            = -1;

    for (int j = 0; j < MAX_MESH_DEVICE_SCHEDULING; ++j) {
        if (mesh_device_scheduling[j].shc_idx != shc_idx)
            continue;

        int dev_idx = mesh_device_scheduling[j].dev_idx;
        if (!is_valid_dev_idx(dev_idx))
            continue;

        mesh_device_state[dev_idx].triggered           = false;
        mesh_device_state[dev_idx].working_time_secs   = 0;
        mesh_device_scheduling_state[dev_idx].tried    = false;
        mesh_device_scheduling_state[dev_idx].executed = false;
        mesh_device_scheduling_state[dev_idx].canceled = false;
        mesh_device_scheduling_state[dev_idx].info     = false;
    }

    if(mesh_scheduling[shc_idx].waterpump_working_time > 0){
        int pump_idx = mesh_scheduling[shc_idx].waterpump_dev_idx;
        if (is_valid_dev_idx(pump_idx))
            mesh_device_scheduling_state[pump_idx].canceled = false;
    }

    if(mesh_scheduling[shc_idx].allow_ferti){
        int ferti_idx = mesh_scheduling[shc_idx].ferti_dev_idx;
        if (is_valid_dev_idx(ferti_idx))
            mesh_device_state[ferti_idx].triggered = false;
    }
}

void reset_all_scheduling_flags(void){
    memset(mesh_scheduling_state,        0, sizeof(mesh_scheduling_state));
    memset(mesh_device_scheduling_state, 0, sizeof(mesh_device_scheduling_state));

    for (int i = 0; i < device_count && i < MAX_MESH_DEVICES; ++i) {
        mesh_device_state[i].triggered         = false;
        mesh_device_state[i].working_time_secs = 0;
        mesh_device_state[i].last_appoint      = 0;
    }
}

static void handle_uart_exception(void)
{
    uart_flush_input(UART_NUM);             // descarta FIFO + ring-buffer
    uart_pattern_queue_reset(UART_NUM, 20);
    xQueueReset(uart_queue);
    ESP_LOGW(TAG_M, "UART Exception: Flush and Clear");
}

static inline time_t get_today_midnight(time_t now)
{
    struct tm tm_midnight;
    localtime_r(&now, &tm_midnight);
    tm_midnight.tm_hour = 0;
    tm_midnight.tm_min  = 0;
    tm_midnight.tm_sec  = 0;
    return mktime(&tm_midnight);
}

bool is_scheduling_running(void)
{
    for (int i = 0; i < MAX_MESH_SCHEDULING; ++i) {
        if (mesh_scheduling_state[i].started ||
            mesh_scheduling_state[i].working)
            return true; // há agendamento ativo
    }
    return false;
}

static inline time_t get_scheduling_start_time(int i, time_t seconds_of_day, int weekday)
{
    unsigned int start_time = mesh_scheduling[i].start_time;

    if(mesh_config.enable_schedule_resumption == true) {
        if (mesh_scheduling_state[i].must_resumption == false) {
            if(mesh_scheduling_state[i].checked_whether_resumption == false && mesh_scheduling_state[i].started == false && mesh_scheduling_state[i].working == false && 
                mesh_scheduling_result[i].status > SD_RS_SUCCESS && mesh_scheduling_state[i].resumption_overlap == false && mesh_scheduling_state[i].resumption_attempts < mesh_config.max_resumption_attempts) {
                unsigned int working_time = 0;
                int pending = 0;
                int last_step = 0;
                bool  overlap = false;
                for (int j = 0; j < schedules_per_device && j < MAX_MESH_DEVICE_SCHEDULING; j++) {
                    if (mesh_device_scheduling[j].shc_idx == i){
                        unsigned int step = mesh_device_scheduling[j].step;
                        if (step >= mesh_scheduling[i].number_of_steps) continue; 

                        if(((1ULL << step) & mesh_scheduling_result[i].sectors) == 0ULL) { // Se o setor ainda não foi executado
                            working_time += mesh_device_scheduling[j].sector_working_time;
                            pending += 1;
                            if(step > last_step){
                                last_step = step; // Encontra o ultimo setor pendente
                            }
                        }
                    }
                }

                uint8_t today_mask = (uint8_t)(1u << weekday); // Mascara para verifica se hoje é dia do agendamento
                uint8_t tomorrow_mask = (uint8_t)(1u << ((weekday + 1) % 7)); // Mascara para verifica se amanhã tem agendamento
                for (int j = 0; j < schedule_count && j < MAX_MESH_SCHEDULING; j++) {
                    if(i != j) {
                        int today = (mesh_scheduling[j].days_of_week & today_mask) != 0;
                        int tomorrow = (mesh_scheduling[j].days_of_week & tomorrow_mask) != 0;
                        if((today && mesh_scheduling[j].start_time > (seconds_of_day+RESUMPTION_DELAY) && (unsigned int)(mesh_scheduling[j].start_time - (seconds_of_day+RESUMPTION_DELAY)) < (working_time*60)) || 
                           (tomorrow && (unsigned int)((mesh_scheduling[j].start_time+86400) - (seconds_of_day+RESUMPTION_DELAY)) < (working_time*60))){ // Verifica se irá ocorrer sobreposição no horário do agendamento
                            overlap = true;
                            mesh_scheduling_state[i].resumption_overlap = true;
                            break;
                        }
                    }
                }

                if(pending > 0){
                    if(overlap == false) {
                        start_time = seconds_of_day + RESUMPTION_DELAY;
                        mesh_scheduling_state[i].resumption_step_count = pending;
                        mesh_scheduling_state[i].last_resumption_step = last_step;
                        mesh_scheduling_state[i].must_resumption = true;
                        mesh_scheduling_state[i].resumption_start_time = start_time;
                        mesh_scheduling_state[i].resumption_attempts += 1;
                        mesh_scheduling_state[i].pump_working_time_if_resumption = working_time;
                        mesh_scheduling_state[i].initial_resumption_setup = true;
                        ESP_LOGW(TAG_M, "[SCH %d] Scheduled Resumption: start time %d, working time %d, number of steps %d, last step %d, attempts %d, sectors %llu, ferti %llu", 
                            i, start_time, working_time, pending, last_step, mesh_scheduling_state[i].resumption_attempts, mesh_scheduling_result[i].sectors, mesh_scheduling_result[i].ferti);
                    } else {
                        ESP_LOGW(TAG_M, "[SCH %d] Scheduled Resumption is not possible due to overlap", i);
                    }
                }
                mesh_scheduling_state[i].checked_whether_resumption = true;
            }
        } else {
            start_time = mesh_scheduling_state[i].resumption_start_time;
        }
    } 

    return start_time;
}

static inline time_t get_waterpump_working_time(int i)
{
    int working_time = mesh_scheduling[i].waterpump_working_time;
    if(mesh_config.enable_schedule_resumption == true && mesh_scheduling_state[i].must_resumption == true) {
        working_time = mesh_scheduling_state[i].pump_working_time_if_resumption;
    }
    return working_time;
}

bool is_schuduling_done(int i)
{
    int steps = mesh_scheduling[i].number_of_steps;
    const uint64_t mask    = ones_mask(steps);
    const uint64_t sectors = mesh_scheduling_result[i].sectors;

    return (sectors & mask) == mask;
}

void init_schuduling_result(int i, int started_time)
{
    mesh_scheduling_result[i].started_time = started_time;

    // Limpa o resultado de agendamento anterior
    mesh_scheduling_result[i].sectors = 0;
    mesh_scheduling_result[i].ferti = 0;
    mesh_scheduling_state[i].resumption_attempts = 0;
}

int get_scheluling_step(int i)
{
    int step = mesh_scheduling_state[i].current_step;
    if(mesh_config.enable_schedule_resumption == true && mesh_scheduling_state[i].must_resumption == true) {
        if(mesh_scheduling_state[i].started == false && mesh_scheduling_state[i].working == false) {
            for (int j = 0; j < mesh_scheduling[i].number_of_steps; j++) {
                if (((1ULL << j) & mesh_scheduling_result[i].sectors) == 0ULL){
                    step = j; // Encontra o primeiro setor pendente
                    break;
                }
            }
            mesh_scheduling_state[i].current_step = step;
        }
    }

    return step;
}

int get_last_step(int i)
{
    int step = mesh_scheduling[i].number_of_steps-1;
    if(mesh_config.enable_schedule_resumption == true && mesh_scheduling_state[i].must_resumption == true) {
        step = mesh_scheduling_state[i].last_resumption_step;
    }
    return step;
}

void goto_next_step(int i)
{
    if(mesh_config.enable_schedule_resumption == true && mesh_scheduling_state[i].must_resumption == true) {
        int current_step = mesh_scheduling_state[i].current_step;
        if(mesh_scheduling_state[i].started == true || mesh_scheduling_state[i].working == true) {
            for (int j = 0; j < mesh_scheduling[i].number_of_steps; j++) {
                if (((1ULL << j) & mesh_scheduling_result[i].sectors) == 0ULL && j > current_step) { // Se o setor ainda não foi executado
                    current_step = j; // Encontra o proximo setor pendente
                    break;
                }
            }
        }
        mesh_scheduling_state[i].current_step = current_step;
    } else {
        mesh_scheduling_state[i].current_step += 1;
    }
}

void main_task(void *pvParameters) {  
    time_t last_scheduling_time = 0, last_info_sch_time = 0;
    time_t wdt_time = 0, memory_debug_time = 0, fail_warning_time = 0;
    double task_max_duration = 0;
    double task_duration = 0;
    int io_num;
    char mac_strig[64];
    char uart_data[UART_BUF_SIZE];
    pause_scheduling = false;
    pause_scheduling_time = 0;
    pause_scheduling_timer = 12*3600; // 12 horas

    sprintf(mac_strig, "%02X%02X%02X%02X%02X%02X",mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
    ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "Codec ID: %s" ANSI_COLOR_CYAN, mac_strig);
    ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "ESP FW: %d, MESH FW: %d, HARD VER: %d" ANSI_COLOR_CYAN, ESP_FW_VERSION, MESH_FW_VERSION, HARDWARE_VERSION);

    vTaskDelay(pdMS_TO_TICKS(200)); // Aguarda 100ms para garantir que o sistema esteja pronto
    get_date_by_rtc();
    initialize_sntp();
    memory_save_system_info(); // Atualiza o número de reinicializações
    report_init();
    esp_task_wdt_add(NULL); // Adiciona a tarefa atual ao watchdog

    uart_event_t event;
    time(&now);

    while (1) {
        int64_t task_start_loop = esp_timer_get_time();
        time(&now);

        struct tm timeinfo;
        localtime_r(&now, &timeinfo);
        int weekday = timeinfo.tm_wday;
        time_t today_midnight = get_today_midnight(now);
        time_t current_ts = now;

        // Verifica a cada 1 segundo
        if (difftime(now, last_scheduling_time) >= 1) {
            last_scheduling_time = now;

            // Percorre todos os agendamentos
            for (int i = 0; i < schedule_count; i++) {

                // Se já ouver um agendamento em andamento no mesmo grupo, aguarda para executar o novo agendamento
                if (group_conflict(i)){
                    if (difftime(now, mesh_scheduling_state[i].last_log_info) >= TIMER_20S) {
                        mesh_scheduling_state[i].last_log_info = now;
                        ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "[SCH %d]: Group Conflict" ANSI_COLOR_RESET, i);
                    }
                    continue;
                }

                if(!(mesh_scheduling[i].days_of_week & (1 << weekday)) && !mesh_scheduling_state[i].working){ // Se o dia da semana não está ativo e se o agendamento não foi iniciado, pula para o próximo agendamento
                    continue;
                }

                if (mesh_scheduling_state[i].working == false) {
                    mesh_scheduling_state[i].reference_day = today_midnight;
                }else{
                    if(mesh_scheduling[i].waterpump_working_time > 0){
                        int elapsed_time = difftime(now, mesh_scheduling_state[i].reference_day + mesh_scheduling[i].start_time);
                        if(elapsed_time > mesh_scheduling[i].waterpump_working_time*60 + TIMER_4H) {
                            reset_scheduling_flags(i);
                            memory_save_scheduling_result();
                            ESP_LOGE(TAG_M, "[SCH %d]: Worktime exceeded 4h — elapsed: %d", i, elapsed_time);
                        }
                    }
                }

                time_t reference_day_ts  = mesh_scheduling_state[i].reference_day;
                time_t schedule_ts = reference_day_ts + get_scheduling_start_time(i, current_ts-reference_day_ts, weekday);
                time_t sector_schedule_ts = schedule_ts + mesh_scheduling_state[i].total_working_time_secs;

                int step = get_scheluling_step(i);
                struct s_mesh_device_scheduling *current_dev_scheduling = get_device_scheduling(i, step);
                if (!current_dev_scheduling) {
                    if (difftime(now, mesh_scheduling_state[i].fail_devsch_log) >= TIMER_20S) {
                        mesh_scheduling_state[i].fail_devsch_log = now;
                        ESP_LOGE(TAG_M, "[SCH %d]: Failed to get device schedule", i);
                    }
                    continue;
                }

                time_t ferti_schedule_ts = sector_schedule_ts + current_dev_scheduling->ferti_delay*60;
                int sector_diff = difftime(current_ts, sector_schedule_ts);
                int ferti_diff = difftime(current_ts, ferti_schedule_ts);
                int sector_working_time = current_dev_scheduling->sector_working_time;
                int ferti_working_time = current_dev_scheduling->ferti_working_time;
                int max_sector_diff = sector_working_time*60 > TIMER_3MIN ? TIMER_3MIN : sector_working_time*60;
                int max_pump_diff = mesh_scheduling[i].waterpump_working_time*60 > TIMER_10MIN ? TIMER_10MIN : mesh_scheduling[i].waterpump_working_time*60;
                int max_ferti_diff = max_sector_diff;
                int max_backwash_diff = max_sector_diff;
                int k = current_dev_scheduling->dev_idx;
                int p = mesh_scheduling[i].waterpump_dev_idx;

                if (!is_valid_dev_idx(k)) { // Verifica se o índice do dispositivo é inválido
                    if (difftime(now, fail_warning_time) >= TIMER_20S) {
                        fail_warning_time = now;
                        ESP_LOGW(TAG_M, "[SCH %d]: Invalid index: dev_idx=%d", i, k);
                    }
                    continue;
                }

                if(mesh_scheduling_state[i].init_info == false) {
                    ESP_LOGI(TAG_M, ANSI_COLOR_BLUE "Weekday: %d - %d" ANSI_COLOR_RESET, mesh_scheduling[i].days_of_week, (1 << weekday));
                    ESP_LOGI(TAG_M, ANSI_COLOR_BLUE "[SCH %d]: curr_secs: %d, schd_secs: %d, schd_fert: %d" ANSI_COLOR_RESET, i, (int)current_ts, (int)sector_schedule_ts, (int)ferti_schedule_ts);
                    mesh_scheduling_state[i].init_info = true;
                }

                if (difftime(now, mesh_scheduling_state[i].last_log_info) >= TIMER_20S) {
                    mesh_scheduling_state[i].last_log_info = now;
                    int total_elapsed_time = difftime(now, schedule_ts)/60;
                    ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "[SCH %d]: step: %d, elap_time: %d, ref_day: %d, schd_secs: %d, curr_secs: %d, curr_wt: %d, t_wt: %d, s_diff: %d, f_diff: %d, pause %d" ANSI_COLOR_RESET, 
                        i, step, total_elapsed_time, (int)reference_day_ts, (int)sector_schedule_ts, (int)current_ts, mesh_device_state[k].working_time_secs, mesh_scheduling_state[i].total_working_time_secs, sector_diff, ferti_diff, pause_scheduling);      
                }

                // Somente se diff estiver entre 0 e max_diff
                if (sector_diff > 0 && sector_diff <= max_sector_diff && !pause_scheduling) {
                    if (mesh_device_state[k].triggered == false) { // Se o dispositivo não foi ativado
                        // Se o dispositivo estiver desligado, envia o comando
                        if (mesh_device_state[k].device_status == 0) {
                            if ((now - mesh_device_state[k].last_cmd) > TIMER_30S) { // 30 segundos
                                char mesh_pkg[64];
                                int additional_time = 2;
                                int last_step = get_last_step(i);
                                if(step == last_step){
                                    additional_time = 5; // Se for o último passo, adiciona 5 minutos
                                }
                                int sector_working_time_fix = sector_working_time+additional_time; // Soma o tempo adicional
                                mesh_pumplink_turn_on_encode(mesh_devices[k].meshid, mesh_devices[k].device_id, mesh_devices[k].out1, mesh_devices[k].out2, mesh_devices[k].input, mesh_devices[k].mode, sector_working_time_fix, mesh_pkg);
                                uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));
                                
                                ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Sending sector %d command to dev_idx=%d meshid=%d device_id=%d (working_time=%d+%d) step=%d" ANSI_COLOR_RESET,
                                        i, mesh_devices[k].sector, k,
                                        mesh_devices[k].meshid,
                                        mesh_devices[k].device_id,
                                        sector_working_time, additional_time, step);

                                // Se for o primeiro passo e o agendamento não foi iniciado, reporta o início do agendamento
                                if(mesh_scheduling_state[i].started == false && mesh_scheduling_state[i].must_resumption == false){
                                    report_scheduling_start(i, now, mesh_scheduling[i].number_of_steps, mesh_scheduling[i].waterpump_working_time > 0, mesh_scheduling[i].allow_ferti);
                                    init_schuduling_result(i, now);
                                } else if(mesh_scheduling_state[i].initial_resumption_setup) {
                                    mesh_scheduling_state[i].initial_resumption_setup = false;
                                    report_scheduling_retrive(i, now); // Reporta a retomada do agendamento
                                }

                                mesh_device_state[k].last_cmd = now;
                                mesh_device_state[k].working_time_secs = sector_working_time*60;
                                mesh_scheduling_state[i].started = true;      // Incio do agendamento, limpa no final da programação
                                mesh_device_scheduling_state[k].tried = true; // Limpa no final de trabalho de cada setor
                                mesh_device_scheduling_state[k].executed = false;
                                mesh_device_scheduling_state[k].canceled = false;
                                if(mesh_scheduling[i].allow_ferti){ // garantir que o flag de fertilização esteja limpa
                                    int f = mesh_scheduling[i].ferti_dev_idx;
                                    mesh_device_state[f].triggered = false;
                                }
                            }
                        } else { // Se o dispositivo estiver ligado
                            if(mesh_device_state[k].triggered == false) {
                                mesh_device_state[k].triggered = true; // Marcamos que está ativo
                                mesh_device_state[k].triggered_time = now;
                                
                                mesh_scheduling_report[i].sector_l1 |= (1 << step);
                                mesh_scheduling_result[i].sectors |= (1ULL << step); // Marca o setor como executado
                                mesh_scheduling_result[i].status = SD_RS_RUNNING; // Marca o agendamento como em execução
                                sched_anticipate_report = true;

                                memory_save_scheduling_result(); // Salva o resultado do agendamento na memória

                                if(mesh_scheduling_state[i].working){ // Se o agendamento já estiver em andamento, desliga a sobreposição
                                    unsigned int last_dev_idx = mesh_scheduling_state[i].last_dev_idx;
                                    if (is_valid_dev_idx(last_dev_idx)) { // Verifica se o índice do último dispositivo é válido
                                        if(mesh_device_state[last_dev_idx].device_status != 0 && mesh_device_scheduling_state[last_dev_idx].canceled == false){ // Desliga a sobreposição se o último dispositivo estiver ligado e o atual também estiver ligado
                                            struct s_mesh_control new_control = {
                                                .dev_idx = last_dev_idx, 
                                                .action = CONTROL_TURN_OFF, 
                                                .working_time = 0, 
                                                .enabled = true, 
                                                .start_time = now, 
                                                .timeout = TIMER_2MIN 
                                            };
                                            mesh_device_scheduling_state[last_dev_idx].canceled = true; // Perminte apenas um único cancelamento por agendamento
                                            mesh_insert_mesh_control(new_control);
                                            ESP_LOGW(TAG_M, "[SCH %d] Disable Overlay Command dev_idx=%d", i, last_dev_idx);
                                        }
                                    }
                                }
                                mesh_scheduling_state[i].working = true; // Mantem até o final do agendamento
                                mesh_device_scheduling_state[k].executed = true; // Limpa no final de trabalho de cada setor, mas garante que o setor foi executado
                                ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Device dev_idx=%d is already on" ANSI_COLOR_RESET, i, k);
                            }
                        }
                    }
                } else {
                    if(mesh_scheduling_state[i].started){
                        int sector_elapsed_time = difftime(now, mesh_device_state[k].last_cmd); // Tempo decorrido desde a última ativação
                        int last_step = get_last_step(i);
                        if(step >= last_step || pause_scheduling){ // Se for o último passo 
                            if((mesh_device_scheduling_state[k].executed == false || sector_elapsed_time >= mesh_device_state[k].working_time_secs) || pause_scheduling){
                                if(mesh_device_scheduling_state[k].tried == true){
                                    ESP_LOGW(TAG_M, "[SCH %d] Programming completed", i);      
                                    mesh_scheduling_state[i].current_step = 0;
                                    mesh_scheduling_state[i].total_working_time_secs = 0;
                                    mesh_scheduling_state[i].init_info = false;
                                    mesh_scheduling_state[i].started = false;
                                    mesh_scheduling_state[i].last_dev_idx = k;    // Guarda o último dispositivo acionado                                       
                                    mesh_device_scheduling_state[k].tried = false;
                                    mesh_device_scheduling_state[k].executed = false;
                                    mesh_device_scheduling_state[k].info = false;
                                }

                                if((mesh_scheduling[i].waterpump_working_time == 0 || mesh_scheduling_state[i].working == false) ){ // Se não houver bomba de água, desliga o agendamento
                                    ESP_LOGW(TAG_M, "[SCH %d] Scheduling finished", i);
                                    mesh_scheduling_state[i].working = false;
                                    mesh_scheduling_state[i].end_time = now;
                                    mesh_scheduling_report[i].status = S_REP_SCHED_END;
                                    if(is_schuduling_done(i)){
                                        mesh_scheduling_result[i].status = SD_RS_SUCCESS; // Marca o agendamento como concluído com sucesso
                                    } else {
                                        mesh_scheduling_result[i].status = SD_RS_FAIL; // Marca o agendamento como falha
                                    }
                                    ESP_LOGI(TAG_M, "[SCH %d] Scheduling result: sectors %llu, ferti %llu, status %d", i, mesh_scheduling_result[i].sectors, mesh_scheduling_result[i].ferti, mesh_scheduling_result[i].status);
                                    memory_save_scheduling_result(); // Salva o resultado do agendamento na memória
                                    reset_scheduling_flags(i); // Reseta os flags do agendamento
                                    report_scheduling_print(i);
                                }
                            }
                        } else {
                            if (mesh_device_scheduling_state[k].tried == true && (mesh_device_scheduling_state[k].executed == false || sector_elapsed_time >= mesh_device_state[k].working_time_secs)) { // Se o dispositivo estiver desligado ou se já passou o tempo de trabalho
                                                    
                                goto_next_step(i); //mesh_scheduling_state[i].current_step++;
                                mesh_scheduling_state[i].last_dev_idx = k;
                                mesh_scheduling_state[i].total_working_time_secs = now - schedule_ts;
                                ESP_LOGW(TAG_M, "[SCH %d] Moving to next step: %d, total_working_time: %d", i, mesh_scheduling_state[i].current_step, mesh_scheduling_state[i].total_working_time_secs);
                                
                                if(mesh_scheduling[i].allow_ferti){
                                    int f = mesh_scheduling[i].ferti_dev_idx; // Limpa o status do dispositivo da ferti para a próxima execução
                                    mesh_device_state[f].device_status = 0;
                                }

                                mesh_device_scheduling_state[k].tried = false;
                                mesh_device_scheduling_state[k].executed = false;
                            }
                        }

                        if (mesh_device_state[k].triggered == true) {
                            mesh_device_state[k].triggered = false; // Para enviar o comando apenas detro da janela de execução
                            ESP_LOGW(TAG_M, "[SCH %d] Sector %d dev_idx=%d triggered is false", i, mesh_devices[k].sector, k);
                        }

                    }

                }

                if(mesh_scheduling_state[i].working){
                    // Bomba de água
                    if(max_pump_diff > 0){
                        if(difftime(now, schedule_ts) < max_pump_diff && !pause_scheduling){ // Se estiver dentro de 10 minutos do horário de início
                            if (!mesh_device_state[p].triggered && mesh_device_state[k].device_status != 0) {
                                if (mesh_device_state[p].device_status == 0) {
                                    if (difftime(now, mesh_device_state[p].last_cmd) >= TIMER_30S) {
                                        char mesh_pkg[64];
                                        int waterpump_working_time = get_waterpump_working_time(i);
                                        mesh_pumplink_turn_on_encode(mesh_devices[p].meshid, mesh_devices[p].device_id, mesh_devices[p].out1, mesh_devices[p].out2, mesh_devices[p].input, mesh_devices[p].mode, waterpump_working_time, mesh_pkg);
                                        uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));
                                        
                                        ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Sending waterpump command to dev_idx=%d meshid=%d device_id=%d (working_time=%d) step=%d" ANSI_COLOR_RESET,
                                            i, p,
                                            mesh_devices[p].meshid,
                                            mesh_devices[p].device_id,
                                            waterpump_working_time, step);

                                        mesh_device_state[p].last_cmd = now;
                                        mesh_device_state[p].working_time_secs = waterpump_working_time*60;
                                        mesh_device_scheduling_state[p].canceled = false; // Será cancelado se o agendamento for pausado
                                        if(mesh_scheduling[i].allow_backwash){
                                            int b = mesh_scheduling[i].backwash_dev_idx;
                                            mesh_device_backwash_state[b].attempt = false;
                                            mesh_device_backwash_state[b].next_attempt_time = 0;
                                        }
                                    }
                                } else { // Se o dispositivo estiver ligado
                                    if(mesh_device_state[p].triggered == false){
                                        mesh_device_state[p].triggered = true; 
                                        mesh_device_state[p].triggered_time = now;
                                        mesh_scheduling_report[i].waterpump = true;
                                        sched_anticipate_report = true;
                                        ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] WaterPump dev_idx=%d is already on" ANSI_COLOR_RESET, i, p);
                                    }
                                }
                            }
                        }else{
                            bool last_dev_turned_off = false;
                            if(!mesh_scheduling_state[i].started){ // Se started é falso é porque já tentou acionar o ultimo setor
                                unsigned int last_dev_idx = mesh_scheduling_state[i].last_dev_idx;
                                if (is_valid_dev_idx(last_dev_idx)) { // Verifica se o índice do último dispositivo é válido
                                    last_dev_turned_off = (mesh_scheduling_state[i].working && mesh_device_state[last_dev_idx].device_status == 0);
                                }
                            }
                            if(mesh_device_state[p].device_status != 0 && mesh_device_scheduling_state[p].canceled == false && (last_dev_turned_off || pause_scheduling)){ // Desligar a bomba de água se o ultimo setor já estiver desligado ou se o agendamento estiver pausado
                                struct s_mesh_control new_control = {
                                    .dev_idx = p,
                                    .action = CONTROL_TURN_OFF,
                                    .working_time = 0,
                                    .enabled = true,
                                    .start_time = now,
                                    .timeout = TIMER_5MIN
                                };
                                mesh_device_scheduling_state[p].canceled = true; // Perminte apenas um único cancelamento por agendamento
                                mesh_insert_mesh_control(new_control);
                                if(last_dev_turned_off) 
                                    ESP_LOGW(TAG_M, "[SCH %d] Last Sector is Turned Off dev_idx=%d", i, mesh_scheduling_state[i].last_dev_idx);
                                ESP_LOGW(TAG_M, "[SCH %d] WaterPump Turn Off Command dev_idx=%d", i, p);
                            }
                            
                            if (mesh_device_state[p].triggered == true) {
                                mesh_device_state[p].triggered = false; 
                                ESP_LOGW(TAG_M, "[SCH %d] WaterPump dev_idx=%d triggered is false", i, p);
                            }

                            if(mesh_scheduling_state[i].working && ((mesh_device_state[p].device_status == 0) || (difftime(now, mesh_device_state[p].last_cmd) > mesh_device_state[p].working_time_secs+60))){
                                ESP_LOGW(TAG_M, "[SCH %d] Scheduling finished", i);
                                mesh_scheduling_state[i].working = false;
                                mesh_scheduling_state[i].end_time = now;
                                mesh_scheduling_report[i].status = S_REP_SCHED_END;
                                if(is_schuduling_done(i)){
                                    mesh_scheduling_result[i].status = SD_RS_SUCCESS; // Marca o agendamento como concluído com sucesso
                                } else {
                                    mesh_scheduling_result[i].status = SD_RS_FAIL; // Marca o agendamento como falha
                                }
                                ESP_LOGI(TAG_M, "[SCH %d] Scheduling result: sectors %llu, ferti %llu, status %d", i, mesh_scheduling_result[i].sectors, mesh_scheduling_result[i].ferti, mesh_scheduling_result[i].status);
                                memory_save_scheduling_result(); // Salva o resultado do agendamento na memória
                                reset_scheduling_flags(i); // Reseta os flags do agendamento
                                report_scheduling_print(i);
                            }
                        }
                    }
                    
                    // Fertilização
                    if(mesh_scheduling[i].allow_ferti){ // Se estiver habilitado e a bomba de água estiver ligada
                        int f = mesh_scheduling[i].ferti_dev_idx;
                        int elapsed_time_sec_trigger = difftime(now, mesh_device_state[k].last_cmd);
                        if(ferti_diff > 0 && ferti_diff <= max_ferti_diff && !pause_scheduling){
                            if((sector_working_time*60 - elapsed_time_sec_trigger) > (ferti_working_time*60+30)){ // Se o tempo restante de trabalho do setor for maior que o tempo de trabalho do fertilizante mais 30 segundos
                                if (!mesh_device_state[f].triggered && mesh_device_state[k].device_status != 0 && mesh_device_state[p].device_status != 0) {
                                    if (mesh_device_state[f].device_status == 0) {
                                        if (difftime(now, mesh_device_state[f].last_cmd) >= TIMER_30S) {
                                            char mesh_pkg[64];
                                            //int ferti_working_time_fix = ferti_working_time-ferti_diff/60;
                                            mesh_pumplink_turn_on_encode(mesh_devices[f].meshid, mesh_devices[f].device_id, mesh_devices[f].out1, mesh_devices[f].out2, mesh_devices[f].input, mesh_devices[f].mode, ferti_working_time, mesh_pkg);
                                            uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));
                                            
                                            ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Sending Ferti command to dev_idx=%d meshid=%d device_id=%d (working_time=%d) step=%d" ANSI_COLOR_RESET,
                                                i, f,
                                                mesh_devices[f].meshid,
                                                mesh_devices[f].device_id,
                                                ferti_working_time, step);

                                            mesh_device_state[f].last_cmd = now;
                                            mesh_device_state[f].working_time_secs = ferti_working_time*60;
                                        }
                                    } else { // Se o dispositivo estiver ligado
                                        if(mesh_device_state[f].triggered == false){
                                            mesh_device_state[f].triggered = true;
                                            mesh_device_state[f].triggered_time = now;
                                            mesh_scheduling_report[i].ferti_l1 |= (1 << step);
                                            sched_anticipate_report = true;
                                            ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Ferti dev_idx=%d is already on" ANSI_COLOR_RESET, i, f);
                                        }
                                    }
                                }
                            }
                        }else{
                            if (mesh_device_state[f].triggered == true) {
                                mesh_device_state[f].triggered = false; 
                                ESP_LOGW(TAG_M, "[SCH %d] Ferti dev_idx=%d triggered is false", i, f);
                            }
                        }
                    }

                    // Retro-Lavagem
                    if(mesh_scheduling[i].allow_backwash){
                        int b = mesh_scheduling[i].backwash_dev_idx;

                        if(mesh_scheduling[i].waterpump_working_time > 0 && mesh_device_state[p].device_status != 0){

                            int backwash_inc_time = difftime(now, mesh_device_backwash_state[b].inc_time);
                            if(backwash_inc_time >= 1){
                                mesh_device_backwash_state[b].inc_time = now;
                                if(backwash_inc_time < TIMER_30S){ // Não pode ser maior que 30 segundos, pois o WDT deve disparar, e evita inc_time = 0
                                    mesh_device_backwash_state[b].timer += backwash_inc_time;  // Incrementa o timer sem retro-lavagem
                                }
                            }

                            int pump_elapsed_time = difftime(now, mesh_device_state[p].triggered_time);
                            int next_attempt_elap_time = difftime(now, mesh_device_backwash_state[b].next_attempt_time);
                            if(mesh_device_backwash_state[b].attempt == false && mesh_device_backwash_state[b].timer > mesh_config.backwash_cycle_time*60 && pump_elapsed_time > mesh_config.backwash_delay && next_attempt_elap_time > 0){
                                mesh_device_backwash_state[b].attempt = true;
                                mesh_device_backwash_state[b].attempt_time = now;
                            }

                            if (difftime(now, mesh_device_backwash_state[b].last_log_info) >= TIMER_20S) {
                                mesh_device_backwash_state[b].last_log_info = now;
                                ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "[SCH %d]: Backwash dev_idx: %d, timer: %d, attempt: %d, next_attempt_elap_time: %d" ANSI_COLOR_RESET, i, b, mesh_device_backwash_state[b].timer, mesh_device_backwash_state[b].attempt, next_attempt_elap_time);
                            }

                            if(mesh_device_backwash_state[b].attempt){
                                int backwash_diff = difftime(now, mesh_device_backwash_state[b].attempt_time);

                                if(backwash_diff > 0 && backwash_diff <= max_backwash_diff && !pause_scheduling){
                                    if((mesh_scheduling[i].waterpump_working_time*60 - pump_elapsed_time) > (mesh_config.backwash_duration*60+30)){ // Se o tempo restante de trabalho do setor for maior que o tempo de trabalho do fertilizante mais 30 segundos
                                        if (mesh_device_state[b].device_status == 0) {
                                            if (difftime(now, mesh_device_state[b].last_cmd) >= TIMER_30S) {
                                                char mesh_pkg[64];
                                                mesh_pumplink_turn_on_encode(mesh_devices[b].meshid, mesh_devices[b].device_id, mesh_devices[b].out1, mesh_devices[b].out2, mesh_devices[b].input, mesh_devices[b].mode, mesh_config.backwash_duration, mesh_pkg);
                                                uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));
                                                
                                                ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Sending Backwash command to dev_idx=%d meshid=%d device_id=%d (working_time=%d) step=%d" ANSI_COLOR_RESET,
                                                    i, b,
                                                    mesh_devices[b].meshid,
                                                    mesh_devices[b].device_id,
                                                    mesh_config.backwash_duration, step);

                                                mesh_device_state[b].last_cmd = now;
                                                mesh_device_state[b].working_time_secs = mesh_config.backwash_duration*60;
                                            }
                                        } else { // Se o dispositivo estiver ligado
                                            mesh_device_backwash_state[b].timer = 0; // Reseta o timer de retro-lavagem
                                            mesh_device_backwash_state[b].attempt = false;
                                            mesh_device_backwash_state[b].next_attempt_time = 0;
                                            mesh_scheduling_report[i].backwash_time = now;
                                            sched_anticipate_report = true;
                                            ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[SCH %d] Backwash dev_idx=%d is already on" ANSI_COLOR_RESET, i, b);
                                        }
                                    }
                                }else{
                                    if(backwash_diff > 0){
                                        mesh_device_backwash_state[b].next_attempt_time = now + TIMER_10MIN;
                                        mesh_device_backwash_state[b].attempt = false;
                                        ESP_LOGW(TAG_M, "[SCH %d] Backwash dev_idx=%d delay", i, b);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Automatização de bomba usando controle de nível
            for (int i = 0; i < automation_count; i++) {
                int l = mesh_automation[i].level_dev_idx; // Dispositivo medidor de nível
                int p = mesh_automation[i].pump_dev_idx;  // Bomba de nivel

                int automation_diff = (int)difftime(now, mesh_automation_state[i].next_time);
                if (difftime(now, mesh_automation_state[i].last_log_info) >= TIMER_25S) {
                    mesh_automation_state[i].last_log_info = now;
                    ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "[AUTO %d]: level_idx: %d, pump_idx: %d, auto_diff: %d" ANSI_COLOR_RESET, i, l, p, automation_diff);
                }

                if(mesh_automation[i].enabled && (mesh_device_state[l].inputs & mesh_automation[i].mask) == mesh_automation[i].value && 
                difftime(now, mesh_device_state[l].last_sync) < TIMER_40MIN && difftime(now, mesh_device_state[p].last_sync) < TIMER_40MIN && mesh_device_state[p].device_status == 0) { // Se a entrada estiver desligada e com dado atual, e a bomba estiver desligada
                    if(automation_diff > 0 && automation_diff <= TIMER_5MIN){
                        mesh_automation_state[i].tried = true;
                        if (difftime(now, mesh_device_state[p].last_cmd) >= TIMER_30S) {
                            char mesh_pkg[64];
                            mesh_pumplink_turn_on_encode(mesh_devices[p].meshid, mesh_devices[p].device_id, mesh_devices[p].out1, mesh_devices[p].out2, mesh_devices[p].input, mesh_devices[p].mode, mesh_automation[i].working_time, mesh_pkg);
                            uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));
                            
                            ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[AUTO %d] Sending Automation command to dev_idx=%d meshid=%d device_id=%d (working_time=%d)" ANSI_COLOR_RESET,
                                i, p,
                                mesh_devices[p].meshid,
                                mesh_devices[p].device_id,
                                mesh_automation[i].working_time);

                            mesh_device_state[p].last_cmd = now;
                            mesh_device_state[p].working_time_secs = mesh_automation[i].working_time*60;
                            mesh_automation_state[i].canceled = false; // Será cancelado se o controle de nível indicar que a bomba deve desligar
                        }
                    }else{
                        if(automation_diff > 0){
                            ESP_LOGI(TAG_M, ANSI_COLOR_CYAN "[AUTO %d]: Before change next_time auto_diff: %d" ANSI_COLOR_RESET, i, automation_diff);
                            if(mesh_automation_state[i].tried == false){ // Inicialização do next_time
                                mesh_automation_state[i].next_time = now; 
                                ESP_LOGW(TAG_M, "[AUTO %d] Automation Next Time Reset", i);
                            }else { // Se passou pelo tempo de espera e ainda não conseguiu ligar a bomba
                                mesh_automation_state[i].next_time = now + 600; // Agora+10min
                                mesh_automation_state[i].tried = false;
                                ESP_LOGW(TAG_M, "[AUTO %d] Automation Next Time + Delay", i);
                            }
                        }
                    }
                }else{
                    if (mesh_automation[i].enabled) { // Se a entrada estiver ligada ou a bomba estiver ligada
                        mesh_automation_state[i].next_time = now;
                        if(mesh_automation_state[i].tried == true){
                            mesh_automation_state[i].tried = false;
                            if(mesh_device_state[p].device_status != 0){
                                mesh_automation_state[i].working = true; // Mantem até o final da automação
                                report_automation_start(i, now);
                                ESP_LOGW(TAG_M, "[AUTO %d] Automation LevelPump dev_idx=%d Working", i, p);
                            }
                        }

                        if(!mesh_automation_state[i].canceled && difftime(now, mesh_device_state[l].last_sync) < TIMER_40MIN && difftime(now, mesh_device_state[p].last_sync) < TIMER_40MIN && 
                        (mesh_device_state[l].inputs & mesh_automation[i].mask) != mesh_automation[i].value && mesh_device_state[p].device_status != 0){
                            struct s_mesh_control new_control = {
                                .dev_idx = p,
                                .action = CONTROL_TURN_OFF, // Desligar dispositivo
                                .working_time = 0,
                                .enabled = true,
                                .start_time = now,
                                .timeout = TIMER_5MIN
                            };
                            mesh_automation_state[i].canceled = true; // Perminte apenas um único cancelamento por automação
                            mesh_insert_mesh_control(new_control);
                            ESP_LOGW(TAG_M, "[AUTO %d] LevelPump Turn Off Command dev_idx=%d", i, p);
                        }

                        if(mesh_automation_state[i].working == true && mesh_device_state[p].device_status == 0){ // Se a bomba estiver desligada
                            mesh_automation_state[i].working = false;
                            report_automation_end(i, now);
                            ESP_LOGW(TAG_M, "[AUTO %d] End of Working to LevelPump dev_idx=%d", i, p);
                        }
                    }
                }
            }

            // Controle de dispositivo por comando
            for (int i = 0; i < MAX_MESH_CONTROL; i++) {
                int c = mesh_control[i].dev_idx;
                if(mesh_control[i].enabled && difftime(now, mesh_device_state[c].last_sync) < TIMER_40MIN) {
                    if(difftime(now, mesh_control[i].start_time) < mesh_control[i].timeout){
                        char mesh_pkg[64];

                        if(mesh_control[i].action == CODEC__IN__CONTROL__MSG_ACTION__MSG_TURN_ON){ // Ligar dispositivo
                            if (mesh_device_state[c].device_status == 0) {
                                if ((now - mesh_device_state[c].last_cmd) > TIMER_30S) { // 30 segundos                                    
                                    mesh_device_state[c].last_cmd = now;
                                    
                                    mesh_pumplink_turn_on_encode(mesh_devices[c].meshid, mesh_devices[c].device_id, mesh_devices[c].out1, mesh_devices[c].out2, mesh_devices[c].input, mesh_devices[c].mode, mesh_control[i].working_time, mesh_pkg);                              
                                    uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));
                                    
                                    ESP_LOGI(TAG_M, "Device Turn ON");                    
                                    ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[CTRL %d] Sending control command to dev_idx=%d meshid=%d device_id=%d (working_time=%d)" ANSI_COLOR_RESET,
                                                i, c,
                                                mesh_devices[c].meshid,
                                                mesh_devices[c].device_id,
                                                mesh_control[i].working_time);
                                }
                            } else { // Se o dispositivo estiver ligado
                                mesh_control[i].enabled = false;
                                ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[CTRL %d] dev_idx=%d is already on" ANSI_COLOR_RESET, i, c);
                            }

                        } else if(mesh_control[i].action == CODEC__IN__CONTROL__MSG_ACTION__MSG_TURN_OFF) { // Desligar dispositivo
                            if (mesh_device_state[c].device_status != 0) {
                                if ((now - mesh_device_state[c].last_cmd) > TIMER_30S) { // 30 segundos
                                    mesh_device_state[c].last_cmd = now;
                                    char out = mesh_devices[c].out1;
                                    if((mesh_devices[c].mode&PULSE) == PULSE){
                                        out = mesh_devices[c].out2;
                                    }
                                    mesh_pumplink_turn_off_encode(mesh_devices[c].meshid, mesh_devices[c].device_id, out, mesh_devices[c].mode, mesh_pkg);
                                    uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));

                                    ESP_LOGI(TAG_M, "Device Turn OFF");                    
                                    ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[CTRL %d] Sending control command to dev_idx=%d meshid=%d device_id=%d (working_time=%d)" ANSI_COLOR_RESET,
                                                i, c,
                                                mesh_devices[c].meshid,
                                                mesh_devices[c].device_id,
                                                mesh_control[i].working_time);
                                }
                            } else { // Se o dispositivo estiver desligado
                                mesh_control[i].enabled = false;
                                ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[CTRL %d] dev_idx=%d is already off" ANSI_COLOR_RESET, i, c);
                            }
                        } else if(mesh_control[i].action == CODEC__IN__CONTROL__MSG_ACTION__MSG_PACKAGE){
                            mesh_pumplink_package_encode(mesh_devices[c].meshid, mesh_control[i].payload, mesh_control[i].size, mesh_pkg);
                            uart_tx_chars(UART_NUM, mesh_pkg, strlen(mesh_pkg));

                            ESP_LOGI(TAG_M, "Device Package");                    
                            ESP_LOGW(TAG_M, ANSI_COLOR_YELLOW "[CTRL %d] Sending package to dev_idx=%d meshid=%d size=%d" ANSI_COLOR_RESET,
                                        i, c,
                                        mesh_devices[c].meshid,
                                        mesh_control[i].size);
                            mesh_control[i].enabled = false;
                        }
                    } else {
                        mesh_control[i].enabled = false;
                    }
                }
            }
        }

        // Verifica se há dados para processar
        uint32_t events;
        if (xTaskNotifyWait(0, UINT32_MAX, &events, 0))
        {
            // Processamento de dados recebidos
            if (events & EVENT_DATA) {
                ESP_LOGI(TAG_M, "Data received...");
                if (xSemaphoreTake(data_mutex, pdMS_TO_TICKS(10)) == pdTRUE) {
                    char *result = NULL;
                    int len = 0;
                    process_json_data(&result, &len);
                    if (result != NULL) {
                        if(protocol.origin == P_MQTT){
                            ESP_LOGI(TAG_M, "MQTT result len %d", len);
                            for (size_t i = 0; i < len; i++) {
                                printf("%02x ", result[i]);
                            }
                            printf("\n");
                            int res = esp_mqtt_client_enqueue(mqtt_client, publish_topic_report, result, len, 1, false, true);
                            if (res < 0) {
                                ESP_LOGW(TAG_M, "MQTT enqueue failed: %d", res);
                            } else {
                                ESP_LOGI(TAG_M, "MQTT enqueued msg_id=%d", res);
                            }
                        }
                    }
                    free(result);
                    protocol.len = 0;
                    xSemaphoreGive(data_mutex);
                }
            }
            // Atualização do RTC
            if (events & EVENT_UPDATE_RTC) {
                if (xSemaphoreTakeRecursive(i2c_mutex, pdMS_TO_TICKS(200)) == pdTRUE) {
                    if(ds1339_set_time_epoch(now) == ESP_OK) {
                        ESP_LOGI(TAG_M, "RTC time updated successfully");
                    } else {
                        ESP_LOGE(TAG_M, "Failed to update RTC time");
                    }
                    xSemaphoreGiveRecursive(i2c_mutex);
                }
            }
        }
        
        // Dados da rede Mesh
        if (xQueueReceive(uart_queue, (void*)&event, pdMS_TO_TICKS(10))) {
            switch (event.type) {
                case UART_BUFFER_FULL:
                case UART_FIFO_OVF:
                    handle_uart_exception();
                break;                
                case UART_PATTERN_DET:
                    int pos = uart_pattern_pop_pos(UART_NUM);
                    if (pos != -1) {
                        int wanted = pos + 1;
                        int len = uart_read_bytes(UART_NUM, uart_data, wanted, pdMS_TO_TICKS(200));
                        if (len != wanted) {
                            handle_uart_exception();
                            break;
                        }

                        uart_data[pos]='}';
                        uart_data[pos+1]='\0';
                        ESP_LOGI(TAG_M, "Received from mesh: %s", uart_data);
                        if (strstr(uart_data, "{01;") != NULL){
                            if(mesh_mesh_decode((uint8_t *)uart_data, strlen(uart_data), &mesh_decode) == ESP_FAIL){
                                handle_uart_exception();
                            }
                        }
                        uart_data[0]='\0';
                    }else{
                        uart_flush_input(UART_NUM);
                        xQueueReset(uart_queue);
                    }

                    break;
                default:
                    break;
            }
        }

        // Acionamento da GPIO do Pluviometro
        if(xQueueReceive(gpio_evt_queue, &io_num, pdMS_TO_TICKS(10))) {
            if(mesh_config.raingauge_enabled == true && difftime(now, last_gpio_interruption) >= 1){
                last_gpio_interruption = now;
                last_its_raining = now;
                rainfall_count[timeinfo.tm_hour] += 1;
                its_raining = true;
                update_rainfall_last24h();
                
                ESP_LOGI(TAG_M, "Rainfall Count[%d]: %d, mm: %d", timeinfo.tm_hour, rainfall_count[timeinfo.tm_hour], rainfall_last24h);
            }
        }

        // Verifica se o agendamento deve ser pausado devido a chuva
        if(mesh_config.raingauge_enabled == true){
            if(current_rainfall_hour != timeinfo.tm_hour){
                current_rainfall_hour = timeinfo.tm_hour;
                rainfall_count[current_rainfall_hour] = 0;
                update_rainfall_last24h();
                ESP_LOGI(TAG_M, "Rainfall Count[%d]=0", current_rainfall_hour);
                printf("Rainfall DB: ");
                for (int i = 0; i < 24; ++i) {
                    printf("%d ", rainfall_count[i]);
                }
                printf("\n");
            }

            if(!pause_scheduling && ((rainfall_last24h >= mesh_config.rainfall_limit && mesh_config.rainfall_limit > 0))){
                pause_scheduling = true;
                pause_scheduling_time = now;
                pause_scheduling_timer = mesh_config.rainfall_pause_duration*3600; // em horas
                ESP_LOGW(TAG_M, "Scheduling Paused for %dh due to rainfall", mesh_config.rainfall_pause_duration);
            }

            if(its_raining == true && difftime(now, last_its_raining) >= 300){ // Se paasaram 5 minutos sem chuva
                its_raining = false;
            } 
        }

        // Verifica se o agendamento deve ser retomado por tempo
        if(pause_scheduling == true && difftime(now, pause_scheduling_time) > pause_scheduling_timer){
            pause_scheduling = false;
            ESP_LOGW(TAG_M, "Scheduling Resumed for time");
        }

        // Envio de relatórios
        mqtt_report();

        // Verifica se o watchdog deve ser reiniciado
        if(difftime(now, wdt_time) >= 1){
            wdt_time = now;
            esp_task_wdt_reset();
        }

        if(MEMORY_DEBUG && difftime(now, memory_debug_time) >= TIMER_30S){
            memory_debug_time = now;
            ESP_LOGI(TAG_M, "Free heap: %" PRIu32 ", Min heap: %" PRIu32 ", Largest block: %zu, "
                    "Stack watermark: %u, Task duration: %.4f ms (max: %.4f ms)", 
                    esp_get_free_heap_size(), 
                    esp_get_minimum_free_heap_size(), 
                    heap_caps_get_largest_free_block(MALLOC_CAP_8BIT), 
                    uxTaskGetStackHighWaterMark(NULL), 
                    task_duration, 
                    task_max_duration);
        }
        
        task_duration = (esp_timer_get_time() - task_start_loop) / 1000.0;
        if (task_duration > task_max_duration) {
            task_max_duration = task_duration;
        }

        vTaskDelay(pdMS_TO_TICKS(200));
    }
}

static esp_err_t i2c_init_bus(void)
{
    i2c_master_bus_config_t bus_cfg = {
        .i2c_port   = I2C_PORT,
        .sda_io_num = I2C_SDA,
        .scl_io_num = I2C_SCL,
        .clk_source = I2C_CLK_SRC_DEFAULT,
        .flags.enable_internal_pullup = false,
        .glitch_ignore_cnt = 7,
    };
    return i2c_new_master_bus(&bus_cfg, &i2c_bus);
}

static void i2c_init(void){
    ESP_ERROR_CHECK_WITHOUT_ABORT(i2c_init_bus());
    ESP_ERROR_CHECK_WITHOUT_ABORT(ds1339_init_device());
    ESP_ERROR_CHECK(memory_eeprom_init());
}

static void i2c_scan(void)
{
    ESP_LOGI(TAG_M, "     00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F");
    for (uint8_t addr = 0; addr < 0x80; ++addr) {
        if ((addr & 0x0F) == 0) ESP_LOGI(TAG_M, "%02X: ", addr);
        esp_err_t r = i2c_master_probe(i2c_bus, addr, 50);
        if (r == ESP_OK) {
            ESP_LOGI(TAG_M, "%02X ", addr);
        } else {
            ESP_LOGI(TAG_M, "-- ");
        }
        if ((addr & 0x0F) == 0x0F) ESP_LOGI(TAG_M, "");
    }
    ESP_LOGI(TAG_M, "Scan finished");
}

void app_main() {

    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_POSEDGE, // Change to GPIO_INTR_NEGEDGE or GPIO_INTR_ANYEDGE as needed
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = (1ULL << GPIO_INPUT_IO_34),
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en = GPIO_PULLUP_DISABLE,
    };
    gpio_config(&io_conf);
    gpio_install_isr_service(ESP_INTR_FLAG_DEFAULT);
    gpio_isr_handler_add(GPIO_INPUT_IO_34, gpio_isr_handler, (void*) GPIO_INPUT_IO_34);

    gpio_config_t io_conf_out = {
        .intr_type    = GPIO_INTR_DISABLE,
        .mode         = GPIO_MODE_OUTPUT,
        .pin_bit_mask = GPIO_OUTPUT_IO_SEL,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .pull_up_en   = GPIO_PULLUP_DISABLE,
    };
    ESP_ERROR_CHECK(gpio_config(&io_conf_out));
    ESP_ERROR_CHECK(gpio_set_level(GPIO_OUTPUT_IO_5, 0));  // E2PROM WP
    ESP_ERROR_CHECK(gpio_set_level(GPIO_OUTPUT_IO_18, 0)); // LED
    ESP_ERROR_CHECK(gpio_set_level(GPIO_OUTPUT_IO_26, 0)); // STM32 RESET
    ESP_ERROR_CHECK(gpio_set_level(GPIO_OUTPUT_IO_27, 0)); // STM32 BOOT0

    gpio_evt_queue = xQueueCreate(10, sizeof(uint32_t));
    rtc_evt_queue = xQueueCreate(1, 0);
    data_mutex = xSemaphoreCreateMutexStatic(&data_mutex_cb);
    configASSERT(data_mutex);
    i2c_mutex = xSemaphoreCreateRecursiveMutex();
    configASSERT(i2c_mutex);

    memory_nvs_init();

    esp_efuse_mac_get_default(mac);

    esp_err_t err = memory_load_system_info();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load system info. err=0x%X", err);
    }     
    err = memory_load_wifi_credentials(sta_ssid, MAX_STA_SSID_SIZE, sta_pass, MAX_STA_PASS_SIZE);
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load config to memory. err=0x%X", err);
    } 
    err = memory_load_devices();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load devices to memory. err=0x%X", err);
    } 
    err = memory_load_scheduling();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load scheduling to memory. err=0x%X", err);
    } 
    err = memory_load_device_scheduling();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load device scheduling to memory. err=0x%X", err);
    } 
    err = memory_load_automation();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load automation to memory. err=0x%X", err);
    } 
    err = memory_load_config();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load config to memory. err=0x%X", err);
    } 
    err = memory_load_scheduling_result();
    if (err != ESP_OK) {
        ESP_LOGE(TAG_M, "Failed to load scheduling result to memory. err=0x%X", err);
    } 

    if(sta_ssid[0]==0x00 || sta_pass[0]==0x00){
        strncpy((char*)sta_ssid, WIFI_STA_SSID, MAX_STA_SSID_SIZE);
        strncpy((char*)sta_pass, WIFI_STA_PASS, MAX_STA_PASS_SIZE);
        memory_save_wifi_credentials(sta_ssid, sta_pass);
        ESP_LOGI(TAG_M, "Save default SSID and Password of STA");
    }
    
    esp_reset_reason_t reason = esp_reset_reason();
    ESP_LOGI(TAG_M, "Reset Reason: %d", reason);
    ESP_LOG_BUFFER_HEX("sta_ssid",sta_ssid, MAX_STA_SSID_SIZE);
    ESP_LOGI(TAG_M, "STA SSID: %s", sta_ssid);
    ESP_LOGI(TAG_M, "STA Password: %s", sta_pass);

    uart_init();
    i2c_init();

    wifi_init_softap_sta();
    
    webserver_init();
    
    mqtt_init();

    xTaskCreate(&main_task, "main_task", 8192, NULL, 5, &main_task_handle);

}
