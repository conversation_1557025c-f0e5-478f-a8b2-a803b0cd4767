#include "driver/uart.h"
#include "driver/i2c_master.h"

#define ESP_FW_VERSION 123
#define MESH_FW_VERSION 101
#define HARDWARE_VERSION 110

#define MAX_STA_SSID_SIZE 32
#define MAX_STA_PASS_SIZE 64

#define WIFI_STA_SSID      "GEO_ENGENHARIA"
#define WIFI_STA_PASS      "engenharia@110105"
#define WIFI_AP_SSID       "Codec"
#define WIFI_AP_PASS       "0123456789"
#define MAX_STA_CONN       4

#define AP_IP_ADDR "***********"
#define AP_GW_ADDR "***********"
#define AP_NETMASK "*************"

#define GPIO_INPUT_IO_34    34
#define GPIO_OUTPUT_IO_5    5
#define GPIO_OUTPUT_IO_18   18
#define GPIO_OUTPUT_IO_26   26
#define GPIO_OUTPUT_IO_27   27
#define GPIO_OUTPUT_IO_SEL  ((1ULL<<GPIO_OUTPUT_IO_5) | (1ULL<<GPIO_OUTPUT_IO_18) | (1ULL<<GPIO_OUTPUT_IO_26) | (1ULL<<GPIO_OUTPUT_IO_27))

#define PORT           3333
#define IP_ADDR        "*************"
#define INTERVAL_5S    5
#define MAX_RETRY      10

#define BASIC_AUTH_USERNAME "byagro"
#define BASIC_AUTH_PASSWORD "i8dEYH7tcNxVf18"

#define MQTT_BROKER_URI "mqtt://mosquitto-codec.saas.byagro.dev.br:8003"
#define MQTT_BROKER_PORT 8003
//#define PUBLISH_TOPIC "/uplink"
//#define PUBLISH_TOPIC_REPORT "/report"
//#define SUBSCRIBE_TOPIC "/downlink"

#define TXD_PIN        (GPIO_NUM_17)
#define RXD_PIN        (GPIO_NUM_16)
#define UART_NUM       UART_NUM_2

#define I2C_SDA     GPIO_NUM_21
#define I2C_SCL     GPIO_NUM_22
#define I2C_PORT    I2C_NUM_0
#define I2C_FREQ_HZ 100000
#define DS1339_ADDR 0x68

#define BUF_SIZE (1024)
#define MAX_MESH_DEVICES 50
#define MAX_MESH_AUTOMATION 5
#define MAX_MESH_CONTROL 5
#define MAX_MESH_SCHEDULING 20
#define MAX_MESH_DEVICE_SCHEDULING 100

#define RESUMPTION_DELAY 120

#define TIMER_10S   10
#define TIMER_20S   20
#define TIMER_25S   20
#define TIMER_30S   30
#define TIMER_2MIN  120
#define TIMER_3MIN  180
#define TIMER_5MIN  300
#define TIMER_10MIN 600
#define TIMER_20MIN 1200
#define TIMER_40MIN 2400
#define TIMER_2H    2*3600
#define TIMER_4H    4*3600
#define TIMER_22H   22*3600

#define EVENT_DATA 0x01
#define EVENT_UPDATE_RTC 0x02

#define PULSE    0x01
#define MONI     0x02
#define MAX_DATA_SIZE 8192

#ifndef MIN
#define MIN(a, b) ((a) < (b) ? (a) : (b))
#endif