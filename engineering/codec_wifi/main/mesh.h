#ifndef MESH_H
#define MESH_H

#include "defines.h"
#include "memory.h"
#include "utils.h"

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include <netinet/in.h>
#include <time.h>
#include "mbedtls/base64.h"

#define CTRL_PAYLOAD_SIZE 10

// Dispositivos a ser acionados
struct s_mesh_devices {
    unsigned int meshid;
    unsigned char device_id;
    unsigned char type; 
    unsigned char out1;
    unsigned char out2;
    unsigned char input;
    unsigned char mode;
    unsigned char group;   // Talhão
    unsigned char sector;
    unsigned char power;   // Bomba de água com inversor
    unsigned char eqpt_type;
};

// Automação de dispositivos
struct s_mesh_automation {
    unsigned short int level_dev_idx; 
    unsigned short int pump_dev_idx;  // indice de s_mesh_devices
    unsigned char mask;
    unsigned char value;
    unsigned char enabled; 
    unsigned short int working_time;
};

struct s_mesh_automation_state {
    unsigned int next_time; 
    bool canceled;
    bool tried;
    bool working;
    time_t last_log_info;
};

struct s_mesh_control {
    unsigned short int dev_idx; 
    unsigned char action;
    unsigned short int working_time;
    unsigned char payload[CTRL_PAYLOAD_SIZE];
    unsigned char size;
    unsigned char enabled; 
    unsigned int start_time;
    unsigned char state;
    unsigned int timeout;
};

// Agendamentos
struct s_mesh_scheduling {
    unsigned int start_time;
    unsigned char days_of_week;
    unsigned char type;
    unsigned char number_of_steps;
    unsigned short int waterpump_dev_idx;
    unsigned short int waterpump_working_time;
    unsigned char allow_backwash;
    unsigned short int backwash_dev_idx;
    unsigned char allow_ferti;
    unsigned short int ferti_dev_idx;
    unsigned short int group;
    unsigned short int once; // não repetir o agendamento
};

struct s_mesh_scheduling_result {
   unsigned int started_time;
   unsigned int last_update;
   unsigned long long sectors; 
   unsigned long long ferti; 
   unsigned char status; 
};

// Configuração da retro lavagem
struct s_mesh_config {
    unsigned short int backwash_cycle_time;
    unsigned short int backwash_duration;
    unsigned short int backwash_delay;
    bool               raingauge_enabled;
    unsigned short int raingauge_factor;           // passos de por mm
    unsigned short int rainfall_limit;             // limite de chuva em mm
    unsigned short int rainfall_pause_duration;    // duração da pausa
    bool               publish_raw_data;           // enviar dados brutos
    bool               debug;                      // habilitar modo de debug
    bool               enable_schedule_resumption; // habilita retomada automática de agendamentos
    bool               enable_ferti_resumption;    // habilita retomada automática devido falha na ferti
    unsigned short int max_resumption_attempts;    // número máximo de tentativas de retomada (0 = indefinido)
};

// Backwash state
struct s_pumplink_backwash_state {
    unsigned int last_time;
    unsigned int last_dev_idx;
};

// Agendamentos por dispositivo
struct s_mesh_device_scheduling {
    unsigned short int shc_idx;  // indice de s_mesh_scheduling
    unsigned short int dev_idx;  // indice de s_mesh_devices
    unsigned char step;          // passo da sequencia
    unsigned int sector_working_time;
    unsigned int ferti_working_time;
    unsigned int ferti_delay;
};

// Agendamentos por dispositivo
struct s_mesh_scheduling_only {
    unsigned short int dev_idx; // indice de s_mesh_devices
    unsigned char days_of_week;
    unsigned char type;
    unsigned char seq_id;       // id da sequencia
    unsigned char step;         // passo da sequencia
    unsigned char n_step;
    unsigned int time;
    //unsigned int working_time;
};

// Estrutura para armazenar o estado dos agendamentos
struct s_mesh_scheduling_state {
    int current_step;             // Passo atual
    int total_working_time_secs;   // Soma dos tempos de trabalho dos passos anteriores em segundos
    bool init_info;
    bool started;
    bool working;
    bool must_resumption; // Indica se o agendamento deve ser retomado
    bool checked_whether_resumption;
    bool resumption_overlap;
    bool initial_resumption_setup;
    int resumption_step_count; // Número de passos na retomada
    int last_resumption_step;  // Último passo executado antes da retomada
    int pump_working_time_if_resumption;
    int resumption_attempts;
    time_t resumption_start_time; // Tempo de início da retomada
    time_t end_time; // Tempo de término do agendamento
    time_t reference_day;
    time_t last_log_info;
    time_t fail_devsch_log;
    unsigned int last_dev_idx;
};

// Estrutura para armazenar o estado dos agendamentos por dispositivo
struct s_mesh_device_scheduling_state {
    unsigned char info;
    bool tried;
    bool executed;
    bool canceled;
};

struct s_mesh_device_backwash_state {
    unsigned int inc_time;
    unsigned int timer;
    unsigned char attempt;
    unsigned int attempt_time;
    unsigned int next_attempt_time;
    unsigned int last_log_info;
};

struct s_mesh_decode {
    unsigned int meshid;
    unsigned char devices_working;
    unsigned char outputs;
    unsigned char inputs;
    unsigned char flags;
    unsigned char device_id;
    unsigned char time_left;
    unsigned int last_sync;
};

struct s_mesh_device_state {
    unsigned short int id;
    unsigned short int m;
    unsigned char device_status;
    unsigned char outputs;
    unsigned char inputs;
    unsigned char flags;
    unsigned char device_id;
    unsigned char time_left;
    unsigned int last_sync;
    unsigned int last_cmd;
    unsigned short int last_appoint; // to cancel scheduling
    unsigned int working_time_secs;
    unsigned char triggered;
    unsigned int triggered_time;
};

struct s_mesh_scheduling_report {
    unsigned short int shc_idx;
    unsigned int started_time;
    unsigned long long sector_l1;
    unsigned long long sector_l2;
    unsigned long long ferti_l1;
    unsigned long long ferti_l2;
    unsigned char waterpump;
    unsigned int backwash_time;
    unsigned int number_of_sectors;
    unsigned char had_waterpump;
    unsigned char had_ferti;
    unsigned int time_of_resumption;
    unsigned char resumption_attempts;
    unsigned char status;
};

struct s_mesh_automation_report {
    unsigned int started_time;
    unsigned int end_time;
    unsigned char status;
};

// Informações do sistema
struct s_system_info {
    unsigned int resets;
};

typedef enum {
    SD_RS_WAITING = 0,
    SD_RS_SUCCESS = 1,
    SD_RS_RUNNING = 2, 
    SD_RS_FAIL    = 3
} result_scheduling_t;

#ifndef MESH_GLOBAL
#define ME_GLOBAL extern
#else
#define ME_GLOBAL
#endif

ME_GLOBAL unsigned short int device_count;
ME_GLOBAL unsigned short int automation_count;
ME_GLOBAL unsigned short int schedule_count;
ME_GLOBAL unsigned short int schedules_per_device;

ME_GLOBAL char pause_scheduling;
ME_GLOBAL time_t pause_scheduling_time;
ME_GLOBAL unsigned int pause_scheduling_timer;

ME_GLOBAL unsigned int last_devs_update;
ME_GLOBAL unsigned int last_sched_update;
ME_GLOBAL unsigned int last_dev_sched_update;
ME_GLOBAL unsigned int last_wifi_update;
ME_GLOBAL unsigned int last_auto_update;
ME_GLOBAL unsigned int last_config_update;

ME_GLOBAL struct s_mesh_devices mesh_devices[MAX_MESH_DEVICES];
ME_GLOBAL struct s_mesh_device_state mesh_device_state[MAX_MESH_DEVICES];
ME_GLOBAL struct s_mesh_scheduling mesh_scheduling[MAX_MESH_SCHEDULING];
ME_GLOBAL struct s_mesh_scheduling_result mesh_scheduling_result[MAX_MESH_SCHEDULING];
ME_GLOBAL struct s_mesh_scheduling_state mesh_scheduling_state[MAX_MESH_SCHEDULING];
ME_GLOBAL struct s_mesh_scheduling_report mesh_scheduling_report[MAX_MESH_SCHEDULING];
ME_GLOBAL struct s_mesh_device_scheduling mesh_device_scheduling[MAX_MESH_DEVICE_SCHEDULING];
ME_GLOBAL struct s_mesh_device_scheduling_state mesh_device_scheduling_state[MAX_MESH_DEVICES]; // Estado dos agendamentos por dispositivo
ME_GLOBAL struct s_mesh_device_backwash_state mesh_device_backwash_state[MAX_MESH_DEVICES];     // Estado da retro lavagem por dispositivo
ME_GLOBAL struct s_mesh_decode mesh_decode;
ME_GLOBAL struct s_mesh_automation mesh_automation[MAX_MESH_AUTOMATION];
ME_GLOBAL struct s_mesh_automation_state mesh_automation_state[MAX_MESH_AUTOMATION];
ME_GLOBAL struct s_mesh_automation_report mesh_automation_report[MAX_MESH_AUTOMATION];
ME_GLOBAL struct s_mesh_control mesh_control[MAX_MESH_CONTROL];
ME_GLOBAL struct s_mesh_config mesh_config;
ME_GLOBAL struct s_system_info system_info;


void mesh_pumplink_turn_on_encode(uint32_t id, uint8_t device_id, uint8_t out1, uint8_t out2, uint8_t input, uint8_t mode, uint16_t working_time, char *mesh_pkg);
void mesh_pumplink_turn_off_encode(uint32_t id, uint8_t device_id, uint8_t out, uint8_t mode, char *mesh_pkg);
void mesh_pumplink_package_encode(uint32_t id, uint8_t *payload, uint8_t size, char *mesh_pkg);
void mesh_pumplink_transmission_timer_encode(uint32_t id, uint8_t time, char *mesh_pkg);
void mesh_insert_mesh_control(struct s_mesh_control new_control);
esp_err_t mesh_mesh_decode(uint8_t *bytes, size_t length, struct s_mesh_decode *decode);

static inline bool is_valid_dev_idx(unsigned idx) {
    return idx < (device_count < MAX_MESH_DEVICES ? device_count : MAX_MESH_DEVICES);
}

#endif // MESH_H
