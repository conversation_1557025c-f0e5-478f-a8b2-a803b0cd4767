#define PROT_GLOBAL
#include "protocol.h"
#include "incoming_packet.pb-c.h"
#include "outgoing_packet.pb-c.h"
#include "esp_task_wdt.h"
#include "esp_https_ota.h"
#include "ds1339.h"

#define OTA_HTTP_URL_BASE "http://atualizacao.byagro.com.br/codec/codec_release_h%d_v%d.bin"
#define OTA_HTTPS_URL_BASE "https://atualizacao.byagro.com.br/codec/codec_release_h%d_v%d.bin"

static const char *TAG_PROT = "PROT";
static char ota_url[256];
static TaskHandle_t ota_handle = NULL;
extern char sta_ssid[MAX_STA_SSID_SIZE];
extern char sta_pass[MAX_STA_PASS_SIZE];
extern int rainfall_count[24];
extern int rainfall_last24h;
extern esp_mqtt_client_handle_t mqtt_client;
extern const uint8_t root_ca_pem_start[] asm("_binary_root_ca_pem_start");
extern bool anticipate_report;

static const esp_task_wdt_config_t OTA_WDT_CFG = {
    .timeout_ms     = 60000,                  // 60 s
    .idle_core_mask = (1 << portNUM_PROCESSORS) - 1,
    .trigger_panic  = true
};

static const esp_task_wdt_config_t DEFAULT_WDT_CFG = {
    .timeout_ms     = CONFIG_ESP_TASK_WDT_TIMEOUT_S * 1000,
    .idle_core_mask = (1 << portNUM_PROCESSORS) - 1,
    .trigger_panic  = true
};

void ota_task(void* pv) {
    ESP_LOGW(TAG_PROT, "OTA started");

    ESP_ERROR_CHECK(esp_task_wdt_reconfigure(&OTA_WDT_CFG)); // Estende o WDT enquanto dura o OTA
    esp_err_t err = esp_mqtt_client_stop(mqtt_client);
    if (err != ESP_OK) {
        ESP_LOGW(TAG_PROT, "esp_mqtt_client_stop()=%s", esp_err_to_name(err));
    }

    const char *base = (ota_fw_update.proto == OTA_PROTO_HTTPS) ? OTA_HTTPS_URL_BASE : OTA_HTTP_URL_BASE;
    snprintf(ota_url, sizeof(ota_url), base, HARDWARE_VERSION, (int)ota_fw_update.version);
    ESP_LOGI(TAG_PROT, "OTA url: %s", ota_url);
    
    esp_http_client_config_t http_cfg = {
        .url = ota_url,
        .timeout_ms = 50000
    };

    if (ota_fw_update.proto == OTA_PROTO_HTTPS) {
        http_cfg.cert_pem = (char*)root_ca_pem_start; // Certificado CA para HTTPS
    }

    esp_https_ota_config_t ota_cfg = {
        .http_config = &http_cfg,
        .partial_http_download = true,
        .bulk_flash_erase = true
    };

    esp_err_t ret = esp_https_ota(&ota_cfg);

    // Restaura o WDT ao timeout original
    ESP_ERROR_CHECK(esp_task_wdt_reconfigure(&DEFAULT_WDT_CFG));

    err = esp_mqtt_client_start(mqtt_client);
    if (err != ESP_OK) {
        ESP_LOGE(TAG_PROT, "MQTT restart failed: %s", esp_err_to_name(err));
    }

    ota_handle = NULL;
    if (ret != ESP_OK) {
        ESP_LOGE(TAG_PROT, "OTA failed: %s", esp_err_to_name(ret));
        vTaskDelete(NULL);
        return;
    }

    ESP_LOGW(TAG_PROT, "OTA completed, rebooting...");
    esp_restart();
}

void reset_only_scheduling_flags(int shc_idx)
{
    mesh_scheduling_result[shc_idx].status = SD_RS_WAITING; // Inicializa o status do agendamento como esperando
    mesh_scheduling_result[shc_idx].sectors = 0; // Zera os setores do agendamento
    mesh_scheduling_result[shc_idx].ferti = 0; // Zera a fertilização

    mesh_scheduling_state[shc_idx].current_step               = 0;
    mesh_scheduling_state[shc_idx].total_working_time_secs    = 0;
    mesh_scheduling_state[shc_idx].init_info                  = false;
    mesh_scheduling_state[shc_idx].started                    = false;
    mesh_scheduling_state[shc_idx].working                    = false;
    mesh_scheduling_state[shc_idx].must_resumption            = false;
    mesh_scheduling_state[shc_idx].checked_whether_resumption = false;
    mesh_scheduling_state[shc_idx].resumption_overlap         = false;
    mesh_scheduling_state[shc_idx].initial_resumption_setup   = false;   
    mesh_scheduling_state[shc_idx].pump_working_time_if_resumption = 0;
}

esp_err_t process_json_data(char **result, int *len)
{
    uint16_t crc_sent = (protocol.data[protocol.len-2] << 8) | (protocol.data[protocol.len-1] & 0xFF);
    uint16_t crc_calculated = crc16((const uint8_t *)protocol.data, protocol.len - 2);

    if(crc_sent != crc_calculated) {
        ESP_LOGE(TAG_PROT, "CRC mismatch: sent=0x%04X, calculated=0x%04X", crc_sent, crc_calculated);
        return ESP_FAIL;
    }

    Codec__In__IncomingPacket *packet = codec__in__incoming_packet__unpack(NULL, protocol.len-2, (const uint8_t *)protocol.data);
    if (!packet) {
        ESP_LOGE(TAG_PROT, "Failed to decode packet");
        return ESP_FAIL;
    }

    switch(packet->payload_case){
        case CODEC__IN__INCOMING_PACKET__PAYLOAD_CONFIG:{
            if (packet->config) {
                bool current_raingauge_enabled = (bool)mesh_config.raingauge_enabled;

                Codec__In__Config__ConfigPackage *cfg = packet->config;
                mesh_config.backwash_cycle_time        = cfg->backwash_cycle;
                mesh_config.backwash_delay             = cfg->backwash_delay;
                mesh_config.backwash_duration          = cfg->backwash_duration;
                mesh_config.raingauge_enabled          = cfg->raingauge_enabled;
                mesh_config.raingauge_factor           = cfg->raingauge_factor;
                mesh_config.rainfall_limit             = cfg->rainfall_limit;
                mesh_config.rainfall_pause_duration    = cfg->rainfall_pause_duration;
                mesh_config.publish_raw_data           = cfg->publish_raw_data;
                mesh_config.debug                      = cfg->debug;
                mesh_config.enable_schedule_resumption = cfg->enable_schedule_resumption;
                mesh_config.enable_ferti_resumption    = cfg->enable_ferti_resumption;
                mesh_config.max_resumption_attempts    = cfg->max_resumption_attempts;

                if(current_raingauge_enabled == true && mesh_config.raingauge_enabled == false) { // Se o raingauge estava ativo e foi desativado, zera a contagem de chuva
                    memset(rainfall_count, 0, sizeof(rainfall_count));
                    rainfall_last24h = 0;
                    ESP_LOGI(TAG_PROT, "Rain gauge disabled, rainfall count reset");
                }

                if(last_config_update != (unsigned int)packet->id){
                    last_config_update = (unsigned int)packet->id;
                    esp_err_t err = memory_save_config();
                    if (err != ESP_OK) {
                        ESP_LOGE(TAG_PROT, "Failed to save config to memory. err=0x%X", err);
                    } else {
                        ESP_LOGI(TAG_PROT, "Config saved successfully");
                    }

                    if(protocol.origin == P_WIFI){
                        set_clock_from_epoch(packet->id);
                    }
                }
                
                if (cfg->wifi) {
                    const char *new_ssid     = cfg->wifi->ssid;
                    const char *new_password = cfg->wifi->password;

                    size_t len_ssid = strlen(new_ssid);
                    size_t len_pass = strlen(new_password);

                    ESP_LOGI(TAG_PROT, "New Wi-Fi config -> SSID=\"%s\" (len=%zu), (len=%zu)", new_ssid, len_ssid, len_pass);

                    bool ssid_valid = (len_ssid > 0 && len_ssid < MAX_STA_SSID_SIZE);
                    bool pass_valid = (len_pass > 0 && len_pass < MAX_STA_PASS_SIZE);
                    if(ssid_valid && pass_valid) {
                        if (new_ssid && new_password && new_ssid[0] != '\0' && new_password[0] != '\0') {
                            if (strcmp(sta_ssid, new_ssid) != 0 || strcmp(sta_pass, new_password) != 0)
                            {
                                strlcpy(sta_ssid, new_ssid, sizeof(sta_ssid));
                                strlcpy(sta_pass, new_password, sizeof(sta_pass));

                                esp_err_t err = memory_save_wifi_credentials(sta_ssid, sta_pass);
                                if (err != ESP_OK) {
                                    ESP_LOGE(TAG_PROT, "Failed to save WiFi credentials. err=0x%X", err);
                                } else {
                                    ESP_LOGI(TAG_PROT, "WiFi credentials saved, restarting...");
                                    esp_restart();
                                }
                            }
                        } else {
                            ESP_LOGW(TAG_PROT, "WiFi config ignored: missing ssid or password");
                        }
                    }
                }

                *len = report_info((uint8_t **)result, S_REP_INFO_BASE);

                ESP_LOGI(TAG_PROT, "CFG: cycle=%d delay=%d dur=%d rain_en=%d rain_fact=%d rain_lim=%d rain_pause=%d publish_raw_data=%d debug=%d res_scheduling_en=%d res_ferti_en=%d max_resum=%d",
                    mesh_config.backwash_cycle_time,
                    mesh_config.backwash_delay,
                    mesh_config.backwash_duration,
                    mesh_config.raingauge_enabled,
                    mesh_config.raingauge_factor,
                    mesh_config.rainfall_limit,
                    mesh_config.rainfall_pause_duration,
                    mesh_config.publish_raw_data,
                    mesh_config.debug,
                    mesh_config.enable_schedule_resumption,
                    mesh_config.enable_ferti_resumption,                    
                    mesh_config.max_resumption_attempts
                );
            }
            break;
        }
        case CODEC__IN__INCOMING_PACKET__PAYLOAD_DEVICES: {
            if (packet->devices) {
                Codec__In__Devices__DevicesPackage *devs = packet->devices;
                if (devs->data) {
                    int devices_size = devs->n_data;
                    if (devices_size > MAX_MESH_DEVICES) {
                        devices_size = MAX_MESH_DEVICES;
                    }

                    for (int i = 0; i < devices_size; i++) {
                        Codec__In__Devices__DevicesData *data = devs->data[i];
                        if (!data) {
                            ESP_LOGE(TAG_PROT, "Null device at index %d", i);
                            continue;
                        }

                        int index = data->idx;
                        if (index < 0 || index >= MAX_MESH_DEVICES) {
                            ESP_LOGE(TAG_PROT, "Skipping out-of-bounds idx: %d", index);
                            continue;
                        }

                        mesh_devices[index].meshid     = data->mesh_id;
                        mesh_devices[index].device_id  = data->device_id;
                        mesh_devices[index].type       = data->device_type;
                        mesh_devices[index].out1       = data->out1;
                        mesh_devices[index].out2       = data->out2;
                        mesh_devices[index].input      = data->input;
                        mesh_devices[index].mode       = data->mode;
                        mesh_devices[index].group      = data->group_idx;
                        mesh_devices[index].sector     = data->sector;
                        mesh_devices[index].power      = data->power;
                        mesh_devices[index].eqpt_type  = data->equipment;
                        //mesh_devices[index].schedule_count = 0;

                        printf("Device(%d) -> mesh_id: %d, device_id: %d, type: %d, out1: %d, out2: %d, input: %d, mode: %d, group: %d, sector: %d, power: %d, eqpt_type: %d\n",
                            index, mesh_devices[index].meshid, mesh_devices[index].device_id,
                            mesh_devices[index].type, mesh_devices[index].out1, mesh_devices[index].out2,
                            mesh_devices[index].input, mesh_devices[index].mode,
                            mesh_devices[index].group, mesh_devices[index].sector, 
                            mesh_devices[index].power, mesh_devices[index].eqpt_type);
                    }

                    if(last_devs_update != (unsigned int)packet->id){
                        last_devs_update = (unsigned int)packet->id;
                        device_count = devices_size;
                        esp_err_t res = memory_save_devices();
                        if (res != ESP_OK) {
                            ESP_LOGE(TAG_PROT, "Failed to save devices to memory. err=0x%X", (unsigned int)res);
                        } else {
                            ESP_LOGI(TAG_PROT, "Devices saved successfully.");
                        }
                    }
                    *len = report_info((uint8_t **)result, S_REP_INFO_BASE);

                } else {
                    ESP_LOGW(TAG_PROT, "No devices in the DevicePackage");
                }

                ESP_LOGI(TAG_PROT, "DEV: %d devices received", devs->n_data);
            }
            break;
        }
        case CODEC__IN__INCOMING_PACKET__PAYLOAD_SCHEDULING: {
            if (packet->scheduling) {
                Codec__In__Scheduling__SchedulingPackage *sched = packet->scheduling;

                if((sched->type & CODEC__IN__SCHEDULING__MSG_TYPE__MSG_SCHEDULING_ONLY) == CODEC__IN__SCHEDULING__MSG_TYPE__MSG_SCHEDULING_ONLY) {
                    int schedulings_size = sched->n_scheduling_data;
                    if (schedulings_size > MAX_MESH_SCHEDULING) {
                        schedulings_size = MAX_MESH_SCHEDULING;
                    }

                    for (int i = 0; i < schedulings_size; i++) {
                        Codec__In__Scheduling__Scheduling *s = sched->scheduling_data[i];
                        if (!s) continue;

                        mesh_scheduling[i].start_time             = s->start_time;
                        mesh_scheduling[i].days_of_week           = s->days_of_week;
                        mesh_scheduling[i].number_of_steps        = s->number_of_steps;
                        mesh_scheduling[i].type                   = 0;
                        mesh_scheduling[i].waterpump_dev_idx      = s->waterpump_idx;
                        mesh_scheduling[i].waterpump_working_time = s->waterpump_working_time;
                        mesh_scheduling[i].allow_backwash         = s->allow_backwash;
                        mesh_scheduling[i].backwash_dev_idx       = s->backwash_idx;
                        mesh_scheduling[i].allow_ferti            = s->allow_ferti;
                        mesh_scheduling[i].ferti_dev_idx          = s->ferti_idx;
                        mesh_scheduling[i].group                  = s->group;
                        mesh_scheduling[i].once                   = s->once;

                        printf("SCHED[%d] -> st: %d, dw: %d, ns: %d, wi: %d, wt: %d, ab: %d, bi: %d, af: %d, fi: %d, gp: %d, oc: %d\n",
                            i, (int)s->start_time, (int)s->days_of_week, (int)s->number_of_steps,
                            (int)s->waterpump_idx, (int)s->waterpump_working_time,
                            (int)s->allow_backwash, (int)s->backwash_idx,
                            (int)s->allow_ferti, (int)s->ferti_idx, (int)s->group, (int)s->once);

                        reset_only_scheduling_flags(i); // Reseta os flags do agendamento
                    }

                    if(last_sched_update != (unsigned int)packet->id){
                        last_sched_update = (unsigned int)packet->id;
                        schedule_count = schedulings_size;
                        esp_err_t res = memory_save_scheduling();
                        if (res != ESP_OK) {
                            ESP_LOGE(TAG_PROT, "Failed to save scheduling. err=0x%X", res);
                        } else {
                            ESP_LOGI(TAG_PROT, "Scheduling saved successfully");
                        }
                        res = memory_save_scheduling_result();
                        if (res != ESP_OK) {
                            ESP_LOGE(TAG_PROT, "Failed to save scheduling result. err=0x%X", res);
                        } else {
                            ESP_LOGI(TAG_PROT, "Scheduling result saved successfully");
                        }
                    }
                    *len = report_info((uint8_t **)result, S_REP_INFO_BASE);

                    ESP_LOGI(TAG_PROT, "SCHED: %d scheduling received", schedulings_size);
                }

                if((sched->type & CODEC__IN__SCHEDULING__MSG_TYPE__MSG_DEV_SCHEDULING_ONLY) == CODEC__IN__SCHEDULING__MSG_TYPE__MSG_DEV_SCHEDULING_ONLY){
                    int device_scheduling_size = sched->n_device_scheduling_data;
                    if (device_scheduling_size > MAX_MESH_DEVICE_SCHEDULING) {
                        device_scheduling_size = MAX_MESH_DEVICE_SCHEDULING;
                    }

                    for (int i = 0; i < device_scheduling_size; i++) {
                        Codec__In__Scheduling__DeviceScheduling *ds = sched->device_scheduling_data[i];
                        if (!ds) continue;

                        mesh_device_scheduling[i].shc_idx = ds->scheduling_idx;
                        mesh_device_scheduling[i].dev_idx = ds->device_idx;
                        mesh_device_scheduling[i].step = ds->order;
                        mesh_device_scheduling[i].sector_working_time = ds->sector_working_time;
                        mesh_device_scheduling[i].ferti_working_time = ds->ferti_working_time;
                        mesh_device_scheduling[i].ferti_delay = ds->ferti_delay;

                        printf("DEV_SCHED %d -> shc_idx: %d, dev_idx: %d, step: %d, sec_wt: %d, fert_wt: %d, fert_delay: %d\n",
                            i, (int)ds->scheduling_idx, (int)ds->device_idx, (int)ds->order,
                            (int)ds->sector_working_time, (int)ds->ferti_working_time, (int)ds->ferti_delay);
                    }
                    
                    if(last_dev_sched_update != (unsigned int)packet->id){
                        last_dev_sched_update = (unsigned int)packet->id;
                        schedules_per_device = device_scheduling_size;
                        esp_err_t res = memory_save_device_scheduling();
                        if (res != ESP_OK) {
                            ESP_LOGE(TAG_PROT, "Failed to save protobuf device scheduling. err=0x%X", res);
                        } else {
                            ESP_LOGI(TAG_PROT, "Device scheduling saved successfully");
                        }
                    }
                    *len = report_info((uint8_t **)result, S_REP_INFO_BASE);

                    ESP_LOGI(TAG_PROT, "DEV_SCHED: %d device scheduling received", device_scheduling_size);
                }
            }
            break;
        }
        case CODEC__IN__INCOMING_PACKET__PAYLOAD_DEV_SCHEDULING: {
            if (packet->dev_scheduling) {
                Codec__In__DeviceScheduling__DeviceSchedulingPackage *dsch = packet->dev_scheduling;
                int size = dsch->n_data;
                if (size > MAX_MESH_DEVICE_SCHEDULING) {
                    size = MAX_MESH_DEVICE_SCHEDULING;
                }

                for (int i = 0; i < size; i++) {
                    Codec__In__DeviceScheduling__DeviceScheduling *ds = dsch->data[i];
                    if (!ds) continue;

                    mesh_device_scheduling[i].shc_idx = ds->scheduling_idx;
                    mesh_device_scheduling[i].dev_idx = ds->device_idx;
                    mesh_device_scheduling[i].step = ds->order;
                    mesh_device_scheduling[i].sector_working_time = ds->sector_working_time;
                    mesh_device_scheduling[i].ferti_working_time = ds->ferti_working_time;
                    mesh_device_scheduling[i].ferti_delay = ds->ferti_delay;

                    printf("DEV_SCHED %d -> shc_idx: %d, dev_idx: %d, step: %d, sec_wt: %d, fert_wt: %d, fert_delay: %d\n",
                        i, (int)ds->scheduling_idx, (int)ds->device_idx, (int)ds->order,
                        (int)ds->sector_working_time, (int)ds->ferti_working_time, (int)ds->ferti_delay);
                }

                if(last_dev_sched_update != (unsigned int)packet->id){
                    last_dev_sched_update = (unsigned int)packet->id;
                    schedules_per_device = size;
                    esp_err_t res = memory_save_device_scheduling();
                    if (res != ESP_OK) {
                        ESP_LOGE(TAG_PROT, "Failed to save protobuf device scheduling. err=0x%X", res);
                    } else {
                        ESP_LOGI(TAG_PROT, "Device scheduling saved successfully");
                    }
                }
                *len = report_info((uint8_t **)result, S_REP_INFO_BASE);

                ESP_LOGI(TAG_PROT, "DEV_SCHED: %d device scheduling received", size);
            }
            break;
        }
        case CODEC__IN__INCOMING_PACKET__PAYLOAD_AUTOMATION: {
            if (packet->automation) {
                Codec__In__Automation__AutomationPackage *auto_pkg = packet->automation;
                int auto_size = auto_pkg->n_data;
                if (auto_size > MAX_MESH_AUTOMATION) {
                    auto_size = MAX_MESH_AUTOMATION;
                }

                for (int i = 0; i < auto_size; i++) {
                    Codec__In__Automation__AutomationData *item = auto_pkg->data[i];
                    if (!item) {
                        ESP_LOGE(TAG_PROT, "Null automation item at index %d", i);
                        continue;
                    }

                    mesh_automation[i].level_dev_idx  = item->level_idx;
                    mesh_automation[i].pump_dev_idx   = item->pump_idx;
                    mesh_automation[i].mask           = item->mask;
                    mesh_automation[i].value          = item->value;
                    mesh_automation[i].working_time   = item->working_time;
                    mesh_automation[i].enabled        = true;

                    printf("AUTO %d -> level_idx: %d, pump_idx: %d, mask: %d, value: %d, enable: %d, working_time: %d\n",
                           i,
                           (int)item->level_idx,
                           (int)item->pump_idx,
                           (int)item->mask,
                           (int)item->value,
                           mesh_automation[i].enabled,
                           (int)item->working_time);
                }

                if(last_auto_update != (unsigned int)packet->id){
                    last_auto_update = (unsigned int)packet->id;
                    automation_count = auto_size;
                    esp_err_t res = memory_save_automation();
                    if (res != ESP_OK) {
                        ESP_LOGE(TAG_PROT, "Failed to save automation to memory. err=0x%X", res);
                    } else {
                        ESP_LOGI(TAG_PROT, "Automation saved successfully");
                    }
                }
                *len = report_info((uint8_t **)result, S_REP_INFO_BASE);

                ESP_LOGI(TAG_PROT, "AUTO: %d automation entries received", auto_size);
            }
            break;
        }
        case CODEC__IN__INCOMING_PACKET__PAYLOAD_CONTROL: {
            if (packet->control) {
                Codec__In__Control__ControlPackage *ctrl = packet->control;

                if(!is_valid_dev_idx(ctrl->idx)) // Verifica se o índice do dispositivo é válido
                    break;
                    
                time_t now;
                time(&now);

                struct s_mesh_control new_control;
                new_control.dev_idx       = ctrl->idx;
                new_control.action        = ctrl->action;
                new_control.working_time  = ctrl->value;
                new_control.enabled       = true;
                new_control.start_time    = now;
                new_control.timeout       = TIMER_3MIN;

                if (ctrl->payload.len > 0 && ctrl->payload.data != NULL) {
                    size_t copy_len = ctrl->payload.len > sizeof(new_control.payload) 
                                    ? sizeof(new_control.payload) 
                                    : ctrl->payload.len;
                    memcpy(new_control.payload, ctrl->payload.data, copy_len);
                    new_control.size = copy_len;
                }

                if (new_control.action == CODEC__IN__CONTROL__MSG_ACTION__MSG_TURN_ON || new_control.action == CODEC__IN__CONTROL__MSG_ACTION__MSG_TURN_OFF ||
                    new_control.action == CODEC__IN__CONTROL__MSG_ACTION__MSG_PACKAGE) {
                    mesh_insert_mesh_control(new_control);
                    ESP_LOGI(TAG_PROT, "CTRL -> idx: %d, action: %d", new_control.dev_idx, new_control.action);
                } else {
                    ESP_LOGW(TAG_PROT, "CTRL: Unknown action received (%d)", new_control.action);
                }
            } else {
                ESP_LOGE(TAG_PROT, "CTRL: NULL payload received");
            }
            break;
        }
        case CODEC__IN__INCOMING_PACKET__PAYLOAD_COMMAND: {
            if (packet->command) {
                Codec__In__Command__CommandPackage *command = packet->command;

                if (command->type == CODEC__IN__COMMAND__MSG_TYPE__MSG_PAUSE) {
                    time_t now;
                    time(&now);
                    pause_scheduling = 1;
                    pause_scheduling_timer = command->value * 60;
                    pause_scheduling_time = now;
                    ESP_LOGW(TAG_PROT, "Scheduling Paused for %dh by command", (int)command->value/60);
                } else if(command->type == CODEC__IN__COMMAND__MSG_TYPE__MSG_RESUME) {
                    pause_scheduling = 0;
                    ESP_LOGW(TAG_PROT, "Scheduling Resumed by command");
                }

                if (protocol.origin == P_MQTT) {
                    *len = report_status((uint8_t **)result);
                }
            } else {
                ESP_LOGE(TAG_PROT, "COMMAND: NULL payload received");
            }
            break;
        }
        case CODEC__IN__INCOMING_PACKET__PAYLOAD_REQUEST_INFO: {
            if (packet->request_info) {
                Codec__In__RequestInfo__RequestInfoPackage *request_info = packet->request_info;

                int type = request_info->type;
                *len = report_info((uint8_t **)result, S_REP_INFO_BASE);
                if (protocol.origin == P_MQTT)
                    anticipate_report = true; // Define o flag para antecipar o relatório
            } else {
                ESP_LOGE(TAG_PROT, "Request Info: NULL payload received");
            }
            break;
        }
        case CODEC__IN__INCOMING_PACKET__PAYLOAD_FIRMWARE_UPDATE: {
            if (packet->request_info) {
                Codec__In__FirmwareUpdate__FirmwareUpdatePackage *firmware_update = packet->firmware_update;

                ota_fw_update.type = firmware_update->type;
                ota_fw_update.proto = firmware_update->protocol;
                ota_fw_update.version = firmware_update->version;
                
                if (ota_handle == NULL) {
                    if(xTaskCreate(&ota_task, "ota_task", 8192, NULL, 5, &ota_handle) == pdPASS) {
                        ESP_LOGI(TAG_PROT, "Firmware Update: OTA task created successfully");
                    } else {
                        ota_handle = NULL; 
                        ESP_LOGE(TAG_PROT, "Firmware Update: Failed to create OTA task");
                    }
                }
            } else {
                ESP_LOGE(TAG_PROT, "Firmware Update: NULL payload received");
            }
            break;
        }
        default:
            ESP_LOGW(TAG_PROT, "Unhandled packet payload %d", packet->payload_case);
        break;
    }   

    codec__in__incoming_packet__free_unpacked(packet, NULL);

    return ESP_OK;
}
