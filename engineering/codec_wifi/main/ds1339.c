#include "ds1339.h"

static const char *TAG_DS = "DS1339";
extern i2c_master_bus_handle_t i2c_bus;
i2c_master_dev_handle_t i2c_ds1339 = NULL;

uint8_t bcd2dec(uint8_t v){ return (v>>4)*10 + (v & 0x0F); }
uint8_t dec2bcd(uint8_t v){ return ((v/10)<<4) | (v%10); }

esp_err_t ds1339_init_device(void)
{
    if (i2c_bus == NULL) return ESP_ERR_INVALID_STATE;

    i2c_device_config_t dev_cfg = {
        .dev_addr_length = I2C_ADDR_BIT_LEN_7,
        .device_address  = DS1339_ADDR,
        .scl_speed_hz    = I2C_FREQ_HZ
    };
    return i2c_master_bus_add_device(i2c_bus, &dev_cfg, &i2c_ds1339);
}

esp_err_t ds1339_set_time_epoch(time_t epoch)
{
    struct tm utc;
    if (gmtime_r(&epoch, &utc) == NULL)
        return ESP_ERR_INVALID_ARG;

    return ds1339_set_time(&utc);
}

esp_err_t ds1339_set_time(const struct tm *t)
{
    uint8_t buf[8] = {
        0x00,
        dec2bcd(t->tm_sec  & 0x7F),
        dec2bcd(t->tm_min  & 0x7F),
        dec2bcd(t->tm_hour & 0x3F),              // 24 h
        dec2bcd(t->tm_wday ? t->tm_wday : 7),    // 1–7
        dec2bcd(t->tm_mday),
        dec2bcd(t->tm_mon + 1),                  // 1–12
        dec2bcd((t->tm_year + 1900) % 100),      // 00–99
    };
    return i2c_master_transmit(i2c_ds1339, buf, sizeof(buf), pdMS_TO_TICKS(100));
}

esp_err_t ds1339_get_time(struct tm *t)
{
    uint8_t reg = 0x00;
    uint8_t buf[7];

    ESP_RETURN_ON_ERROR(i2c_master_transmit(i2c_ds1339, &reg, 1, pdMS_TO_TICKS(100)), TAG_DS, "RTC send register");
    ESP_RETURN_ON_ERROR(i2c_master_receive(i2c_ds1339, buf, sizeof(buf), pdMS_TO_TICKS(100)), TAG_DS, "RTC read date");

    t->tm_sec  = bcd2dec(buf[0] & 0x7F);
    t->tm_min  = bcd2dec(buf[1]);
    t->tm_hour = bcd2dec(buf[2] & 0x3F);
    t->tm_wday = bcd2dec(buf[3]);
    t->tm_mday = bcd2dec(buf[4]);
    t->tm_mon  = bcd2dec(buf[5] & 0x1F) - 1;      // 0-11
    t->tm_year = bcd2dec(buf[6]) + 100;           // base 1900 → 20xx
    return ESP_OK;
}

void set_clock_from_epoch(time_t epoch)
{
    struct timeval tv = { .tv_sec = epoch, .tv_usec = 0 };
    settimeofday(&tv, NULL);

    esp_err_t err = ds1339_set_time_epoch(epoch);
    if (err == ESP_OK) {
        ESP_LOGI(TAG_DS, "Clock & DS1339 set from epoch: %lld", (long long)epoch);
    } else {
        ESP_LOGE(TAG_DS, "Failed to write DS1339: %s", esp_err_to_name(err));
    }
}