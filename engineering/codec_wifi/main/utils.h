#ifndef UTILS_H
#define UTILS_H

#include "defines.h"
#include <stdint.h>
#include <string.h>
#include <ctype.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

void url_decode(char *dest, const char *src);
int is_valid_char(char c);
int is_valid_string(const char *str);
void remove_invalid_chars(char *str);
uint16_t crc16(uint8_t *buffer, uint16_t size);
uint64_t ones_mask(uint32_t bits);

#endif // UTILS_H