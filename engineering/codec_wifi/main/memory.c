#include "memory.h"
#include "mesh.h"

static const char *TAG_MEM = "Memory";
extern char sta_ssid[MAX_STA_SSID_SIZE];
extern char sta_pass[MAX_STA_PASS_SIZE];
extern i2c_master_bus_handle_t i2c_bus;
static i2c_master_dev_handle_t eeprom = NULL;

esp_err_t memory_eeprom_init(void)
{
    i2c_device_config_t dev_cfg = {
        .dev_addr_length = I2C_ADDR_BIT_LEN_7,
        .device_address  = EEPROM_ADDR,
        .scl_speed_hz    = I2C_FREQ_HZ,
        .scl_wait_us = 50,
    };
    return i2c_master_bus_add_device(i2c_bus, &dev_cfg, &eeprom);
}

esp_err_t memory_eeprom_wait_ready(uint32_t max_wait_ms) {
    const TickType_t probe_intv = pdMS_TO_TICKS(2);
    TickType_t t0 = xTaskGetTickCount();
    while ((xTaskGetTickCount() - t0) < pdMS_TO_TICKS(max_wait_ms)) {
        if (i2c_master_probe(i2c_bus, EEPROM_ADDR, 25) == ESP_OK) {
            return ESP_OK;
        }
        vTaskDelay(probe_intv);
    }
    return ESP_ERR_TIMEOUT;
}

esp_err_t memory_eeprom_write_page(uint16_t mem, const uint8_t *data, size_t len)
{
    uint8_t buf[2 + EEPROM_PAGE_SIZE];
    buf[0] = mem >> 8;          /* endereço alto  */
    buf[1] = mem & 0xFF;        /* endereço baixo */
    memcpy(&buf[2], data, len);

    /* 2‑byte address + payload  → 1× WRITE */
    ESP_RETURN_ON_ERROR(i2c_master_transmit(eeprom, buf, 2 + len, 50), EEPROM_TAG, "Tx");

    return memory_eeprom_wait_ready(10);
}

esp_err_t memory_eeprom_write(uint16_t mem, const uint8_t *d, size_t n)
{
    gpio_set_level(GPIO_OUTPUT_IO_5, 0);            /* Desbloqueia WP */

    while (n) {
        size_t off   = mem % EEPROM_PAGE_SIZE;
        size_t room  = EEPROM_PAGE_SIZE - off;
        size_t chunk = n < room ? n : room;

        ESP_RETURN_ON_ERROR(memory_eeprom_write_page(mem, d, chunk), EEPROM_TAG, "Page");

        mem += chunk; d += chunk; n -= chunk;
    }

    gpio_set_level(GPIO_OUTPUT_IO_5, 1);            /* Restaura WP */
    return ESP_OK;
}

esp_err_t memory_eeprom_read(uint16_t mem, uint8_t *out, size_t len) {
    uint8_t addr[2] = {mem >> 8, mem & 0xFF};
    esp_err_t err = i2c_master_transmit_receive(eeprom, addr, sizeof(addr), out, len, 50);
    if (err != ESP_OK) {
        ESP_LOGE(EEPROM_TAG, "Tx/Rx failed: %s", esp_err_to_name(err));
    }
    return err;
}

esp_err_t memory_nvs_init()
{
    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }

    memory_nvs_stats();

    ESP_ERROR_CHECK(ret);
    return ret;
}

void memory_nvs_stats()
{
    nvs_stats_t nvs_stats;
    esp_err_t err = nvs_get_stats(NULL, &nvs_stats); 
    if (err == ESP_OK) {
        ESP_LOGI("NVS_STATS", "NVS partition usage: used_entries = %d, free_entries = %d, total_entries = %d",
                 nvs_stats.used_entries, nvs_stats.free_entries, nvs_stats.total_entries);
    } else {
        ESP_LOGE("NVS_STATS", "Failed to get NVS stats! (err=0x%X)", err);
    }
}

esp_err_t memory_save_system_info()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("system", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Informações do sistema
    err = nvs_set_blob(nvs_handle, "sysinfo", &system_info, sizeof(system_info));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_system_info()
{
    nvs_handle_t nvs_handle;
    system_info.resets = 0;
    esp_err_t err = nvs_open("system", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar as informações do sistema
    size_t size = sizeof(system_info);
    err = nvs_get_blob(nvs_handle, "sysinfo", &system_info, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Atualiza o número de reinicializações
    system_info.resets += 1;
    ESP_LOGI(TAG_MEM, "Number of Resets: %u", system_info.resets);

    return ESP_OK;
}

esp_err_t memory_save_wifi_credentials(char* ssid, char* password)
{
    nvs_handle_t nvs_handle;
    esp_err_t err;

    // Abre o NVS
    err = nvs_open("wifi", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Salva o SSID
    err = nvs_set_str(nvs_handle, "ssid", ssid);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salva a senha
    err = nvs_set_str(nvs_handle, "passwd", password);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_wifi_credentials(char* ssid, size_t ssid_size, char* password, size_t password_size)
{
    nvs_handle_t nvs_handle;
    esp_err_t err;

    // Abre o NVS em modo de leitura
    err = nvs_open("wifi", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carrega o SSID
    err = nvs_get_str(nvs_handle, "ssid", ssid, &ssid_size);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carrega a senha
    err = nvs_get_str(nvs_handle, "passwd", password, &password_size);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_save_devices()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("devs", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Salva horário da última atualização
    err = nvs_set_u32(nvs_handle, "devs_t", last_devs_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar o número de dispositivos
    err = nvs_set_u16(nvs_handle, "devs_c", device_count);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar a lista de dispositivos
    err = nvs_set_blob(nvs_handle, "devs_l", mesh_devices, sizeof(mesh_devices));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_devices()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("devs", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar o horário da última atualização
    err = nvs_get_u32(nvs_handle, "devs_t", (uint32_t *)&last_devs_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar o número de dispositivos
    err = nvs_get_u16(nvs_handle, "devs_c", &device_count);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar a lista de dispositivos
    size_t size = sizeof(mesh_devices);
    err = nvs_get_blob(nvs_handle, "devs_l", mesh_devices, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    ESP_LOGI(TAG_MEM, "Loaded Device List: %d devices", device_count);
    for (int i = 0; i < device_count; i++) {
        ESP_LOGI(TAG_MEM, "Device %d -> MeshID: %d, DeviceID: %d, Type: %d, Out1: %d, Out2: %d, Input: %d, Mode: %d, Group: %d, Sector: %d, Power %d, Eqpt_type %d",
            i, mesh_devices[i].meshid, mesh_devices[i].device_id, mesh_devices[i].type,
            mesh_devices[i].out1, mesh_devices[i].out2, mesh_devices[i].input,
            mesh_devices[i].mode, mesh_devices[i].group, mesh_devices[i].sector, 
            mesh_devices[i].power, mesh_devices[i].eqpt_type);
    }

    return ESP_OK;
}

esp_err_t memory_save_scheduling()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("sched", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Salva horário da última atualização
    err = nvs_set_u32(nvs_handle, "sched_t", last_sched_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar o número de agendamentos
    err = nvs_set_u16(nvs_handle, "sched_c", schedule_count);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar a lista de agendamentos
    err = nvs_set_blob(nvs_handle, "sched_l", mesh_scheduling, sizeof(mesh_scheduling));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_scheduling()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("sched", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar o horário da última atualização
    err = nvs_get_u32(nvs_handle, "sched_t", (uint32_t *)&last_sched_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar o número de agendamentos
    err = nvs_get_u16(nvs_handle, "sched_c", &schedule_count);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar a lista de agendamentos
    size_t size = sizeof(mesh_scheduling);
    err = nvs_get_blob(nvs_handle, "sched_l", mesh_scheduling, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Exibir a lista de agendamentos carregada
    ESP_LOGI(TAG_MEM, "Loaded Schedules List: %d items", schedule_count);
    for (int i = 0; i < schedule_count; i++) {
        ESP_LOGI(TAG_MEM, "Scheduling %d -> stime: %d, dweek: %d, nsteps: %d, wpump_idx: %d, wpump_wt: %d, allow_back: %d, back_idx: %d, allow_fert: %d, ferti_idx: %d, group: %d, once: %d\n",
                i, mesh_scheduling[i].start_time, mesh_scheduling[i].days_of_week,
                mesh_scheduling[i].number_of_steps, mesh_scheduling[i].waterpump_dev_idx, 
                mesh_scheduling[i].waterpump_working_time, mesh_scheduling[i].allow_backwash,
                mesh_scheduling[i].backwash_dev_idx, mesh_scheduling[i].allow_ferti,
                mesh_scheduling[i].ferti_dev_idx, mesh_scheduling[i].group, mesh_scheduling[i].once);
    }

    return ESP_OK;
}

esp_err_t memory_save_scheduling_result()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("sched_result", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

   // Salvar o resultado dos agendamentos
    err = nvs_set_blob(nvs_handle, "sched_result_l", mesh_scheduling_result, sizeof(mesh_scheduling_result));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_scheduling_result()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("sched_result", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar os resultados dos agendamentos
    size_t size = sizeof(mesh_scheduling_result);
    err = nvs_get_blob(nvs_handle, "sched_result_l", mesh_scheduling_result, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    ESP_LOGI(TAG_MEM, "Loaded Schedules Result:");
    for (int i = 0; i < schedule_count; i++) {
        ESP_LOGI(TAG_MEM, "Scheduling Result %d -> last update: %d, sectors: %llu, ferti: %llu, status: %d\n",
                i, mesh_scheduling_result[i].last_update, mesh_scheduling_result[i].sectors,
                mesh_scheduling_result[i].ferti, mesh_scheduling_result[i].status);
    }

    return ESP_OK;
}

esp_err_t memory_save_device_scheduling()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("dev_sched", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Horário da última atualização
    err = nvs_set_u32(nvs_handle, "dev_sched_t", last_dev_sched_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar o número de agendamentos
    err = nvs_set_u16(nvs_handle, "dev_sched_c", schedules_per_device);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar a lista de agendamentos
    err = nvs_set_blob(nvs_handle, "dev_sched_l", mesh_device_scheduling, sizeof(mesh_device_scheduling));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_device_scheduling()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("dev_sched", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar o horário da última atualização
    err = nvs_get_u32(nvs_handle, "dev_sched_t", (uint32_t *)&last_dev_sched_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar o número de agendamentos
    err = nvs_get_u16(nvs_handle, "dev_sched_c", &schedules_per_device);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar a lista de agendamentos
    size_t size = sizeof(mesh_device_scheduling);
    err = nvs_get_blob(nvs_handle, "dev_sched_l", mesh_device_scheduling, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Exibir a lista de agendamentos carregada
    ESP_LOGI(TAG_MEM, "Loaded Device Schedules List: %d items", schedules_per_device);
    for (int i = 0; i < schedules_per_device; i++) {
        ESP_LOGI(TAG_MEM, "Device Scheduling %d -> shc_idx: %d, dev_idx: %d, step: %d, sector_working_time: %d, ferti_working_time: %d, ferti_delay: %d",
            i, mesh_device_scheduling[i].shc_idx, mesh_device_scheduling[i].dev_idx,
                mesh_device_scheduling[i].step, mesh_device_scheduling[i].sector_working_time,
                mesh_device_scheduling[i].ferti_working_time, mesh_device_scheduling[i].ferti_delay);
    }

    return ESP_OK;
}

esp_err_t memory_save_automation()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("auto", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Salvar horário da última atualização
    err = nvs_set_u32(nvs_handle, "auto_t", last_auto_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar o número de agendamentos
    err = nvs_set_u16(nvs_handle, "auto_c", automation_count);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Salvar a lista de agendamentos
    err = nvs_set_blob(nvs_handle, "auto_l", mesh_automation, sizeof(mesh_automation));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_automation()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("auto", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar o horário da última atualização
    err = nvs_get_u32(nvs_handle, "auto_t", (uint32_t *)&last_auto_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar o número de agendamentos
    err = nvs_get_u16(nvs_handle, "auto_c", &automation_count);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar a lista de agendamentos
    size_t size = sizeof(mesh_automation);
    err = nvs_get_blob(nvs_handle, "auto_l", mesh_automation, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Exibir a lista de agendamentos carregada
    ESP_LOGI(TAG_MEM, "Automation List: %d items", automation_count);
    for (int i = 0; i < automation_count; i++) {
        ESP_LOGI(TAG_MEM,"Automation %d -> level_idx: %d, pump_idx: %d, mask: %d, value: %d, enable: %d, working_time: %d\n",
                i, mesh_automation[i].level_dev_idx, mesh_automation[i].pump_dev_idx,
                mesh_automation[i].mask, mesh_automation[i].value, mesh_automation[i].enabled,
                mesh_automation[i].working_time);
    }

    return ESP_OK;
}

esp_err_t memory_save_config()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("config", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Horário da última atualização
    err = nvs_set_u32(nvs_handle, "time", last_config_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Configuração da retro lavagem
    err = nvs_set_blob(nvs_handle, "others", &mesh_config, sizeof(mesh_config));
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    return err;
}

esp_err_t memory_load_config()
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("config", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    // Carregar o horário da última atualização
    err = nvs_get_u32(nvs_handle, "time", (uint32_t *)&last_config_update);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    // Carregar a configuração da retro lavagem
    size_t size = sizeof(mesh_config);
    err = nvs_get_blob(nvs_handle, "others", &mesh_config, &size);
    nvs_close(nvs_handle);
    if (err != ESP_OK) {
        return err;
    }

    ESP_LOGI(TAG_MEM, "Loaded configurations:");
    ESP_LOGI(TAG_MEM, "BWcyc:%u BWdur:%u BWdel:%u RainE:%u RainF:%u RLim:%u RPause:%u Raw:%u Dbg:%u ResE:%u ResF:%u ResAtt:%u",
        mesh_config.backwash_cycle_time,
        mesh_config.backwash_duration,
        mesh_config.backwash_delay,
        mesh_config.raingauge_enabled,
        mesh_config.raingauge_factor,
        mesh_config.rainfall_limit,
        mesh_config.rainfall_pause_duration,
        mesh_config.publish_raw_data,
        mesh_config.debug,
        mesh_config.enable_schedule_resumption,
        mesh_config.enable_ferti_resumption,
        mesh_config.max_resumption_attempts
    );

    return ESP_OK;
}
