#ifndef MAIN_H
#define MAIN_H

#include "defines.h"

#include <stdint.h>
#include <string.h>
#include <ctype.h>
#include "esp_mac.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_netif.h"
#include <sys/socket.h>
#include <netinet/in.h>
#include <driver/uart.h>
#include <driver/gpio.h> 
#include <unistd.h>
#include <time.h>
#include "esp_sntp.h"
#include <inttypes.h>

typedef enum {
    CONTROL_NONE      = 0,
    CONTROL_TURN_ON   = 1,
    CONTROL_TURN_OFF  = 2,
    CONTROL_PACKAGE   = 3,
    CONTROL_UP_PARAM  = 4
} control_action_t;

#endif // MAIN_H