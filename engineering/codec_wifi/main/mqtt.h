#ifndef MQTT_H
#define MQTT_H

#include "defines.h"
#include "mesh.h"
#include "report.h"
#include "protocol.h"

#include <stdint.h>
#include <string.h>
#include <ctype.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include <unistd.h>
#include <time.h>
#include "mqtt_client.h"
#include "esp_mac.h"
#include "esp_timer.h"

#ifndef MQTT_GLOBAL
#define MQ_GLOBAL extern
#else
#define MQ_GLOBAL
#endif

MQ_GLOBAL esp_mqtt_client_handle_t mqtt_client;
MQ_GLOBAL bool mqtt_connected;
MQ_GLOBAL char publish_topic[120];
MQ_GLOBAL char publish_topic_report[120];
MQ_GLOBAL char subscribe_topic[120];

void mqtt_init(void);
void mqtt_event_handler(void *handler_args, esp_event_base_t base, int32_t event_id, void *event_data);

#endif // MQTT_H