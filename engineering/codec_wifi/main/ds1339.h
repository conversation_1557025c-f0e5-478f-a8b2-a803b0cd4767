#ifndef DS1339_H
#define DS1339_H

#include "defines.h"
#include "driver/i2c_master.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_check.h"
#include <sys/time.h>
#include <time.h>

esp_err_t ds1339_init_device(void);
esp_err_t ds1339_set_time(const struct tm *t);
esp_err_t ds1339_set_time_epoch(time_t epoch);
esp_err_t ds1339_get_time(struct tm *t);
void set_clock_from_epoch(time_t epoch);

#endif // DS1339_H