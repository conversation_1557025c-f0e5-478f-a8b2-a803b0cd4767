#define MQTT_GLOBAL
#include "mqtt.h"

static const char *TAG_MQTT = "MQTT";
bool mqtt_connected = false;
bool task_notify = false;

//extern char json_buffer[MAX_JSON_SIZE];
//extern int json_buffer_len;
static int64_t data_last_time = 0;
extern SemaphoreHandle_t data_mutex;
extern TaskHandle_t main_task_handle;

void mqtt_init(void) {
    uint8_t mac[6];
    esp_efuse_mac_get_default(mac);
    
    snprintf(publish_topic, sizeof(publish_topic),
             "/codec/%02X%02X%02X%02X%02X%02X/uplink",
             mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

    snprintf(publish_topic_report, sizeof(publish_topic_report),
             "/codec/%02X%02X%02X%02X%02X%02X/report",
             mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

    snprintf(subscribe_topic, sizeof(subscribe_topic),
             "/codec/%02X%02X%02X%02X%02X%02X/downlink",
             mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

    ESP_LOGI(TAG_MQTT, "Publish Topic: %s", publish_topic);
    ESP_LOGI(TAG_MQTT, "Publish Report Topic: %s", publish_topic_report);
    ESP_LOGI(TAG_MQTT, "Subscribe Topic: %s", subscribe_topic);

    const esp_mqtt_client_config_t mqtt_cfg = {
        .broker.address.uri = MQTT_BROKER_URI,
        .credentials.username        = "codec",
        .credentials.authentication.password = "Y29kZWM=",
        .network.reconnect_timeout_ms = 3000,
        .network.timeout_ms           = 3000,
        .outbox = {
            .limit = 8192,
        },
    };

    mqtt_client = esp_mqtt_client_init(&mqtt_cfg);
    esp_mqtt_client_register_event(mqtt_client, ESP_EVENT_ANY_ID, mqtt_event_handler, mqtt_client);
}


// Callback de eventos MQTT
void mqtt_event_handler(void *handler_args, esp_event_base_t base, int32_t event_id, void *event_data) 
{
    esp_mqtt_event_handle_t event = event_data;
    mqtt_client = event->client;

    switch ((esp_mqtt_event_id_t)event_id) {
        case MQTT_EVENT_CONNECTED:
            ESP_LOGI(TAG_MQTT, "MQTT_EVENT_CONNECTED");
            mqtt_connected = true;
            esp_mqtt_client_subscribe(mqtt_client, subscribe_topic, 0);
            break;
        case MQTT_EVENT_DISCONNECTED:
            mqtt_connected = false;
            ESP_LOGI(TAG_MQTT, "MQTT_EVENT_DISCONNECTED");
            break;
        case MQTT_EVENT_SUBSCRIBED:
            ESP_LOGI(TAG_MQTT, "MQTT_EVENT_SUBSCRIBED, msg_id=%d", event->msg_id);
            break;
        case MQTT_EVENT_UNSUBSCRIBED:
            ESP_LOGI(TAG_MQTT, "MQTT_EVENT_UNSUBSCRIBED, msg_id=%d", event->msg_id);
            break;
        case MQTT_EVENT_PUBLISHED:
            ESP_LOGI(TAG_MQTT, "MQTT_EVENT_PUBLISHED, msg_id=%d", event->msg_id);
            break;
        case MQTT_EVENT_DATA:
            ESP_LOGI(TAG_MQTT, "MQTT_EVENT_DATA V2");
            printf("Topic: %d -> %.*s\r\n", event->topic_len, event->topic_len, event->topic);
            printf("Offset: %d, Total %d\r\n", event->current_data_offset, event->total_data_len);
            //printf("Data: %d -> %.*s\r\n", event->data_len, event->data_len, event->data);

            int64_t now = esp_timer_get_time();
            if (protocol.len > 0 && task_notify == false && (now - data_last_time) > 3 * 1000000) { // 3s
                ESP_LOGW(TAG_MQTT, "Previous mount timeout");
                protocol.len = 0;
                xSemaphoreGive(data_mutex);
            }

            if (event->current_data_offset == 0) {
                if (xSemaphoreTake(data_mutex, 0) != pdTRUE) {
                    ESP_LOGW(TAG_MQTT, "Buffer busy – discarding");
                    return;
                }
                protocol.len = 0;
                task_notify = false;
                memset(protocol.data, 0, MAX_DATA_SIZE);
                data_last_time = now;
            }

            if (protocol.len + event->data_len <= MAX_DATA_SIZE) {
                memcpy(protocol.data + protocol.len, event->data, event->data_len);
                protocol.len += event->data_len;
                data_last_time = now;
            } else {
                ESP_LOGE(TAG_MQTT, "Buffer overflow – discarding message");
                protocol.len = 0;
                xSemaphoreGive(data_mutex);
                return;
            }

            if (protocol.len == event->total_data_len) {
                task_notify = true;
                protocol.origin = P_MQTT;
                xSemaphoreGive(data_mutex); 
                xTaskNotify(main_task_handle, EVENT_DATA, eSetBits);
            }

            break;
        case MQTT_EVENT_ERROR:
            ESP_LOGI(TAG_MQTT, "MQTT_EVENT_ERROR");
            break;
        default:
            ESP_LOGI(TAG_MQTT, "Another id event:%d", event->event_id);
            break;
    }
}