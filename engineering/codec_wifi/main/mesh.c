#define MESH_GLOBAL
#include "mesh.h"

static const char *TAG_MESH = "MESH";
uint16_t device_count = 0;
uint16_t schedule_count = 0;

void mesh_insert_mesh_control(struct s_mesh_control new_control) 
{
    for (int i = 0; i < MAX_MESH_CONTROL; i++) {
        if (mesh_control[i].dev_idx == new_control.dev_idx) {
            mesh_control[i].dev_idx = 0;
            mesh_control[i].action = 0;
            mesh_control[i].start_time = 0;
            mesh_control[i].working_time = 0;
            mesh_control[i].enabled = false;
            mesh_control[i].timeout = TIMER_3MIN;
            break;
        }
    }

    // Shift elements to the right
    for (int i = MAX_MESH_CONTROL - 1; i > 0; i--) {
        mesh_control[i] = mesh_control[i - 1];
    }

    // Insert new element at index 0
    mesh_control[0] = new_control;
}

void mesh_pumplink_turn_on_encode(uint32_t id, uint8_t device_id, uint8_t out1, uint8_t out2, uint8_t input, uint8_t mode, uint16_t working_time, char *mesh_pkg)
{
    uint8_t outputs = (out2 << 4) | out1;
    uint8_t mesh_array[10] = {
        (id >> 16) & 0xFF,
        (id >> 8) & 0xFF,
        id & 0xFF,
        1,
        device_id,
        outputs,
        input,
        mode,
        (working_time >> 8) & 0xFF,
        working_time & 0xFF
    };

    char hex_string[21]; 
    for (int i = 0; i < 10; i++) {
        sprintf(&hex_string[i * 2], "%02X", mesh_array[i]);
    }

    sprintf(mesh_pkg, "{01;%s;", hex_string);

    uint16_t crc = crc16((uint8_t *)mesh_pkg, strlen(mesh_pkg));
    sprintf(mesh_pkg + strlen(mesh_pkg), "%04X}", crc);
}

void mesh_pumplink_turn_off_encode(uint32_t id, uint8_t device_id, uint8_t out, uint8_t mode, char *mesh_pkg) {
    uint8_t mesh_array[7] = {
        (id >> 16) & 0xFF,
        (id >> 8) & 0xFF,
        id & 0xFF,
        2,
        device_id,
        out,
        mode
    };

    char hex_string[15]; 
    for (int i = 0; i < 7; i++) {
        sprintf(&hex_string[i * 2], "%02X", mesh_array[i]);
    }

    sprintf(mesh_pkg, "{01;%s;", hex_string);

    uint16_t crc = crc16((uint8_t *)mesh_pkg, strlen(mesh_pkg));
    sprintf(mesh_pkg + strlen(mesh_pkg), "%04X}", crc);
}

void mesh_pumplink_package_encode(uint32_t id, uint8_t *payload, uint8_t size, char *mesh_pkg) {
    uint8_t header[3] = {
        (id >> 16) & 0xFF,
        (id >> 8)  & 0xFF,
        id & 0xFF
    };

    char *cursor = mesh_pkg;
    int n = sprintf(cursor, "{01;");
    cursor += n;

    for (int i = 0; i < 3; i++) {
        cursor += sprintf(cursor, "%02X", header[i]);
    }
    for (int i = 0; i < size; i++) {
        cursor += sprintf(cursor, "%02X", payload[i]);
    }

    cursor += sprintf(cursor, ";");

    uint16_t crc = crc16((uint8_t *)mesh_pkg, strlen(mesh_pkg));
    sprintf(cursor, "%04X}", crc);
}

void mesh_pumplink_transmission_timer_encode(uint32_t id, uint8_t time, char *mesh_pkg) {
    uint8_t mesh_array[7] = {
        (id >> 16) & 0xFF,
        (id >> 8) & 0xFF,
        id & 0xFF,
        4,
        time
    };

    char hex_string[15]; 
    for (int i = 0; i < 7; i++) {
        sprintf(&hex_string[i * 2], "%02X", mesh_array[i]);
    }

    sprintf(mesh_pkg, "{01;%s;", hex_string);

    uint16_t crc = crc16((uint8_t *)mesh_pkg, strlen(mesh_pkg));
    sprintf(mesh_pkg + strlen(mesh_pkg), "%04X}", crc);
}

esp_err_t mesh_mesh_decode(uint8_t *bytes, size_t length, struct s_mesh_decode *decode)
{
    char prefix[3];
    char payload[51];
    char crc[5];
    unsigned char data[26];
    
    const uint8_t *p_start = memchr(bytes, '{', length);
    const uint8_t *p_end   = memchr(bytes, '}', length);
    if (!p_start || !p_end || p_end < p_start + 5)
        return ESP_FAIL;
    
    uint16_t crc_rx;
    if (sscanf((const char *)(p_end - 4), "%4hx", &crc_rx) != 1)
        return ESP_FAIL;

    size_t region_len = (size_t)((p_end - 4) - p_start);
    uint16_t crc_calc = crc16(p_start, region_len);
    if (crc_calc != crc_rx) {
        ESP_LOGW(TAG_MESH, "CRC Invalid: rx=%04X calc=%04X", crc_rx, crc_calc);
        return ESP_ERR_INVALID_CRC;
    }

    int result = sscanf((const char *)bytes, "{%2[^;];%99[^;];%99[^}]}", prefix, payload, crc);
    if (result != 3) {
        return ESP_FAIL;
    }

    int size = strlen(payload);
    for(int i=0; i<size/2; i++){
        sscanf(payload + i * 2, "%02hhX", &data[i]);
    }

    struct timeval tv;
    gettimeofday(&tv, NULL);
    time_t now;
    time(&now);
    uint32_t id = (data[0] << 16) | (data[1] << 8) | data[2];
    if(id != 0 && data[4] == 0x05) {
        decode->meshid = id;
        decode->devices_working = data[5];
        decode->outputs = data[6];
        decode->inputs = data[7];
        decode->device_id = data[8] & 0x0F;
        decode->flags = (data[8] >> 4) & 0x0F;
        decode->time_left = (data[9] << 8) | data[10];
        decode->last_sync = now;

        unsigned int elap_sync = 0;
        for(int i=0; i<MAX_MESH_DEVICES; i++){
            if(mesh_devices[i].meshid == decode->meshid){
                mesh_device_state[i].id = decode->meshid;
                mesh_device_state[i].device_status = decode->devices_working & (1 << mesh_devices[i].device_id);
                mesh_device_state[i].outputs = decode->outputs;
                mesh_device_state[i].inputs = decode->inputs;
                mesh_device_state[i].flags = decode->flags;
                if(mesh_devices[i].device_id == decode->device_id){
                    mesh_device_state[i].time_left = decode->time_left;
                }
                elap_sync = decode->last_sync - mesh_device_state[i].last_sync;
                mesh_device_state[i].last_sync = decode->last_sync;
            }
        }

        ESP_LOGI(TAG_MESH, "DEV_STATUS -> meshid: %d, devices_working: %d, inputs: %d, outputs: %d, flags: %d, device_id: %d, time_left: %d, last_sync: %d, elap_sync: %d",
         decode->meshid, decode->devices_working, decode->inputs, decode->outputs, decode->flags, decode->device_id, decode->time_left, decode->last_sync, elap_sync);
        return ESP_OK;
    } if (id != 0 && data[4] == 0x00) {
        unsigned int meshid = id;
        unsigned int last_sync = now;
        unsigned char equipment = data[5];
        unsigned char model = data[6];
        unsigned int version = (data[7] << 8) | data[8];
        unsigned char resets = data[9];
       
        ESP_LOGI(TAG_MESH, "DEV_RESET -> meshid: %d, equipt: %d, model: %d, fw_ver: %d, resets: %d, last_sync: %d", meshid, equipment, model, version, resets, last_sync); 
        return ESP_OK;
    } if (id != 0 && data[4] == 0x02) {
        unsigned int meshid = id;
        unsigned int last_sync = now;
        unsigned int transmission_timer = data[5]*60;
        unsigned char resend_limit = data[6];
        unsigned char sending_attempts = data[7];
        unsigned char resend_attempts = data[8];
        unsigned int fixed_send_delay = data[9]*100;
        
        ESP_LOGI(TAG_MESH, "DEV_CONFIG -> meshid: %d, send_timer: %d, resend_lim: %d, send_attempts: %d, resend_attempts: %d, fixed_send_delay: %d, last_sync: %d", meshid, transmission_timer, resend_limit, sending_attempts, resend_attempts, fixed_send_delay, last_sync);
        return ESP_OK;
    } else {
        return ESP_FAIL;
    }
}
