#ifndef PROTOCOL_H
#define PROTOCOL_H

#include "defines.h"
#include "mesh.h"
#include "memory.h"
#include "report.h"

#include <stdint.h>
#include <string.h>
#include <ctype.h>
#include "esp_log.h"
#include <time.h>
#include <inttypes.h>

typedef enum {
    P_MQTT,
    P_WIFI,
} prot_origin_t;

struct s_protocol {
    char data[MAX_DATA_SIZE+1];
    char origin; // 0 = MQTT, 1 = WIFI
    unsigned int len;
};

typedef enum {
    OTA_TYPE_ESP  = 0,
    OTA_TYPE_MESH = 1,
} ota_type_t;

typedef enum {
    OTA_PROTO_HTTPS = 0,
    OTA_PROTO_HTTP  = 1,
} ota_proto_t;

struct s_ota_fw_update {
    ota_type_t type;
    ota_proto_t proto;
    u_int32_t version;
};

#ifndef PROT_GLOBAL
#define PE_GLOBAL extern
#else
#define PE_GLOBAL
#endif

PE_GLOBAL struct s_protocol protocol;
PE_GLOBAL struct s_ota_fw_update ota_fw_update;

esp_err_t process_json_data(char **result, int *len);

#endif // PROTOCOL_H