/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: info.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "info.pb-c.h"
void   codec__out__info__info_package__init
                     (Codec__Out__Info__InfoPackage         *message)
{
  static const Codec__Out__Info__InfoPackage init_value = CODEC__OUT__INFO__INFO_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__out__info__info_package__get_packed_size
                     (const Codec__Out__Info__InfoPackage *message)
{
  assert(message->base.descriptor == &codec__out__info__info_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__out__info__info_package__pack
                     (const Codec__Out__Info__InfoPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__out__info__info_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__out__info__info_package__pack_to_buffer
                     (const Codec__Out__Info__InfoPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__out__info__info_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__Out__Info__InfoPackage *
       codec__out__info__info_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__Out__Info__InfoPackage *)
     protobuf_c_message_unpack (&codec__out__info__info_package__descriptor,
                                allocator, len, data);
}
void   codec__out__info__info_package__free_unpacked
                     (Codec__Out__Info__InfoPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__out__info__info_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__out__info__info_package__field_descriptors[13] =
{
  {
    "codec_id",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_STRING,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, codec_id),
    NULL,
    &protobuf_c_empty_string,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "firmware_esp",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, firmware_esp),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "firmware_mesh",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, firmware_mesh),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "hardware_version",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, hardware_version),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "resets",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, resets),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scheduling_running",
    6,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, scheduling_running),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scheduling_paused",
    7,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, scheduling_paused),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "devices_id",
    8,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, devices_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scheduling_id",
    9,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, scheduling_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "dev_scheduling_id",
    10,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, dev_scheduling_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "automation_id",
    11,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, automation_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "config_id",
    12,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, config_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "failed_bitmask",
    13,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Info__InfoPackage, failed_bitmask),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__out__info__info_package__field_indices_by_name[] = {
  10,   /* field[10] = automation_id */
  0,   /* field[0] = codec_id */
  11,   /* field[11] = config_id */
  9,   /* field[9] = dev_scheduling_id */
  7,   /* field[7] = devices_id */
  12,   /* field[12] = failed_bitmask */
  1,   /* field[1] = firmware_esp */
  2,   /* field[2] = firmware_mesh */
  3,   /* field[3] = hardware_version */
  4,   /* field[4] = resets */
  8,   /* field[8] = scheduling_id */
  6,   /* field[6] = scheduling_paused */
  5,   /* field[5] = scheduling_running */
};
static const ProtobufCIntRange codec__out__info__info_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 13 }
};
const ProtobufCMessageDescriptor codec__out__info__info_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.out.info.InfoPackage",
  "InfoPackage",
  "Codec__Out__Info__InfoPackage",
  "codec.out.info",
  sizeof(Codec__Out__Info__InfoPackage),
  13,
  codec__out__info__info_package__field_descriptors,
  codec__out__info__info_package__field_indices_by_name,
  1,  codec__out__info__info_package__number_ranges,
  (ProtobufCMessageInit) codec__out__info__info_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
