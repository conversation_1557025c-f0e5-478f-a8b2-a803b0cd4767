/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: raw.proto */

#ifndef PROTOBUF_C_raw_2eproto__INCLUDED
#define PROTOBUF_C_raw_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct Codec__Out__Raw__RawPackage Codec__Out__Raw__RawPackage;


/* --- enums --- */


/* --- messages --- */

struct  Codec__Out__Raw__RawPackage
{
  ProtobufCMessage base;
  /*
   * Índice do dispositivo
   */
  int32_t idx;
  /*
   * Tipo de equipamento
   */
  int32_t equipment;
  /*
   * Tipo de pacote
   */
  int32_t type;
  /*
   * Payload
   */
  ProtobufCBinaryData payload;
};
#define CODEC__OUT__RAW__RAW_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__out__raw__raw_package__descriptor) \
    , 0, 0, 0, {0,NULL} }


/* Codec__Out__Raw__RawPackage methods */
void   codec__out__raw__raw_package__init
                     (Codec__Out__Raw__RawPackage         *message);
size_t codec__out__raw__raw_package__get_packed_size
                     (const Codec__Out__Raw__RawPackage   *message);
size_t codec__out__raw__raw_package__pack
                     (const Codec__Out__Raw__RawPackage   *message,
                      uint8_t             *out);
size_t codec__out__raw__raw_package__pack_to_buffer
                     (const Codec__Out__Raw__RawPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__Out__Raw__RawPackage *
       codec__out__raw__raw_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__out__raw__raw_package__free_unpacked
                     (Codec__Out__Raw__RawPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__Out__Raw__RawPackage_Closure)
                 (const Codec__Out__Raw__RawPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__out__raw__raw_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_raw_2eproto__INCLUDED */
