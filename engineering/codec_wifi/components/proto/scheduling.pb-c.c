/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: scheduling.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "scheduling.pb-c.h"
void   codec__in__scheduling__scheduling__init
                     (Codec__In__Scheduling__Scheduling         *message)
{
  static const Codec__In__Scheduling__Scheduling init_value = CODEC__IN__SCHEDULING__SCHEDULING__INIT;
  *message = init_value;
}
size_t codec__in__scheduling__scheduling__get_packed_size
                     (const Codec__In__Scheduling__Scheduling *message)
{
  assert(message->base.descriptor == &codec__in__scheduling__scheduling__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__scheduling__scheduling__pack
                     (const Codec__In__Scheduling__Scheduling *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__scheduling__scheduling__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__scheduling__scheduling__pack_to_buffer
                     (const Codec__In__Scheduling__Scheduling *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__scheduling__scheduling__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Scheduling__Scheduling *
       codec__in__scheduling__scheduling__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Scheduling__Scheduling *)
     protobuf_c_message_unpack (&codec__in__scheduling__scheduling__descriptor,
                                allocator, len, data);
}
void   codec__in__scheduling__scheduling__free_unpacked
                     (Codec__In__Scheduling__Scheduling *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__scheduling__scheduling__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   codec__in__scheduling__device_scheduling__init
                     (Codec__In__Scheduling__DeviceScheduling         *message)
{
  static const Codec__In__Scheduling__DeviceScheduling init_value = CODEC__IN__SCHEDULING__DEVICE_SCHEDULING__INIT;
  *message = init_value;
}
size_t codec__in__scheduling__device_scheduling__get_packed_size
                     (const Codec__In__Scheduling__DeviceScheduling *message)
{
  assert(message->base.descriptor == &codec__in__scheduling__device_scheduling__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__scheduling__device_scheduling__pack
                     (const Codec__In__Scheduling__DeviceScheduling *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__scheduling__device_scheduling__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__scheduling__device_scheduling__pack_to_buffer
                     (const Codec__In__Scheduling__DeviceScheduling *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__scheduling__device_scheduling__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Scheduling__DeviceScheduling *
       codec__in__scheduling__device_scheduling__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Scheduling__DeviceScheduling *)
     protobuf_c_message_unpack (&codec__in__scheduling__device_scheduling__descriptor,
                                allocator, len, data);
}
void   codec__in__scheduling__device_scheduling__free_unpacked
                     (Codec__In__Scheduling__DeviceScheduling *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__scheduling__device_scheduling__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   codec__in__scheduling__scheduling_package__init
                     (Codec__In__Scheduling__SchedulingPackage         *message)
{
  static const Codec__In__Scheduling__SchedulingPackage init_value = CODEC__IN__SCHEDULING__SCHEDULING_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__in__scheduling__scheduling_package__get_packed_size
                     (const Codec__In__Scheduling__SchedulingPackage *message)
{
  assert(message->base.descriptor == &codec__in__scheduling__scheduling_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__scheduling__scheduling_package__pack
                     (const Codec__In__Scheduling__SchedulingPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__scheduling__scheduling_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__scheduling__scheduling_package__pack_to_buffer
                     (const Codec__In__Scheduling__SchedulingPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__scheduling__scheduling_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Scheduling__SchedulingPackage *
       codec__in__scheduling__scheduling_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Scheduling__SchedulingPackage *)
     protobuf_c_message_unpack (&codec__in__scheduling__scheduling_package__descriptor,
                                allocator, len, data);
}
void   codec__in__scheduling__scheduling_package__free_unpacked
                     (Codec__In__Scheduling__SchedulingPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__scheduling__scheduling_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__in__scheduling__scheduling__field_descriptors[12] =
{
  {
    "idx",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__Scheduling, idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_time",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__Scheduling, start_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "days_of_week",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__Scheduling, days_of_week),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "number_of_steps",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__Scheduling, number_of_steps),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "waterpump_idx",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__Scheduling, waterpump_idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "waterpump_working_time",
    6,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__Scheduling, waterpump_working_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "allow_ferti",
    7,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BOOL,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__Scheduling, allow_ferti),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ferti_idx",
    8,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__Scheduling, ferti_idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "allow_backwash",
    9,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BOOL,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__Scheduling, allow_backwash),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "backwash_idx",
    10,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__Scheduling, backwash_idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "group",
    11,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__Scheduling, group),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "once",
    12,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BOOL,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__Scheduling, once),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__scheduling__scheduling__field_indices_by_name[] = {
  8,   /* field[8] = allow_backwash */
  6,   /* field[6] = allow_ferti */
  9,   /* field[9] = backwash_idx */
  2,   /* field[2] = days_of_week */
  7,   /* field[7] = ferti_idx */
  10,   /* field[10] = group */
  0,   /* field[0] = idx */
  3,   /* field[3] = number_of_steps */
  11,   /* field[11] = once */
  1,   /* field[1] = start_time */
  4,   /* field[4] = waterpump_idx */
  5,   /* field[5] = waterpump_working_time */
};
static const ProtobufCIntRange codec__in__scheduling__scheduling__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 12 }
};
const ProtobufCMessageDescriptor codec__in__scheduling__scheduling__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.scheduling.Scheduling",
  "Scheduling",
  "Codec__In__Scheduling__Scheduling",
  "codec.in.scheduling",
  sizeof(Codec__In__Scheduling__Scheduling),
  12,
  codec__in__scheduling__scheduling__field_descriptors,
  codec__in__scheduling__scheduling__field_indices_by_name,
  1,  codec__in__scheduling__scheduling__number_ranges,
  (ProtobufCMessageInit) codec__in__scheduling__scheduling__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor codec__in__scheduling__device_scheduling__field_descriptors[7] =
{
  {
    "idx",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__DeviceScheduling, idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scheduling_idx",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__DeviceScheduling, scheduling_idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_idx",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__DeviceScheduling, device_idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "order",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__DeviceScheduling, order),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sector_working_time",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__DeviceScheduling, sector_working_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ferti_working_time",
    6,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__DeviceScheduling, ferti_working_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ferti_delay",
    7,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__DeviceScheduling, ferti_delay),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__scheduling__device_scheduling__field_indices_by_name[] = {
  2,   /* field[2] = device_idx */
  6,   /* field[6] = ferti_delay */
  5,   /* field[5] = ferti_working_time */
  0,   /* field[0] = idx */
  3,   /* field[3] = order */
  1,   /* field[1] = scheduling_idx */
  4,   /* field[4] = sector_working_time */
};
static const ProtobufCIntRange codec__in__scheduling__device_scheduling__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 7 }
};
const ProtobufCMessageDescriptor codec__in__scheduling__device_scheduling__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.scheduling.DeviceScheduling",
  "DeviceScheduling",
  "Codec__In__Scheduling__DeviceScheduling",
  "codec.in.scheduling",
  sizeof(Codec__In__Scheduling__DeviceScheduling),
  7,
  codec__in__scheduling__device_scheduling__field_descriptors,
  codec__in__scheduling__device_scheduling__field_indices_by_name,
  1,  codec__in__scheduling__device_scheduling__number_ranges,
  (ProtobufCMessageInit) codec__in__scheduling__device_scheduling__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor codec__in__scheduling__scheduling_package__field_descriptors[3] =
{
  {
    "type",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Scheduling__SchedulingPackage, type),
    &codec__in__scheduling__msg_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scheduling_data",
    2,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__Scheduling__SchedulingPackage, n_scheduling_data),
    offsetof(Codec__In__Scheduling__SchedulingPackage, scheduling_data),
    &codec__in__scheduling__scheduling__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_scheduling_data",
    3,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__Scheduling__SchedulingPackage, n_device_scheduling_data),
    offsetof(Codec__In__Scheduling__SchedulingPackage, device_scheduling_data),
    &codec__in__scheduling__device_scheduling__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__scheduling__scheduling_package__field_indices_by_name[] = {
  2,   /* field[2] = device_scheduling_data */
  1,   /* field[1] = scheduling_data */
  0,   /* field[0] = type */
};
static const ProtobufCIntRange codec__in__scheduling__scheduling_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor codec__in__scheduling__scheduling_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.scheduling.SchedulingPackage",
  "SchedulingPackage",
  "Codec__In__Scheduling__SchedulingPackage",
  "codec.in.scheduling",
  sizeof(Codec__In__Scheduling__SchedulingPackage),
  3,
  codec__in__scheduling__scheduling_package__field_descriptors,
  codec__in__scheduling__scheduling_package__field_indices_by_name,
  1,  codec__in__scheduling__scheduling_package__number_ranges,
  (ProtobufCMessageInit) codec__in__scheduling__scheduling_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue codec__in__scheduling__msg_type__enum_values_by_number[4] =
{
  { "MSG_NONE", "CODEC__IN__SCHEDULING__MSG_TYPE__MSG_NONE", 0 },
  { "MSG_SCHEDULING_ONLY", "CODEC__IN__SCHEDULING__MSG_TYPE__MSG_SCHEDULING_ONLY", 1 },
  { "MSG_DEV_SCHEDULING_ONLY", "CODEC__IN__SCHEDULING__MSG_TYPE__MSG_DEV_SCHEDULING_ONLY", 2 },
  { "MSG_SCHEDULING_ALL", "CODEC__IN__SCHEDULING__MSG_TYPE__MSG_SCHEDULING_ALL", 3 },
};
static const ProtobufCIntRange codec__in__scheduling__msg_type__value_ranges[] = {
{0, 0},{0, 4}
};
static const ProtobufCEnumValueIndex codec__in__scheduling__msg_type__enum_values_by_name[4] =
{
  { "MSG_DEV_SCHEDULING_ONLY", 2 },
  { "MSG_NONE", 0 },
  { "MSG_SCHEDULING_ALL", 3 },
  { "MSG_SCHEDULING_ONLY", 1 },
};
const ProtobufCEnumDescriptor codec__in__scheduling__msg_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "codec.in.scheduling.MsgType",
  "MsgType",
  "Codec__In__Scheduling__MsgType",
  "codec.in.scheduling",
  4,
  codec__in__scheduling__msg_type__enum_values_by_number,
  4,
  codec__in__scheduling__msg_type__enum_values_by_name,
  1,
  codec__in__scheduling__msg_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
