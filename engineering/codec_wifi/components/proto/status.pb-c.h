/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: status.proto */

#ifndef PROTOBUF_C_status_2eproto__INCLUDED
#define PROTOBUF_C_status_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct Codec__Out__Status__SystemStatusPackage Codec__Out__Status__SystemStatusPackage;


/* --- enums --- */


/* --- messages --- */

typedef enum {
  CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_PAUSED_TIME__NOT_SET = 0,
  CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_PAUSED_TIME_PAUSED_TIME = 4
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_PAUSED_TIME__CASE)
} Codec__Out__Status__SystemStatusPackage__HasPausedTimeCase;

typedef enum {
  CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINING__NOT_SET = 0,
  CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINING_RAINING = 5
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINING__CASE)
} Codec__Out__Status__SystemStatusPackage__HasRainingCase;

typedef enum {
  CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINFALL__NOT_SET = 0,
  CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINFALL_RAINFALL = 6
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINFALL__CASE)
} Codec__Out__Status__SystemStatusPackage__HasRainfallCase;

struct  Codec__Out__Status__SystemStatusPackage
{
  ProtobufCMessage base;
  /*
   * Número de resets
   */
  uint32_t resets;
  /*
   * Agendamento em execução
   */
  uint32_t scheduling_running;
  /*
   * Pausa de agendamento ativa
   */
  uint32_t scheduling_paused;
  /*
   * Máscara de bits de dispositivos sincronizados
   */
  uint64_t sync_bitmask;
  /*
   * Máscara de bits de dispositivos ligados
   */
  uint64_t on_bitmask;
  /*
   * Máscara de bits de dispositivos com entrada 1 
   */
  uint64_t input_bitmask;
  /*
   * Máscara de bits de falhas do sistema
   */
  uint32_t failed_bitmask;
  Codec__Out__Status__SystemStatusPackage__HasPausedTimeCase has_paused_time_case;
  union {
    /*
     * Minutos desde que a pausa foi ativada
     */
    uint32_t paused_time;
  };
  Codec__Out__Status__SystemStatusPackage__HasRainingCase has_raining_case;
  union {
    /*
     * Está chovendo (1 = sim)
     */
    uint32_t raining;
  };
  Codec__Out__Status__SystemStatusPackage__HasRainfallCase has_rainfall_case;
  union {
    /*
     * Chuva acumulada nas últimas 24h
     */
    uint32_t rainfall;
  };
};
#define CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__out__status__system_status_package__descriptor) \
    , 0, 0, 0, 0, 0, 0, 0, CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_PAUSED_TIME__NOT_SET, {0}, CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINING__NOT_SET, {0}, CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__HAS_RAINFALL__NOT_SET, {0} }


/* Codec__Out__Status__SystemStatusPackage methods */
void   codec__out__status__system_status_package__init
                     (Codec__Out__Status__SystemStatusPackage         *message);
size_t codec__out__status__system_status_package__get_packed_size
                     (const Codec__Out__Status__SystemStatusPackage   *message);
size_t codec__out__status__system_status_package__pack
                     (const Codec__Out__Status__SystemStatusPackage   *message,
                      uint8_t             *out);
size_t codec__out__status__system_status_package__pack_to_buffer
                     (const Codec__Out__Status__SystemStatusPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__Out__Status__SystemStatusPackage *
       codec__out__status__system_status_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__out__status__system_status_package__free_unpacked
                     (Codec__Out__Status__SystemStatusPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__Out__Status__SystemStatusPackage_Closure)
                 (const Codec__Out__Status__SystemStatusPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__out__status__system_status_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_status_2eproto__INCLUDED */
