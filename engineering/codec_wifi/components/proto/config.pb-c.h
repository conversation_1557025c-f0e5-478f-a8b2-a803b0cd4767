/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: config.proto */

#ifndef PROTOBUF_C_config_2eproto__INCLUDED
#define PROTOBUF_C_config_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct Codec__In__Config__WifiConfig Codec__In__Config__WifiConfig;
typedef struct Codec__In__Config__MeshConfig Codec__In__Config__MeshConfig;
typedef struct Codec__In__Config__ConfigPackage Codec__In__Config__ConfigPackage;


/* --- enums --- */


/* --- messages --- */

struct  Codec__In__Config__WifiConfig
{
  ProtobufCMessage base;
  /*
   * Wi-Fi SSID
   */
  char *ssid;
  /*
   * Wi-Fi password
   */
  char *password;
};
#define CODEC__IN__CONFIG__WIFI_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__config__wifi_config__descriptor) \
    , (char *)protobuf_c_empty_string, (char *)protobuf_c_empty_string }


struct  Codec__In__Config__MeshConfig
{
  ProtobufCMessage base;
  /*
   * Chave da criptografia da rede Mesh
   */
  ProtobufCBinaryData key;
  /*
   * Canal da rede Mesh
   */
  uint32_t channel;
};
#define CODEC__IN__CONFIG__MESH_CONFIG__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__config__mesh_config__descriptor) \
    , {0,NULL}, 0 }


struct  Codec__In__Config__ConfigPackage
{
  ProtobufCMessage base;
  /*
   * número do ciclo de retrolavagem
   */
  int32_t backwash_cycle;
  /*
   * duração da retrolavagem
   */
  int32_t backwash_duration;
  /*
   * atraso antes da retrolavagem
   */
  int32_t backwash_delay;
  /*
   * sensor de chuva ativado
   */
  protobuf_c_boolean raingauge_enabled;
  /*
   * resolução do pluviômetro
   */
  int32_t raingauge_factor;
  /*
   * limite de chuva
   */
  int32_t rainfall_limit;
  /*
   * tempo de pausa após chuva
   */
  int32_t rainfall_pause_duration;
  /*
   * configuração do Wi-Fi
   */
  Codec__In__Config__WifiConfig *wifi;
  /*
   * configuração da rede Mesh
   */
  Codec__In__Config__MeshConfig *mesh;
  /*
   * enviar pacotes brutos para o mqtt
   */
  protobuf_c_boolean publish_raw_data;
  /*
   * habilita modo de invertigação de problemas
   */
  protobuf_c_boolean debug;
  /*
   * habilita retomada automática de agendamentos
   */
  protobuf_c_boolean enable_schedule_resumption;
  /*
   * habilita retomada automática do setor quando a ferti falhar
   */
  protobuf_c_boolean enable_ferti_resumption;
  /*
   * número máximo de tentativas de retomada (0 = indefinido)
   */
  int32_t max_resumption_attempts;
};
#define CODEC__IN__CONFIG__CONFIG_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__config__config_package__descriptor) \
    , 0, 0, 0, 0, 0, 0, 0, NULL, NULL, 0, 0, 0, 0, 0 }


/* Codec__In__Config__WifiConfig methods */
void   codec__in__config__wifi_config__init
                     (Codec__In__Config__WifiConfig         *message);
size_t codec__in__config__wifi_config__get_packed_size
                     (const Codec__In__Config__WifiConfig   *message);
size_t codec__in__config__wifi_config__pack
                     (const Codec__In__Config__WifiConfig   *message,
                      uint8_t             *out);
size_t codec__in__config__wifi_config__pack_to_buffer
                     (const Codec__In__Config__WifiConfig   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__Config__WifiConfig *
       codec__in__config__wifi_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__config__wifi_config__free_unpacked
                     (Codec__In__Config__WifiConfig *message,
                      ProtobufCAllocator *allocator);
/* Codec__In__Config__MeshConfig methods */
void   codec__in__config__mesh_config__init
                     (Codec__In__Config__MeshConfig         *message);
size_t codec__in__config__mesh_config__get_packed_size
                     (const Codec__In__Config__MeshConfig   *message);
size_t codec__in__config__mesh_config__pack
                     (const Codec__In__Config__MeshConfig   *message,
                      uint8_t             *out);
size_t codec__in__config__mesh_config__pack_to_buffer
                     (const Codec__In__Config__MeshConfig   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__Config__MeshConfig *
       codec__in__config__mesh_config__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__config__mesh_config__free_unpacked
                     (Codec__In__Config__MeshConfig *message,
                      ProtobufCAllocator *allocator);
/* Codec__In__Config__ConfigPackage methods */
void   codec__in__config__config_package__init
                     (Codec__In__Config__ConfigPackage         *message);
size_t codec__in__config__config_package__get_packed_size
                     (const Codec__In__Config__ConfigPackage   *message);
size_t codec__in__config__config_package__pack
                     (const Codec__In__Config__ConfigPackage   *message,
                      uint8_t             *out);
size_t codec__in__config__config_package__pack_to_buffer
                     (const Codec__In__Config__ConfigPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__Config__ConfigPackage *
       codec__in__config__config_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__config__config_package__free_unpacked
                     (Codec__In__Config__ConfigPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__In__Config__WifiConfig_Closure)
                 (const Codec__In__Config__WifiConfig *message,
                  void *closure_data);
typedef void (*Codec__In__Config__MeshConfig_Closure)
                 (const Codec__In__Config__MeshConfig *message,
                  void *closure_data);
typedef void (*Codec__In__Config__ConfigPackage_Closure)
                 (const Codec__In__Config__ConfigPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__in__config__wifi_config__descriptor;
extern const ProtobufCMessageDescriptor codec__in__config__mesh_config__descriptor;
extern const ProtobufCMessageDescriptor codec__in__config__config_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_config_2eproto__INCLUDED */
