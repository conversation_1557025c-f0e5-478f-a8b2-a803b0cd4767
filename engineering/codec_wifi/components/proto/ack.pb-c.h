/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: ack.proto */

#ifndef PROTOBUF_C_ack_2eproto__INCLUDED
#define PROTOBUF_C_ack_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct Codec__Out__Ack__AckPackage Codec__Out__Ack__AckPackage;


/* --- enums --- */


/* --- messages --- */

struct  Codec__Out__Ack__AckPackage
{
  ProtobufCMessage base;
  uint32_t package;
  uint32_t value;
};
#define CODEC__OUT__ACK__ACK_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__out__ack__ack_package__descriptor) \
    , 0, 0 }


/* Codec__Out__Ack__AckPackage methods */
void   codec__out__ack__ack_package__init
                     (Codec__Out__Ack__AckPackage         *message);
size_t codec__out__ack__ack_package__get_packed_size
                     (const Codec__Out__Ack__AckPackage   *message);
size_t codec__out__ack__ack_package__pack
                     (const Codec__Out__Ack__AckPackage   *message,
                      uint8_t             *out);
size_t codec__out__ack__ack_package__pack_to_buffer
                     (const Codec__Out__Ack__AckPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__Out__Ack__AckPackage *
       codec__out__ack__ack_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__out__ack__ack_package__free_unpacked
                     (Codec__Out__Ack__AckPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__Out__Ack__AckPackage_Closure)
                 (const Codec__Out__Ack__AckPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__out__ack__ack_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_ack_2eproto__INCLUDED */
