/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: firmware_update.proto */

#ifndef PROTOBUF_C_firmware_5fupdate_2eproto__INCLUDED
#define PROTOBUF_C_firmware_5fupdate_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct Codec__In__FirmwareUpdate__FirmwareUpdatePackage Codec__In__FirmwareUpdate__FirmwareUpdatePackage;


/* --- enums --- */

typedef enum _Codec__In__FirmwareUpdate__MsgType {
  CODEC__IN__FIRMWARE_UPDATE__MSG_TYPE__MSG_ESP = 0,
  CODEC__IN__FIRMWARE_UPDATE__MSG_TYPE__MSG_STM = 1
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(CODEC__IN__FIRMWARE_UPDATE__MSG_TYPE)
} Codec__In__FirmwareUpdate__MsgType;
typedef enum _Codec__In__FirmwareUpdate__MsgProtocol {
  CODEC__IN__FIRMWARE_UPDATE__MSG_PROTOCOL__MSG_HTTPS = 0,
  CODEC__IN__FIRMWARE_UPDATE__MSG_PROTOCOL__MSG_HTTP = 1
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(CODEC__IN__FIRMWARE_UPDATE__MSG_PROTOCOL)
} Codec__In__FirmwareUpdate__MsgProtocol;

/* --- messages --- */

struct  Codec__In__FirmwareUpdate__FirmwareUpdatePackage
{
  ProtobufCMessage base;
  /*
   * Tipo de atualização
   */
  Codec__In__FirmwareUpdate__MsgType type;
  /*
   * Tipo de protocolo
   */
  Codec__In__FirmwareUpdate__MsgProtocol protocol;
  /*
   * Código indiviual para ativação do UpDate 
   */
  int32_t activation_code;
  /*
   * Nova versão do firmware 
   */
  int32_t version;
};
#define CODEC__IN__FIRMWARE_UPDATE__FIRMWARE_UPDATE_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__firmware_update__firmware_update_package__descriptor) \
    , CODEC__IN__FIRMWARE_UPDATE__MSG_TYPE__MSG_ESP, CODEC__IN__FIRMWARE_UPDATE__MSG_PROTOCOL__MSG_HTTPS, 0, 0 }


/* Codec__In__FirmwareUpdate__FirmwareUpdatePackage methods */
void   codec__in__firmware_update__firmware_update_package__init
                     (Codec__In__FirmwareUpdate__FirmwareUpdatePackage         *message);
size_t codec__in__firmware_update__firmware_update_package__get_packed_size
                     (const Codec__In__FirmwareUpdate__FirmwareUpdatePackage   *message);
size_t codec__in__firmware_update__firmware_update_package__pack
                     (const Codec__In__FirmwareUpdate__FirmwareUpdatePackage   *message,
                      uint8_t             *out);
size_t codec__in__firmware_update__firmware_update_package__pack_to_buffer
                     (const Codec__In__FirmwareUpdate__FirmwareUpdatePackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__FirmwareUpdate__FirmwareUpdatePackage *
       codec__in__firmware_update__firmware_update_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__firmware_update__firmware_update_package__free_unpacked
                     (Codec__In__FirmwareUpdate__FirmwareUpdatePackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__In__FirmwareUpdate__FirmwareUpdatePackage_Closure)
                 (const Codec__In__FirmwareUpdate__FirmwareUpdatePackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCEnumDescriptor    codec__in__firmware_update__msg_type__descriptor;
extern const ProtobufCEnumDescriptor    codec__in__firmware_update__msg_protocol__descriptor;
extern const ProtobufCMessageDescriptor codec__in__firmware_update__firmware_update_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_firmware_5fupdate_2eproto__INCLUDED */
