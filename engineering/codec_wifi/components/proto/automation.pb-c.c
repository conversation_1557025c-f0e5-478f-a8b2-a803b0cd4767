/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: automation.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "automation.pb-c.h"
void   codec__in__automation__automation_data__init
                     (Codec__In__Automation__AutomationData         *message)
{
  static const Codec__In__Automation__AutomationData init_value = CODEC__IN__AUTOMATION__AUTOMATION_DATA__INIT;
  *message = init_value;
}
size_t codec__in__automation__automation_data__get_packed_size
                     (const Codec__In__Automation__AutomationData *message)
{
  assert(message->base.descriptor == &codec__in__automation__automation_data__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__automation__automation_data__pack
                     (const Codec__In__Automation__AutomationData *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__automation__automation_data__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__automation__automation_data__pack_to_buffer
                     (const Codec__In__Automation__AutomationData *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__automation__automation_data__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Automation__AutomationData *
       codec__in__automation__automation_data__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Automation__AutomationData *)
     protobuf_c_message_unpack (&codec__in__automation__automation_data__descriptor,
                                allocator, len, data);
}
void   codec__in__automation__automation_data__free_unpacked
                     (Codec__In__Automation__AutomationData *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__automation__automation_data__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   codec__in__automation__automation_package__init
                     (Codec__In__Automation__AutomationPackage         *message)
{
  static const Codec__In__Automation__AutomationPackage init_value = CODEC__IN__AUTOMATION__AUTOMATION_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__in__automation__automation_package__get_packed_size
                     (const Codec__In__Automation__AutomationPackage *message)
{
  assert(message->base.descriptor == &codec__in__automation__automation_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__automation__automation_package__pack
                     (const Codec__In__Automation__AutomationPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__automation__automation_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__automation__automation_package__pack_to_buffer
                     (const Codec__In__Automation__AutomationPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__automation__automation_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Automation__AutomationPackage *
       codec__in__automation__automation_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Automation__AutomationPackage *)
     protobuf_c_message_unpack (&codec__in__automation__automation_package__descriptor,
                                allocator, len, data);
}
void   codec__in__automation__automation_package__free_unpacked
                     (Codec__In__Automation__AutomationPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__automation__automation_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__in__automation__automation_data__field_descriptors[5] =
{
  {
    "level_idx",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Automation__AutomationData, level_idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "pump_idx",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Automation__AutomationData, pump_idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mask",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Automation__AutomationData, mask),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "value",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Automation__AutomationData, value),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "working_time",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Automation__AutomationData, working_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__automation__automation_data__field_indices_by_name[] = {
  0,   /* field[0] = level_idx */
  2,   /* field[2] = mask */
  1,   /* field[1] = pump_idx */
  3,   /* field[3] = value */
  4,   /* field[4] = working_time */
};
static const ProtobufCIntRange codec__in__automation__automation_data__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor codec__in__automation__automation_data__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.automation.AutomationData",
  "AutomationData",
  "Codec__In__Automation__AutomationData",
  "codec.in.automation",
  sizeof(Codec__In__Automation__AutomationData),
  5,
  codec__in__automation__automation_data__field_descriptors,
  codec__in__automation__automation_data__field_indices_by_name,
  1,  codec__in__automation__automation_data__number_ranges,
  (ProtobufCMessageInit) codec__in__automation__automation_data__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor codec__in__automation__automation_package__field_descriptors[1] =
{
  {
    "data",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__Automation__AutomationPackage, n_data),
    offsetof(Codec__In__Automation__AutomationPackage, data),
    &codec__in__automation__automation_data__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__automation__automation_package__field_indices_by_name[] = {
  0,   /* field[0] = data */
};
static const ProtobufCIntRange codec__in__automation__automation_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor codec__in__automation__automation_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.automation.AutomationPackage",
  "AutomationPackage",
  "Codec__In__Automation__AutomationPackage",
  "codec.in.automation",
  sizeof(Codec__In__Automation__AutomationPackage),
  1,
  codec__in__automation__automation_package__field_descriptors,
  codec__in__automation__automation_package__field_indices_by_name,
  1,  codec__in__automation__automation_package__number_ranges,
  (ProtobufCMessageInit) codec__in__automation__automation_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
