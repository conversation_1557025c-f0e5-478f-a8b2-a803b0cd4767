/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: devices.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "devices.pb-c.h"
void   codec__in__devices__devices_data__init
                     (Codec__In__Devices__DevicesData         *message)
{
  static const Codec__In__Devices__DevicesData init_value = CODEC__IN__DEVICES__DEVICES_DATA__INIT;
  *message = init_value;
}
size_t codec__in__devices__devices_data__get_packed_size
                     (const Codec__In__Devices__DevicesData *message)
{
  assert(message->base.descriptor == &codec__in__devices__devices_data__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__devices__devices_data__pack
                     (const Codec__In__Devices__DevicesData *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__devices__devices_data__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__devices__devices_data__pack_to_buffer
                     (const Codec__In__Devices__DevicesData *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__devices__devices_data__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Devices__DevicesData *
       codec__in__devices__devices_data__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Devices__DevicesData *)
     protobuf_c_message_unpack (&codec__in__devices__devices_data__descriptor,
                                allocator, len, data);
}
void   codec__in__devices__devices_data__free_unpacked
                     (Codec__In__Devices__DevicesData *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__devices__devices_data__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   codec__in__devices__devices_package__init
                     (Codec__In__Devices__DevicesPackage         *message)
{
  static const Codec__In__Devices__DevicesPackage init_value = CODEC__IN__DEVICES__DEVICES_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__in__devices__devices_package__get_packed_size
                     (const Codec__In__Devices__DevicesPackage *message)
{
  assert(message->base.descriptor == &codec__in__devices__devices_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__devices__devices_package__pack
                     (const Codec__In__Devices__DevicesPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__devices__devices_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__devices__devices_package__pack_to_buffer
                     (const Codec__In__Devices__DevicesPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__devices__devices_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Devices__DevicesPackage *
       codec__in__devices__devices_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Devices__DevicesPackage *)
     protobuf_c_message_unpack (&codec__in__devices__devices_package__descriptor,
                                allocator, len, data);
}
void   codec__in__devices__devices_package__free_unpacked
                     (Codec__In__Devices__DevicesPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__devices__devices_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__in__devices__devices_data__field_descriptors[12] =
{
  {
    "idx",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Devices__DevicesData, idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mesh_id",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Devices__DevicesData, mesh_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_id",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Devices__DevicesData, device_id),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_type",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Devices__DevicesData, device_type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "out1",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Devices__DevicesData, out1),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "out2",
    6,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Devices__DevicesData, out2),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "input",
    7,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Devices__DevicesData, input),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "mode",
    8,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Devices__DevicesData, mode),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sector",
    9,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Devices__DevicesData, sector),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "group_idx",
    10,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Devices__DevicesData, group_idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "power",
    11,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Devices__DevicesData, power),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "equipment",
    12,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Devices__DevicesData, equipment),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__devices__devices_data__field_indices_by_name[] = {
  2,   /* field[2] = device_id */
  3,   /* field[3] = device_type */
  11,   /* field[11] = equipment */
  9,   /* field[9] = group_idx */
  0,   /* field[0] = idx */
  6,   /* field[6] = input */
  1,   /* field[1] = mesh_id */
  7,   /* field[7] = mode */
  4,   /* field[4] = out1 */
  5,   /* field[5] = out2 */
  10,   /* field[10] = power */
  8,   /* field[8] = sector */
};
static const ProtobufCIntRange codec__in__devices__devices_data__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 12 }
};
const ProtobufCMessageDescriptor codec__in__devices__devices_data__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.devices.DevicesData",
  "DevicesData",
  "Codec__In__Devices__DevicesData",
  "codec.in.devices",
  sizeof(Codec__In__Devices__DevicesData),
  12,
  codec__in__devices__devices_data__field_descriptors,
  codec__in__devices__devices_data__field_indices_by_name,
  1,  codec__in__devices__devices_data__number_ranges,
  (ProtobufCMessageInit) codec__in__devices__devices_data__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor codec__in__devices__devices_package__field_descriptors[1] =
{
  {
    "data",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__Devices__DevicesPackage, n_data),
    offsetof(Codec__In__Devices__DevicesPackage, data),
    &codec__in__devices__devices_data__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__devices__devices_package__field_indices_by_name[] = {
  0,   /* field[0] = data */
};
static const ProtobufCIntRange codec__in__devices__devices_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor codec__in__devices__devices_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.devices.DevicesPackage",
  "DevicesPackage",
  "Codec__In__Devices__DevicesPackage",
  "codec.in.devices",
  sizeof(Codec__In__Devices__DevicesPackage),
  1,
  codec__in__devices__devices_package__field_descriptors,
  codec__in__devices__devices_package__field_indices_by_name,
  1,  codec__in__devices__devices_package__number_ranges,
  (ProtobufCMessageInit) codec__in__devices__devices_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
