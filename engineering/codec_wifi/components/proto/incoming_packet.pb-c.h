/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: incoming_packet.proto */

#ifndef PROTOBUF_C_incoming_5fpacket_2eproto__INCLUDED
#define PROTOBUF_C_incoming_5fpacket_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif

#include "config.pb-c.h"
#include "devices.pb-c.h"
#include "scheduling.pb-c.h"
#include "device_scheduling.pb-c.h"
#include "automation.pb-c.h"
#include "control.pb-c.h"
#include "command.pb-c.h"
#include "request_info.pb-c.h"
#include "firmware_update.pb-c.h"

typedef struct Codec__In__IncomingPacket Codec__In__IncomingPacket;


/* --- enums --- */


/* --- messages --- */

typedef enum {
  CODEC__IN__INCOMING_PACKET__PAYLOAD__NOT_SET = 0,
  CODEC__IN__INCOMING_PACKET__PAYLOAD_CONFIG = 2,
  CODEC__IN__INCOMING_PACKET__PAYLOAD_DEVICES = 3,
  CODEC__IN__INCOMING_PACKET__PAYLOAD_SCHEDULING = 4,
  CODEC__IN__INCOMING_PACKET__PAYLOAD_DEV_SCHEDULING = 5,
  CODEC__IN__INCOMING_PACKET__PAYLOAD_AUTOMATION = 6,
  CODEC__IN__INCOMING_PACKET__PAYLOAD_CONTROL = 7,
  CODEC__IN__INCOMING_PACKET__PAYLOAD_COMMAND = 8,
  CODEC__IN__INCOMING_PACKET__PAYLOAD_REQUEST_INFO = 9,
  CODEC__IN__INCOMING_PACKET__PAYLOAD_FIRMWARE_UPDATE = 10
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(CODEC__IN__INCOMING_PACKET__PAYLOAD__CASE)
} Codec__In__IncomingPacket__PayloadCase;

struct  Codec__In__IncomingPacket
{
  ProtobufCMessage base;
  uint64_t id;
  Codec__In__IncomingPacket__PayloadCase payload_case;
  union {
    Codec__In__Config__ConfigPackage *config;
    Codec__In__Devices__DevicesPackage *devices;
    Codec__In__Scheduling__SchedulingPackage *scheduling;
    Codec__In__DeviceScheduling__DeviceSchedulingPackage *dev_scheduling;
    Codec__In__Automation__AutomationPackage *automation;
    Codec__In__Control__ControlPackage *control;
    Codec__In__Command__CommandPackage *command;
    Codec__In__RequestInfo__RequestInfoPackage *request_info;
    Codec__In__FirmwareUpdate__FirmwareUpdatePackage *firmware_update;
  };
};
#define CODEC__IN__INCOMING_PACKET__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__incoming_packet__descriptor) \
    , 0, CODEC__IN__INCOMING_PACKET__PAYLOAD__NOT_SET, {0} }


/* Codec__In__IncomingPacket methods */
void   codec__in__incoming_packet__init
                     (Codec__In__IncomingPacket         *message);
size_t codec__in__incoming_packet__get_packed_size
                     (const Codec__In__IncomingPacket   *message);
size_t codec__in__incoming_packet__pack
                     (const Codec__In__IncomingPacket   *message,
                      uint8_t             *out);
size_t codec__in__incoming_packet__pack_to_buffer
                     (const Codec__In__IncomingPacket   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__IncomingPacket *
       codec__in__incoming_packet__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__incoming_packet__free_unpacked
                     (Codec__In__IncomingPacket *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__In__IncomingPacket_Closure)
                 (const Codec__In__IncomingPacket *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__in__incoming_packet__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_incoming_5fpacket_2eproto__INCLUDED */
