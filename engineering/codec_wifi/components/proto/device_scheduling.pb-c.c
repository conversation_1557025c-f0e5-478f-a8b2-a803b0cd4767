/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: device_scheduling.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "device_scheduling.pb-c.h"
void   codec__in__device_scheduling__device_scheduling__init
                     (Codec__In__DeviceScheduling__DeviceScheduling         *message)
{
  static const Codec__In__DeviceScheduling__DeviceScheduling init_value = CODEC__IN__DEVICE_SCHEDULING__DEVICE_SCHEDULING__INIT;
  *message = init_value;
}
size_t codec__in__device_scheduling__device_scheduling__get_packed_size
                     (const Codec__In__DeviceScheduling__DeviceScheduling *message)
{
  assert(message->base.descriptor == &codec__in__device_scheduling__device_scheduling__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__device_scheduling__device_scheduling__pack
                     (const Codec__In__DeviceScheduling__DeviceScheduling *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__device_scheduling__device_scheduling__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__device_scheduling__device_scheduling__pack_to_buffer
                     (const Codec__In__DeviceScheduling__DeviceScheduling *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__device_scheduling__device_scheduling__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__DeviceScheduling__DeviceScheduling *
       codec__in__device_scheduling__device_scheduling__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__DeviceScheduling__DeviceScheduling *)
     protobuf_c_message_unpack (&codec__in__device_scheduling__device_scheduling__descriptor,
                                allocator, len, data);
}
void   codec__in__device_scheduling__device_scheduling__free_unpacked
                     (Codec__In__DeviceScheduling__DeviceScheduling *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__device_scheduling__device_scheduling__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   codec__in__device_scheduling__device_scheduling_package__init
                     (Codec__In__DeviceScheduling__DeviceSchedulingPackage         *message)
{
  static const Codec__In__DeviceScheduling__DeviceSchedulingPackage init_value = CODEC__IN__DEVICE_SCHEDULING__DEVICE_SCHEDULING_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__in__device_scheduling__device_scheduling_package__get_packed_size
                     (const Codec__In__DeviceScheduling__DeviceSchedulingPackage *message)
{
  assert(message->base.descriptor == &codec__in__device_scheduling__device_scheduling_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__device_scheduling__device_scheduling_package__pack
                     (const Codec__In__DeviceScheduling__DeviceSchedulingPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__device_scheduling__device_scheduling_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__device_scheduling__device_scheduling_package__pack_to_buffer
                     (const Codec__In__DeviceScheduling__DeviceSchedulingPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__device_scheduling__device_scheduling_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__DeviceScheduling__DeviceSchedulingPackage *
       codec__in__device_scheduling__device_scheduling_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__DeviceScheduling__DeviceSchedulingPackage *)
     protobuf_c_message_unpack (&codec__in__device_scheduling__device_scheduling_package__descriptor,
                                allocator, len, data);
}
void   codec__in__device_scheduling__device_scheduling_package__free_unpacked
                     (Codec__In__DeviceScheduling__DeviceSchedulingPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__device_scheduling__device_scheduling_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__in__device_scheduling__device_scheduling__field_descriptors[7] =
{
  {
    "idx",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__DeviceScheduling__DeviceScheduling, idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scheduling_idx",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__DeviceScheduling__DeviceScheduling, scheduling_idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "device_idx",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__DeviceScheduling__DeviceScheduling, device_idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "order",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__DeviceScheduling__DeviceScheduling, order),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sector_working_time",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__DeviceScheduling__DeviceScheduling, sector_working_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ferti_working_time",
    6,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__DeviceScheduling__DeviceScheduling, ferti_working_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "ferti_delay",
    7,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__DeviceScheduling__DeviceScheduling, ferti_delay),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__device_scheduling__device_scheduling__field_indices_by_name[] = {
  2,   /* field[2] = device_idx */
  6,   /* field[6] = ferti_delay */
  5,   /* field[5] = ferti_working_time */
  0,   /* field[0] = idx */
  3,   /* field[3] = order */
  1,   /* field[1] = scheduling_idx */
  4,   /* field[4] = sector_working_time */
};
static const ProtobufCIntRange codec__in__device_scheduling__device_scheduling__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 7 }
};
const ProtobufCMessageDescriptor codec__in__device_scheduling__device_scheduling__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.device_scheduling.DeviceScheduling",
  "DeviceScheduling",
  "Codec__In__DeviceScheduling__DeviceScheduling",
  "codec.in.device_scheduling",
  sizeof(Codec__In__DeviceScheduling__DeviceScheduling),
  7,
  codec__in__device_scheduling__device_scheduling__field_descriptors,
  codec__in__device_scheduling__device_scheduling__field_indices_by_name,
  1,  codec__in__device_scheduling__device_scheduling__number_ranges,
  (ProtobufCMessageInit) codec__in__device_scheduling__device_scheduling__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor codec__in__device_scheduling__device_scheduling_package__field_descriptors[1] =
{
  {
    "data",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__In__DeviceScheduling__DeviceSchedulingPackage, n_data),
    offsetof(Codec__In__DeviceScheduling__DeviceSchedulingPackage, data),
    &codec__in__device_scheduling__device_scheduling__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__device_scheduling__device_scheduling_package__field_indices_by_name[] = {
  0,   /* field[0] = data */
};
static const ProtobufCIntRange codec__in__device_scheduling__device_scheduling_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor codec__in__device_scheduling__device_scheduling_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.device_scheduling.DeviceSchedulingPackage",
  "DeviceSchedulingPackage",
  "Codec__In__DeviceScheduling__DeviceSchedulingPackage",
  "codec.in.device_scheduling",
  sizeof(Codec__In__DeviceScheduling__DeviceSchedulingPackage),
  1,
  codec__in__device_scheduling__device_scheduling_package__field_descriptors,
  codec__in__device_scheduling__device_scheduling_package__field_indices_by_name,
  1,  codec__in__device_scheduling__device_scheduling_package__number_ranges,
  (ProtobufCMessageInit) codec__in__device_scheduling__device_scheduling_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
