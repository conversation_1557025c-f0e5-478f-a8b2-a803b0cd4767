/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: request_info.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "request_info.pb-c.h"
void   codec__in__request_info__request_info_package__init
                     (Codec__In__RequestInfo__RequestInfoPackage         *message)
{
  static const Codec__In__RequestInfo__RequestInfoPackage init_value = CODEC__IN__REQUEST_INFO__REQUEST_INFO_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__in__request_info__request_info_package__get_packed_size
                     (const Codec__In__RequestInfo__RequestInfoPackage *message)
{
  assert(message->base.descriptor == &codec__in__request_info__request_info_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__request_info__request_info_package__pack
                     (const Codec__In__RequestInfo__RequestInfoPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__request_info__request_info_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__request_info__request_info_package__pack_to_buffer
                     (const Codec__In__RequestInfo__RequestInfoPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__request_info__request_info_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__RequestInfo__RequestInfoPackage *
       codec__in__request_info__request_info_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__RequestInfo__RequestInfoPackage *)
     protobuf_c_message_unpack (&codec__in__request_info__request_info_package__descriptor,
                                allocator, len, data);
}
void   codec__in__request_info__request_info_package__free_unpacked
                     (Codec__In__RequestInfo__RequestInfoPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__request_info__request_info_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__in__request_info__request_info_package__field_descriptors[1] =
{
  {
    "type",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__RequestInfo__RequestInfoPackage, type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__request_info__request_info_package__field_indices_by_name[] = {
  0,   /* field[0] = type */
};
static const ProtobufCIntRange codec__in__request_info__request_info_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor codec__in__request_info__request_info_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.request_info.RequestInfoPackage",
  "RequestInfoPackage",
  "Codec__In__RequestInfo__RequestInfoPackage",
  "codec.in.request_info",
  sizeof(Codec__In__RequestInfo__RequestInfoPackage),
  1,
  codec__in__request_info__request_info_package__field_descriptors,
  codec__in__request_info__request_info_package__field_indices_by_name,
  1,  codec__in__request_info__request_info_package__number_ranges,
  (ProtobufCMessageInit) codec__in__request_info__request_info_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
