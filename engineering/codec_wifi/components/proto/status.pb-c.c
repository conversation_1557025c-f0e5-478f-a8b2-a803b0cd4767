/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: status.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "status.pb-c.h"
void   codec__out__status__system_status_package__init
                     (Codec__Out__Status__SystemStatusPackage         *message)
{
  static const Codec__Out__Status__SystemStatusPackage init_value = CODEC__OUT__STATUS__SYSTEM_STATUS_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__out__status__system_status_package__get_packed_size
                     (const Codec__Out__Status__SystemStatusPackage *message)
{
  assert(message->base.descriptor == &codec__out__status__system_status_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__out__status__system_status_package__pack
                     (const Codec__Out__Status__SystemStatusPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__out__status__system_status_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__out__status__system_status_package__pack_to_buffer
                     (const Codec__Out__Status__SystemStatusPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__out__status__system_status_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__Out__Status__SystemStatusPackage *
       codec__out__status__system_status_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__Out__Status__SystemStatusPackage *)
     protobuf_c_message_unpack (&codec__out__status__system_status_package__descriptor,
                                allocator, len, data);
}
void   codec__out__status__system_status_package__free_unpacked
                     (Codec__Out__Status__SystemStatusPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__out__status__system_status_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__out__status__system_status_package__field_descriptors[10] =
{
  {
    "resets",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Status__SystemStatusPackage, resets),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scheduling_running",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Status__SystemStatusPackage, scheduling_running),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "scheduling_paused",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Status__SystemStatusPackage, scheduling_paused),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "paused_time",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(Codec__Out__Status__SystemStatusPackage, has_paused_time_case),
    offsetof(Codec__Out__Status__SystemStatusPackage, paused_time),
    NULL,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "raining",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(Codec__Out__Status__SystemStatusPackage, has_raining_case),
    offsetof(Codec__Out__Status__SystemStatusPackage, raining),
    NULL,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "rainfall",
    6,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    offsetof(Codec__Out__Status__SystemStatusPackage, has_rainfall_case),
    offsetof(Codec__Out__Status__SystemStatusPackage, rainfall),
    NULL,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "sync_bitmask",
    7,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Status__SystemStatusPackage, sync_bitmask),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "on_bitmask",
    8,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Status__SystemStatusPackage, on_bitmask),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "input_bitmask",
    9,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Status__SystemStatusPackage, input_bitmask),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "failed_bitmask",
    12,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Status__SystemStatusPackage, failed_bitmask),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__out__status__system_status_package__field_indices_by_name[] = {
  9,   /* field[9] = failed_bitmask */
  8,   /* field[8] = input_bitmask */
  7,   /* field[7] = on_bitmask */
  3,   /* field[3] = paused_time */
  5,   /* field[5] = rainfall */
  4,   /* field[4] = raining */
  0,   /* field[0] = resets */
  2,   /* field[2] = scheduling_paused */
  1,   /* field[1] = scheduling_running */
  6,   /* field[6] = sync_bitmask */
};
static const ProtobufCIntRange codec__out__status__system_status_package__number_ranges[2 + 1] =
{
  { 1, 0 },
  { 12, 9 },
  { 0, 10 }
};
const ProtobufCMessageDescriptor codec__out__status__system_status_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.out.status.SystemStatusPackage",
  "SystemStatusPackage",
  "Codec__Out__Status__SystemStatusPackage",
  "codec.out.status",
  sizeof(Codec__Out__Status__SystemStatusPackage),
  10,
  codec__out__status__system_status_package__field_descriptors,
  codec__out__status__system_status_package__field_indices_by_name,
  2,  codec__out__status__system_status_package__number_ranges,
  (ProtobufCMessageInit) codec__out__status__system_status_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
