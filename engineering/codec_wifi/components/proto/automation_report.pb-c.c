/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: automation_report.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "automation_report.pb-c.h"
void   codec__out__automation_report__automation_report_data__init
                     (Codec__Out__AutomationReport__AutomationReportData         *message)
{
  static const Codec__Out__AutomationReport__AutomationReportData init_value = CODEC__OUT__AUTOMATION_REPORT__AUTOMATION_REPORT_DATA__INIT;
  *message = init_value;
}
size_t codec__out__automation_report__automation_report_data__get_packed_size
                     (const Codec__Out__AutomationReport__AutomationReportData *message)
{
  assert(message->base.descriptor == &codec__out__automation_report__automation_report_data__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__out__automation_report__automation_report_data__pack
                     (const Codec__Out__AutomationReport__AutomationReportData *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__out__automation_report__automation_report_data__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__out__automation_report__automation_report_data__pack_to_buffer
                     (const Codec__Out__AutomationReport__AutomationReportData *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__out__automation_report__automation_report_data__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__Out__AutomationReport__AutomationReportData *
       codec__out__automation_report__automation_report_data__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__Out__AutomationReport__AutomationReportData *)
     protobuf_c_message_unpack (&codec__out__automation_report__automation_report_data__descriptor,
                                allocator, len, data);
}
void   codec__out__automation_report__automation_report_data__free_unpacked
                     (Codec__Out__AutomationReport__AutomationReportData *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__out__automation_report__automation_report_data__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   codec__out__automation_report__automation_report_package__init
                     (Codec__Out__AutomationReport__AutomationReportPackage         *message)
{
  static const Codec__Out__AutomationReport__AutomationReportPackage init_value = CODEC__OUT__AUTOMATION_REPORT__AUTOMATION_REPORT_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__out__automation_report__automation_report_package__get_packed_size
                     (const Codec__Out__AutomationReport__AutomationReportPackage *message)
{
  assert(message->base.descriptor == &codec__out__automation_report__automation_report_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__out__automation_report__automation_report_package__pack
                     (const Codec__Out__AutomationReport__AutomationReportPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__out__automation_report__automation_report_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__out__automation_report__automation_report_package__pack_to_buffer
                     (const Codec__Out__AutomationReport__AutomationReportPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__out__automation_report__automation_report_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__Out__AutomationReport__AutomationReportPackage *
       codec__out__automation_report__automation_report_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__Out__AutomationReport__AutomationReportPackage *)
     protobuf_c_message_unpack (&codec__out__automation_report__automation_report_package__descriptor,
                                allocator, len, data);
}
void   codec__out__automation_report__automation_report_package__free_unpacked
                     (Codec__Out__AutomationReport__AutomationReportPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__out__automation_report__automation_report_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__out__automation_report__automation_report_data__field_descriptors[5] =
{
  {
    "auto_idx",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__AutomationReport__AutomationReportData, auto_idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "start_time",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__AutomationReport__AutomationReportData, start_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "restart_time",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__AutomationReport__AutomationReportData, restart_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "end_time",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT64,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__AutomationReport__AutomationReportData, end_time),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "status",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__AutomationReport__AutomationReportData, status),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__out__automation_report__automation_report_data__field_indices_by_name[] = {
  0,   /* field[0] = auto_idx */
  3,   /* field[3] = end_time */
  2,   /* field[2] = restart_time */
  1,   /* field[1] = start_time */
  4,   /* field[4] = status */
};
static const ProtobufCIntRange codec__out__automation_report__automation_report_data__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor codec__out__automation_report__automation_report_data__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.out.automation_report.AutomationReportData",
  "AutomationReportData",
  "Codec__Out__AutomationReport__AutomationReportData",
  "codec.out.automation_report",
  sizeof(Codec__Out__AutomationReport__AutomationReportData),
  5,
  codec__out__automation_report__automation_report_data__field_descriptors,
  codec__out__automation_report__automation_report_data__field_indices_by_name,
  1,  codec__out__automation_report__automation_report_data__number_ranges,
  (ProtobufCMessageInit) codec__out__automation_report__automation_report_data__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor codec__out__automation_report__automation_report_package__field_descriptors[1] =
{
  {
    "data",
    1,
    PROTOBUF_C_LABEL_REPEATED,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(Codec__Out__AutomationReport__AutomationReportPackage, n_data),
    offsetof(Codec__Out__AutomationReport__AutomationReportPackage, data),
    &codec__out__automation_report__automation_report_data__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__out__automation_report__automation_report_package__field_indices_by_name[] = {
  0,   /* field[0] = data */
};
static const ProtobufCIntRange codec__out__automation_report__automation_report_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 1 }
};
const ProtobufCMessageDescriptor codec__out__automation_report__automation_report_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.out.automation_report.AutomationReportPackage",
  "AutomationReportPackage",
  "Codec__Out__AutomationReport__AutomationReportPackage",
  "codec.out.automation_report",
  sizeof(Codec__Out__AutomationReport__AutomationReportPackage),
  1,
  codec__out__automation_report__automation_report_package__field_descriptors,
  codec__out__automation_report__automation_report_package__field_indices_by_name,
  1,  codec__out__automation_report__automation_report_package__number_ranges,
  (ProtobufCMessageInit) codec__out__automation_report__automation_report_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
