/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: command.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "command.pb-c.h"
void   codec__in__command__command_package__init
                     (Codec__In__Command__CommandPackage         *message)
{
  static const Codec__In__Command__CommandPackage init_value = CODEC__IN__COMMAND__COMMAND_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__in__command__command_package__get_packed_size
                     (const Codec__In__Command__CommandPackage *message)
{
  assert(message->base.descriptor == &codec__in__command__command_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__command__command_package__pack
                     (const Codec__In__Command__CommandPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__command__command_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__command__command_package__pack_to_buffer
                     (const Codec__In__Command__CommandPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__command__command_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Command__CommandPackage *
       codec__in__command__command_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Command__CommandPackage *)
     protobuf_c_message_unpack (&codec__in__command__command_package__descriptor,
                                allocator, len, data);
}
void   codec__in__command__command_package__free_unpacked
                     (Codec__In__Command__CommandPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__command__command_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__in__command__command_package__field_descriptors[2] =
{
  {
    "type",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Command__CommandPackage, type),
    &codec__in__command__msg_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "value",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Command__CommandPackage, value),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__command__command_package__field_indices_by_name[] = {
  0,   /* field[0] = type */
  1,   /* field[1] = value */
};
static const ProtobufCIntRange codec__in__command__command_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor codec__in__command__command_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.command.CommandPackage",
  "CommandPackage",
  "Codec__In__Command__CommandPackage",
  "codec.in.command",
  sizeof(Codec__In__Command__CommandPackage),
  2,
  codec__in__command__command_package__field_descriptors,
  codec__in__command__command_package__field_indices_by_name,
  1,  codec__in__command__command_package__number_ranges,
  (ProtobufCMessageInit) codec__in__command__command_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue codec__in__command__msg_type__enum_values_by_number[2] =
{
  { "MSG_RESUME", "CODEC__IN__COMMAND__MSG_TYPE__MSG_RESUME", 0 },
  { "MSG_PAUSE", "CODEC__IN__COMMAND__MSG_TYPE__MSG_PAUSE", 1 },
};
static const ProtobufCIntRange codec__in__command__msg_type__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex codec__in__command__msg_type__enum_values_by_name[2] =
{
  { "MSG_PAUSE", 1 },
  { "MSG_RESUME", 0 },
};
const ProtobufCEnumDescriptor codec__in__command__msg_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "codec.in.command.MsgType",
  "MsgType",
  "Codec__In__Command__MsgType",
  "codec.in.command",
  2,
  codec__in__command__msg_type__enum_values_by_number,
  2,
  codec__in__command__msg_type__enum_values_by_name,
  1,
  codec__in__command__msg_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
