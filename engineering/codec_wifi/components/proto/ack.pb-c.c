/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: ack.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "ack.pb-c.h"
void   codec__out__ack__ack_package__init
                     (Codec__Out__Ack__AckPackage         *message)
{
  static const Codec__Out__Ack__AckPackage init_value = CODEC__OUT__ACK__ACK_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__out__ack__ack_package__get_packed_size
                     (const Codec__Out__Ack__AckPackage *message)
{
  assert(message->base.descriptor == &codec__out__ack__ack_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__out__ack__ack_package__pack
                     (const Codec__Out__Ack__AckPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__out__ack__ack_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__out__ack__ack_package__pack_to_buffer
                     (const Codec__Out__Ack__AckPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__out__ack__ack_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__Out__Ack__AckPackage *
       codec__out__ack__ack_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__Out__Ack__AckPackage *)
     protobuf_c_message_unpack (&codec__out__ack__ack_package__descriptor,
                                allocator, len, data);
}
void   codec__out__ack__ack_package__free_unpacked
                     (Codec__Out__Ack__AckPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__out__ack__ack_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__out__ack__ack_package__field_descriptors[2] =
{
  {
    "package",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Ack__AckPackage, package),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "value",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Ack__AckPackage, value),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__out__ack__ack_package__field_indices_by_name[] = {
  0,   /* field[0] = package */
  1,   /* field[1] = value */
};
static const ProtobufCIntRange codec__out__ack__ack_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor codec__out__ack__ack_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.out.ack.AckPackage",
  "AckPackage",
  "Codec__Out__Ack__AckPackage",
  "codec.out.ack",
  sizeof(Codec__Out__Ack__AckPackage),
  2,
  codec__out__ack__ack_package__field_descriptors,
  codec__out__ack__ack_package__field_indices_by_name,
  1,  codec__out__ack__ack_package__number_ranges,
  (ProtobufCMessageInit) codec__out__ack__ack_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
