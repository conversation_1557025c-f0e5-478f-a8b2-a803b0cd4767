/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: firmware_update.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "firmware_update.pb-c.h"
void   codec__in__firmware_update__firmware_update_package__init
                     (Codec__In__FirmwareUpdate__FirmwareUpdatePackage         *message)
{
  static const Codec__In__FirmwareUpdate__FirmwareUpdatePackage init_value = CODEC__IN__FIRMWARE_UPDATE__FIRMWARE_UPDATE_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__in__firmware_update__firmware_update_package__get_packed_size
                     (const Codec__In__FirmwareUpdate__FirmwareUpdatePackage *message)
{
  assert(message->base.descriptor == &codec__in__firmware_update__firmware_update_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__firmware_update__firmware_update_package__pack
                     (const Codec__In__FirmwareUpdate__FirmwareUpdatePackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__firmware_update__firmware_update_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__firmware_update__firmware_update_package__pack_to_buffer
                     (const Codec__In__FirmwareUpdate__FirmwareUpdatePackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__firmware_update__firmware_update_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__FirmwareUpdate__FirmwareUpdatePackage *
       codec__in__firmware_update__firmware_update_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__FirmwareUpdate__FirmwareUpdatePackage *)
     protobuf_c_message_unpack (&codec__in__firmware_update__firmware_update_package__descriptor,
                                allocator, len, data);
}
void   codec__in__firmware_update__firmware_update_package__free_unpacked
                     (Codec__In__FirmwareUpdate__FirmwareUpdatePackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__firmware_update__firmware_update_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__in__firmware_update__firmware_update_package__field_descriptors[4] =
{
  {
    "type",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(Codec__In__FirmwareUpdate__FirmwareUpdatePackage, type),
    &codec__in__firmware_update__msg_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "protocol",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(Codec__In__FirmwareUpdate__FirmwareUpdatePackage, protocol),
    &codec__in__firmware_update__msg_protocol__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "activation_code",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__FirmwareUpdate__FirmwareUpdatePackage, activation_code),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "version",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__FirmwareUpdate__FirmwareUpdatePackage, version),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__firmware_update__firmware_update_package__field_indices_by_name[] = {
  2,   /* field[2] = activation_code */
  1,   /* field[1] = protocol */
  0,   /* field[0] = type */
  3,   /* field[3] = version */
};
static const ProtobufCIntRange codec__in__firmware_update__firmware_update_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor codec__in__firmware_update__firmware_update_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.firmware_update.FirmwareUpdatePackage",
  "FirmwareUpdatePackage",
  "Codec__In__FirmwareUpdate__FirmwareUpdatePackage",
  "codec.in.firmware_update",
  sizeof(Codec__In__FirmwareUpdate__FirmwareUpdatePackage),
  4,
  codec__in__firmware_update__firmware_update_package__field_descriptors,
  codec__in__firmware_update__firmware_update_package__field_indices_by_name,
  1,  codec__in__firmware_update__firmware_update_package__number_ranges,
  (ProtobufCMessageInit) codec__in__firmware_update__firmware_update_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue codec__in__firmware_update__msg_type__enum_values_by_number[2] =
{
  { "MSG_ESP", "CODEC__IN__FIRMWARE_UPDATE__MSG_TYPE__MSG_ESP", 0 },
  { "MSG_STM", "CODEC__IN__FIRMWARE_UPDATE__MSG_TYPE__MSG_STM", 1 },
};
static const ProtobufCIntRange codec__in__firmware_update__msg_type__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex codec__in__firmware_update__msg_type__enum_values_by_name[2] =
{
  { "MSG_ESP", 0 },
  { "MSG_STM", 1 },
};
const ProtobufCEnumDescriptor codec__in__firmware_update__msg_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "codec.in.firmware_update.MsgType",
  "MsgType",
  "Codec__In__FirmwareUpdate__MsgType",
  "codec.in.firmware_update",
  2,
  codec__in__firmware_update__msg_type__enum_values_by_number,
  2,
  codec__in__firmware_update__msg_type__enum_values_by_name,
  1,
  codec__in__firmware_update__msg_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue codec__in__firmware_update__msg_protocol__enum_values_by_number[2] =
{
  { "MSG_HTTPS", "CODEC__IN__FIRMWARE_UPDATE__MSG_PROTOCOL__MSG_HTTPS", 0 },
  { "MSG_HTTP", "CODEC__IN__FIRMWARE_UPDATE__MSG_PROTOCOL__MSG_HTTP", 1 },
};
static const ProtobufCIntRange codec__in__firmware_update__msg_protocol__value_ranges[] = {
{0, 0},{0, 2}
};
static const ProtobufCEnumValueIndex codec__in__firmware_update__msg_protocol__enum_values_by_name[2] =
{
  { "MSG_HTTP", 1 },
  { "MSG_HTTPS", 0 },
};
const ProtobufCEnumDescriptor codec__in__firmware_update__msg_protocol__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "codec.in.firmware_update.MsgProtocol",
  "MsgProtocol",
  "Codec__In__FirmwareUpdate__MsgProtocol",
  "codec.in.firmware_update",
  2,
  codec__in__firmware_update__msg_protocol__enum_values_by_number,
  2,
  codec__in__firmware_update__msg_protocol__enum_values_by_name,
  1,
  codec__in__firmware_update__msg_protocol__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
