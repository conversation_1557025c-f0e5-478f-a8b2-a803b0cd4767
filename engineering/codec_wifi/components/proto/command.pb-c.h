/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: command.proto */

#ifndef PROTOBUF_C_command_2eproto__INCLUDED
#define PROTOBUF_C_command_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct Codec__In__Command__CommandPackage Codec__In__Command__CommandPackage;


/* --- enums --- */

typedef enum _Codec__In__Command__MsgType {
  CODEC__IN__COMMAND__MSG_TYPE__MSG_RESUME = 0,
  CODEC__IN__COMMAND__MSG_TYPE__MSG_PAUSE = 1
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(CODEC__IN__COMMAND__MSG_TYPE)
} Codec__In__Command__MsgType;

/* --- messages --- */

struct  Codec__In__Command__CommandPackage
{
  ProtobufCMessage base;
  /*
   * Tipo: 1 = pausar, 0 = retomar
   */
  Codec__In__Command__MsgType type;
  /*
   * No caso de MSG_PAUSE é a duração (em minutos)
   */
  int32_t value;
};
#define CODEC__IN__COMMAND__COMMAND_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__command__command_package__descriptor) \
    , CODEC__IN__COMMAND__MSG_TYPE__MSG_RESUME, 0 }


/* Codec__In__Command__CommandPackage methods */
void   codec__in__command__command_package__init
                     (Codec__In__Command__CommandPackage         *message);
size_t codec__in__command__command_package__get_packed_size
                     (const Codec__In__Command__CommandPackage   *message);
size_t codec__in__command__command_package__pack
                     (const Codec__In__Command__CommandPackage   *message,
                      uint8_t             *out);
size_t codec__in__command__command_package__pack_to_buffer
                     (const Codec__In__Command__CommandPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__Command__CommandPackage *
       codec__in__command__command_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__command__command_package__free_unpacked
                     (Codec__In__Command__CommandPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__In__Command__CommandPackage_Closure)
                 (const Codec__In__Command__CommandPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCEnumDescriptor    codec__in__command__msg_type__descriptor;
extern const ProtobufCMessageDescriptor codec__in__command__command_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_command_2eproto__INCLUDED */
