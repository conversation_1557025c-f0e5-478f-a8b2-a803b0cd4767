/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: scheduling.proto */

#ifndef PROTOBUF_C_scheduling_2eproto__INCLUDED
#define PROTOBUF_C_scheduling_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct Codec__In__Scheduling__Scheduling Codec__In__Scheduling__Scheduling;
typedef struct Codec__In__Scheduling__DeviceScheduling Codec__In__Scheduling__DeviceScheduling;
typedef struct Codec__In__Scheduling__SchedulingPackage Codec__In__Scheduling__SchedulingPackage;


/* --- enums --- */

typedef enum _Codec__In__Scheduling__MsgType {
  CODEC__IN__SCHEDULING__MSG_TYPE__MSG_NONE = 0,
  CODEC__IN__SCHEDULING__MSG_TYPE__MSG_SCHEDULING_ONLY = 1,
  CODEC__IN__SCHEDULING__MSG_TYPE__MSG_DEV_SCHEDULING_ONLY = 2,
  CODEC__IN__SCHEDULING__MSG_TYPE__MSG_SCHEDULING_ALL = 3
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(CODEC__IN__SCHEDULING__MSG_TYPE)
} Codec__In__Scheduling__MsgType;

/* --- messages --- */

struct  Codec__In__Scheduling__Scheduling
{
  ProtobufCMessage base;
  /*
   * Índice do agendamento
   */
  int32_t idx;
  /*
   * Horário de início (em minutos desde a meia-noite)
   */
  int32_t start_time;
  /*
   * Dias da semana em que o agendamento é válido (bitmask: 0b01111111)
   */
  int32_t days_of_week;
  /*
   * Número total de etapas a executar no agendamento
   */
  int32_t number_of_steps;
  /*
   * Índice da bomba de água usada
   */
  int32_t waterpump_idx;
  /*
   * Tempo de funcionamento da bomba de água (em minutos)
   */
  int32_t waterpump_working_time;
  /*
   * Permite aplicação de fertilizante (1 = sim, 0 = não)
   */
  protobuf_c_boolean allow_ferti;
  /*
   * Índice da ferti
   */
  int32_t ferti_idx;
  /*
   * Permite retrolavagem (1 = sim, 0 = não)
   */
  protobuf_c_boolean allow_backwash;
  /*
   * Índice da retrolavagem
   */
  int32_t backwash_idx;
  /*
   * Grupo ao qual o agendamento pertence
   */
  int32_t group;
  /*
   * Não repetir o agendamento
   */
  protobuf_c_boolean once;
};
#define CODEC__IN__SCHEDULING__SCHEDULING__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__scheduling__scheduling__descriptor) \
    , 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }


struct  Codec__In__Scheduling__DeviceScheduling
{
  ProtobufCMessage base;
  /*
   * Índice do agendamento do dispositivo
   */
  int32_t idx;
  /*
   * Índice do agendamento principal
   */
  int32_t scheduling_idx;
  /*
   * Índice do dispositivo
   */
  int32_t device_idx;
  /*
   * Ordem de execução no agendamento
   */
  int32_t order;
  /*
   * Tempo de início relativo (minutos após início do agendamento)
   */
  int32_t sector_working_time;
  /*
   * Tempo de funcionamento para fertilização (em minutos)
   */
  int32_t ferti_working_time;
  /*
   * Atraso antes da fertilização (em minutos)
   */
  int32_t ferti_delay;
};
#define CODEC__IN__SCHEDULING__DEVICE_SCHEDULING__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__scheduling__device_scheduling__descriptor) \
    , 0, 0, 0, 0, 0, 0, 0 }


struct  Codec__In__Scheduling__SchedulingPackage
{
  ProtobufCMessage base;
  Codec__In__Scheduling__MsgType type;
  /*
   * Lista de agendamentos
   */
  size_t n_scheduling_data;
  Codec__In__Scheduling__Scheduling **scheduling_data;
  /*
   * Lista de agendamentos por dispositivo
   */
  size_t n_device_scheduling_data;
  Codec__In__Scheduling__DeviceScheduling **device_scheduling_data;
};
#define CODEC__IN__SCHEDULING__SCHEDULING_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__in__scheduling__scheduling_package__descriptor) \
    , CODEC__IN__SCHEDULING__MSG_TYPE__MSG_NONE, 0,NULL, 0,NULL }


/* Codec__In__Scheduling__Scheduling methods */
void   codec__in__scheduling__scheduling__init
                     (Codec__In__Scheduling__Scheduling         *message);
size_t codec__in__scheduling__scheduling__get_packed_size
                     (const Codec__In__Scheduling__Scheduling   *message);
size_t codec__in__scheduling__scheduling__pack
                     (const Codec__In__Scheduling__Scheduling   *message,
                      uint8_t             *out);
size_t codec__in__scheduling__scheduling__pack_to_buffer
                     (const Codec__In__Scheduling__Scheduling   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__Scheduling__Scheduling *
       codec__in__scheduling__scheduling__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__scheduling__scheduling__free_unpacked
                     (Codec__In__Scheduling__Scheduling *message,
                      ProtobufCAllocator *allocator);
/* Codec__In__Scheduling__DeviceScheduling methods */
void   codec__in__scheduling__device_scheduling__init
                     (Codec__In__Scheduling__DeviceScheduling         *message);
size_t codec__in__scheduling__device_scheduling__get_packed_size
                     (const Codec__In__Scheduling__DeviceScheduling   *message);
size_t codec__in__scheduling__device_scheduling__pack
                     (const Codec__In__Scheduling__DeviceScheduling   *message,
                      uint8_t             *out);
size_t codec__in__scheduling__device_scheduling__pack_to_buffer
                     (const Codec__In__Scheduling__DeviceScheduling   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__Scheduling__DeviceScheduling *
       codec__in__scheduling__device_scheduling__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__scheduling__device_scheduling__free_unpacked
                     (Codec__In__Scheduling__DeviceScheduling *message,
                      ProtobufCAllocator *allocator);
/* Codec__In__Scheduling__SchedulingPackage methods */
void   codec__in__scheduling__scheduling_package__init
                     (Codec__In__Scheduling__SchedulingPackage         *message);
size_t codec__in__scheduling__scheduling_package__get_packed_size
                     (const Codec__In__Scheduling__SchedulingPackage   *message);
size_t codec__in__scheduling__scheduling_package__pack
                     (const Codec__In__Scheduling__SchedulingPackage   *message,
                      uint8_t             *out);
size_t codec__in__scheduling__scheduling_package__pack_to_buffer
                     (const Codec__In__Scheduling__SchedulingPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__In__Scheduling__SchedulingPackage *
       codec__in__scheduling__scheduling_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__in__scheduling__scheduling_package__free_unpacked
                     (Codec__In__Scheduling__SchedulingPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__In__Scheduling__Scheduling_Closure)
                 (const Codec__In__Scheduling__Scheduling *message,
                  void *closure_data);
typedef void (*Codec__In__Scheduling__DeviceScheduling_Closure)
                 (const Codec__In__Scheduling__DeviceScheduling *message,
                  void *closure_data);
typedef void (*Codec__In__Scheduling__SchedulingPackage_Closure)
                 (const Codec__In__Scheduling__SchedulingPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCEnumDescriptor    codec__in__scheduling__msg_type__descriptor;
extern const ProtobufCMessageDescriptor codec__in__scheduling__scheduling__descriptor;
extern const ProtobufCMessageDescriptor codec__in__scheduling__device_scheduling__descriptor;
extern const ProtobufCMessageDescriptor codec__in__scheduling__scheduling_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_scheduling_2eproto__INCLUDED */
