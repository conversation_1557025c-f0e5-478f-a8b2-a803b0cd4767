/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: automation_report.proto */

#ifndef PROTOBUF_C_automation_5freport_2eproto__INCLUDED
#define PROTOBUF_C_automation_5freport_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct Codec__Out__AutomationReport__AutomationReportData Codec__Out__AutomationReport__AutomationReportData;
typedef struct Codec__Out__AutomationReport__AutomationReportPackage Codec__Out__AutomationReport__AutomationReportPackage;


/* --- enums --- */


/* --- messages --- */

struct  Codec__Out__AutomationReport__AutomationReportData
{
  ProtobufCMessage base;
  /*
   * Índice da automação
   */
  int32_t auto_idx;
  /*
   * Horário do início
   */
  uint64_t start_time;
  /*
   * Horário do reinicio, 0 se não ocorreu reinicio
   */
  uint64_t restart_time;
  /*
   * Horário do término, 0 se não finalizou
   */
  uint64_t end_time;
  /*
   * Status de execução
   */
  int32_t status;
};
#define CODEC__OUT__AUTOMATION_REPORT__AUTOMATION_REPORT_DATA__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__out__automation_report__automation_report_data__descriptor) \
    , 0, 0, 0, 0, 0 }


struct  Codec__Out__AutomationReport__AutomationReportPackage
{
  ProtobufCMessage base;
  /*
   * Relatório da automação
   */
  size_t n_data;
  Codec__Out__AutomationReport__AutomationReportData **data;
};
#define CODEC__OUT__AUTOMATION_REPORT__AUTOMATION_REPORT_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__out__automation_report__automation_report_package__descriptor) \
    , 0,NULL }


/* Codec__Out__AutomationReport__AutomationReportData methods */
void   codec__out__automation_report__automation_report_data__init
                     (Codec__Out__AutomationReport__AutomationReportData         *message);
size_t codec__out__automation_report__automation_report_data__get_packed_size
                     (const Codec__Out__AutomationReport__AutomationReportData   *message);
size_t codec__out__automation_report__automation_report_data__pack
                     (const Codec__Out__AutomationReport__AutomationReportData   *message,
                      uint8_t             *out);
size_t codec__out__automation_report__automation_report_data__pack_to_buffer
                     (const Codec__Out__AutomationReport__AutomationReportData   *message,
                      ProtobufCBuffer     *buffer);
Codec__Out__AutomationReport__AutomationReportData *
       codec__out__automation_report__automation_report_data__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__out__automation_report__automation_report_data__free_unpacked
                     (Codec__Out__AutomationReport__AutomationReportData *message,
                      ProtobufCAllocator *allocator);
/* Codec__Out__AutomationReport__AutomationReportPackage methods */
void   codec__out__automation_report__automation_report_package__init
                     (Codec__Out__AutomationReport__AutomationReportPackage         *message);
size_t codec__out__automation_report__automation_report_package__get_packed_size
                     (const Codec__Out__AutomationReport__AutomationReportPackage   *message);
size_t codec__out__automation_report__automation_report_package__pack
                     (const Codec__Out__AutomationReport__AutomationReportPackage   *message,
                      uint8_t             *out);
size_t codec__out__automation_report__automation_report_package__pack_to_buffer
                     (const Codec__Out__AutomationReport__AutomationReportPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__Out__AutomationReport__AutomationReportPackage *
       codec__out__automation_report__automation_report_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__out__automation_report__automation_report_package__free_unpacked
                     (Codec__Out__AutomationReport__AutomationReportPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__Out__AutomationReport__AutomationReportData_Closure)
                 (const Codec__Out__AutomationReport__AutomationReportData *message,
                  void *closure_data);
typedef void (*Codec__Out__AutomationReport__AutomationReportPackage_Closure)
                 (const Codec__Out__AutomationReport__AutomationReportPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__out__automation_report__automation_report_data__descriptor;
extern const ProtobufCMessageDescriptor codec__out__automation_report__automation_report_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_automation_5freport_2eproto__INCLUDED */
