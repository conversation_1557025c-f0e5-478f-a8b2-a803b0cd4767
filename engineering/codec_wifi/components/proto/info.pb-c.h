/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: info.proto */

#ifndef PROTOBUF_C_info_2eproto__INCLUDED
#define PROTOBUF_C_info_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1004001 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct Codec__Out__Info__InfoPackage Codec__Out__Info__InfoPackage;


/* --- enums --- */


/* --- messages --- */

struct  Codec__Out__Info__InfoPackage
{
  ProtobufCMessage base;
  /*
   * Número de série do Codec
   */
  char *codec_id;
  /*
   * Versão do firmware ESP
   */
  uint32_t firmware_esp;
  /*
   * Versão do firmware do mesh
   */
  uint32_t firmware_mesh;
  /*
   * Versão do hardware
   */
  uint32_t hardware_version;
  /*
   * Número de resets
   */
  uint32_t resets;
  /*
   * Agendamento em execução
   */
  uint32_t scheduling_running;
  /*
   * Agendamento pausado
   */
  uint32_t scheduling_paused;
  /*
   * Última atualização dos dispositivos
   */
  uint32_t devices_id;
  /*
   * Última atualização dos agendamentos
   */
  uint32_t scheduling_id;
  /*
   * Última atualização dos agendamentos por dispositivo
   */
  uint32_t dev_scheduling_id;
  /*
   * Última atualização das automações
   */
  uint32_t automation_id;
  /*
   * Última atualização da configuração
   */
  uint32_t config_id;
  /*
   * Máscara de bits de falhas do sistema
   */
  uint32_t failed_bitmask;
};
#define CODEC__OUT__INFO__INFO_PACKAGE__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&codec__out__info__info_package__descriptor) \
    , (char *)protobuf_c_empty_string, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 }


/* Codec__Out__Info__InfoPackage methods */
void   codec__out__info__info_package__init
                     (Codec__Out__Info__InfoPackage         *message);
size_t codec__out__info__info_package__get_packed_size
                     (const Codec__Out__Info__InfoPackage   *message);
size_t codec__out__info__info_package__pack
                     (const Codec__Out__Info__InfoPackage   *message,
                      uint8_t             *out);
size_t codec__out__info__info_package__pack_to_buffer
                     (const Codec__Out__Info__InfoPackage   *message,
                      ProtobufCBuffer     *buffer);
Codec__Out__Info__InfoPackage *
       codec__out__info__info_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   codec__out__info__info_package__free_unpacked
                     (Codec__Out__Info__InfoPackage *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Codec__Out__Info__InfoPackage_Closure)
                 (const Codec__Out__Info__InfoPackage *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCMessageDescriptor codec__out__info__info_package__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_info_2eproto__INCLUDED */
