syntax = "proto3";

package codec.in.control;

message ControlPackage {
  int32 idx = 1;             // Índice do dispositivo
  MsgAction action = 2;      // Código da ação (ex: 1 = ligar, 2 = desligar)
  int32 value = 3;           // Tempo de funcionamento (em minutos)
  bytes payload = 4;         // Payload
  int32 activation_code = 5; // Código de ativação
}

enum MsgAction {
  MSG_NONE      = 0;
  MSG_TURN_ON   = 1;
  MSG_TURN_OFF  = 2;
  MSG_PACKAGE   = 3;
}