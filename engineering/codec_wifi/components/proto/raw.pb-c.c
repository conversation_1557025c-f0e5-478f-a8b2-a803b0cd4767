/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: raw.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "raw.pb-c.h"
void   codec__out__raw__raw_package__init
                     (Codec__Out__Raw__RawPackage         *message)
{
  static const Codec__Out__Raw__RawPackage init_value = CODEC__OUT__RAW__RAW_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__out__raw__raw_package__get_packed_size
                     (const Codec__Out__Raw__RawPackage *message)
{
  assert(message->base.descriptor == &codec__out__raw__raw_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__out__raw__raw_package__pack
                     (const Codec__Out__Raw__RawPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__out__raw__raw_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__out__raw__raw_package__pack_to_buffer
                     (const Codec__Out__Raw__RawPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__out__raw__raw_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__Out__Raw__RawPackage *
       codec__out__raw__raw_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__Out__Raw__RawPackage *)
     protobuf_c_message_unpack (&codec__out__raw__raw_package__descriptor,
                                allocator, len, data);
}
void   codec__out__raw__raw_package__free_unpacked
                     (Codec__Out__Raw__RawPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__out__raw__raw_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__out__raw__raw_package__field_descriptors[4] =
{
  {
    "idx",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Raw__RawPackage, idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "equipment",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Raw__RawPackage, equipment),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "type",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Raw__RawPackage, type),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "payload",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(Codec__Out__Raw__RawPackage, payload),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__out__raw__raw_package__field_indices_by_name[] = {
  1,   /* field[1] = equipment */
  0,   /* field[0] = idx */
  3,   /* field[3] = payload */
  2,   /* field[2] = type */
};
static const ProtobufCIntRange codec__out__raw__raw_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 4 }
};
const ProtobufCMessageDescriptor codec__out__raw__raw_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.out.raw.RawPackage",
  "RawPackage",
  "Codec__Out__Raw__RawPackage",
  "codec.out.raw",
  sizeof(Codec__Out__Raw__RawPackage),
  4,
  codec__out__raw__raw_package__field_descriptors,
  codec__out__raw__raw_package__field_indices_by_name,
  1,  codec__out__raw__raw_package__number_ranges,
  (ProtobufCMessageInit) codec__out__raw__raw_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
