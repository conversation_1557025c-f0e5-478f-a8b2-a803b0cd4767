/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: control.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "control.pb-c.h"
void   codec__in__control__control_package__init
                     (Codec__In__Control__ControlPackage         *message)
{
  static const Codec__In__Control__ControlPackage init_value = CODEC__IN__CONTROL__CONTROL_PACKAGE__INIT;
  *message = init_value;
}
size_t codec__in__control__control_package__get_packed_size
                     (const Codec__In__Control__ControlPackage *message)
{
  assert(message->base.descriptor == &codec__in__control__control_package__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t codec__in__control__control_package__pack
                     (const Codec__In__Control__ControlPackage *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &codec__in__control__control_package__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t codec__in__control__control_package__pack_to_buffer
                     (const Codec__In__Control__ControlPackage *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &codec__in__control__control_package__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
Codec__In__Control__ControlPackage *
       codec__in__control__control_package__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (Codec__In__Control__ControlPackage *)
     protobuf_c_message_unpack (&codec__in__control__control_package__descriptor,
                                allocator, len, data);
}
void   codec__in__control__control_package__free_unpacked
                     (Codec__In__Control__ControlPackage *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &codec__in__control__control_package__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor codec__in__control__control_package__field_descriptors[5] =
{
  {
    "idx",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Control__ControlPackage, idx),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "action",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Control__ControlPackage, action),
    &codec__in__control__msg_action__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "value",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Control__ControlPackage, value),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "payload",
    4,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Control__ControlPackage, payload),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "activation_code",
    5,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_INT32,
    0,   /* quantifier_offset */
    offsetof(Codec__In__Control__ControlPackage, activation_code),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned codec__in__control__control_package__field_indices_by_name[] = {
  1,   /* field[1] = action */
  4,   /* field[4] = activation_code */
  0,   /* field[0] = idx */
  3,   /* field[3] = payload */
  2,   /* field[2] = value */
};
static const ProtobufCIntRange codec__in__control__control_package__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 5 }
};
const ProtobufCMessageDescriptor codec__in__control__control_package__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "codec.in.control.ControlPackage",
  "ControlPackage",
  "Codec__In__Control__ControlPackage",
  "codec.in.control",
  sizeof(Codec__In__Control__ControlPackage),
  5,
  codec__in__control__control_package__field_descriptors,
  codec__in__control__control_package__field_indices_by_name,
  1,  codec__in__control__control_package__number_ranges,
  (ProtobufCMessageInit) codec__in__control__control_package__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue codec__in__control__msg_action__enum_values_by_number[4] =
{
  { "MSG_NONE", "CODEC__IN__CONTROL__MSG_ACTION__MSG_NONE", 0 },
  { "MSG_TURN_ON", "CODEC__IN__CONTROL__MSG_ACTION__MSG_TURN_ON", 1 },
  { "MSG_TURN_OFF", "CODEC__IN__CONTROL__MSG_ACTION__MSG_TURN_OFF", 2 },
  { "MSG_PACKAGE", "CODEC__IN__CONTROL__MSG_ACTION__MSG_PACKAGE", 3 },
};
static const ProtobufCIntRange codec__in__control__msg_action__value_ranges[] = {
{0, 0},{0, 4}
};
static const ProtobufCEnumValueIndex codec__in__control__msg_action__enum_values_by_name[4] =
{
  { "MSG_NONE", 0 },
  { "MSG_PACKAGE", 3 },
  { "MSG_TURN_OFF", 2 },
  { "MSG_TURN_ON", 1 },
};
const ProtobufCEnumDescriptor codec__in__control__msg_action__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "codec.in.control.MsgAction",
  "MsgAction",
  "Codec__In__Control__MsgAction",
  "codec.in.control",
  4,
  codec__in__control__msg_action__enum_values_by_number,
  4,
  codec__in__control__msg_action__enum_values_by_name,
  1,
  codec__in__control__msg_action__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
