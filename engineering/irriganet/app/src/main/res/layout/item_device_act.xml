<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/text_mesh_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Mesh ID"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/text_device_id"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Device ID"
            android:textSize="14sp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>