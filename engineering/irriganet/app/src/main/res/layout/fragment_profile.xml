<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.ProfileFragment">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="10dp">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/frag_profile_text_input_layout_title"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:boxStrokeColor="@color/black"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/frag_profile_text_input_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Título"
                    android:inputType="textCapWords"
                    android:background="@android:color/transparent" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/frag_profile_text_input_layout_name"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:boxStrokeColor="@color/black"
                app:layout_constraintTop_toBottomOf="@id/frag_profile_text_input_layout_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/frag_profile_text_input_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Nome"
                    android:inputType="textCapWords"
                    android:background="@android:color/transparent" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/frag_profile_text_input_layout_phone"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:boxStrokeColor="@color/black"
                app:layout_constraintTop_toBottomOf="@id/frag_profile_text_input_layout_name"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/frag_profile_text_input_phone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Telefone"
                    android:inputType="phone"
                    android:background="@android:color/transparent" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/frag_profile_text_input_layout_email"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:boxStrokeColor="@color/black"
                app:layout_constraintTop_toBottomOf="@id/frag_profile_text_input_layout_phone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/frag_profile_text_input_email"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="E-mail"
                    android:inputType="textEmailAddress"
                    android:background="@android:color/transparent" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/frag_profile_text_input_layout_address"
                android:layout_width="250dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                app:boxStrokeColor="@color/black"
                app:layout_constraintTop_toBottomOf="@id/frag_profile_text_input_layout_email"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/frag_profile_text_input_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Endereço"
                    android:inputType="textCapSentences"
                    android:background="@android:color/transparent" />
            </com.google.android.material.textfield.TextInputLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

</FrameLayout>
