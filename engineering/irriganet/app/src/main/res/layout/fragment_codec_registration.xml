<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".ui.CodecRegistrationFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/frag_codec_reg_recycler_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintBottom_toTopOf="@id/frag_codec_reg_fab_add_codec"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:listitem="@layout/item_model_text_btn" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/frag_codec_reg_fab_add_codec"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/baseline_add_24"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginEnd="@dimen/fab_margin"
            android:layout_marginBottom="16dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</FrameLayout>