<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="4dp"
    app:cardElevation="4dp"
    app:cardUseCompatPadding="true">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp">

       <LinearLayout
            android:id="@+id/text_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_alignParentStart="true">

            <TextView
                android:id="@+id/item_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Valvulas 1 a 3"
                android:textSize="16sp"
                android:textColor="@android:color/black" />

            <TextView
                android:id="@+id/item_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ID: 00FFAD-1"
                android:textSize="14sp"
                android:textColor="@android:color/black" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/icon_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:gravity="end">

            <ImageView
                android:id="@+id/item_sync"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_transmicao"  />

            <Space
                android:layout_width="8dp"
                android:layout_height="0dp" />

            <ImageView
                android:id="@+id/item_on"
                android:layout_width="24dp"
                android:layout_height="match_parent"
                android:src="@drawable/ic_power"  />
        </LinearLayout>

    </RelativeLayout>

</androidx.cardview.widget.CardView>
