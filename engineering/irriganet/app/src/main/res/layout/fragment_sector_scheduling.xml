<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.SectorSchedulingFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="10dp">

        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/frag_sector_scheduling_text_input_layout_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            app:boxStrokeColor="@color/black"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/frag_sector_scheduling_text_input_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                android:hint="Nome do Agendamento"
                android:inputType="textCapSentences" />
        </com.google.android.material.textfield.TextInputLayout>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/frag_sector_scheduling_linear_layout_compat"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="10dp"
            app:layout_constraintTop_toBottomOf="@id/frag_sector_scheduling_text_input_layout_name"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/daysConstraintLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/timeLayout">

                <com.google.android.material.chip.Chip
                    android:id="@+id/frag_sector_scheduling_chip_sunday"
                    style="@style/Widget.MaterialComponents.Chip.Choice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checkable="true"
                    android:text="D"
                    android:textSize="12sp"
                    app:chipMinHeight="32dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/frag_sector_scheduling_chip_monday"
                    style="@style/Widget.MaterialComponents.Chip.Choice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checkable="true"
                    android:text="S"
                    android:textSize="12sp"
                    app:chipMinHeight="32dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/frag_sector_scheduling_chip_sunday"
                    app:layout_constraintEnd_toStartOf="@id/frag_sector_scheduling_chip_tuesday" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/frag_sector_scheduling_chip_tuesday"
                    style="@style/Widget.MaterialComponents.Chip.Choice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checkable="true"
                    android:text="T"
                    android:textSize="12sp"
                    app:chipMinHeight="32dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/frag_sector_scheduling_chip_monday"
                    app:layout_constraintEnd_toStartOf="@id/frag_sector_scheduling_chip_wednesday" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/frag_sector_scheduling_chip_wednesday"
                    style="@style/Widget.MaterialComponents.Chip.Choice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checkable="true"
                    android:text="Q"
                    android:textSize="12sp"
                    app:chipMinHeight="32dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/frag_sector_scheduling_chip_tuesday"
                    app:layout_constraintEnd_toStartOf="@id/frag_sector_scheduling_chip_thursday" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/frag_sector_scheduling_chip_thursday"
                    style="@style/Widget.MaterialComponents.Chip.Choice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checkable="true"
                    android:text="Q"
                    android:textSize="12sp"
                    app:chipMinHeight="32dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/frag_sector_scheduling_chip_wednesday"
                    app:layout_constraintEnd_toStartOf="@id/frag_sector_scheduling_chip_friday" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/frag_sector_scheduling_chip_friday"
                    style="@style/Widget.MaterialComponents.Chip.Choice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checkable="true"
                    android:text="S"
                    android:textSize="12sp"
                    app:chipMinHeight="32dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/frag_sector_scheduling_chip_saturday"
                    app:layout_constraintStart_toEndOf="@id/frag_sector_scheduling_chip_thursday" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/frag_sector_scheduling_chip_saturday"
                    style="@style/Widget.MaterialComponents.Chip.Choice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checkable="true"
                    android:text="S"
                    android:textSize="12sp"
                    app:chipMinHeight="32dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.appcompat.widget.LinearLayoutCompat>

        <LinearLayout
            android:id="@+id/timeLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="5dp"
            android:orientation="horizontal"
            android:weightSum="3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/frag_sector_scheduling_linear_layout_compat">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="25dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="Hora">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/frag_sector_scheduling_input_hour"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:inputType="number"
                    android:maxLength="2" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="35dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="Minuto">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/frag_sector_scheduling_input_minute"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@android:color/transparent"
                    android:inputType="number"
                    android:maxLength="2" />
            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginLeft="10dp"
                android:orientation="vertical"
                android:gravity="center_vertical">
                <Switch
                    android:id="@+id/frag_sector_scheduling_switch_ferti"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Ferti" />

                <Switch
                    android:id="@+id/frag_sector_scheduling_switch_backwash"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Retro Lavagem" />

            </LinearLayout>

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/frag_sector_scheduling_recycler_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/timeLayout"
            tools:listitem="@layout/item_model_sector_scheduling" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/frag_sector_scheduling_fab_save"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|bottom"
        android:layout_margin="16dp"
        app:srcCompat="@android:drawable/ic_menu_save"
        android:contentDescription="Salvar"/>

</FrameLayout>
