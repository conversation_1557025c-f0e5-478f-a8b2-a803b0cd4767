package br.com.byagro.irriganet

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import br.com.byagro.irriganet.databinding.ItemModelSchedulingBinding
import java.util.Collections

class SchedulingAdapter(
    private val items: MutableList<SchedulingItem>,
    private val onItemCheck: (SchedulingItem, Boolean) -> Unit,
    private val onItemClick: (SchedulingItem) -> Unit,
    private val onDeleteClick: (Int) -> Unit
) :
    RecyclerView.Adapter<SchedulingAdapter.SchedulingViewHolder>() {

    class SchedulingViewHolder(val binding: ItemModelSchedulingBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: SchedulingItem, onItemCheck: (SchedulingItem, Boolean) -> Unit, onItemClick: (SchedulingItem) -> Unit, onDeleteClick: (Int) -> Unit) {
            binding.itemText.text = item.name

            binding.checkBox.setOnCheckedChangeListener(null)
            binding.checkBox.isChecked = item.enabled
            binding.checkBox.setOnCheckedChangeListener { _, isChecked ->
                item.enabled = isChecked
                onItemCheck(item, isChecked)
            }

            binding.itemText.setOnClickListener {
                onItemClick(item)
            }

            binding.btnDelete.setOnClickListener {
                onDeleteClick(adapterPosition)
            }

        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SchedulingViewHolder {
        val binding = ItemModelSchedulingBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return SchedulingViewHolder(binding)
    }

    override fun onBindViewHolder(holder: SchedulingViewHolder, position: Int) {
        holder.bind(items[position], onItemCheck, onItemClick, onDeleteClick)
    }

    override fun getItemCount() = items.size

}