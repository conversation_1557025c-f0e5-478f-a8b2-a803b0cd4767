package br.com.byagro.irriganet

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import br.com.byagro.irriganet.databinding.ItemReportBinding
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.util.Date
import java.util.Locale
import java.util.TimeZone

class ReportAdapter(
    private val items: MutableList<ReportItem>,
    private val onItemClick: (ReportItem) -> Unit
) :
    RecyclerView.Adapter<ReportAdapter.ReportViewHolder>() {

   class ReportViewHolder(val binding: ItemReportBinding) : RecyclerView.ViewHolder(binding.root) {
       fun convertEpoch(epochSeconds: Long): String {
           val date = Date(epochSeconds * 1000) // convert seconds to millis
           val format = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
           format.timeZone = TimeZone.getDefault()
           return format.format(date)
       }

       fun bind(item: ReportItem, onItemClick: (ReportItem) -> Unit) {
            binding.itemTextTitle.text = item.title
            binding.itemTextDate.text = item.date

            if(item.type == 0) {
                binding.itemIconSectors.visibility = View.VISIBLE
                binding.itemTextSectors.visibility = View.VISIBLE
                if (!item.completed) {
                    binding.itemTextSectors.text = "Irrigando o setor "+item.currentSector
                } else {
                    if (item.sectorsCount == item.totalSectors) {
                        binding.itemTextSectors.text = "Irrigação completa"
                    } else {
                        binding.itemTextSectors.text = "Irrigação incompleta"
                    }
                }

                if (item.allowFerti) {
                    binding.itemIconFerti.visibility = View.VISIBLE
                    binding.itemTextFerti.visibility = View.VISIBLE
                    if (!item.completed) {
                        if(item.currentFertiActivated){
                            binding.itemTextFerti.text = "Ferti acionada"
                        } else {
                            binding.itemTextFerti.text = "Ferti aguardando"
                        }
                    } else {
                        if (item.fertiCount == item.totalSectors) {
                            binding.itemTextFerti.text = "Ferti completa"
                        } else {
                            binding.itemTextFerti.text = "Ferti incompleta"
                        }
                    }
                } else {
                    binding.itemIconFerti.visibility = View.GONE
                    binding.itemTextFerti.visibility = View.GONE
                }

                if (item.backwash_time != 0) {
                    binding.itemLlBackwash.visibility = View.VISIBLE
                    val hour = convertEpoch(item.backwash_time.toLong())
                    binding.itemTextBackwash.text = "Retrolavagem às $hour"
                } else {
                    binding.itemLlBackwash.visibility = View.GONE
                }
            } else if(item.type == 1) {
                binding.itemIconSectors.visibility = View.GONE
                binding.itemTextSectors.visibility = View.GONE
                binding.itemIconFerti.visibility = View.GONE
                binding.itemTextFerti.visibility = View.GONE
                binding.itemLlBackwash.visibility = View.GONE
            }

            binding.itemTextTitle.setOnClickListener {
                onItemClick(item)
            }

        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ReportViewHolder {
        val binding = ItemReportBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ReportViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ReportViewHolder, position: Int) {
        holder.bind(items[position], onItemClick)
    }

    override fun getItemCount() = items.size

}