import android.util.Log
import com.hivemq.client.mqtt.MqttClient
import com.hivemq.client.mqtt.MqttClientState
import com.hivemq.client.mqtt.datatypes.MqttQos
import com.hivemq.client.mqtt.mqtt3.Mqtt3Client
import com.hivemq.client.mqtt.mqtt5.Mqtt5AsyncClient
import kotlinx.coroutines.future.await
import java.nio.charset.StandardCharsets
import com.hivemq.client.mqtt.mqtt5.Mqtt5Client
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import com.hivemq.client.util.TypeSwitch

class HiveMqttManager(
    serverHost : String,
    serverPort : Int = 8003,
    clientId   : String,
    private val user: String,
    private val pass: String,
    private val topics: List<String>,
    coroutineContext: CoroutineContext = Dispatchers.IO,
    private val onMessage: suspend (topic: String, payload: ByteArray) -> Unit
) {

    private val scope  = CoroutineScope(SupervisorJob() + coroutineContext)
    private val online = AtomicBoolean(false)

    private val client5: Mqtt5AsyncClient = Mqtt5Client.builder()
        .identifier(clientId)
        .serverHost(serverHost)
        .serverPort(serverPort)
        .automaticReconnect()
        .initialDelay(100, TimeUnit.MILLISECONDS)
        .maxDelay(10, TimeUnit.SECONDS)
        .applyAutomaticReconnect()
        .simpleAuth()
        .username(user)
        .password(pass.toByteArray())
        .applySimpleAuth()
        .transportConfig()
        .socketConnectTimeout(3, TimeUnit.SECONDS)
        .mqttConnectTimeout(3, TimeUnit.SECONDS)
        .applyTransportConfig()
        .addConnectedListener {
            online.set(true)
            scope.launch { subscribeAll() }
        }
        .addDisconnectedListener { ctx ->
            online.set(false)
            Log.w("MQTT","DISCONNECTED source=${ctx.source} cause=${ctx.cause?.message}", ctx.cause)
        }
        .buildAsync()

    private val client3 = Mqtt3Client.builder()
        .identifier(clientId)
        .serverHost(serverHost)
        .serverPort(serverPort)
        .automaticReconnect()
        .initialDelay(100, TimeUnit.MILLISECONDS)
        .maxDelay(10, TimeUnit.SECONDS)
        .applyAutomaticReconnect()
        .simpleAuth()
        .username(user)
        .password(pass.toByteArray())
        .applySimpleAuth()
        .transportConfig()
        .socketConnectTimeout(3, TimeUnit.SECONDS)
        .mqttConnectTimeout(3, TimeUnit.SECONDS)
        .applyTransportConfig()
        .addConnectedListener {
            online.set(true)
            scope.launch { subscribeAll() }
        }
        .addDisconnectedListener { ctx ->
            online.set(false)
            Log.w("MQTT","DISCONNECTED source=${ctx.source} cause=${ctx.cause?.message}", ctx.cause)
        }
        .buildAsync()

    private val client = client3

    suspend fun connectAndWait() = withContext(scope.coroutineContext) {
        client.connectWith()
            .simpleAuth()
            .username(user)
            .password(pass.toByteArray())
            .applySimpleAuth()
            .keepAlive(60)
            .cleanSession(true) // MQTT3
            //.cleanStart(true) // MQTT5
            .send()
            .await()
    }

    fun isConnected(): Boolean = online.get()

    private fun commonCallback(topic: String) = { bytes: ByteArray ->
        scope.launch { onMessage(topic, bytes) }
    }

    private suspend fun subscribeAll() {
        for (topic in topics) {
            try {
                client.subscribeWith()
                    .topicFilter(topic)
                    .qos(MqttQos.AT_LEAST_ONCE)
                    .callback { pub ->
                        pub.payload.ifPresent { buf ->
                            val data = ByteArray(buf.remaining()).also { buf.get(it) }
                            commonCallback(pub.topic.toString())(data)
                        }
                    }
                    .send()
                    .await()
            } catch (ex: Throwable) {
                Log.e("MQTT", "Failed to subscribe $topic", ex)
            }
        }
    }

    suspend fun publish(
        topic: String,
        payload: ByteArray,
        qos: MqttQos = MqttQos.AT_LEAST_ONCE,
        retained: Boolean = false
    ): Boolean {
        if (client.state != MqttClientState.CONNECTED) return false

        return try {
            client.publishWith()
                .topic(topic)
                .qos(qos)
                .payload(payload)
                .retain(retained)
                .send()
                .await()
            true
        } catch (t: Throwable) {
            Log.e("MQTT", "Publish failed on $topic", t)
            false
        }
    }

    suspend fun publish(topic: String, text: String, retained: Boolean = false) =
        publish(topic, text.encodeToByteArray(), retained = retained)
}
