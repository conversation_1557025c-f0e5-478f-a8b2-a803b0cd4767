package br.com.byagro.irriganet.ui

import android.app.AlertDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.EditText
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import br.com.byagro.irriganet.CodecAdapter
import br.com.byagro.irriganet.CodecItem
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.R
import br.com.byagro.irriganet.databinding.FragmentCodecRegistrationBinding


// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [CodecRegistrationFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class CodecRegistrationFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private lateinit var binding: FragmentCodecRegistrationBinding
    private lateinit var dbHelper: DBHelper

    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: CodecAdapter
    private var codecList: MutableList<CodecItem> = mutableListOf()

    private var codecIdx: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            codecIdx = it.getString("codecIdx")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        //(activity as AppCompatActivity).supportActionBar!!.title = "Lista de Codecs"

        //val view = inflater.inflate(R.layout.fragment_codecs, container, false)
        binding = FragmentCodecRegistrationBinding.inflate(inflater, container, false)

        try {
            //recyclerView = view.findViewById(R.id.recycler_view_codecs)
            binding.fragCodecRegRecyclerView.layoutManager = LinearLayoutManager(context)

            adapter = CodecAdapter(
                codecList,
                { selectedItem -> onItemClick(selectedItem) },
                { position -> configCodec(position) })
            binding.fragCodecRegRecyclerView.adapter = adapter

            binding.fragCodecRegFabAddCodec.setOnClickListener {
                showCodecInputDialog()
            }

            dbHelper = DBHelper(requireContext())

            codecList.clear()
            codecList.addAll(dbHelper.getAllCodecs())
            adapter.notifyDataSetChanged()
        } catch (e: Exception){
            e.printStackTrace()
        }

        return binding.root
    }

    private fun showCodecInputDialog() {
        // Inflate the custom layout
        val dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_codec_input, null)
        val editTextId: EditText = dialogView.findViewById(R.id.editTextId)

        // Build the AlertDialog
        val dialogBuilder = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setTitle("Codec")
            .setPositiveButton("OK") { dialog, _ ->
                val inputId = editTextId.text.toString()
                if (inputId.isNotEmpty()) {
                    val name = "Codec "+inputId
                    val idx = dbHelper.insertCodec(inputId, name, "", "").toInt()
                    codecList.add(CodecItem(idx, inputId, name))
                    adapter.notifyItemInserted(codecList.size - 1)
                } else {

                }
                dialog.dismiss()
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.cancel()
            }

        // Create and show the dialog
        val alertDialog = dialogBuilder.create()
        alertDialog.show()
    }

    private fun onItemClick(Item: CodecItem) {
        val bundle = Bundle().apply {
            putInt("codecIdx", Item.idx)
            putString("codecIdentity", Item.identity)
            putString("codecName", Item.name)
        }
        findNavController().navigate(R.id.action_nav_codecs_to_nav_codec, bundle)
    }

    private fun configCodec(position: Int) {
        val codecId = codecList[position].idx

        val bundle = Bundle().apply {
            putInt("codecIdx", codecId)
        }
        findNavController().navigate(R.id.action_nav_codecs_to_nav_codec_config, bundle)
    }

    override fun onDestroyView() {
        super.onDestroyView()
    }
}