package br.com.byagro.irriganet.ui

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.text.Editable
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.IndividualPumpsAdapter
import br.com.byagro.irriganet.LevelPumpAdapter
import br.com.byagro.irriganet.LevelPumpItem
import br.com.byagro.irriganet.R
import br.com.byagro.irriganet.SimpleItem
import br.com.byagro.irriganet.databinding.FragmentMeshDeviceConfigBinding

const val PULSE = 0x01
const val MONI = 0x02
const val MAX_DEVICES_LIMIT = 50

/**
 * A simple [Fragment] subclass.
 * Use the [MeshDeviceFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class MeshDeviceFragment : Fragment() {
    private val PREFS_NAME = "byagro_prefs"
    private lateinit var binding: FragmentMeshDeviceConfigBinding
    private lateinit var sharedPref: SharedPreferences
    private lateinit var dbHelper: DBHelper

    private lateinit var adapter: LevelPumpAdapter
    private var levelPumpList: MutableList<LevelPumpItem> = mutableListOf()

    private var meshIdx: Int = -1
    private var meshDeviceIdentity: String? = null
    private var name: String? = null
    private var codecIdx: Int = -1
    private var groupIdx: Int = -1
    private var sector: Int = 0
    private var devicesCount = 0

    enum class MainType(val value: Int) {
        Valve(0),
        Pump(1),
        Level(2),
    }

    enum class DevType(val value: Int) {
        Valve(0),
        IrrigationPump(1),
        Ferti(2),
        Backwash(3),
        ServicePump(4),
        Level(5)
    }

    enum class EquiptType(val value: Int) {
        PL10(0),
        PL50(1)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            meshIdx = it.getInt("meshIdx", -1)
            meshDeviceIdentity = it.getString("meshDeviceIdentity")
            name = it.getString("name")
            codecIdx = it.getInt("codecIdx", -1)
            groupIdx = it.getInt("groupIdx", -1)
        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentMeshDeviceConfigBinding.inflate(inflater, container, false)

        try {
            binding.fragMeshDeviceRecyclerView.layoutManager = LinearLayoutManager(context)

            adapter = LevelPumpAdapter(levelPumpList)
            binding.fragMeshDeviceRecyclerView.adapter = adapter
            binding.fragMeshDeviceRecyclerView.setNestedScrollingEnabled(false)

            devicesCount = dbHelper.getDevicesCountByCodec(codecIdx)

            sharedPref = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

            levelPumpList.clear()

            if (meshIdx != -1) {
                meshIdx.let { idx ->
                    val meshDevice = dbHelper.getMeshDeviceById(idx)
                    meshDevice?.let {
                        val meshIdText = it["identity"] as String
                        binding.fragMeshDeviceMeshId.setText("%06X".format(meshIdText.toInt()))
                        binding.fragMeshDeviceMeshName.setText(it["name"] as String)

                        // tipo de dispositivo
                        when (it["type"] as Int) {
                            MainType.Valve.value -> {
                                binding.fragMeshDeviceRadioValve.isChecked = true

                                val devices = dbHelper.getAllDevicesByMesh(meshIdx)
                                for (device in devices) {
                                    val identity = device["identity"] as? String ?: continue
                                    val sector = device["sector"] as? Int ?: continue
                                    when (identity.toIntOrNull()) {
                                        0 -> {
                                            binding.fragMeshDeviceCheckBox1.isChecked = true
                                            binding.fragMeshDeviceEditTextSectorS1.setText(sector.toString())
                                        }

                                        1 -> {
                                            binding.fragMeshDeviceCheckBox2.isChecked = true
                                            binding.fragMeshDeviceEditTextSectorS2.setText(sector.toString())
                                        }

                                        2 -> {
                                            binding.fragMeshDeviceCheckBox3.isChecked = true
                                            binding.fragMeshDeviceEditTextSectorS3.setText(sector.toString())
                                        }

                                        3 -> {
                                            binding.fragMeshDeviceCheckBox4.isChecked = true
                                            binding.fragMeshDeviceEditTextSectorS4.setText(sector.toString())
                                        }

                                        else -> {
                                        }
                                    }
                                }
                            }
                            MainType.Pump.value -> binding.fragMeshDeviceRadioPump.isChecked = true
                            MainType.Level.value -> {
                                binding.fragMeshDeviceRadioLevel.isChecked = true
                                val deviceMaps = dbHelper.getAllDevicesByTypeOnMap(DevType.ServicePump.value)
                                val levelPumpItems = deviceMaps.map { map ->
                                    val idx = map["idx"] as? Int ?: 0
                                    val name = map["mesh_name"] as? String ?: ""
                                    LevelPumpItem(idx, name, false)
                                }
                                binding.fragMeshDeviceLevelPumpWorkingTime.setText(
                                    (it["level_pump_working_time"] as? Int ?: 0).toString()
                                )
                                levelPumpList.addAll(levelPumpItems)
                                val chosenIdx = it["level_pump_idx"] as? Int ?: 0
                                for (item in levelPumpList) {
                                    item.selected = item.idx == chosenIdx
                                }
                            }
                        }

                        if (it["type"] as Int == MainType.Valve.value) {
                            binding.fragMeshDeviceLinearLayoutOutputs.visibility = View.VISIBLE
                            binding.fragMeshDeviceLinearLayoutPumpDevices.visibility = View.GONE
                            binding.fragMeshDeviceLinearLayoutIndividualPumps.visibility = View.GONE
                        } else if (it["type"] as Int == MainType.Level.value) {
                            binding.fragMeshDeviceLinearLayoutOutputs.visibility = View.GONE
                            binding.fragMeshDeviceLinearLayoutPumpDevices.visibility = View.GONE
                            binding.fragMeshDeviceLinearLayoutIndividualPumps.visibility = View.VISIBLE
                        } else { // Bombas
                            binding.fragMeshDeviceLinearLayoutOutputs.visibility = View.GONE
                            binding.fragMeshDeviceLinearLayoutPumpDevices.visibility = View.VISIBLE
                            binding.fragMeshDeviceLinearLayoutIndividualPumps.visibility = View.GONE
                        }

                        // Tipo de equipamento
                        when (it["equipament"] as Int) {
                            EquiptType.PL10.value -> {
                                binding.fragMeshDeviceRadioPl10.isChecked = true
                                binding.fragMeshDeviceSwitchFerti.visibility = View.VISIBLE
                                binding.fragMeshDeviceSwitchBackwash.visibility = View.VISIBLE
                                binding.fragMeshDeviceLinearLayoutPumpMode.visibility = View.GONE
                            }
                            EquiptType.PL50.value -> {
                                binding.fragMeshDeviceRadioPl50.isChecked = true
                                if(binding.fragMeshDeviceRadioPulse.isChecked)
                                    binding.fragMeshDeviceSwitchFerti.visibility = View.GONE
                                binding.fragMeshDeviceSwitchBackwash.visibility = View.GONE
                                binding.fragMeshDeviceLinearLayoutPumpMode.visibility = View.VISIBLE
                            }
                        }

                        // Modo de operação
                        when (it["mode"] as Int) {
                            0 -> {
                                binding.fragMeshDeviceSwitchFerti.visibility = View.VISIBLE
                                binding.fragMeshDeviceRadioContinue.isChecked = true
                            }
                            1 -> {
                                binding.fragMeshDeviceSwitchFerti.visibility = View.GONE
                                binding.fragMeshDeviceRadioPulse.isChecked = true
                            }
                        }

                        binding.fragMeshDeviceSwitchIndividualPump.isChecked = (((it["devices_bitmask"] as Int) shr DevType.ServicePump.value) and 1) == 1
                        binding.fragMeshDeviceSwitchIrrigationPump.isChecked = (((it["devices_bitmask"] as Int) shr DevType.IrrigationPump.value) and 1) == 1
                        binding.fragMeshDeviceSwitchFerti.isChecked = (((it["devices_bitmask"] as Int) shr DevType.Ferti.value) and 1) == 1
                        binding.fragMeshDeviceSwitchBackwash.isChecked = (((it["devices_bitmask"] as Int) shr DevType.Backwash.value) and 1) == 1
                        binding.fragMeshDeviceSwitchCheckInput.isChecked = (it["check_input"] as Int) == 1
                        if(binding.fragMeshDeviceSwitchIrrigationPump.isChecked){
                            binding.fragMeshDeviceSwitchIndividualPump.visibility = View.GONE
                        }
                        if(binding.fragMeshDeviceSwitchIndividualPump.isChecked){
                            binding.fragMeshDeviceSwitchIrrigationPump.visibility = View.GONE
                            binding.fragMeshDeviceSwitchFerti.visibility = View.GONE
                            binding.fragMeshDeviceSwitchBackwash.visibility = View.GONE
                        }
                        if(binding.fragMeshDeviceSwitchIndividualPump.isChecked or binding.fragMeshDeviceSwitchIrrigationPump.isChecked){
                            binding.fragMeshDeviceSwitchCheckInput.visibility = View.VISIBLE
                        } else {
                            binding.fragMeshDeviceSwitchCheckInput.visibility = View.GONE
                        }
                    }
                }
            } else {
                binding.fragMeshDeviceLinearLayoutOutputs.visibility = View.VISIBLE
                binding.fragMeshDeviceLinearLayoutPumpDevices.visibility = View.GONE
                binding.fragMeshDeviceLinearLayoutIndividualPumps.visibility = View.GONE
            }

            sector = dbHelper.getMaxSector()

            // visibilidade por tipo
            binding.fragMeshDeviceTypeGroup.setOnCheckedChangeListener { group, checkedId ->
                when (checkedId) {
                    binding.fragMeshDeviceRadioValve.id -> {
                        binding.fragMeshDeviceLinearLayoutOutputs.visibility = View.VISIBLE
                        binding.fragMeshDeviceLinearLayoutPumpDevices.visibility = View.GONE
                        binding.fragMeshDeviceLinearLayoutIndividualPumps.visibility = View.GONE
                    }
                    binding.fragMeshDeviceRadioPump.id -> {
                        binding.fragMeshDeviceLinearLayoutOutputs.visibility = View.GONE
                        binding.fragMeshDeviceLinearLayoutPumpDevices.visibility = View.VISIBLE
                        binding.fragMeshDeviceLinearLayoutIndividualPumps.visibility = View.GONE
                    }
                    binding.fragMeshDeviceRadioLevel.id -> {
                        binding.fragMeshDeviceLinearLayoutIndividualPumps.visibility = View.VISIBLE
                        binding.fragMeshDeviceLinearLayoutOutputs.visibility = View.GONE
                        binding.fragMeshDeviceLinearLayoutPumpDevices.visibility = View.GONE
                        if(levelPumpList.size == 0) {
                            val deviceMaps = dbHelper.getAllDevicesByTypeOnMap(DevType.ServicePump.value)
                            val levelPumpItems = deviceMaps.map { map ->
                                val idx = map["idx"] as? Int ?: 0
                                val name = map["mesh_name"] as? String ?: ""
                                LevelPumpItem(idx, name, false)
                            }
                            levelPumpList.addAll(levelPumpItems)
                        }
                    }
                    else -> {
                        binding.fragMeshDeviceLinearLayoutOutputs.visibility = View.GONE
                        binding.fragMeshDeviceLinearLayoutIndividualPumps.visibility = View.GONE
                        binding.fragMeshDeviceLinearLayoutPumpDevices.visibility = View.VISIBLE
                    }
                }
            }

            // Visibilidade por tipo de Equipamento
            binding.fragMeshDeviceEquipamentGroup.setOnCheckedChangeListener { _, checkedId ->
                if (checkedId == binding.fragMeshDeviceRadioPl10.id) {
                    if(!binding.fragMeshDeviceSwitchIndividualPump.isChecked) {
                        binding.fragMeshDeviceSwitchBackwash.visibility = View.VISIBLE
                    }
                    binding.fragMeshDeviceLinearLayoutPumpMode.visibility = View.GONE
                } else {
                    binding.fragMeshDeviceSwitchBackwash.visibility = View.GONE // não tem retro no PL50
                    binding.fragMeshDeviceLinearLayoutPumpMode.visibility = View.VISIBLE // mostra o modo no PL50
                }
                binding.fragMeshDeviceSwitchIndividualPump.isChecked = false
                binding.fragMeshDeviceSwitchIrrigationPump.isChecked = false
                binding.fragMeshDeviceSwitchFerti.isChecked = false
                binding.fragMeshDeviceSwitchBackwash.isChecked = false
                binding.fragMeshDeviceSwitchCheckInput.isChecked = false
                binding.fragMeshDeviceRadioContinue.isChecked = true
            }

            // Visibilidade por modo de operação
            binding.fragMeshDeviceModeGroup.setOnCheckedChangeListener { _, checkedId ->
                if (checkedId == binding.fragMeshDeviceRadioContinue.id) {
                    if(!binding.fragMeshDeviceSwitchIndividualPump.isChecked){
                        binding.fragMeshDeviceSwitchFerti.visibility = View.VISIBLE
                    }else{
                        binding.fragMeshDeviceSwitchFerti.isChecked = false
                    }
                } else {
                    binding.fragMeshDeviceSwitchFerti.visibility = View.GONE
                    binding.fragMeshDeviceSwitchFerti.isChecked = false
                }
            }

            // Visibilidade ao acionar a bomba de Irrigação
            binding.fragMeshDeviceSwitchIrrigationPump.setOnCheckedChangeListener{
                    _, isChecked ->
                if (isChecked) {
                    binding.fragMeshDeviceSwitchIndividualPump.visibility = View.GONE
                    binding.fragMeshDeviceSwitchIndividualPump.isChecked = false
                    binding.fragMeshDeviceSwitchCheckInput.visibility = View.VISIBLE
                } else {
                    binding.fragMeshDeviceSwitchIndividualPump.visibility = View.VISIBLE
                    binding.fragMeshDeviceSwitchCheckInput.visibility = View.GONE
                    binding.fragMeshDeviceSwitchCheckInput.isChecked = false
                }
            }

            // Visibilidade ao acionar a bomba avulsa
            binding.fragMeshDeviceSwitchIndividualPump.setOnCheckedChangeListener{
                    _, isChecked ->
                if (isChecked) {
                    binding.fragMeshDeviceSwitchIrrigationPump.visibility = View.GONE
                    binding.fragMeshDeviceSwitchFerti.visibility = View.GONE
                    binding.fragMeshDeviceSwitchBackwash.visibility = View.GONE
                    binding.fragMeshDeviceSwitchIrrigationPump.isChecked = false
                    binding.fragMeshDeviceSwitchFerti.isChecked = false
                    binding.fragMeshDeviceSwitchBackwash.isChecked = false
                    binding.fragMeshDeviceSwitchCheckInput.visibility = View.VISIBLE
                } else {
                    if(binding.fragMeshDeviceRadioContinue.isChecked) {
                        binding.fragMeshDeviceSwitchFerti.visibility = View.VISIBLE
                        if(binding.fragMeshDeviceRadioPl10.isChecked) {
                            binding.fragMeshDeviceSwitchBackwash.visibility = View.VISIBLE
                        }
                    }
                    binding.fragMeshDeviceSwitchIrrigationPump.visibility = View.VISIBLE
                    binding.fragMeshDeviceSwitchCheckInput.visibility = View.GONE
                    binding.fragMeshDeviceSwitchCheckInput.isChecked = false
                }
            }

            binding.fragMeshDeviceCheckBox1.setOnCheckedChangeListener{
                    _, isChecked ->
                if (isChecked) {
                    sector += 1
                    binding.fragMeshDeviceEditTextSectorS1.setText(sector.toString())
                } else {
                    sector -= 1
                    binding.fragMeshDeviceEditTextSectorS1.setText("")
                }
            }

            binding.fragMeshDeviceCheckBox2.setOnCheckedChangeListener{
                    _, isChecked ->
                if (isChecked) {
                    sector += 1
                    binding.fragMeshDeviceEditTextSectorS2.setText(sector.toString())
                } else {
                    sector -= 1
                    binding.fragMeshDeviceEditTextSectorS2.setText("")
                }
            }

            binding.fragMeshDeviceCheckBox3.setOnCheckedChangeListener{
                    _, isChecked ->
                if (isChecked) {
                    sector += 1
                    binding.fragMeshDeviceEditTextSectorS3.setText(sector.toString())
                } else {
                    sector -= 1
                    binding.fragMeshDeviceEditTextSectorS3.setText("")
                }
            }

            binding.fragMeshDeviceCheckBox4.setOnCheckedChangeListener{
                    _, isChecked ->
                if (isChecked) {
                    sector += 1
                    binding.fragMeshDeviceEditTextSectorS4.setText(sector.toString())
                } else {
                    sector -= 1
                    binding.fragMeshDeviceEditTextSectorS4.setText("")
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }

        binding.fragMeshDeviceFabSave.setOnClickListener {
            try {
                // --- 1. DATA GATHERING ---
                // Read the raw text input from the UI fields.
                val meshDeviceIdentityText = binding.fragMeshDeviceMeshId.text.toString()
                val name = binding.fragMeshDeviceMeshName.text.toString()

                // Determine the main functional type of the mesh device (Valve, Pump, or Level).
                val mainType = when (binding.fragMeshDeviceTypeGroup.checkedRadioButtonId) {
                    binding.fragMeshDeviceRadioValve.id -> MainType.Valve.value
                    binding.fragMeshDeviceRadioPump.id -> MainType.Pump.value
                    binding.fragMeshDeviceRadioLevel.id -> MainType.Level.value
                    else -> -1
                }

                // Gather the boolean states of all configuration switches.
                val individualPump = binding.fragMeshDeviceSwitchIndividualPump.isChecked
                val irrigationPump = binding.fragMeshDeviceSwitchIrrigationPump.isChecked
                val ferti = binding.fragMeshDeviceSwitchFerti.isChecked
                val backwash = binding.fragMeshDeviceSwitchBackwash.isChecked
                val checkInput = binding.fragMeshDeviceSwitchCheckInput.isChecked

                // Create a list of enabled device sub-types for pump configurations.
                val deviceTypes = mutableListOf<Int>().apply {
                    if (irrigationPump)  add(DevType.IrrigationPump.value)
                    if (ferti)           add(DevType.Ferti.value)
                    if (backwash)        add(DevType.Backwash.value)
                    if (individualPump)  add(DevType.ServicePump.value)
                }
                // Calculate a bitmask representing the enabled sub-devices.
                var devicesBitmask = 0
                deviceTypes.forEach { typeValue ->
                    devicesBitmask = devicesBitmask or (1 shl typeValue)
                }

                // --- 2. INPUT VALIDATION ---
                // If the main type is Pump, ensure at least one sub-type is selected.
                if(mainType == MainType.Pump.value){
                    if(deviceTypes.isEmpty()) {
                        Toast.makeText(
                            requireContext(),
                            "Selecione pelo menos um tipo de equipamento!",
                            Toast.LENGTH_SHORT
                        ).show()
                        return@setOnClickListener
                    }
                }

                // Ensure the Mesh ID and Name fields are not empty.
                if (meshDeviceIdentityText.isEmpty() || name.isEmpty()) {
                    Toast.makeText(
                        requireContext(),
                        "Preencha todos os campos!",
                        Toast.LENGTH_SHORT
                    ).show()
                    return@setOnClickListener
                }

                // Convert the hexadecimal Mesh ID from the UI into a decimal string for database storage.
                val meshDeviceIdentity = meshDeviceIdentityText.toInt(16).toString()

                // Prevent duplicate Irrigation or Ferti pumps within the same group.
                if (deviceTypes.any { it == DevType.IrrigationPump.value || it == DevType.Ferti.value }) {
                    val devicesList = dbHelper.getAllDevicesByGroup(groupIdx)
                    val duplicated = devicesList.any { existing ->
                        val existingType = existing["type"]?.toString()
                        val existingMesh = existing["mesh_idx"]?.toString()
                        deviceTypes.map(Int::toString).contains(existingType) && existingMesh != meshIdx.toString()
                    }

                    if (duplicated) {
                        Toast.makeText(
                            requireContext(),
                            "Este tipo de equipamento já foi cadastrado para este Grupo!",
                            Toast.LENGTH_SHORT
                        ).show()
                        return@setOnClickListener
                    }
                }

                // --- 3. LEVEL CONTROLLER SETUP ---
                // If the device is a Level controller, find the selected service pump and its settings.
                var levelPumpIdx: Int? = null
                var levelPumpEnable: Int? = 0
                var levelPumpWorkingTime: Int? = null
                if (mainType == MainType.Level.value) {
                    for (item in levelPumpList) {
                        if (item.selected) {
                            levelPumpIdx = item.idx
                            break
                        }
                    }
                    levelPumpEnable = 1
                    levelPumpWorkingTime =
                        binding.fragMeshDeviceLevelPumpWorkingTime.text.toString().toInt()
                }

                // --- 4. UPSERT MESH DEVICE ---
                // If meshIdx is valid, update the existing record. Otherwise, insert a new one.
                if (meshIdx != -1) {
                    val result = dbHelper.updateMeshDevice(
                        meshIdx,
                        meshDeviceIdentity.toString(),
                        name,
                        mainType,
                        if (binding.fragMeshDeviceRadioContinue.isChecked) 0 else 1,
                        if (binding.fragMeshDeviceRadioPl10.isChecked) 0 else 1,
                        if (checkInput) 1 else 0,
                        devicesBitmask,
                        levelPumpIdx,
                        levelPumpEnable,
                        levelPumpWorkingTime,
                        codecIdx,
                        groupIdx
                    )
                } else {
                    meshIdx = dbHelper.insertMeshDevice(
                        meshDeviceIdentity.toString(),
                        name,
                        mainType,
                        if (binding.fragMeshDeviceRadioContinue.isChecked) 0 else 1,
                        if (binding.fragMeshDeviceRadioPl10.isChecked) 0 else 1,
                        if (checkInput) 1 else 0,
                        devicesBitmask,
                        levelPumpIdx,
                        levelPumpEnable,
                        levelPumpWorkingTime,
                        codecIdx,
                        groupIdx
                    ).toInt()
                }

                // --- 5. BUILD CHILD DEVICE LIST ---
                // Prepare a list of logical child devices based on the configuration.
                val listOfPairs: List<Pair<Boolean, Editable>> = listOf(
                    Pair(
                        binding.fragMeshDeviceCheckBox1.isChecked,
                        binding.fragMeshDeviceEditTextSectorS1.text
                    ),
                    Pair(
                        binding.fragMeshDeviceCheckBox2.isChecked,
                        binding.fragMeshDeviceEditTextSectorS2.text
                    ),
                    Pair(
                        binding.fragMeshDeviceCheckBox3.isChecked,
                        binding.fragMeshDeviceEditTextSectorS3.text
                    ),
                    Pair(
                        binding.fragMeshDeviceCheckBox4.isChecked,
                        binding.fragMeshDeviceEditTextSectorS4.text
                    )
                )

                val devicesToInsert = mutableListOf<Map<String, Any?>>()
                val mode = (if (binding.fragMeshDeviceRadioPulse.isChecked) PULSE else 0) or (if (binding.fragMeshDeviceSwitchCheckInput.isChecked) MONI else 0)
                
                if (mainType == MainType.Valve.value) { // Case 1: Valve Controller
                    listOfPairs.forEachIndexed { index, (check, sector) ->
                        if (check) {
                            val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to index.toString(),
                                "type" to MainType.Valve.value,
                                "out1" to (2*index + 1).toString(),
                                "out2" to (2*index + 2).toString(),
                                "input" to "0",
                                "mode" to PULSE,
                                "sector" to sector.toString()
                            )
                            devicesToInsert.add(device)
                        }
                    }
                } else if (mainType == MainType.Pump.value) { // Case 2: Pump Controller
                    if (binding.fragMeshDeviceRadioPl10.isChecked) {
                        if (irrigationPump) {
                            val input = if (checkInput) "1" else "0"
                            val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "0",
                                "type" to DevType.IrrigationPump.value,
                                "out1" to "1",
                                "out2" to "0",
                                "input" to input,
                                "mode" to mode,
                                "sector" to null
                            )
                            devicesToInsert.add(device)
                        } else if (individualPump) {
                            val input = if (checkInput) "1" else "0"
                            val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "0",
                                "type" to DevType.ServicePump.value,
                                "out1" to "1",
                                "out2" to "0",
                                "input" to input,
                                "mode" to mode,
                                "sector" to null
                            )
                            devicesToInsert.add(device)
                        }

                        if (ferti) {
                            val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "1",
                                "type" to DevType.Ferti.value,
                                "out1" to "2",
                                "out2" to "0",
                                "input" to "0",
                                "mode" to 0,
                                "sector" to null
                            )
                            devicesToInsert.add(device)
                        }

                        if (backwash) {
                            val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "2",
                                "type" to DevType.Backwash.value,
                                "out1" to "3",
                                "out2" to "0",
                                "input" to "0",
                                "mode" to 0,
                                "sector" to null
                            )
                            devicesToInsert.add(device)
                        }

                    } else {
                        val input = if (checkInput) "1" else "0"
                        if (irrigationPump) {
                            val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "0",
                                "type" to DevType.IrrigationPump.value,
                                "out1" to "1",
                                "out2" to if (binding.fragMeshDeviceRadioPulse.isChecked) "2" else "0",
                                "input" to input,
                                "mode" to mode,
                                "sector" to null
                            )
                            devicesToInsert.add(device)
                        } else if(individualPump) {
                            val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "0",
                                "type" to DevType.ServicePump.value,
                                "out1" to "1",
                                "out2" to if (binding.fragMeshDeviceRadioPulse.isChecked) "2" else "0",
                                "input" to input,
                                "mode" to mode,
                                "sector" to null
                            )
                            devicesToInsert.add(device)
                        }

                        if (ferti && binding.fragMeshDeviceRadioContinue.isChecked) {
                            val device = mapOf(
                                "mesh_idx" to meshIdx,
                                "identity" to "1",
                                "type" to DevType.Ferti.value,
                                "out1" to "2",
                                "out2" to "0",
                                "input" to "0",
                                "mode" to 0,
                                "sector" to null
                            )
                            devicesToInsert.add(device)
                        }
                    }
                } else if (mainType == MainType.Level.value) { // Case 3: Level Controller
                    val device = mapOf(
                        "mesh_idx" to meshIdx,
                        "identity" to "0",
                        "type" to DevType.Level.value,
                        "out1" to "0",
                        "out2" to "0",
                        "input" to "0",
                        "mode" to 0,
                        "sector" to null
                    )
                    devicesToInsert.add(device)
                }

                if(devicesCount+devicesToInsert.size > MAX_DEVICES_LIMIT){
                    Toast.makeText(
                        requireContext(),
                        "Limite de dispositivos alcançado para este Codec.",
                        Toast.LENGTH_SHORT
                    ).show()
                    return@setOnClickListener
                }

                // --- 6. SYNCHRONIZE CHILD DEVICES ---
                // Atomically insert, update, or delete child devices to match the new configuration.
                if (devicesToInsert.isNotEmpty()) {
                    dbHelper.syncDevicesForMesh(meshIdx, devicesToInsert)
                }

                // --- 7. TRIGGER SYSTEM-WIDE UPDATE ---
                val timeStamp = System.currentTimeMillis() / 1000
                dbHelper.updateCodecFields(
                    codecIdx ?: 0, mapOf(
                        "last_devices_update" to timeStamp,
                        "last_automation_update" to timeStamp,
                        "last_scheduling_update" to timeStamp,
                        "last_device_scheduling_update" to timeStamp,
                        "last_config_update" to timeStamp
                    )
                )
                // Signal to MainActivity that data has changed and a sync/reorganization is needed.
                with(sharedPref.edit()) {
                    putBoolean("dataUpdated", true)
                    putBoolean("dataUpdateEvent", true)
                    apply()
                }
                // --- 8. NAVIGATE BACK ---
                findNavController().popBackStack()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        return binding.root
    }

}