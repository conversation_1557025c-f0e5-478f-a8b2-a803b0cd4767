package br.com.byagro.irriganet

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

data class MqttMessage(val codec: String, val message: ByteArray)

class MqttViewModel : ViewModel() {

    private val _mqttMessage = MutableLiveData<MqttMessage>()
    val mqttMessage: LiveData<MqttMessage> get() = _mqttMessage

    fun onMessageArrived(codec: String, message: ByteArray) {
        _mqttMessage.postValue(MqttMessage(codec, message))
    }
}