package br.com.byagro.irriganet.ui

import android.app.AlertDialog
import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import br.com.byagro.irriganet.DBHelper
import br.com.byagro.irriganet.R
import br.com.byagro.irriganet.SchedulingAdapter
import br.com.byagro.irriganet.SchedulingItem
import br.com.byagro.irriganet.databinding.FragmentGroupSchedulingBinding

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"
const val MAX_SCHEDULING_LIMIT = 20
/**
 * A simple [Fragment] subclass.
 * Use the [GroupSchedulingFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class GroupSchedulingFragment : Fragment() {
    private val PREFS_NAME = "byagro_prefs"
    private lateinit var binding: FragmentGroupSchedulingBinding
    private lateinit var sharedPref: SharedPreferences
    private lateinit var dbHelper: DBHelper
    private var codecIdx: Int? = -1
    private var groupIdx: Int = -1
    private var enabledChanged: Boolean = false
    private var schedulingCount = 0

    private lateinit var adapter: SchedulingAdapter
    private var schedulingList: MutableList<SchedulingItem> = mutableListOf()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            //codecIdx = it.getInt("codecIdx")
            groupIdx = it.getInt("groupIdx")
        }
        dbHelper = DBHelper(requireContext())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentGroupSchedulingBinding.inflate(inflater, container, false)

        try {
            sharedPref = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

            binding.fragGroupSchedulingRecyclerViewSchedulings.layoutManager =
                LinearLayoutManager(context)

            adapter = SchedulingAdapter(
                schedulingList,
                { selectedItem, isChecked -> onItemCheck(selectedItem, isChecked) },
                { selectedItem -> onItemClick(selectedItem) },
                { position -> deleteScheduling(position) }
            )
            binding.fragGroupSchedulingRecyclerViewSchedulings.adapter = adapter

            codecIdx = dbHelper.getCodecIdxByGroup(groupIdx)
            schedulingCount = dbHelper.getSchedulingsCountByCodec(codecIdx?:0)

            schedulingList.clear()
            schedulingList.addAll(dbHelper.getAllSchedulings(groupIdx))
            adapter.notifyDataSetChanged()

            binding.fragGroupSchedulinFabAddScheduling.setOnClickListener {

                if(schedulingCount >= MAX_SCHEDULING_LIMIT){
                    Toast.makeText(
                        requireContext(),
                        "Limite de agendamentos alcançado para este Codec.",
                        Toast.LENGTH_SHORT
                    ).show()
                    return@setOnClickListener
                }

                val bundle = Bundle().apply {
                    putInt("groupIdx", groupIdx)
                }
                findNavController().navigate(
                    R.id.action_nav_group_scheduling_to_nav_scheduling,
                    bundle
                )
            }
        } catch (e: Exception){
            e.printStackTrace()
        }

        return binding.root
    }

    fun onItemCheck(item: SchedulingItem, isChecked: Boolean) {
        dbHelper.updateSchedulingEnabled(item.idx, if(item.enabled) 1 else 0)
        enabledChanged = true
    }

    private fun onItemClick(item: SchedulingItem) {
        val bundle = Bundle().apply {
            putInt("schedulingIdx", item.idx)
            putInt("groupIdx", item.groupIdx)
            putString("name", item.name)
        }
        Log.i("GroupScheduling", "OnItemClick to Sector Scheduling")
        findNavController().navigate(R.id.action_nav_group_scheduling_to_nav_scheduling, bundle)
    }

    private fun deleteScheduling(position: Int) {
        val builder = AlertDialog.Builder(requireContext())
        builder.setTitle("Confirmar Exclusão")
        builder.setMessage("Tem certeza de que deseja excluir este Agendamento?")

        builder.setPositiveButton("Sim") { dialog, _ ->

            val Id = schedulingList[position].idx
            val rowsDeleted = dbHelper.deleteScheduling(Id)

            if (rowsDeleted > 0) {
                schedulingList.removeAt(position)
                adapter.notifyItemRemoved(position) // Notify adapter about the change
            }

            val timeStamp = System.currentTimeMillis() / 1000
            val codecIdx = dbHelper.getCodecIdxByGroup(groupIdx)
            dbHelper.updateCodecFields(
                codecIdx ?: 0, mapOf(
                    "last_scheduling_update" to timeStamp,
                    "last_device_scheduling_update" to timeStamp,
                )
            )
            with(sharedPref.edit()) {
                putBoolean("dataUpdated", true)
                putBoolean("dataUpdateEvent", true)
                apply()
            }

            dialog.dismiss()
        }

        builder.setNegativeButton("Cancelar") { dialog, _ ->
            dialog.dismiss()
        }

        val alertDialog = builder.create()
        alertDialog.show()
    }

    override fun onPause() {
        super.onPause()
        try {
            if (enabledChanged) {
                val timeStamp = System.currentTimeMillis() / 1000
                val codecIdx = dbHelper.getCodecIdxByGroup(groupIdx)
                dbHelper.updateCodecFields(
                    codecIdx ?: 0, mapOf(
                        "last_scheduling_update" to timeStamp,
                        "last_device_scheduling_update" to timeStamp,
                    )
                )
                with(sharedPref.edit()) {
                    putBoolean("dataUpdated", true)
                    putBoolean("dataUpdateEvent", true)
                    apply()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}