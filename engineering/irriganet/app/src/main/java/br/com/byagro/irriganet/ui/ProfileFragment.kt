package br.com.byagro.irriganet.ui

import android.content.Context
import android.content.SharedPreferences
import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import br.com.byagro.irriganet.R
import br.com.byagro.irriganet.databinding.FragmentProfileBinding

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

/**
 * A simple [Fragment] subclass.
 * Use the [ProfileFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class ProfileFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private val PREFS_NAME = "byagro_prefs"
    private lateinit var binding: FragmentProfileBinding
    private lateinit var sharedPref: SharedPreferences

    private var title: String? = null
    private var name: String? = null
    private var phone: String? = null
    private var email: String? = null
    private var address: String? = null
    private var latitude: String? = null
    private var longitude: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {

        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        binding = FragmentProfileBinding.inflate(inflater, container, false)

        try {
            sharedPref = requireContext().getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

            title = sharedPref.getString("title", "")
            name = sharedPref.getString("name", "")
            phone = sharedPref.getString("phone", "")
            email = sharedPref.getString("email", "")
            address = sharedPref.getString("address", "")
            latitude = sharedPref.getString("latitude", "")
            longitude = sharedPref.getString("longitude", "")

            binding.fragProfileTextInputTitle.setText(title)
            binding.fragProfileTextInputName.setText(name)
            binding.fragProfileTextInputPhone.setText(phone)
            binding.fragProfileTextInputEmail.setText(email)
            binding.fragProfileTextInputAddress.setText(address)
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return binding.root
    }

    override fun onPause() {
        super.onPause()
        with(sharedPref.edit()) {
            putString("title", binding.fragProfileTextInputTitle.text.toString())
            putString("name", binding.fragProfileTextInputName.text.toString())
            putString("phone", binding.fragProfileTextInputPhone.text.toString())
            putString("email", binding.fragProfileTextInputEmail.text.toString())
            putString("address", binding.fragProfileTextInputAddress.text.toString())
            putString("latitude", "")
            putString("longitude", "")
            apply()
        }

    }
}