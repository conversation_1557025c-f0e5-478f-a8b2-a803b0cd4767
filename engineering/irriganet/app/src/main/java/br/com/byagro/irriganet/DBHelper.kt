package br.com.byagro.irriganet

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import android.database.SQLException
import android.database.sqlite.SQLiteConstraintException
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import br.com.byagro.irriganet.ui.MeshDeviceFragment
import com.google.gson.Gson

/**
 * Database helper class for the IrrigaNet irrigation management system.
 * 
 * This class manages the SQLite database that stores information about:
 * - Codecs: Communication devices that control irrigation systems
 * - Groups: Logical groupings of devices under a codec
 * - Mesh Devices: Physical devices in the mesh network
 * - Devices: Individual components like valves, pumps, sensors
 * - Schedulings: Irrigation schedules and timing configurations
 * - Device Schedulings: Specific scheduling assignments for devices
 * 
 * The database schema supports a hierarchical structure:
 * Codec -> Groups -> Mesh Devices -> Devices -> Schedulings -> Device Schedulings
 * 
 * Key Features:
 * - Foreign key constraints with CASCADE DELETE for referential integrity
 * - Order index (ord_idx) management for communication protocol sequencing
 * - Batch operations with transaction support
 * - Comprehensive device synchronization capabilities
 * - Data reorganization utilities for maintaining consistency
 * 
 * @param context The Android application context
 */
class DBHelper(context: Context) : SQLiteOpenHelper(context, DATABASE_NAME, null, DATABASE_VERSION) {

    /**
     * Creates all database tables and indexes when the database is first created.
     * 
     * Sets up the complete database schema including:
     * - All main tables (Codecs, Groups, Mesh_Devices, Devices, Schedulings, etc.)
     * - Unique indexes for data integrity
     * - Performance indexes for frequently queried columns
     * - Foreign key relationships between related tables
     * 
     * @param db The SQLite database instance
     */
    override fun onCreate(db: SQLiteDatabase) {
        try {
            // Create all main tables in dependency order
            db.execSQL(CREATE_TABLE_CODECS)
            db.execSQL(CREATE_TABLE_GROUPS)
            db.execSQL(CREATE_TABLE_MESH_DEVICES)
            db.execSQL(CREATE_TABLE_DEVICES)
            db.execSQL(CREATE_TABLE_SCHEDULINGS)
            db.execSQL(CREATE_TABLE_SECTOR_SCHEDULINGS)
            db.execSQL(CREATE_TABLE_DEVICE_SCHEDULINGS)

            // Create unique index to prevent duplicate device identities within the same mesh
            db.execSQL(CREATE_DEVICES_UNIQUE_INDEX)

            // Create performance indexes for Groups table
            db.execSQL(CREATE_INDEX_GROUPS_CODEC_IDX)

            // Create performance indexes for Mesh_Devices table  
            db.execSQL(CREATE_INDEX_MESH_DEVICES_CODEC_IDX)
            db.execSQL(CREATE_INDEX_MESH_DEVICES_GROUP_IDX)

            // Create performance indexes for Devices table
            db.execSQL(CREATE_INDEX_DEVICES_MESH_IDX)
            db.execSQL(CREATE_INDEX_DEVICES_TYPE)
            db.execSQL(CREATE_INDEX_DEVICES_SECTOR)

            // Create performance indexes for Schedulings table
            db.execSQL(CREATE_INDEX_SCHEDULINGS_GROUP_IDX)

            // Create performance indexes for Sector_Schedulings table
            db.execSQL(CREATE_INDEX_SECTOR_SCHEDULINGS_SCHEDULING_IDX)
            db.execSQL(CREATE_INDEX_SECTOR_SCHEDULINGS_DEVICE_IDX)

            // Create performance indexes for Device_Schedulings table
            db.execSQL(CREATE_INDEX_DEVICE_SCHEDULINGS_SCHEDULING_IDX)
            db.execSQL(CREATE_INDEX_DEVICE_SCHEDULINGS_DEVICE_IDX)
        } catch (e: SQLException) {
            e.printStackTrace()
        }
    }

    /**
     * Handles database schema upgrades when the database version changes.
     * 
     * This method completely recreates the database by:
     * 1. Dropping all existing indexes in reverse dependency order
     * 2. Dropping all tables in reverse dependency order  
     * 3. Calling onCreate() to recreate everything with the new schema
     * 
     * Note: This approach loses all existing data. In production, consider
     * implementing incremental migration strategies to preserve user data.
     * 
     * @param db The SQLite database instance
     * @param oldVersion The previous database version number
     * @param newVersion The new database version number
     */
    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        try {
            // Drop all indexes first to avoid dependency issues
            db.execSQL("DROP INDEX IF EXISTS idx_groups_codec_idx")

            db.execSQL("DROP INDEX IF EXISTS idx_mesh_devices_codec_idx")
            db.execSQL("DROP INDEX IF EXISTS idx_mesh_devices_group_idx")

            db.execSQL("DROP INDEX IF EXISTS idx_devices_mesh_idx")
            db.execSQL("DROP INDEX IF EXISTS idx_devices_type")
            db.execSQL("DROP INDEX IF EXISTS idx_devices_sector")

            db.execSQL("DROP INDEX IF EXISTS idx_schedulings_group_idx")

            db.execSQL("DROP INDEX IF EXISTS idx_sector_schedulings_scheduling_idx")
            db.execSQL("DROP INDEX IF EXISTS idx_sector_schedulings_device_idx")

            db.execSQL("DROP INDEX IF EXISTS idx_device_schedulings_scheduling_idx")
            db.execSQL("DROP INDEX IF EXISTS idx_device_schedulings_device_idx")

            // Drop all tables in reverse dependency order (child tables first)
            db.execSQL("DROP TABLE IF EXISTS $TABLE_DEVICE_SCHEDULINGS")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_SECTOR_SCHEDULINGS")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_SCHEDULINGS")

            db.execSQL("DROP TABLE IF EXISTS $TABLE_DEVICES")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_MESH_DEVICES")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_GROUPS")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_CODECS")

            // Recreate everything with the new schema
            onCreate(db)
        } catch (e: SQLException) {
            e.printStackTrace()
        }
    }

    /**
     * Called when the database connection is opened.
     * 
     * Enables foreign key constraint enforcement to maintain referential integrity
     * between related tables. This ensures that operations like deletes cascade
     * properly and invalid foreign key references are rejected.
     * 
     * @param db The SQLite database instance
     */
    override fun onOpen(db: SQLiteDatabase) {
        super.onOpen(db)
        // Enable foreign key constraints for referential integrity
        db.execSQL("PRAGMA foreign_keys = ON;")
    }

    /**
     * Called before the database is opened for configuration.
     * 
     * Enables foreign key constraint enforcement at the database level.
     * This is called before onOpen() and ensures constraints are active
     * throughout the database lifecycle.
     * 
     * @param db The SQLite database instance
     */
    override fun onConfigure(db: SQLiteDatabase) {
        try {
            super.onConfigure(db)
            db.setForeignKeyConstraintsEnabled(true)
        } catch (e: SQLException) {
            e.printStackTrace()
        }
    }

    companion object {
        /** The name of the SQLite database file */
        private const val DATABASE_NAME = "irriganet.db"
        
        /** Current database schema version - increment when schema changes */
        private const val DATABASE_VERSION = 58

        // Table names - these define the main entities in the irrigation system
        
        /** Table storing codec (communication hub) information */
        private const val TABLE_CODECS = "Codecs"
        
        /** Table storing logical groups of devices under a codec */
        private const val TABLE_GROUPS = "_Groups"
        
        /** Table storing mesh network device information */
        private const val TABLE_MESH_DEVICES = "Mesh_Devices"
        
        /** Table storing individual device/component information */
        private const val TABLE_DEVICES = "Devices"
        
        /** Table storing irrigation scheduling configurations */
        private const val TABLE_SCHEDULINGS = "Schedulings"
        
        /** Table storing sector-specific scheduling details */
        private const val TABLE_SECTOR_SCHEDULINGS = "Sector_Schedulings"
        
        /** Table storing device-specific scheduling assignments */
        private const val TABLE_DEVICE_SCHEDULINGS = "Device_Schedulings"

        /**
         * SQL statement to create the Codecs table.
         * 
         * Codecs are the main communication hubs that control irrigation systems.
         * Each codec manages multiple groups of devices and maintains WiFi connectivity.
         * 
         * Fields:
         * - idx: Primary key auto-increment
         * - identity: Unique identifier for the codec device
         * - name: Human-readable name for the codec
         * - wifi_ssid: WiFi network name for codec connectivity
         * - wifi_passwd: WiFi password for network access
         * - last_*_update: Timestamps for tracking various update synchronizations
         * - enabled: Boolean flag to enable/disable the codec (1=enabled, 0=disabled)
         */
        private const val CREATE_TABLE_CODECS = """
            CREATE TABLE $TABLE_CODECS (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                identity TEXT NOT NULL,
                name TEXT NOT NULL,
                wifi_ssid TEXT,
                wifi_passwd TEXT,
                last_devices_update INTEGER,
                last_scheduling_update INTEGER,
                last_device_scheduling_update INTEGER,
                last_automation_update INTEGER,
                last_config_update INTEGER,
                enabled INTEGER DEFAULT 1
            )
        """

        /**
         * SQL statement to create the Groups table.
         * 
         * Groups are logical collections of mesh devices under a single codec.
         * They allow organizing devices by location, function, or management needs.
         * 
         * Fields:
         * - idx: Primary key auto-increment
         * - name: Human-readable name for the group
         * - codec_idx: Foreign key reference to the parent codec
         */
        private const val CREATE_TABLE_GROUPS = """
            CREATE TABLE $TABLE_GROUPS (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                codec_idx INTEGER,
                FOREIGN KEY (codec_idx) REFERENCES $TABLE_CODECS(idx) ON DELETE CASCADE
            )
        """

        /** Index for efficient lookups of groups by codec */
        private const val CREATE_INDEX_GROUPS_CODEC_IDX =
            "CREATE INDEX IF NOT EXISTS idx_groups_codec_idx ON $TABLE_GROUPS(codec_idx)"

        /**
         * SQL statement to create the Mesh_Devices table.
         * 
         * Mesh devices are physical network nodes that can contain multiple individual devices.
         * They represent the hardware units deployed in the field.
         * 
         * Fields:
         * - idx: Primary key auto-increment
         * - identity: Unique identifier for the mesh device
         * - name: Human-readable name for the mesh device
         * - type: Device type classification (0=standard, other values for specialized types)
         * - mode: Operating mode of the device
         * - equipament: Equipment type identifier
         * - check_input: Flag for input monitoring capability
         * - devices_bitmask: Bitmask representing connected sub-devices
         * - level_pump_*: Configuration for level pump functionality
         * - codec_idx: Foreign key to parent codec
         * - group_idx: Foreign key to parent group
         */
        private const val CREATE_TABLE_MESH_DEVICES = """
            CREATE TABLE $TABLE_MESH_DEVICES (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                identity TEXT NOT NULL,
                name TEXT NOT NULL,
                type INTEGER DEFAULT 0,
                mode INTEGER DEFAULT 0,
                equipament INTEGER DEFAULT 0,
                check_input INTEGER DEFAULT 0,
                devices_bitmask INTEGER DEFAULT 0,
                level_pump_idx INTEGER DEFAULT 0,
                level_pump_enable INTEGER DEFAULT 0,
                level_pump_working_time INTEGER DEFAULT 0,
                codec_idx INTEGER,
                group_idx INTEGER,
                FOREIGN KEY (codec_idx) REFERENCES $TABLE_CODECS(idx) ON DELETE CASCADE,
                FOREIGN KEY (group_idx) REFERENCES $TABLE_GROUPS(idx) ON DELETE CASCADE
            )
        """

        /** Index for efficient lookups of mesh devices by codec */
        private const val CREATE_INDEX_MESH_DEVICES_CODEC_IDX =
            "CREATE INDEX IF NOT EXISTS idx_mesh_devices_codec_idx ON $TABLE_MESH_DEVICES(codec_idx)"
            
        /** Index for efficient lookups of mesh devices by group */
        private const val CREATE_INDEX_MESH_DEVICES_GROUP_IDX =
            "CREATE INDEX IF NOT EXISTS idx_mesh_devices_group_idx ON $TABLE_MESH_DEVICES(group_idx)"

        /**
         * SQL statement to create the Devices table.
         * 
         * Devices represent individual controllable components like valves, pumps, sensors.
         * Multiple devices can exist within a single mesh device.
         * 
         * Fields:
         * - idx: Primary key auto-increment
         * - ord_idx: Order index for sequencing operations
         * - mesh_idx: Foreign key to parent mesh device
         * - identity: Unique identifier within the mesh device
         * - type: Device type (0=sector/valve, 1=pump, 2=fertilizer, etc.)
         * - out1, out2: Output channel configurations
         * - input: Input channel configuration
         * - mode: Operating mode
         * - sector: Sector number for irrigation zones
         * - eqpt_ver: Equipment version identifier
         */
        private const val CREATE_TABLE_DEVICES = """
            CREATE TABLE $TABLE_DEVICES (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                ord_idx INTEGER NOT NULL,
                mesh_idx INTEGER NOT NULL,
                identity TEXT NOT NULL,
                type INTEGER NOT NULL,
                out1 INTEGER,
                out2 INTEGER,
                input INTEGER,
                mode INTEGER,
                sector INTEGER,
                power INTEGER,
                eqpt_ver INTEGER,
                FOREIGN KEY (mesh_idx) REFERENCES $TABLE_MESH_DEVICES(idx) ON DELETE CASCADE
            );
        """

        /** Index for efficient lookups of devices by mesh device */
        private const val CREATE_INDEX_DEVICES_MESH_IDX = """
            CREATE INDEX IF NOT EXISTS idx_devices_mesh_idx ON $TABLE_DEVICES(mesh_idx)
        """
        
        /** Index for efficient lookups of devices by sector number */
        private const val CREATE_INDEX_DEVICES_SECTOR = """
            CREATE INDEX IF NOT EXISTS idx_devices_sector ON $TABLE_DEVICES(sector)
        """
        
        /** Index for efficient lookups of devices by type */
        private const val CREATE_INDEX_DEVICES_TYPE = """
            CREATE INDEX IF NOT EXISTS idx_devices_type ON $TABLE_DEVICES(type)
        """
        
        /** Unique index to prevent duplicate device identities within the same mesh */
        private const val CREATE_DEVICES_UNIQUE_INDEX = """
            CREATE UNIQUE INDEX IF NOT EXISTS u_mesh_identity ON TABLE_DEVICES(mesh_idx, identity);
        """

        /**
         * SQL statement to create the Schedulings table.
         * 
         * Schedulings define when and how irrigation should occur.
         * Each scheduling can control multiple devices and includes timing,
         * fertilization, and backwash configurations.
         * 
         * Fields:
         * - idx: Primary key auto-increment
         * - ord_idx: Order index for execution sequence
         * - group_idx: Foreign key to parent group
         * - name: Human-readable name for the schedule
         * - hour, min: Start time (24-hour format)
         * - start_time: Start time in minutes from midnight
         * - end_time: Optional end time constraint
         * - days_of_week: Bitmask for days (bit 0=Sunday, bit 1=Monday, etc.)
         * - number_of_steps: Number of irrigation steps/phases
         * - allow_ferti: Whether fertilizer injection is allowed
         * - allow_backwash: Whether backwash operation is allowed
         * - waterpump_*: Water pump device configuration
         * - ferti_*: Fertilizer device configuration  
         * - backwash_*: Backwash device configuration
         * - enabled: Whether this schedule is active
         */
        private const val CREATE_TABLE_SCHEDULINGS = """
            CREATE TABLE $TABLE_SCHEDULINGS (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                ord_idx INTEGER NOT NULL,
                group_idx INTEGER NOT NULL,
                name TEXT NOT NULL,
                hour INTEGER NOT NULL,
                min INTEGER NOT NULL,
                start_time INTEGER NOT NULL,
                end_time INTEGER DEFAULT NULL,
                days_of_week INTEGER NOT NULL,
                number_of_steps INTEGER NOT NULL,
                allow_ferti INTEGER NOT NULL,
                allow_backwash INTEGER NOT NULL,
                waterpump_idx INTEGER DEFAULT NULL,
                waterpump_ord_idx INTEGER DEFAULT NULL,
                waterpump_working_time INTEGER DEFAULT NULL,
                ferti_idx INTEGER DEFAULT NULL,
                ferti_ord_idx INTEGER DEFAULT NULL,
                backwash_idx INTEGER DEFAULT NULL,
                backwash_ord_idx INTEGER DEFAULT NULL,
                once INTEGER DEFAULT 0,                
                enabled INTEGER DEFAULT 1,
                FOREIGN KEY (group_idx) REFERENCES $TABLE_GROUPS(idx) ON DELETE CASCADE
            )
        """

        /** Index for efficient lookups of schedulings by group */
        private const val CREATE_INDEX_SCHEDULINGS_GROUP_IDX =
            "CREATE INDEX IF NOT EXISTS idx_schedulings_group_idx ON $TABLE_SCHEDULINGS(group_idx)"

        /**
         * SQL statement to create the Sector_Schedulings table.
         * 
         * This table defines sector-specific scheduling details including
         * timing, fertilization, and operational parameters for each sector.
         * 
         * Fields:
         * - idx: Primary key auto-increment
         * - scheduling_idx: Foreign key to parent scheduling
         * - device_idx: Foreign key to the controlled device
         * - n_order: Execution order within the schedule
         * - enabled: Whether this sector scheduling is active
         * - type: Type of operation
         * - ferti: Fertilizer injection flag
         * - ferti_delay: Delay before fertilizer injection (seconds)
         * - working_time: Duration of operation (seconds)
         */
        private const val CREATE_TABLE_SECTOR_SCHEDULINGS = """
            CREATE TABLE $TABLE_SECTOR_SCHEDULINGS (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                scheduling_idx INTEGER NOT NULL,
                device_idx INTEGER NOT NULL,
                n_order INTEGER NOT NULL,
                enabled INTEGER NOT NULL,
                type INTEGER NOT NULL,  
                ferti INTEGER NOT NULL,
                ferti_delay INTEGER NOT NULL,
                working_time INTEGER NOT NULL,
                FOREIGN KEY (scheduling_idx) REFERENCES $TABLE_SCHEDULINGS(idx) ON DELETE CASCADE
            )
        """

        /** Index for efficient lookups of sector schedulings by scheduling */
        private const val CREATE_INDEX_SECTOR_SCHEDULINGS_SCHEDULING_IDX =
            "CREATE INDEX IF NOT EXISTS idx_sector_schedulings_scheduling_idx ON $TABLE_SECTOR_SCHEDULINGS(scheduling_idx)"
            
        /** Index for efficient lookups of sector schedulings by device */
        private const val CREATE_INDEX_SECTOR_SCHEDULINGS_DEVICE_IDX =
            "CREATE INDEX IF NOT EXISTS idx_sector_schedulings_device_idx ON $TABLE_SECTOR_SCHEDULINGS(device_idx)"

        /**
         * SQL statement to create the Device_Schedulings table.
         * 
         * This table stores detailed scheduling assignments for individual devices,
         * including timing, status, and operational parameters.
         * 
         * Fields:
         * - idx: Primary key auto-increment
         * - ord_idx: Order index for execution sequence
         * - scheduling_idx: Foreign key to parent scheduling
         * - device_idx: Foreign key to the controlled device
         * - n_order: Execution order within the schedule
         * - status: Current operational status
         * - type: Type of device operation
         * - time: Scheduled execution time
         * - sector_working_time: Duration for sector operation (seconds)
         * - ferti_working_time: Duration for fertilizer operation (seconds)
         * - ferti_delay: Delay before fertilizer injection (seconds)
         */
        private const val CREATE_TABLE_DEVICE_SCHEDULINGS = """
            CREATE TABLE $TABLE_DEVICE_SCHEDULINGS (
                idx INTEGER PRIMARY KEY AUTOINCREMENT,
                ord_idx INTEGER NOT NULL,
                scheduling_idx INTEGER NOT NULL,
                device_idx INTEGER NOT NULL,
                n_order INTEGER NOT NULL,
                status INTEGER NOT NULL,
                type INTEGER NOT NULL,  
                time INTEGER NOT NULL,
                sector_working_time INTEGER NOT NULL,
                ferti_working_time INTEGER DEFAULT NULL,
                ferti_delay INTEGER NOT NULL,
                FOREIGN KEY (scheduling_idx) REFERENCES $TABLE_SCHEDULINGS(idx) ON DELETE CASCADE
            )
        """

        /** Index for efficient lookups of device schedulings by scheduling */
        private const val CREATE_INDEX_DEVICE_SCHEDULINGS_SCHEDULING_IDX =
            "CREATE INDEX IF NOT EXISTS idx_device_schedulings_scheduling_idx ON $TABLE_DEVICE_SCHEDULINGS(scheduling_idx)"
            
        /** Index for efficient lookups of device schedulings by device */
        private const val CREATE_INDEX_DEVICE_SCHEDULINGS_DEVICE_IDX =
            "CREATE INDEX IF NOT EXISTS idx_device_schedulings_device_idx ON $TABLE_DEVICE_SCHEDULINGS(device_idx)"
    }

    // ==============================================
    // CODEC MANAGEMENT METHODS
    // ==============================================
    
    /**
     * Inserts a new codec into the database.
     * 
     * Creates a new codec entry with the provided network configuration.
     * The codec will be enabled by default and all timestamp fields will be null initially.
     * 
     * @param identity Unique identifier for the codec device
     * @param name Human-readable name for the codec
     * @param wifiSsid WiFi network name for codec connectivity
     * @param wifiPasswd WiFi password for network access
     * @return The row ID of the newly inserted codec, or -1 if an error occurred
     */
    fun insertCodec(identity: String, name: String, wifiSsid: String, wifiPasswd: String): Long {
        return try {
            val db = this.writableDatabase
            // Create ContentValues object to hold the field values
            val values = ContentValues().apply {
                put("identity", identity)
                put("name", name)
                put("wifi_ssid", wifiSsid)
                put("wifi_passwd", wifiPasswd)
                // Note: enabled defaults to 1, timestamps default to null
            }
            // Insert the new record and return the generated ID
            db.insert(TABLE_CODECS, null, values)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1 // Return -1 to indicate failure
        }
    }

    /**
     * Updates an existing codec's information.
     * 
     * Modifies the codec record identified by idx with new configuration values.
     * All provided fields will be updated, including the enabled status.
     * 
     * @param idx Database ID of the codec to update
     * @param identity New unique identifier for the codec
     * @param name New human-readable name
     * @param wifiSsid New WiFi network name
     * @param wifiPasswd New WiFi password
     * @param enabled New enabled status (1=enabled, 0=disabled)
     * @return Number of rows affected (should be 1 if successful, 0 if codec not found)
     */
    fun updateCodec(idx: Int, identity: String, name: String, wifiSsid: String, wifiPasswd: String, enabled: Int): Int {
        return try {
            val db = this.writableDatabase

            // Prepare the updated values
            val values = ContentValues().apply {
                put("identity", identity)
                put("name", name)
                put("wifi_ssid", wifiSsid)
                put("wifi_passwd", wifiPasswd)
                put("enabled", enabled)
            }
            
            // Define the WHERE clause to target the specific codec
            val whereClause = "idx = ?"
            val whereArgs = arrayOf(idx.toString())

            // Execute the update and return the number of affected rows
            db.update(TABLE_CODECS, values, whereClause, whereArgs)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1
        }
    }

    /**
     * Updates specific fields of an existing codec.
     * 
     * Provides a flexible way to update only certain fields of a codec record
     * without needing to provide all field values. Supports various data types
     * and automatically converts boolean values to integers for SQLite storage.
     * 
     * @param idx Database ID of the codec to update
     * @param fieldsToUpdate Map containing field names as keys and new values
     * @return Number of rows affected (should be 1 if successful, 0 if codec not found, -1 on error)
     */
    fun updateCodecFields(idx: Int, fieldsToUpdate: Map<String, Any>): Int {
        return try {
            val db = this.writableDatabase

            // Build ContentValues from the provided field map
            val values = ContentValues().apply {
                for ((key, value) in fieldsToUpdate) {
                    // Handle different data types appropriately
                    when (value) {
                        is String -> put(key, value)
                        is Int -> put(key, value)
                        is Long -> put(key, value)
                        is Float -> put(key, value)
                        is Double -> put(key, value)
                        is Boolean -> put(key, if (value) 1 else 0) // Convert boolean to integer
                        else -> throw IllegalArgumentException("Unsupported value type for column $key")
                    }
                }
            }

            // Define WHERE clause to target specific codec
            val whereClause = "idx = ?"
            val whereArgs = arrayOf(idx.toString())

            // Execute update and return affected row count
            db.update(TABLE_CODECS, values, whereClause, whereArgs)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1 // Return -1 to indicate error
        }
    }


    /**
     * Deletes a codec and all its associated data.
     * 
     * This operation will CASCADE DELETE all related records:
     * - Groups belonging to this codec
     * - Mesh devices in those groups  
     * - Individual devices in those mesh devices
     * - Schedulings for those groups
     * - All scheduling assignments
     * 
     * Use with caution as this removes an entire codec hierarchy from the system.
     * 
     * @param codecIdx Database ID of the codec to delete
     * @return Number of codecs deleted (should be 1 if successful, 0 if not found)
     */
    fun deleteCodec(codecIdx: Int): Int {
        return try {
            val db = this.writableDatabase
            // Foreign key constraints with CASCADE DELETE will automatically
            // remove all related records when the parent codec is deleted
            //db.delete(TABLE_GROUPS, "codec_idx = ?", arrayOf(codecIdx.toString()))
            //db.delete(TABLE_MESH_DEVICES, "codec_idx = ?", arrayOf(codecIdx.toString()))
            
            // Delete the codec - cascading deletes will handle related records
            db.delete(TABLE_CODECS, "idx = ?", arrayOf(codecIdx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0 // Return 0 to indicate failure
        }
    }

    /**
     * Retrieves all codecs as a list of CodecItem objects.
     * 
     * Returns basic codec information (idx, identity, name) for all codecs
     * in the database, regardless of their enabled status. Used primarily
     * for populating UI lists and dropdowns.
     * 
     * @return List of CodecItem objects, or empty list if no codecs found or on error
     */
    fun getAllCodecs(): List<CodecItem> {
        val codecList = mutableListOf<CodecItem>()
        return try {
            val db = this.readableDatabase
            // Query for basic codec information
            val cursor: Cursor = db.rawQuery("SELECT idx, identity, name FROM $TABLE_CODECS", null)
            
            if (cursor.moveToFirst()) {
                do {
                    // Extract data from cursor
                    val idx = cursor.getInt(0)
                    val identity = cursor.getString(1)
                    val name = cursor.getString(2)
                    
                    // Create CodecItem and add to list
                    codecList.add(CodecItem(idx, identity, name))
                } while (cursor.moveToNext())
            }
            cursor.close()
            codecList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList() // Return empty list on error
        }
    }

    /**
     * Retrieves all enabled codecs with complete information as a list of maps.
     * 
     * Returns detailed codec information including WiFi configuration and
     * synchronization timestamps. Only includes enabled codecs (enabled = 1).
     * Data is returned as a list of maps for flexible data handling.
     * 
     * @return List of maps containing codec data, or empty list if none found or on error
     */
    fun getAllCodecsOnMap(): List<Map<String, Any>> {
        val codecList = mutableListOf<Map<String, Any>>() // Create a mutable list of maps
        return try {
            val db = this.readableDatabase
            // Query for complete codec information, only enabled codecs
            val cursor: Cursor = db.rawQuery("SELECT idx, identity, name, wifi_ssid, wifi_passwd, last_devices_update, last_scheduling_update, " +
                    "last_device_scheduling_update, last_automation_update, last_config_update FROM $TABLE_CODECS WHERE enabled = 1", null)
            
            if (cursor.moveToFirst()) {
                do {
                    // Extract all fields from the cursor
                    val idx = cursor.getInt(0)
                    val identity = cursor.getString(1)
                    val name = cursor.getString(2)
                    val wifiSsid = cursor.getString(3)
                    val wifiPasswd = cursor.getString(4)
                    val lastDevicesUpdate = cursor.getLong(5)
                    val lastSchedulingUpdate = cursor.getLong(6)
                    val lastDeviceSchedulingUpdate = cursor.getLong(7)
                    val lastAutomationUpdate = cursor.getLong(8)
                    val lastConfigUpdate = cursor.getLong(9)

                    // Create a map for the current codec with all its data
                    val rowMap = mapOf(
                        "idx" to idx,
                        "identity" to identity,
                        "name" to name,
                        "wifi_ssid" to wifiSsid,
                        "wifi_passwd" to wifiPasswd,
                        "last_devices_update" to lastDevicesUpdate,
                        "last_scheduling_update" to lastSchedulingUpdate,
                        "last_device_scheduling_update" to lastDeviceSchedulingUpdate,
                        "last_automation_update" to lastAutomationUpdate,
                        "last_config_update" to lastConfigUpdate
                    )

                    // Add the codec map to the list
                    codecList.add(rowMap)
                } while (cursor.moveToNext())
            }
            cursor.close()
            codecList // Return the list of codec maps
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList() // Return an empty list in case of an error
        }
    }

    /**
     * Retrieves a specific codec by its database ID.
     * 
     * Returns complete codec information for the specified codec ID.
     * Used when detailed codec configuration is needed for a specific codec.
     * 
     * @param idx Database ID of the codec to retrieve
     * @return Map containing codec data, or null if codec not found
     */
    fun getCodecByIdx(idx: Int): Map<String, Any>? {
        val db = readableDatabase
        // Query for all codec fields for the specified ID
        val query = "SELECT * FROM $TABLE_CODECS WHERE idx = ?"
        val cursor = db.rawQuery(query, arrayOf(idx.toString()))

        return if (cursor.moveToFirst()) {
            // Create map with codec data
            val result = mapOf(
                "identity" to cursor.getString(cursor.getColumnIndexOrThrow("identity")),
                "name" to cursor.getString(cursor.getColumnIndexOrThrow("name")),
                "wifi_ssid" to cursor.getString(cursor.getColumnIndexOrThrow("wifi_ssid")),
                "wifi_passwd" to cursor.getString(cursor.getColumnIndexOrThrow("wifi_passwd")),
                "enabled" to cursor.getInt(cursor.getColumnIndexOrThrow("enabled"))
            )
            cursor.close()
            db.close()
            result
        } else {
            // Codec not found
            cursor.close()
            db.close()
            null
        }
    }

    /**
     * Gets the total count of codecs in the database.
     * 
     * Returns the number of codec records regardless of their enabled status.
     * Used for statistics and capacity planning.
     * 
     * @return Total number of codecs, or 0 on error
     */
    fun getCodecsCount(): Int {
        return try {
            val db = this.readableDatabase
            // Count all codec records
            val cursor = db.rawQuery("SELECT COUNT(*) FROM $TABLE_CODECS", null)
            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0 // Return 0 on error
        }
    }

    // ==============================================
    // GROUP MANAGEMENT METHODS
    // ==============================================

    /**
     * Inserts a new group into the database.
     * 
     * Creates a new group associated with the specified codec.
     * Groups are logical containers for mesh devices and provide
     * an organizational layer between codecs and devices.
     * 
     * @param name Human-readable name for the group
     * @param codecIdx Database ID of the parent codec (can be null)
     * @return The row ID of the newly inserted group, or -1 if an error occurred
     */
    fun insertGroup(name: String, codecIdx: Int?): Long {
        return try {
            val db = this.writableDatabase
            // Create ContentValues with group data
            val values = ContentValues().apply {
                put("name", name)
                put("codec_idx", codecIdx) // Foreign key to parent codec
            }
            // Insert new group and return generated ID
            db.insert(TABLE_GROUPS, null, values)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1 // Return -1 to indicate failure
        }
    }

    /**
     * Retrieves groups based on codec filter criteria.
     * 
     * If codecIdx is null, returns all groups belonging to enabled codecs.
     * If codecIdx is specified, returns only groups belonging to that codec.
     * This provides flexibility for both global group listings and codec-specific views.
     * 
     * @param codecIdx Database ID of codec to filter by, or null for all enabled codecs
     * @return List of GroupItem objects, or empty list if none found or on error
     */
    fun getAllGroups(codecIdx: Int?): List<GroupItem> {
        val groupList = mutableListOf<GroupItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            // Choose query based on codecIdx parameter
            cursor = if (codecIdx == null) {
                // Get groups from all enabled codecs
                db.rawQuery("SELECT g.idx, g.name FROM $TABLE_GROUPS AS g JOIN $TABLE_CODECS AS c ON g.codec_idx = c.idx WHERE c.enabled = 1", null)
            } else {
                // Get groups from specific codec
                db.rawQuery("SELECT idx, name FROM $TABLE_GROUPS WHERE codec_idx = ?", arrayOf(codecIdx.toString()))
            }

            // Process query results
            while (cursor.moveToNext()) {
                val idx = cursor.getInt(0)
                val name = cursor.getString(1)
                // Create GroupItem and add to list
                groupList.add(GroupItem(idx, name))
            }

            groupList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList() // Return empty list on error
        } finally {
            cursor?.close() // Ensure cursor is closed
        }
    }

    /**
     * Deletes a group and all its associated mesh devices.
     * 
     * This operation will remove:
     * 1. All mesh devices belonging to this group
     * 2. The group record itself  
     * 
     * Related devices, schedulings, and device schedulings will be automatically
     * deleted due to foreign key cascade constraints.
     * 
     * @param groupIdx Database ID of the group to delete
     * @return Number of groups deleted (should be 1 if successful, 0 if not found)
     */
    fun deleteGroup(groupIdx: Int): Int {
        return try {
            val db = this.writableDatabase
            // First delete all mesh devices belonging to this group
            // (this will cascade delete all related devices and schedulings)
            db.delete(TABLE_MESH_DEVICES, "group_idx = ?", arrayOf(groupIdx.toString()))
            
            // Then delete the group itself
            db.delete(TABLE_GROUPS, "idx = ?", arrayOf(groupIdx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0 // Return 0 to indicate failure
        }
    }

    /**
     * Generates the next available group index ID.
     * 
     * Finds the highest existing group idx value and returns the next sequential number.
     * This is used when creating new groups to ensure unique idx values.
     * 
     * @return Next available group index (starts at 1), or 1 on error
     */
    fun getNextGroupIdx(): Int {
        val db = this.readableDatabase
        var cursor: Cursor? = null
        var nextIdx = 1

        return try {
            // Find the maximum existing idx value
            cursor = db.rawQuery("SELECT MAX(idx) FROM $TABLE_GROUPS", null)

            if (cursor.moveToFirst() && !cursor.isNull(0)) {
                // Increment the maximum found idx
                nextIdx = cursor.getInt(0) + 1
            }
            // If no records exist, nextIdx remains 1

            nextIdx
        } catch (e: SQLException) {
            e.printStackTrace()
            1 // Return 1 as default on error
        } finally {
            cursor?.close() // Ensure cursor is closed
        }
    }

    // ==============================================
    // SCHEDULING MANAGEMENT METHODS
    // ==============================================

    /**
     * Inserts a new irrigation scheduling into the database.
     * 
     * Creates a new scheduling record with basic timing and permission settings.
     * The scheduling will be enabled by default and assigned the next available ord_idx.
     * Device assignments (waterpump, fertilizer, backwash) can be added later via updates.
     * 
     * @param groupIdx Database ID of the parent group
     * @param name Human-readable name for the scheduling
     * @param hour Start hour (0-23, 24-hour format)
     * @param min Start minute (0-59)
     * @param startTime Start time in minutes from midnight (hour * 60 + min)
     * @param endTime End time constraint in minutes from midnight, or null for no limit
     * @param daysOfWeek Bitmask for days of week (bit 0=Sunday, bit 1=Monday, etc.)
     * @param allowFerti Whether fertilizer injection is permitted
     * @param allowBackwash Whether backwash operation is permitted
     * @return The row ID of the newly inserted scheduling, or -1 if an error occurred
     */
    fun insertScheduling(
        groupIdx: Int, name: String, hour: Int, min: Int, startTime: Int, endTime: Int, daysOfWeek: Int, allowFerti: Boolean, allowBackwash: Boolean
    ): Long {
        return try {
            val db = this.writableDatabase
            val values = ContentValues().apply {
                put("ord_idx", 0)
                put("group_idx", groupIdx)
                put("name", name)
                put("hour", hour)
                put("min", min)
                put("start_time", startTime)
                put("end_time", endTime)
                put("days_of_week", daysOfWeek)
                put("number_of_steps", 0)
                put("allow_ferti", if (allowFerti) 1 else 0)
                put("allow_backwash", if (allowBackwash) 1 else 0)
            }
            db.insert(TABLE_SCHEDULINGS, null, values)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1
        }
    }

    /**
     * Updates an existing scheduling with complete configuration.
     * 
     * Modifies all fields of an existing scheduling record including timing,
     * device assignments, and operational parameters. This method allows for
     * comprehensive reconfiguration of scheduling behavior.
     * 
     * @param idx Database ID of the scheduling to update
     * @param groupIdx Database ID of the group this scheduling belongs to
     * @param name Human-readable name for the scheduling
     * @param hour Hour component of the scheduling time (0-23)
     * @param min Minute component of the scheduling time (0-59)
     * @param startTime Start time in minutes from midnight
     * @param endTime End time in minutes from midnight (nullable)
     * @param daysOfWeek Bitmask representing active days (bit 0=Sunday, bit 1=Monday, etc.)
     * @param numberOfSteps Number of execution steps in this scheduling
     * @param allowFerti Whether fertilizer application is allowed
     * @param allowBackwash Whether backwash operations are allowed
     * @param waterpumpIdx Database ID of water pump device (nullable)
     * @param waterpumpWorkingTime Working time for water pump operations (nullable)
     * @param fertiIdx Database ID of fertilizer device (nullable)
     * @param backwashIdx Database ID of backwash device (nullable)
     * @param enabled Whether the scheduling is currently enabled (nullable)
     * @return Number of rows updated (should be 1 if successful), or 0 on error
     */
    fun updateScheduling(idx: Int, groupIdx: Int, name: String, hour: Int, min: Int, startTime: Int, endTime: Int?, daysOfWeek: Int, numberOfSteps: Int, allowFerti: Boolean, allowBackwash: Boolean, waterpumpIdx: Int?, waterpumpWorkingTime: Int?, fertiIdx: Int?, backwashIdx: Int?, enabled: Boolean?): Int {
        val db = this.writableDatabase
        return try {
            val values = ContentValues().apply {
                put("group_idx", groupIdx)
                put("name", name)
                put("hour", hour)
                put("min", min)
                put("start_time", startTime)
                put("end_time", endTime)
                put("days_of_week", daysOfWeek)
                put("number_of_steps", numberOfSteps)
                put("allow_ferti", if (allowFerti) 1 else 0)
                put("allow_backwash", if (allowBackwash) 1 else 0)
                put("waterpump_idx", waterpumpIdx)
                put("waterpump_working_time", waterpumpWorkingTime)
                put("ferti_idx", fertiIdx)
                put("backwash_idx", backwashIdx)
                put("enabled", enabled)
            }
            db.update(TABLE_SCHEDULINGS, values, "idx = ?", arrayOf(idx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    /**
     * Updates the enabled status of a scheduling.
     * 
     * Changes whether a scheduling is active or inactive without modifying
     * other scheduling parameters. This provides a quick way to enable or
     * disable irrigation schedules.
     * 
     * @param idx Database ID of the scheduling to update
     * @param enabled 1 to enable the scheduling, 0 to disable it
     * @return Number of rows updated (should be 1 if successful), or 0 on error
     */
    fun updateSchedulingEnabled(idx: Int, enabled: Int): Int {
        val db = this.writableDatabase
        return try {
            val values = ContentValues().apply {
                put("enabled", enabled)
            }
            db.update(TABLE_SCHEDULINGS, values, "idx = ?", arrayOf(idx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    /**
     * Retrieves a scheduling by its database ID.
     * 
     * Returns complete information about a specific scheduling including all
     * configuration parameters and device assignments. Data is returned as a
     * map for flexible access to scheduling properties.
     * 
     * @param idx Database ID of the scheduling to retrieve
     * @return Map containing scheduling data, or null if not found or on error
     */
    fun getScheduling(idx: Int): Map<String, Any>? {
        val db = readableDatabase
        val query = "SELECT * FROM $TABLE_SCHEDULINGS WHERE idx = ?"
        val cursor = db.rawQuery(query, arrayOf(idx.toString()))

        return if (cursor.moveToFirst()) {
            val result = mapOf(
                "idx" to cursor.getInt(cursor.getColumnIndexOrThrow("idx")),
                "group_idx" to cursor.getInt(cursor.getColumnIndexOrThrow("group_idx")),
                "name" to cursor.getString(cursor.getColumnIndexOrThrow("name")),
                "hour" to cursor.getInt(cursor.getColumnIndexOrThrow("hour")),
                "min" to cursor.getInt(cursor.getColumnIndexOrThrow("min")),
                "days_of_week" to cursor.getInt(cursor.getColumnIndexOrThrow("days_of_week")),
                "allow_ferti" to cursor.getInt(cursor.getColumnIndexOrThrow("allow_ferti")),
                "allow_backwash" to cursor.getInt(cursor.getColumnIndexOrThrow("allow_backwash")),
                "enabled" to (cursor.getInt(cursor.getColumnIndexOrThrow("enabled")) == 1)
            )
            cursor.close()
            db.close()
            result
        } else {
            cursor.close()
            db.close()
            null
        }
    }

    /**
     * Retrieves all schedulings, optionally filtered by group.
     * 
     * Returns scheduling data as SchedulingItem objects for structured access.
     * If groupIdx is provided, only schedulings for that group are returned.
     * If groupIdx is null, all schedulings in the system are returned.
     * 
     * @param groupIdx Database ID of the group to filter by, or null for all schedulings
     * @return List of SchedulingItem objects containing scheduling data, or empty list on error
     */
    fun getAllSchedulings(groupIdx: Int?): List<SchedulingItem> {
        val schedulingList = mutableListOf<SchedulingItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = if (groupIdx == null) {
                db.rawQuery("SELECT idx, group_idx, name, enabled FROM $TABLE_SCHEDULINGS", null)
            } else {
                db.rawQuery("SELECT idx, group_idx, name, enabled FROM $TABLE_SCHEDULINGS WHERE group_idx = ?", arrayOf(groupIdx.toString()))
            }

            while (cursor.moveToNext()) {
                val idx = cursor.getInt(0)
                val groupIdxValue = cursor.getInt(1)
                val name = cursor.getString(2)
                val enabled = cursor.getInt(3)==1
                schedulingList.add(SchedulingItem(idx, groupIdxValue, name, enabled))
            }

            schedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
        }
    }

    /**
     * Retrieves all schedulings as maps for flexible data handling.
     * 
     * Returns all scheduling data as maps instead of structured objects,
     * providing more flexibility for dynamic property access and JSON
     * serialization. Includes comprehensive scheduling information.
     * 
     * @return List of maps containing scheduling data, or empty list on error
     */
    fun getAllSchedulingsOnMap(): List<Map<String, Any>> {
        val db = this.readableDatabase
        val schedulingList = mutableListOf<Map<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT s.idx, s.ord_idx, s.name, s.start_time, s.end_time, 
                   s.number_of_steps, s.allow_ferti, s.allow_backwash, 
                   c.identity AS codec_identity
            FROM $TABLE_SCHEDULINGS s
            INNER JOIN $TABLE_GROUPS g ON s.group_idx = g.idx
            INNER JOIN $TABLE_CODECS c ON g.codec_idx = c.idx
            ORDER BY s.ord_idx ASC
        """.trimIndent()

            cursor = db.rawQuery(query, null)

            if (cursor.moveToFirst()) {
                do {
                    val item = mapOf(
                        "idx" to cursor.getInt(cursor.getColumnIndexOrThrow("idx")),
                        "ord_idx" to cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx")),
                        "name" to cursor.getString(cursor.getColumnIndexOrThrow("name")),
                        "start_time" to cursor.getInt(cursor.getColumnIndexOrThrow("start_time")),
                        "end_time" to cursor.getInt(cursor.getColumnIndexOrThrow("end_time")),
                        "number_of_steps" to cursor.getInt(cursor.getColumnIndexOrThrow("number_of_steps")),
                        "allow_ferti" to cursor.getInt(cursor.getColumnIndexOrThrow("allow_ferti")),
                        "allow_backwash" to cursor.getInt(cursor.getColumnIndexOrThrow("allow_backwash")),
                        "codec_identity" to cursor.getString(cursor.getColumnIndexOrThrow("codec_identity"))
                    )
                    schedulingList.add(item)
                } while (cursor.moveToNext())
            }

            schedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves all schedulings for a specific group as maps.
     * 
     * Returns scheduling data for a specific group as maps for flexible
     * data handling. This method is useful when you need schedulings for
     * a particular irrigation group in a format that's easy to serialize
     * or manipulate dynamically.
     * 
     * @param groupIdx Database ID of the group to get schedulings for
     * @return List of maps containing scheduling data for the specified group, or empty list on error
     */
    fun getAllSchedulingsByGroupOnMap(groupIdx: Int): List<Map<String, Any>> {
        val db = this.readableDatabase
        val schedulingList = mutableListOf<Map<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT idx, ord_idx, name, start_time, end_time, number_of_steps, allow_ferti, allow_backwash
            FROM $TABLE_SCHEDULINGS
            ORDER BY ord_idx ASC
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(groupIdx.toString()))

            if (cursor.moveToFirst()) {
                do {
                    val item = mapOf(
                        "idx" to cursor.getInt(cursor.getColumnIndexOrThrow("idx")),
                        "ord_idx" to cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx")),
                        "name" to cursor.getString(cursor.getColumnIndexOrThrow("name")),
                        "start_time" to cursor.getInt(cursor.getColumnIndexOrThrow("start_time")),
                        "end_time" to cursor.getInt(cursor.getColumnIndexOrThrow("end_time")),
                        "number_of_steps" to cursor.getInt(cursor.getColumnIndexOrThrow("number_of_steps")),
                        "allow_ferti" to cursor.getInt(cursor.getColumnIndexOrThrow("allow_ferti")),
                        "allow_backwash" to cursor.getInt(cursor.getColumnIndexOrThrow("allow_backwash"))
                    )
                    schedulingList.add(item)
                } while (cursor.moveToNext())
            }

            schedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Deletes a scheduling from the database.
     * 
     * Removes the specified scheduling record and handles foreign key constraints.
     * If there are dependent records (like device schedulings), the deletion may
     * fail with a constraint exception, in which case 0 is returned.
     * 
     * @param idx Database ID of the scheduling to delete
     * @return Number of rows deleted (should be 1 if successful), or 0 on constraint violation or error
     */
    fun deleteScheduling(idx: Int): Int {
        val db = this.writableDatabase
        return try {
            db.beginTransaction()
            //db.delete(TABLE_DEVICE_SCHEDULINGS, "scheduling_idx = ?", arrayOf(idx.toString()))
            val rowsDeleted = db.delete(TABLE_SCHEDULINGS, "idx = ?", arrayOf(idx.toString()))
            db.setTransactionSuccessful()
            rowsDeleted
        } catch (e: SQLiteConstraintException) {
            e.printStackTrace()
            0
        } finally {
            db.endTransaction()
        }
    }

    fun getSchedulingsCountByCodec(codecIdx: Int): Int {
        return try {
            val db = this.readableDatabase
            val cursor = db.rawQuery("""
            SELECT COUNT(*)
            FROM $TABLE_SCHEDULINGS s
            JOIN "$TABLE_GROUPS" g ON s.group_idx = g.idx
            WHERE g.codec_idx = ?
        """.trimIndent(), arrayOf(codecIdx.toString()))

            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    /**
     * Batch inserts sector scheduling records.
     * 
     * Creates multiple sector scheduling entries within a single transaction.
     * Sector schedulings define which sectors (irrigation zones) are activated
     * during specific scheduling periods and their operational parameters.
     * 
     * @param deviceSchedulings List of maps containing sector scheduling data
     * @return true if all records were inserted successfully, false on error
     */
    fun insertSectorSchedulings(deviceSchedulings: List<Map<String, Any?>>): Boolean {
        val db = this.writableDatabase
        db.beginTransaction()
        return try {
            for (scheduling in deviceSchedulings) {
                val values = ContentValues().apply {
                    put("scheduling_idx", scheduling["scheduling_idx"] as Int)
                    put("device_idx", scheduling["device_idx"] as Int)
                    put("n_order", scheduling["n_order"] as Int)
                    put("enabled", scheduling["enabled"] as Int)
                    put("type", scheduling["type"] as Int)
                    put("ferti", scheduling["ferti"] as Int)
                    put("ferti_delay", scheduling["ferti_delay"] as Int)
                    put("working_time", scheduling["working_time"] as Int)
                }
                db.insert(TABLE_SECTOR_SCHEDULINGS, null, values)
            }
            db.setTransactionSuccessful()
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        } finally {
            db.endTransaction()
        }
    }

    /**
     * Retrieves all sector schedulings for a specific scheduling.
     * 
     * Returns sector scheduling entries (type = 0) associated with the specified
     * scheduling, including device and sector information. Results are ordered by
     * execution order (n_order) and returned as SectorSchedulingItem objects.
     * 
     * @param schedulingIdx Database ID of the scheduling to get sector schedulings for
     * @return List of SectorSchedulingItem objects, or empty list on error
     */
    fun getAllSectorSchedulings(schedulingIdx: Int): MutableList<SectorSchedulingItem> {
        val sectorSchedulingList = mutableListOf<SectorSchedulingItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
        SELECT ss.device_idx, ss.type, ss.n_order, ss.enabled, d.sector, ss.working_time, ss.ferti, ss.ferti_delay
        FROM $TABLE_SECTOR_SCHEDULINGS ss
        JOIN $TABLE_DEVICES d ON ss.device_idx = d.idx
        WHERE ss.scheduling_idx = ? AND ss.type = 0
        ORDER BY ss.n_order ASC""".trimIndent()

            cursor = db.rawQuery(query, arrayOf(schedulingIdx.toString()))

            while (cursor.moveToNext()) {
                val deviceIdx = cursor.getInt(cursor.getColumnIndexOrThrow("device_idx"))
                val type = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                val lastNOrder = cursor.getInt(cursor.getColumnIndexOrThrow("n_order"))
                val enabled = cursor.getInt(cursor.getColumnIndexOrThrow("enabled")) == 1
                val sector = cursor.getString(cursor.getColumnIndexOrThrow("sector")) ?: ""
                val duration = cursor.getString(cursor.getColumnIndexOrThrow("working_time"))
                val ferti = cursor.getString(cursor.getColumnIndexOrThrow("ferti"))
                val ferti_delay = cursor.getString(cursor.getColumnIndexOrThrow("ferti_delay"))

                sectorSchedulingList.add(SectorSchedulingItem(deviceIdx, type, lastNOrder, enabled, sector, duration, ferti, ferti_delay))
            }

            sectorSchedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            mutableListOf()
        } finally {
            cursor?.close()
        }
    }

    /**
     * Deletes all sector schedulings for a specific scheduling.
     * 
     * Removes all sector scheduling records associated with the specified scheduling.
     * This is typically called when a scheduling is being deleted or its sector
     * configuration is being completely reset.
     * 
     * @param idx Database ID of the scheduling whose sector schedulings should be deleted
     * @return Number of sector schedulings deleted, or 0 on error
     */
    fun deleteSectorSchedulings(idx: Int): Int {
        val db = this.writableDatabase
        return try {
            db.delete(TABLE_SECTOR_SCHEDULINGS, "scheduling_idx = ?", arrayOf(idx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    /**
     * Batch inserts device scheduling records.
     * 
     * Creates multiple device scheduling entries within a single transaction.
     * Device schedulings define the operational sequence and parameters for
     * individual devices during scheduled irrigation periods.
     * 
     * @param deviceSchedulings List of maps containing device scheduling data
     * @return true if all records were inserted successfully, false on error
     */
    fun insertDeviceSchedulings(deviceSchedulings: List<Map<String, Any?>>): Boolean {
        val db = this.writableDatabase
        db.beginTransaction()
        return try {
            for (scheduling in deviceSchedulings) {
                val values = ContentValues().apply {
                    put("ord_idx", 0)
                    put("scheduling_idx", scheduling["scheduling_idx"] as Int)
                    put("device_idx", scheduling["device_idx"] as Int)
                    put("n_order", scheduling["n_order"] as Int)
                    put("status", scheduling["status"] as Int)
                    put("type", scheduling["type"] as Int)
                    put("time", scheduling["time"] as Int)
                    put("sector_working_time", scheduling["sector_working_time"] as Int)
                    put("ferti_working_time", scheduling["ferti_working_time"] as Int)
                    put("ferti_delay", scheduling["ferti_delay"] as Int)
                }
                db.insert(TABLE_DEVICE_SCHEDULINGS, null, values)
            }
            db.setTransactionSuccessful()
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        } finally {
            db.endTransaction()
        }
    }

    /**
     * Retrieves all device schedulings for a specific scheduling.
     * 
     * Returns device scheduling entries (type = 0) associated with the specified
     * scheduling, including device and sector information. Results are ordered by
     * execution order (n_order) and returned as SectorSchedulingItem objects.
     * 
     * Note: Despite the return type name, this method returns device schedulings,
     * not sector schedulings. The return type appears to be reused for similar data structures.
     * 
     * @param schedulingIdx Database ID of the scheduling to get device schedulings for
     * @return List of SectorSchedulingItem objects containing device scheduling data, or empty list on error
     */
    fun getAllDeviceSchedulings(schedulingIdx: Int): MutableList<SectorSchedulingItem> {
        val sectorSchedulingList = mutableListOf<SectorSchedulingItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
        SELECT ds.device_idx, ds.type, ds.n_order, ds.status, d.sector, ds.working_time
        FROM $TABLE_DEVICE_SCHEDULINGS ds
        JOIN $TABLE_DEVICES d ON ds.device_idx = d.idx
        WHERE ds.scheduling_idx = ? AND ds.type = 0
        ORDER BY ds.n_order ASC""".trimIndent()

            cursor = db.rawQuery(query, arrayOf(schedulingIdx.toString()))

            while (cursor.moveToNext()) {
                val deviceIdx = cursor.getInt(cursor.getColumnIndexOrThrow("device_idx"))
                val type = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                val lastNOrder = cursor.getInt(cursor.getColumnIndexOrThrow("n_order"))
                val enabled = cursor.getInt(cursor.getColumnIndexOrThrow("status")) == 1
                val sector = cursor.getString(cursor.getColumnIndexOrThrow("sector")) ?: ""
                val duration = cursor.getString(cursor.getColumnIndexOrThrow("working_time"))

                sectorSchedulingList.add(SectorSchedulingItem(deviceIdx, type, lastNOrder, enabled, sector, duration, "0", "0"))
            }

            sectorSchedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            mutableListOf()
        } finally {
            cursor?.close()
        }
    }

    /**
     * Retrieves device schedulings as maps for flexible data handling.
     * 
     * Returns device scheduling data for a specific scheduling (or all if null is passed)
     * as maps for flexible property access. This provides more dynamic access to
     * device scheduling information compared to the structured object approach.
     * 
     * @param schedulingIdx Database ID of the scheduling to get device schedulings for,
     *                      or null to get all device schedulings
     * @return List of maps containing device scheduling data, or empty list on error
     */
    fun getAllDeviceSchedulingsOnMap(schedulingIdx: Int?): List<Map<String, Any>> {
        val deviceSchedulingList = mutableListOf<Map<String, Any>>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = if (schedulingIdx == null) {
                db.rawQuery("SELECT * FROM $TABLE_DEVICE_SCHEDULINGS", null)
            } else {
                db.rawQuery("SELECT * FROM $TABLE_DEVICE_SCHEDULINGS WHERE scheduling_idx = ?", arrayOf(schedulingIdx.toString()))
            }

            while (cursor.moveToNext()) {
                val item = mutableMapOf<String, Any>()
                item["idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                item["scheduling_idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("scheduling_idx"))
                item["codec_idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("codec_idx"))
                item["mesh_idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("mesh_idx"))
                item["device_idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("device_idx"))
                item["type"] = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                item["time"] = cursor.getInt(cursor.getColumnIndexOrThrow("time"))
                item["working_time"] = cursor.getInt(cursor.getColumnIndexOrThrow("working_time"))
                deviceSchedulingList.add(item)
            }

            deviceSchedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
        }
    }

    /**
     * Deletes all device schedulings for a specific scheduling.
     * 
     * Removes all device scheduling records associated with the specified scheduling.
     * This is typically called when a scheduling is being deleted or its device
     * configuration is being completely reset.
     * 
     * @param idx Database ID of the scheduling whose device schedulings should be deleted
     * @return Number of device schedulings deleted, or 0 on error
     */
    fun deleteDeviceSchedulings(idx: Int): Int {
        val db = this.writableDatabase
        return try {
            db.delete(TABLE_DEVICE_SCHEDULINGS, "scheduling_idx = ?", arrayOf(idx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun getDeviceSchedulingsCountByCodec(codecIdx: Int): Int {
        return try {
            val db = this.readableDatabase
            val cursor = db.rawQuery("""
            SELECT COUNT(*)
            FROM $TABLE_DEVICE_SCHEDULINGS ds
            JOIN $TABLE_SCHEDULINGS s ON ds.scheduling_idx = s.idx
            JOIN "$TABLE_GROUPS" g ON s.group_idx = g.idx
            WHERE g.codec_idx = ?
        """.trimIndent(), arrayOf(codecIdx.toString()))

            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    // ==============================================
    // MESH DEVICE MANAGEMENT METHODS
    // ==============================================

    /**
     * Inserts a new mesh device into the database.
     * 
     * Creates a mesh device record with complete configuration including level pump settings.
     * Mesh devices represent communication nodes that manage multiple individual devices
     * within the irrigation network. They act as intermediaries between the codec and
     * individual control devices.
     * 
     * @param identity Unique identifier for the mesh device
     * @param name Human-readable name for the mesh device
     * @param type Type/category of the mesh device
     * @param mode Operating mode configuration
     * @param equipament Equipment type identifier
     * @param checkInput Input checking configuration
     * @param devicesBitmask Bitmask representing which device slots are occupied
     * @param levelPumpIdx Database ID of device to use as level pump (nullable)
     * @param levelPumpEnable Whether level pump functionality is enabled (nullable)
     * @param levelPumpWorkingTime Working time for level pump operations (nullable)
     * @param codecIdx Database ID of the parent codec
     * @param groupIdx Database ID of the parent group
     * @return The row ID of the newly inserted mesh device, or -1 if an error occurred
     */
    fun insertMeshDevice(identity: String, name: String, type: Int, mode: Int, equipament: Int, checkInput: Int, devicesBitmask: Int, levelPumpIdx: Int?, levelPumpEnable: Int?, levelPumpWorkingTime: Int?, codecIdx: Int, groupIdx: Int): Long {
        val db = this.writableDatabase
        return try {
            val values = ContentValues().apply {
                put("identity", identity)
                put("name", name)
                put("type", type)
                put("mode", mode)
                put("equipament", equipament)
                put("check_input", checkInput)
                put("devices_bitmask", devicesBitmask)
                put("level_pump_idx", levelPumpIdx)
                put("level_pump_enable", levelPumpEnable)
                put("level_pump_working_time", levelPumpWorkingTime)
                put("codec_idx", codecIdx)
                put("group_idx", groupIdx)
            }
            db.insert(TABLE_MESH_DEVICES, null, values)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1
        }
    }

    /**
     * Updates an existing mesh device in the database.
     * 
     * Modifies all configuration fields of an existing mesh device record.
     * This allows for complete reconfiguration of mesh device settings including
     * level pump parameters and device relationships.
     * 
     * @param idx Database ID of the mesh device to update
     * @param identity Unique identifier for the mesh device
     * @param name Human-readable name for the mesh device
     * @param type Type/category of the mesh device
     * @param mode Operating mode configuration
     * @param equipament Equipment type identifier
     * @param checkInput Input checking configuration
     * @param devicesBitmask Bitmask representing which device slots are occupied
     * @param levelPumpIdx Database ID of device to use as level pump (nullable)
     * @param levelPumpEnable Whether level pump functionality is enabled (nullable)
     * @param levelPumpWorkingTime Working time for level pump operations (nullable)
     * @param codecIdx Database ID of the parent codec
     * @param groupIdx Database ID of the parent group
     * @return Number of rows updated (should be 1 if successful), or 0 on error
     */
    fun updateMeshDevice(idx: Int, identity: String, name: String, type: Int, mode: Int, equipament: Int, checkInput: Int, devicesBitmask: Int, levelPumpIdx: Int?, levelPumpEnable: Int?, levelPumpWorkingTime: Int?, codecIdx: Int, groupIdx: Int): Int {
        val db = this.writableDatabase
        return try {
            val values = ContentValues().apply {
                put("identity", identity)
                put("name", name)
                put("type", type)
                put("mode", mode)
                put("equipament", equipament)
                put("check_input", checkInput)
                put("devices_bitmask", devicesBitmask)
                put("level_pump_idx", levelPumpIdx)
                put("level_pump_enable", levelPumpEnable)
                put("level_pump_working_time", levelPumpWorkingTime)
                put("codec_idx", codecIdx)
                put("group_idx", groupIdx)
            }
            db.update(TABLE_MESH_DEVICES, values, "idx = ?", arrayOf(idx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    /**
     * Retrieves all mesh devices belonging to a specific group.
     * 
     * Returns all mesh devices that are associated with the specified group.
     * Results are returned as MeshDeviceItem objects for structured access to
     * mesh device properties. This is useful for displaying or managing mesh
     * devices within a specific group context.
     * 
     * @param groupIdx Database ID of the group to get mesh devices for
     * @return List of MeshDeviceItem objects containing mesh device data, or empty list on error
     */
    fun getAllMeshDevices(groupIdx: Int): List<MeshDeviceItem> {
        val meshDeviceList = mutableListOf<MeshDeviceItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = db.rawQuery("SELECT idx, identity, name, type FROM $TABLE_MESH_DEVICES WHERE group_idx = ?", arrayOf(groupIdx.toString()))

            if (cursor.moveToFirst()) {
                do {
                    val idx = cursor.getInt(0)
                    val identity = "%06X".format(cursor.getString(1).toInt())
                    val name = cursor.getString(2)
                    val type = cursor.getInt(3)

                    val strType = when (type) {
                        0 -> "Valvulas"
                        1 -> "Bomba"
                        2 -> "Controle de Nível"
                        else -> ""
                    }
                    val label = "ID: $identity - $name"
                    meshDeviceList.add(MeshDeviceItem(idx, identity, name, label))
                } while (cursor.moveToNext())
            }

            meshDeviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
        }
    }

    /**
     * Retrieves a mesh device by its database ID.
     * 
     * Returns detailed information about a specific mesh device including all
     * configuration parameters. Data is returned as a map for flexible access
     * to mesh device properties.
     * 
     * @param idx Database ID of the mesh device to retrieve
     * @return Map containing mesh device data, or null if not found or on error
     */
    fun getMeshDeviceById(idx: Int): Map<String, Any>? {
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = db.rawQuery(
                "SELECT identity, name, type, mode, equipament, check_input, devices_bitmask, level_pump_idx, level_pump_working_time FROM $TABLE_MESH_DEVICES WHERE idx = ?",
                arrayOf(idx.toString())
            )

            if (cursor.moveToFirst()) {
                mapOf(
                    "identity" to cursor.getString(0),
                    "name" to cursor.getString(1),
                    "type" to cursor.getInt(2),
                    "mode" to cursor.getInt(3),
                    "equipament" to cursor.getInt(4),
                    "check_input" to cursor.getInt(5),
                    "devices_bitmask" to cursor.getInt(6),
                    "level_pump_idx" to cursor.getInt(7),
                    "level_pump_working_time" to cursor.getInt(8)
                )
            } else {
                null
            }
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        } finally {
            cursor?.close()
        }
    }

    /**
     * Deletes a mesh device from the database.
     * 
     * Removes the specified mesh device record from the database. Note that this
     * method does not automatically handle dependent records (devices belonging to
     * this mesh device). The commented code suggests devices should be deleted first,
     * but this is not currently implemented.
     * 
     * Warning: Deleting a mesh device without first handling its dependent devices
     * may leave orphaned device records or cause foreign key constraint violations.
     * 
     * @param idx Database ID of the mesh device to delete
     * @return Number of rows deleted (should be 1 if successful), or 0 on error
     */
    fun deleteMeshDevice(idx: Int): Int {
        val db = this.writableDatabase
        return try {
            /*val sql = "DELETE FROM $TABLE_DEVICES WHERE mesh_idx = ?"
            val statement = db.compileStatement(sql)
            statement.bindLong(1, idx.toLong())
            statement.executeUpdateDelete()*/
            db.delete(TABLE_MESH_DEVICES, "idx = ?", arrayOf(idx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    // ==============================================
    // DEVICE MANAGEMENT METHODS
    // ==============================================

    /**
     * Inserts a new individual device into the database.
     * 
     * Creates a device record associated with a specific mesh device.
     * Individual devices represent controllable components like valves, pumps,
     * or sensors within a mesh device.
     * 
     * Note: This method appears to have a bug - it uses "mesh_id" instead of "mesh_idx"
     * which doesn't match the table schema. This should be corrected.
     * 
     * @param meshIdx Database ID of the parent mesh device
     * @param identity Unique identifier for this device within the mesh
     * @param type Device type as string (should be converted to integer)
     * @param out1 Output channel 1 configuration (nullable)
     * @param out2 Output channel 2 configuration (nullable)
     * @param input Input channel configuration (nullable)
     * @param mode Operating mode configuration (nullable)
     * @param sector Sector/zone number this device controls (nullable)
     * @return The row ID of the newly inserted device, or -1 if an error occurred
     */
    fun insertDevice(meshIdx: Int, identity: String, type: String, out1: String?, out2: String?, input: String?, mode: String?, sector: String?): Long {
        val db = this.writableDatabase
        return try {
            // Create ContentValues with device configuration
            // Note: Bug - should use "mesh_idx" not "mesh_id" to match table schema
            val values = ContentValues().apply {
                put("mesh_id", meshIdx) // BUG: Should be "mesh_idx"
                put("identity", identity)
                put("type", type) // Note: Table expects INTEGER, this passes STRING
                put("out1", out1)
                put("out2", out2)
                put("input", input)
                put("mode", mode) // Note: Table expects INTEGER, this passes STRING
                put("sector", sector) // Note: Table expects INTEGER, this passes STRING
            }
            // Insert device record and return generated ID
            db.insert(TABLE_DEVICES, null, values)
        } catch (e: SQLException) {
            e.printStackTrace()
            -1 // Return -1 to indicate failure
        }
    }

    /**
     * Data class representing a device record for synchronization operations.
     * 
     * Used internally by syncDevicesForMesh to track existing devices
     * and identify which ones need to be updated, inserted, or deleted.
     * 
     * @property idx Database ID of the device
     * @property identity Unique identifier of the device within its mesh
     */
    private data class DeviceRow(val idx: Int, val identity: String)

    /**
     * Synchronizes devices for a specific mesh device with a desired state.
     * 
     * This method performs a complete synchronization of devices belonging to a mesh device:
     * - Updates existing devices with new configurations
     * - Inserts new devices that don't exist
     * - Deletes devices that are no longer in the desired list
     * 
     * The operation is performed in a database transaction for consistency.
     * Uses device identity as the matching key for synchronization.
     * 
     * @param meshIdx Database ID of the mesh device to synchronize
     * @param desired List of device specifications representing the desired state
     * @return true if synchronization was successful, false on error
     */
    fun syncDevicesForMesh(meshIdx: Int, desired: List<Map<String, Any?>>): Boolean {
        val db = writableDatabase
        db.beginTransaction()
        try {
            /**
             * Helper function to build ContentValues from device specification map.
             * Handles type conversion and provides default values for missing fields.
             */
            fun buildCV(spec: Map<String, Any?>): ContentValues = ContentValues().apply {
                put("mesh_idx", meshIdx)
                put("identity", spec["identity"]?.toString())
                put("type",     (spec["type"]   as? Int) ?: 0)
                put("out1",     spec["out1"]?.toString())
                put("out2",     spec["out2"]?.toString())
                put("input",    spec["input"]?.toString())
                put("mode",     (spec["mode"]   as? Int) ?: 0)
                put("sector",   spec["sector"]?.toString())
                put("ord_idx",  (spec["ord_idx"] as? Int) ?: 0)
            }

            // Build map of existing devices indexed by identity
            val existing = mutableMapOf<String, DeviceRow>()
            db.rawQuery(
                "SELECT idx, identity FROM $TABLE_DEVICES WHERE mesh_idx = ?",
                arrayOf(meshIdx.toString())
            ).use { c ->
                while (c.moveToNext()) {
                    existing[c.getString(1)] = DeviceRow(c.getInt(0), c.getString(1))
                }
            }

            // Process desired devices: update existing or insert new
            for (spec in desired) {
                val identity = spec["identity"]?.toString() ?: continue
                val cv = buildCV(spec)

                val row = existing.remove(identity)
                if (row == null) {
                    // Device doesn't exist - insert new
                    db.insert(TABLE_DEVICES, null, cv)
                } else {
                    // Device exists - update it
                    db.update(TABLE_DEVICES, cv, "idx = ?", arrayOf(row.idx.toString()))
                }
            }

            // Delete devices that are no longer in the desired list
            for (row in existing.values) {
                db.delete(TABLE_DEVICES, "idx = ?", arrayOf(row.idx.toString()))
            }

            db.setTransactionSuccessful()
            return true
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        } finally {
            db.endTransaction()
        }
    }

    /**
     * Batch inserts multiple devices into the database.
     * 
     * Performs a bulk insert operation for multiple device records within a single transaction.
     * This is more efficient than inserting devices individually when dealing with large datasets.
     * All devices must be inserted successfully or the entire operation is rolled back.
     * 
     * @param devices List of device specification maps, each containing device configuration
     * @return true if all devices were inserted successfully, false on any error
     */
    fun insertDevices(devices: List<Map<String, Any?>>): Boolean {
        val db = this.writableDatabase
        db.beginTransaction()
        return try {
            // Insert each device in the list
            for (device in devices) {
                val values = ContentValues().apply {
                    put("ord_idx", 0) // Default order index
                    put("mesh_idx", device["mesh_idx"] as Int)
                    put("identity", device["identity"] as String)
                    put("type", device["type"] as Int)
                    put("out1", device["out1"] as String?)
                    put("out2", device["out2"] as String?)
                    put("input", device["input"] as String?)
                    put("mode", device["mode"] as Int)
                    put("sector", device["sector"] as String?)
                }
                db.insert(TABLE_DEVICES, null, values)
            }
            db.setTransactionSuccessful()
            true // All devices inserted successfully
        } catch (e: Exception) {
            e.printStackTrace()
            false // Error occurred, transaction will be rolled back
        } finally {
            db.endTransaction()
        }
    }

    /**
     * Deletes all devices belonging to a specific mesh device.
     * 
     * Removes all individual devices that are associated with the specified mesh device.
     * This is typically called when a mesh device is being removed or reset.
     * Related device schedulings will also be deleted due to foreign key constraints.
     * 
     * Note: The method closes the database connection, which may not be desirable
     * if other operations are planned. Consider removing the db.close() call.
     * 
     * @param meshIdx Database ID of the mesh device whose devices should be deleted
     * @return Number of devices deleted, or 0 on error
     */
    fun deleteDevicesByMeshId(meshIdx: Int): Int {
        val db = this.writableDatabase

        return try {
            // Delete all devices belonging to the specified mesh device
            db.delete(TABLE_DEVICES, "mesh_idx = ?", arrayOf(meshIdx.toString()))
        } catch (e: SQLException) {
            e.printStackTrace()
            0 // Return 0 to indicate failure
        } finally {
            db.close() // Note: This may be undesirable in some contexts
        }
    }

    // ==============================================
    // DEVICE QUERY METHODS
    // ==============================================

    /**
     * Retrieves all devices belonging to a specific group.
     * 
     * Returns devices from all mesh devices that belong to the specified group.
     * Results are ordered by sector number for logical presentation.
     * Data is returned as a list of maps for flexible handling.
     * 
     * @param groupIdx Database ID of the group to get devices for
     * @return List of maps containing device data, or empty list if none found or on error
     */
    fun getAllDevicesByGroup(groupIdx: Int): List<Map<String, Any>> {
        val deviceList = mutableListOf<Map<String, Any>>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            // Query devices through their mesh device's group membership
            cursor = db.rawQuery(
                """
            SELECT d.idx, d.mesh_idx, d.identity, d.type, d.out1, d.out2, d.input, d.mode, d.sector 
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            WHERE m.group_idx = ?
            ORDER BY d.sector ASC
            """.trimIndent(),
                arrayOf(groupIdx.toString())
            )

            // Process each device record
            while (cursor.moveToNext()) {
                val item = mutableMapOf<String, Any>()
                item["idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                item["mesh_idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("mesh_idx"))
                item["identity"] = cursor.getString(cursor.getColumnIndexOrThrow("identity"))
                item["type"] = cursor.getString(cursor.getColumnIndexOrThrow("type"))
                item["out1"] = cursor.getString(cursor.getColumnIndexOrThrow("out1")) ?: ""
                item["out2"] = cursor.getString(cursor.getColumnIndexOrThrow("out2")) ?: ""
                item["input"] = cursor.getString(cursor.getColumnIndexOrThrow("input")) ?: ""
                item["mode"] = cursor.getString(cursor.getColumnIndexOrThrow("mode")) ?: ""
                item["sector"] = cursor.getString(cursor.getColumnIndexOrThrow("sector")) ?: ""

                deviceList.add(item)
            }

            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves all devices belonging to a specific mesh device.
     * 
     * Returns all individual devices that are associated with the specified mesh device.
     * Results include all device configuration details and are ordered by sector for
     * logical presentation. This is useful for displaying or managing devices within
     * a specific mesh device.
     * 
     * @param meshIdx Database ID of the mesh device to get devices for
     * @return List of maps containing device data with keys: idx, mesh_idx, identity, 
     *         type, out1, out2, input, mode, sector. Empty list if none found or on error.
     */
    fun getAllDevicesByMesh(meshIdx: Int): List<Map<String, Any>> {
        val deviceList = mutableListOf<Map<String, Any>>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = db.rawQuery(
                """
            SELECT idx, mesh_idx, identity, type, out1, out2, input, mode, sector
            FROM $TABLE_DEVICES
            WHERE mesh_idx = ?
            ORDER BY sector ASC
            """.trimIndent(),
                arrayOf(meshIdx.toString())
            )

            while (cursor.moveToNext()) {
                val item = mutableMapOf<String, Any>()
                item["idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                item["mesh_idx"] = cursor.getInt(cursor.getColumnIndexOrThrow("mesh_idx"))
                item["identity"] = cursor.getString(cursor.getColumnIndexOrThrow("identity"))
                item["type"] = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                item["out1"] = cursor.getInt(cursor.getColumnIndexOrThrow("out1"))
                item["out2"] = cursor.getInt(cursor.getColumnIndexOrThrow("out2"))
                item["input"] = cursor.getInt(cursor.getColumnIndexOrThrow("input"))
                item["mode"] = cursor.getInt(cursor.getColumnIndexOrThrow("mode"))
                item["sector"] = cursor.getInt(cursor.getColumnIndexOrThrow("sector"))

                deviceList.add(item)
            }

            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves all devices of a specific type across all mesh devices.
     * 
     * Returns devices filtered by device type, along with mesh device information.
     * Results are returned as DeviceItem objects for structured access to device data.
     * Useful for finding all devices of a particular type (e.g., all valves, all pumps)
     * regardless of which mesh device they belong to.
     * 
     * @param type Device type ID to filter by (0=sector, 1=pump, 2=fertilizer, etc.)
     * @return List of DeviceItem objects containing device and mesh info, or empty list on error
     */
    fun getAllDevicesByType(type: Int): List<DeviceItem> {
        val deviceList = mutableListOf<DeviceItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = db.rawQuery(
                """
            SELECT d.idx, d.ord_idx, d.mesh_idx, d.identity AS device_identity, d.type, d.out1, d.out2, d.input, d.mode, d.sector, 
                   m.name AS mesh_name, m.identity AS mesh_identity
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            WHERE d.type = ? 
            ORDER BY d.idx ASC
            """.trimIndent(),
                arrayOf(type.toString())
            )

            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                val ord_idx = cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx"))
                val name = cursor.getString(cursor.getColumnIndexOrThrow("mesh_name"))
                val type = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                val deviceIdentity = cursor.getString(cursor.getColumnIndexOrThrow("device_identity"))
                val meshIdentity = cursor.getString(cursor.getColumnIndexOrThrow("mesh_identity"))
                deviceList.add(DeviceItem(idx, ord_idx, name, type, deviceIdentity, meshIdentity, "", null, null))
            }

            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves all devices of a specific type as maps for flexible data handling.
     * 
     * Similar to getAllDevicesByType but returns data as maps instead of DeviceItem objects.
     * This provides more flexibility for dynamic property access and JSON serialization.
     * Includes mesh device name for context in the returned data.
     * 
     * @param type Device type ID to filter by (0=sector, 1=pump, 2=fertilizer, etc.)
     * @return List of maps containing device data with keys: idx, mesh_idx, identity, type, 
     *         out1, out2, input, mode, sector, mesh_name. Empty list on error.
     */
    fun getAllDevicesByTypeOnMap(type: Int): List<Map<String, Any?>> {
        val deviceList = mutableListOf<Map<String, Any?>>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            cursor = db.rawQuery(
                """
            SELECT d.idx, d.mesh_idx, d.identity, d.type, d.out1, d.out2, d.input, d.mode, d.sector, 
                   m.name AS mesh_name
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            WHERE d.type = ? 
            ORDER BY d.idx ASC
            """.trimIndent(),
                arrayOf(type.toString())
            )

            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                val meshIdx = cursor.getInt(cursor.getColumnIndexOrThrow("mesh_idx"))
                val identity = cursor.getString(cursor.getColumnIndexOrThrow("identity"))
                val deviceType = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                val out1 = cursor.getInt(cursor.getColumnIndexOrThrow("out1"))
                val out2 = cursor.getInt(cursor.getColumnIndexOrThrow("out2"))
                val input = cursor.getInt(cursor.getColumnIndexOrThrow("input"))
                val mode = cursor.getInt(cursor.getColumnIndexOrThrow("mode"))
                val sector = cursor.getInt(cursor.getColumnIndexOrThrow("sector"))
                val meshName = cursor.getString(cursor.getColumnIndexOrThrow("mesh_name"))

                val deviceMap = mapOf(
                    "idx" to idx,
                    "mesh_idx" to meshIdx,
                    "identity" to identity,
                    "type" to deviceType,
                    "out1" to out1,
                    "out2" to out2,
                    "input" to input,
                    "mode" to mode,
                    "sector" to sector,
                    "mesh_name" to meshName
                )

                deviceList.add(deviceMap)
            }

            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves all devices with complete mesh device and codec information.
     * 
     * Returns a comprehensive view of all devices including their mesh device context
     * and associated codec information. The mesh identity is formatted as a hexadecimal
     * string for proper display. This method is useful for system-wide device overviews
     * where complete hierarchical information is needed.
     * 
     * @return List of DeviceItem objects with complete device, mesh, and codec information.
     *         Empty list on error.
     */
    fun getAllDevicesWithMeshInfo(): List<DeviceItem> {
        val deviceList = mutableListOf<DeviceItem>()
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT 
                d.idx AS idx, 
                d.ord_idx AS ord_idx, 
                d.type AS device_type, 
                d.identity AS device_identity,
                m.identity AS mesh_identity, 
                m.name AS mesh_name,
                c.identity AS codec_identity
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            LEFT JOIN $TABLE_CODECS c ON m.codec_idx = c.idx
        """.trimIndent()

            cursor = db.rawQuery(query, null)

            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                val ordIdx = cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx"))
                val name = cursor.getString(cursor.getColumnIndexOrThrow("mesh_name"))
                val type = cursor.getInt(cursor.getColumnIndexOrThrow("device_type"))
                val deviceIdentity = cursor.getString(cursor.getColumnIndexOrThrow("device_identity"))
                val meshIdentity = "%06X".format(cursor.getString(cursor.getColumnIndexOrThrow("mesh_identity")).toInt())
                val codecIdentity = cursor.getString(cursor.getColumnIndexOrThrow("codec_identity"))
                deviceList.add(DeviceItem(idx, ordIdx, name, type, deviceIdentity, meshIdentity, codecIdentity, null, null))
            }

            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves the highest sector number used across all devices.
     * 
     * Finds the maximum sector value from all devices in the database.
     * This is useful for determining the next available sector number when
     * creating new devices, or for understanding the current extent of the
     * irrigation system.
     * 
     * @return The highest sector number currently in use, or 0 if no devices exist or on error
     */
    fun getMaxSector(): Int {
        val db = this.readableDatabase
        var cursor: Cursor? = null
        var maxSector = 0

        return try {
            cursor = db.rawQuery("SELECT MAX(sector) FROM $TABLE_DEVICES", null)

            if (cursor.moveToFirst() && !cursor.isNull(0)) {
                maxSector = cursor.getInt(0)
            }

            maxSector
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves all devices belonging to a specific codec with mesh information.
     * 
     * Returns devices from all mesh devices that belong to the specified codec.
     * Results include device configuration and mesh device identity for complete context.
     * This is useful for codec-specific operations and device management within
     * a particular communication hub.
     * 
     * @param codecIdx Database ID of the codec to get devices for
     * @return List of maps containing device data with keys: idx, ord_idx, mesh_idx,
     *         identity, type, out1, out2, input, mode, sector, mesh_id, group_idx.
     *         Empty list on error.
     */
    fun getDevicesByCodec(codecIdx: Int): List<Map<String, Any>> {
        val db = this.readableDatabase
        val deviceList = mutableListOf<Map<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT d.idx, d.ord_idx, d.mesh_idx, d.identity, d.type, d.out1, d.out2, d.input, d.mode, d.sector, d.power, d.eqpt_ver, 
                   m.identity AS mesh_id, m.group_idx
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            WHERE m.codec_idx = ? 
            ORDER BY d.idx ASC
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(codecIdx.toString()))

            if (cursor.moveToFirst()) {
                do {
                    val item = mutableMapOf<String, Any>()
                    item["ix"] = cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx"))
                    item["mi"] = cursor.getInt(cursor.getColumnIndexOrThrow("mesh_id"))
                    item["di"] = cursor.getString(cursor.getColumnIndexOrThrow("identity")).toIntOrNull() ?: 0
                    item["tp"] = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                    item["o1"] = cursor.getInt(cursor.getColumnIndexOrThrow("out1"))
                    item["o2"] = cursor.getInt(cursor.getColumnIndexOrThrow("out2"))
                    item["ip"] = cursor.getInt(cursor.getColumnIndexOrThrow("input"))
                    item["md"] = cursor.getInt(cursor.getColumnIndexOrThrow("mode"))
                    item["sc"] = cursor.getInt(cursor.getColumnIndexOrThrow("sector"))
                    item["gp"] = cursor.getInt(cursor.getColumnIndexOrThrow("group_idx"))
                    item["pw"] = cursor.getInt(cursor.getColumnIndexOrThrow("power"))
                    item["eq"] = cursor.getInt(cursor.getColumnIndexOrThrow("eqpt_ver"))

                    deviceList.add(item)
                } while (cursor.moveToNext())
            }
            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves device information by database ID with codec context.
     * 
     * Returns essential device information including order index, type, and codec identity.
     * This method provides minimal but essential data for device identification and
     * communication purposes, particularly useful for protocol operations.
     * 
     * @param idx Database ID of the device to retrieve
     * @return Map containing device data with keys: ord_idx, type, codec_identity.
     *         null if device not found or on error.
     */
    fun getDeviceByIdx(idx: Int): Map<String, Any>? {
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT d.ord_idx, d.type, c.identity AS codec_identity
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            JOIN $TABLE_GROUPS g ON m.group_idx = g.idx
            JOIN $TABLE_CODECS c ON g.codec_idx = c.idx
            WHERE d.idx = ?
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(idx.toString()))

            if (cursor.moveToFirst()) {
                mapOf(
                    "ord_idx" to cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx")),
                    "type" to cursor.getInt(cursor.getColumnIndexOrThrow("type")),
                    "codec_identity" to cursor.getString(cursor.getColumnIndexOrThrow("codec_identity"))
                )
            } else {
                null
            }
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        } finally {
            cursor?.close()
            db.close()
        }
    }

    // ==============================================
    // DATA REORGANIZATION UTILITY METHODS
    // ==============================================

    /**
     * Reorganizes the ord_idx values for all devices belonging to a specific codec.
     * 
     * This method reassigns sequential ord_idx values (starting from 0) to all devices
     * that belong to mesh devices under the specified codec. The devices are ordered
     * by their database idx for consistent ordering.
     * 
     * This is typically called after device deletions or bulk operations to ensure
     * that ord_idx values are sequential and consistent for communication protocols
     * that rely on these order values.
     * 
     * @param codecIdx Database ID of the codec whose devices should be reorganized
     */
    fun devicesReorganizeOrdIdxByCodec(codecIdx: Int) {
        val db = this.writableDatabase
        var cursor: Cursor? = null
        try {
            // Query all devices belonging to the specified codec, ordered by idx
            cursor = db.rawQuery(
                """
            SELECT D.idx 
            FROM $TABLE_DEVICES D
            INNER JOIN $TABLE_MESH_DEVICES M ON D.mesh_idx = M.idx
            WHERE M.codec_idx = ?
            ORDER BY D.idx ASC
            """, arrayOf(codecIdx.toString())
            )

            var ordIdx = 0
            // Update each device with a sequential ord_idx value
            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))

                val values = ContentValues().apply {
                    put("ord_idx", ordIdx)
                }
                db.update(TABLE_DEVICES, values, "idx = ?", arrayOf(idx.toString()))

                ordIdx++ // Increment for next device
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves schedulings for a specific codec with optional nickname processing.
     * 
     * Returns enabled schedulings that belong to groups managed by the specified codec.
     * This method supports two modes: regular data retrieval and nickname processing mode.
     * When nicknames mode is enabled, additional processing is performed on the data.
     * 
     * @param codecIdx Database ID of the codec to get schedulings for
     * @param nicknames Whether to enable nickname processing mode (default: false)
     * @return List of mutable maps containing scheduling data, or empty list on error
     */
    fun getSchedulingsByCodec(codecIdx: Int, nicknames: Boolean = false): MutableList<MutableMap<String, Any>> {
        val db = this.readableDatabase
        val schedulingList = mutableListOf<MutableMap<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT DISTINCT s.*
            FROM $TABLE_SCHEDULINGS s
            JOIN $TABLE_GROUPS g ON s.group_idx = g.idx
            JOIN $TABLE_MESH_DEVICES m ON g.idx = m.group_idx
            WHERE m.codec_idx = ? AND s.enabled = 1
            ORDER BY s.idx ASC
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(codecIdx.toString()))

            if(nicknames) {
                if (cursor.moveToFirst()) {
                    do {
                        val item = mutableMapOf<String, Any>()
                        item["ix"] = cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx"))
                        //item["ix"] = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                        item["gi"] = cursor.getInt(cursor.getColumnIndexOrThrow("group_idx"))
                        //item["nm"] = cursor.getInt(cursor.getColumnIndexOrThrow("name"))
                        //item["hr"] = cursor.getInt(cursor.getColumnIndexOrThrow("hour"))
                        //item["mn"] = cursor.getInt(cursor.getColumnIndexOrThrow("min"))
                        item["st"] = cursor.getInt(cursor.getColumnIndexOrThrow("start_time"))
                        item["dw"] = cursor.getInt(cursor.getColumnIndexOrThrow("days_of_week"))
                        item["ns"] = cursor.getInt(cursor.getColumnIndexOrThrow("number_of_steps"))
                        item["af"] = cursor.getInt(cursor.getColumnIndexOrThrow("allow_ferti"))
                        item["ab"] = cursor.getInt(cursor.getColumnIndexOrThrow("allow_backwash"))
                        item["wi"] = cursor.getInt(cursor.getColumnIndexOrThrow("waterpump_ord_idx"))
                        item["wt"] = cursor.getInt(cursor.getColumnIndexOrThrow("waterpump_working_time"))
                        item["fi"] = cursor.getInt(cursor.getColumnIndexOrThrow("ferti_ord_idx"))
                        item["bi"] = cursor.getInt(cursor.getColumnIndexOrThrow("backwash_ord_idx"))
                        item["oc"] = cursor.getInt(cursor.getColumnIndexOrThrow("once"))
                        schedulingList.add(item)
                    } while (cursor.moveToNext())
                }
            }else{
                while (cursor.moveToNext()) {
                    val item = mutableMapOf<String, Any>()
                    for (i in 0 until cursor.columnCount) {
                        val columnName = cursor.getColumnName(i)
                        when (cursor.getType(i)) {
                            Cursor.FIELD_TYPE_INTEGER -> item[columnName] = cursor.getInt(i)
                            Cursor.FIELD_TYPE_FLOAT -> item[columnName] = cursor.getFloat(i)
                            Cursor.FIELD_TYPE_STRING -> item[columnName] = cursor.getString(i)
                            Cursor.FIELD_TYPE_BLOB -> item[columnName] = cursor.getBlob(i)
                            Cursor.FIELD_TYPE_NULL -> item[columnName] = "NULL"
                        }
                    }
                    schedulingList.add(item)
                }
            }

            schedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            mutableListOf()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    // ==============================================
    // DEVICE SCHEDULING QUERY METHODS
    // ==============================================

    /**
     * Retrieves device schedulings for a specific scheduling with compact data format.
     * 
     * Returns device scheduling information for the specified scheduling ID using
     * abbreviated field names for efficient data transmission. The method includes
     * comprehensive scheduling and device context through table joins.
     * 
     * @param schedulingIdx Database ID of the scheduling to get device schedulings for
     * @return List of maps with abbreviated keys for device scheduling data, or empty list on error
     */
    fun getDevicesSchedulingsByScheduling(schedulingIdx: Int): List<Map<String, Any>> {
        val db = this.readableDatabase
        val deviceSchedulingList = mutableListOf<Map<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
        SELECT ds.idx, ds.ord_idx, d.ord_idx AS device_ord_idx, ds.scheduling_idx, ds.device_idx, ds.n_order, ds.status, ds.type, ds.time, ds.sector_working_time,
               ds.ferti_working_time, s.days_of_week, s.ord_idx AS scheduling_ord_idx
        FROM $TABLE_DEVICE_SCHEDULINGS ds
        JOIN $TABLE_SCHEDULINGS s ON ds.scheduling_idx = s.idx
        JOIN $TABLE_GROUPS g ON s.group_idx = g.idx
        JOIN $TABLE_MESH_DEVICES m ON g.idx = m.group_idx
        JOIN $TABLE_DEVICES d ON ds.device_idx = d.idx 
        WHERE ds.scheduling_idx = ?
        GROUP BY ds.idx 
        ORDER BY ds.idx ASC
    """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(schedulingIdx.toString()))

            if (cursor.moveToFirst()) {
                do {
                    val item = mutableMapOf<String, Any>()
                    item["ix"] = cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx"))
                    item["sh"] = cursor.getInt(cursor.getColumnIndexOrThrow("scheduling_ord_idx"))
                    item["dx"] = cursor.getInt(cursor.getColumnIndexOrThrow("device_ord_idx"))
                    item["od"] = cursor.getInt(cursor.getColumnIndexOrThrow("n_order"))
                    item["st"] = cursor.getInt(cursor.getColumnIndexOrThrow("status"))
                    item["tp"] = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                    item["tm"] = cursor.getInt(cursor.getColumnIndexOrThrow("time"))
                    item["st"] = cursor.getInt(cursor.getColumnIndexOrThrow("sector_working_time"))
                    item["ft"] = cursor.getInt(cursor.getColumnIndexOrThrow("ferti_working_time"))

                    deviceSchedulingList.add(item)
                } while (cursor.moveToNext())
            }

            deviceSchedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves device schedulings for a specific codec with compact data format.
     * 
     * Returns enabled device schedulings for all schedulings managed by the specified codec.
     * Uses abbreviated field names for efficient data transmission and includes timing,
     * fertilizer, and device order information. Only includes enabled schedulings.
     * 
     * @param codecIdx Database ID of the codec to get device schedulings for
     * @return List of maps with abbreviated keys for device scheduling data, or empty list on error
     */
    fun getDevicesSchedulingsByCodec(codecIdx: Int): List<Map<String, Any>> {
        val db = this.readableDatabase
        val schedulingList = mutableListOf<Map<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT ds.idx, ds.ord_idx, d.ord_idx AS device_ord_idx, ds.scheduling_idx, ds.device_idx, ds.n_order, ds.status, ds.type, ds.time, ds.sector_working_time,
                   ds.ferti_working_time, ds.ferti_delay, s.days_of_week, s.ord_idx AS scheduling_ord_idx
            FROM $TABLE_DEVICE_SCHEDULINGS ds
            JOIN $TABLE_SCHEDULINGS s ON ds.scheduling_idx = s.idx
            JOIN $TABLE_GROUPS g ON s.group_idx = g.idx
            JOIN $TABLE_MESH_DEVICES m ON g.idx = m.group_idx
            JOIN $TABLE_DEVICES d ON ds.device_idx = d.idx 
            WHERE m.codec_idx = ? AND s.enabled = 1 
            GROUP BY ds.idx 
            ORDER BY ds.idx ASC
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(codecIdx.toString()))

            if (cursor.moveToFirst()) {
                do {
                    val item = mutableMapOf<String, Any>()
                    item["ix"] = cursor.getInt(cursor.getColumnIndexOrThrow("ord_idx"))
                    item["sh"] = cursor.getInt(cursor.getColumnIndexOrThrow("scheduling_ord_idx"))
                    item["dx"] = cursor.getInt(cursor.getColumnIndexOrThrow("device_ord_idx"))
                    item["od"] = cursor.getInt(cursor.getColumnIndexOrThrow("n_order"))
                    item["tp"] = cursor.getInt(cursor.getColumnIndexOrThrow("type"))
                    item["tm"] = cursor.getInt(cursor.getColumnIndexOrThrow("time"))
                    item["st"] = cursor.getInt(cursor.getColumnIndexOrThrow("sector_working_time"))
                    item["ft"] = cursor.getInt(cursor.getColumnIndexOrThrow("ferti_working_time"))
                    item["fd"] = cursor.getInt(cursor.getColumnIndexOrThrow("ferti_delay"))
                    item["dw"] = cursor.getInt(cursor.getColumnIndexOrThrow("days_of_week"))

                    schedulingList.add(item)
                } while (cursor.moveToNext())
            }

            schedulingList
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves all device schedulings with complete hierarchical information.
     * 
     * Returns comprehensive device scheduling data including device, scheduling, group,
     * and codec information. This method provides complete context for each device
     * scheduling entry and is ordered by scheduling and device scheduling order.
     * 
     * @return List of maps containing complete device scheduling data with keys:
     *         ord_idx, scheduling_idx, device_idx, n_order, status, type, time,
     *         sector_working_time, ferti_working_time, ferti_delay, sector,
     *         scheduling_ord_idx, codec_identity. Empty list on error.
     */
    fun getAllDeviceSchedulingsOnMap(): List<Map<String, Any>> {
        val db = this.readableDatabase
        val list = mutableListOf<Map<String, Any>>()
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT ds.idx,
                   ds.ord_idx,
                   ds.scheduling_idx,
                   ds.device_idx,
                   ds.n_order,
                   ds.status,
                   ds.type,
                   ds.time,
                   ds.sector_working_time,
                   ds.ferti_working_time,
                   ds.ferti_delay,
                   d.sector,
                   s.ord_idx AS scheduling_ord_idx,
                   c.identity AS codec_identity
            FROM $TABLE_DEVICE_SCHEDULINGS  ds
            INNER JOIN $TABLE_DEVICES       d  ON ds.device_idx     = d.idx
            INNER JOIN $TABLE_SCHEDULINGS   s  ON ds.scheduling_idx = s.idx
            INNER JOIN $TABLE_GROUPS        g  ON s.group_idx       = g.idx
            INNER JOIN $TABLE_CODECS        c  ON g.codec_idx       = c.idx
            ORDER BY s.ord_idx ASC, ds.ord_idx ASC
        """.trimIndent()

            cursor = db.rawQuery(query, null)

            if (cursor.moveToFirst()) {
                do {
                    val item = mapOf(
                        "idx"                    to cursor.getInt   (cursor.getColumnIndexOrThrow("idx")),
                        "ord_idx"                to cursor.getInt   (cursor.getColumnIndexOrThrow("ord_idx")),
                        "scheduling_idx"         to cursor.getInt   (cursor.getColumnIndexOrThrow("scheduling_idx")),
                        "device_idx"             to cursor.getInt   (cursor.getColumnIndexOrThrow("device_idx")),
                        "n_order"                to cursor.getInt   (cursor.getColumnIndexOrThrow("n_order")),
                        "status"                 to cursor.getInt   (cursor.getColumnIndexOrThrow("status")),
                        "type"                   to cursor.getInt   (cursor.getColumnIndexOrThrow("type")),
                        "time"                   to cursor.getInt   (cursor.getColumnIndexOrThrow("time")),
                        "sector_working_time"    to cursor.getInt   (cursor.getColumnIndexOrThrow("sector_working_time")),
                        "ferti_working_time"     to cursor.getInt   (cursor.getColumnIndexOrThrow("ferti_working_time")),
                        "ferti_delay"            to cursor.getInt   (cursor.getColumnIndexOrThrow("ferti_delay")),
                        "sector"                 to cursor.getInt   (cursor.getColumnIndexOrThrow("sector")),
                        "scheduling_ord_idx"     to cursor.getInt   (cursor.getColumnIndexOrThrow("scheduling_ord_idx")),
                        "codec_identity"         to cursor.getString(cursor.getColumnIndexOrThrow("codec_identity"))
                    )
                    list.add(item)
                } while (cursor.moveToNext())
            }

            list
        } catch (e: SQLException) {
            e.printStackTrace()
            emptyList()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Reorganizes ord_idx values for all schedulings and updates device references.
     * 
     * This method performs a comprehensive reorganization of scheduling order indexes:
     * 1. Assigns sequential ord_idx values to all schedulings (starting from 0)
     * 2. Updates waterpump_ord_idx, ferti_ord_idx, and backwash_ord_idx with current device ord_idx values
     * 3. Ensures consistency between scheduling references and actual device ordering
     * 
     * This is critical for maintaining proper scheduling execution order and ensuring
     * that device references in schedulings match the current device ord_idx values.
     */
    fun schedulingsReorganizeOrdIdx() {
        val db = this.writableDatabase
        var cursor: Cursor? = null
        try {
            // Query all schedulings with their device references
            cursor = db.rawQuery("SELECT idx, waterpump_idx, ferti_idx, backwash_idx FROM $TABLE_SCHEDULINGS ORDER BY idx ASC", null)

            var ordIdx = 0
            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                val waterpumpIdx = cursor.getIntOrNull(cursor.getColumnIndexOrThrow("waterpump_idx"))
                val fertiIdx = cursor.getIntOrNull(cursor.getColumnIndexOrThrow("ferti_idx"))
                val backwashIdx = cursor.getIntOrNull(cursor.getColumnIndexOrThrow("backwash_idx"))

                val values = ContentValues().apply {
                    put("ord_idx", ordIdx)

                    // Update waterpump_ord_idx if waterpump_idx is not null
                    if (waterpumpIdx != null) {
                        val waterpumpCursor = db.rawQuery("SELECT ord_idx FROM $TABLE_DEVICES WHERE idx = ?", arrayOf(waterpumpIdx.toString()))
                        if (waterpumpCursor.moveToFirst()) {
                            put("waterpump_ord_idx", waterpumpCursor.getInt(waterpumpCursor.getColumnIndexOrThrow("ord_idx")))
                        }
                        waterpumpCursor.close()
                    }

                    // Update ferti_ord_idx if ferti_idx is not null
                    if (fertiIdx != null) {
                        val fertiCursor = db.rawQuery("SELECT ord_idx FROM $TABLE_DEVICES WHERE idx = ?", arrayOf(fertiIdx.toString()))
                        }
                        waterpumpCursor.close()
                    }

                    if (fertiIdx != null) {  // Atualiza ferti_ord_idx se ferti_idx não for nulo
                        val fertiCursor = db.rawQuery("SELECT ord_idx FROM $TABLE_DEVICES WHERE idx = ?", arrayOf(fertiIdx.toString()))
                        if (fertiCursor.moveToFirst()) {
                            put("ferti_ord_idx", fertiCursor.getInt(fertiCursor.getColumnIndexOrThrow("ord_idx")))
                        }
                        fertiCursor.close()
                    }

                    if (backwashIdx != null) {  // Atualiza bachwash_ord_idx se bachwash_idx não for nulo
                        val backwashCursor = db.rawQuery("SELECT ord_idx FROM $TABLE_DEVICES WHERE idx = ?", arrayOf(backwashIdx.toString()))
                        if (backwashCursor.moveToFirst()) {
                            put("backwash_ord_idx", backwashCursor.getInt(backwashCursor.getColumnIndexOrThrow("ord_idx")))
                        }
                        backwashCursor.close()
                    }
                }

                db.update(TABLE_SCHEDULINGS, values, "idx = ?", arrayOf(idx.toString()))
                ordIdx++
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Reorganizes scheduling ord_idx values for schedulings belonging to a specific codec.
     * 
     * Similar to schedulingsReorganizeOrdIdx but limited to schedulings that belong to
     * groups managed by the specified codec. This provides codec-specific reorganization
     * while maintaining system-wide consistency for related schedulings.
     * 
     * Also updates device reference ord_idx values (waterpump_ord_idx, ferti_ord_idx,
     * backwash_ord_idx) to match current device ord_idx values.
     * 
     * @param codecIdx Database ID of the codec whose schedulings should be reorganized
     */
    fun schedulingsReorganizeOrdIdxByCodec(codecIdx: Int) {
        val db = this.writableDatabase
        var cursor: Cursor? = null
        try {
            cursor = db.rawQuery(
                """
            SELECT S.idx, S.waterpump_idx, S.ferti_idx, S.backwash_idx 
            FROM $TABLE_SCHEDULINGS S
            INNER JOIN $TABLE_GROUPS G ON S.group_idx = G.idx
            WHERE G.codec_idx = ?
            ORDER BY S.idx ASC
            """,
                arrayOf(codecIdx.toString())
            )

            var ordIdx = 0
            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
                val waterpumpIdx = cursor.getIntOrNull(cursor.getColumnIndexOrThrow("waterpump_idx"))
                val fertiIdx = cursor.getIntOrNull(cursor.getColumnIndexOrThrow("ferti_idx"))
                val backwashIdx = cursor.getIntOrNull(cursor.getColumnIndexOrThrow("backwash_idx"))

                val values = ContentValues().apply {
                    put("ord_idx", ordIdx)

                    waterpumpIdx?.let {
                        val waterpumpCursor = db.rawQuery(
                            """
                        SELECT D.ord_idx 
                        FROM $TABLE_DEVICES D
                        INNER JOIN $TABLE_MESH_DEVICES M ON D.mesh_idx = M.idx
                        WHERE D.idx = ? AND M.codec_idx = ?
                        """,
                            arrayOf(it.toString(), codecIdx.toString())
                        )
                        if (waterpumpCursor.moveToFirst()) {
                            put("waterpump_ord_idx", waterpumpCursor.getInt(waterpumpCursor.getColumnIndexOrThrow("ord_idx")))
                        }
                        waterpumpCursor.close()
                    }

                    fertiIdx?.let {
                        val fertiCursor = db.rawQuery(
                            """
                        SELECT D.ord_idx 
                        FROM $TABLE_DEVICES D
                        INNER JOIN $TABLE_MESH_DEVICES M ON D.mesh_idx = M.idx
                        WHERE D.idx = ? AND M.codec_idx = ?
                        """,
                            arrayOf(it.toString(), codecIdx.toString())
                        )
                        if (fertiCursor.moveToFirst()) {
                            put("ferti_ord_idx", fertiCursor.getInt(fertiCursor.getColumnIndexOrThrow("ord_idx")))
                        }
                        fertiCursor.close()
                    }

                    backwashIdx?.let {
                        val backwashCursor = db.rawQuery(
                            """
                        SELECT D.ord_idx 
                        FROM $TABLE_DEVICES D
                        INNER JOIN $TABLE_MESH_DEVICES M ON D.mesh_idx = M.idx
                        WHERE D.idx = ? AND M.codec_idx = ?
                        """,
                            arrayOf(it.toString(), codecIdx.toString())
                        )
                        if (backwashCursor.moveToFirst()) {
                            put("backwash_ord_idx", backwashCursor.getInt(backwashCursor.getColumnIndexOrThrow("ord_idx")))
                        }
                        backwashCursor.close()
                    }
                }

                db.update(TABLE_SCHEDULINGS, values, "idx = ?", arrayOf(idx.toString()))
                ordIdx++
            }
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Reorganizes ord_idx values for all device schedulings in the database.
     * 
     * Assigns sequential ord_idx values (starting from 0) to all device scheduling
     * records, ordered by their database idx. This ensures consistent ordering
     * for device scheduling execution and communication protocols.
     * 
     * This method is typically called during system maintenance or after bulk
     * operations that may have disrupted the ord_idx sequence.
     */
    fun devicesSchedulingsReorganizeOrdIdx() {
        val db = this.writableDatabase
        var cursor: Cursor? = null
        try {
            cursor = db.rawQuery("SELECT idx FROM $TABLE_DEVICE_SCHEDULINGS ORDER BY idx ASC", null)

            var ordIdx = 0
            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))

                val values = ContentValues().apply {
                    put("ord_idx", ordIdx)
                }
                db.update(TABLE_DEVICE_SCHEDULINGS, values, "idx = ?", arrayOf(idx.toString()))

                ordIdx++
            }
        } catch (e: SQLException) {
            e.printStackTrace()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Reorganizes device scheduling ord_idx values for schedulings belonging to a specific codec.
     * 
     * Similar to devicesSchedulingsReorganizeOrdIdx but limited to device schedulings
     * that belong to schedulings managed by groups under the specified codec.
     * This provides codec-specific reorganization while maintaining consistency
     * for related device scheduling operations.
     * 
     * @param codecIdx Database ID of the codec whose device schedulings should be reorganized
     */
    fun devicesSchedulingsReorganizeOrdIdxByCodec(codecIdx: Int) {
        val db = this.writableDatabase
        var cursor: Cursor? = null
        try {
            cursor = db.rawQuery(
                """
            SELECT DS.idx 
            FROM $TABLE_DEVICE_SCHEDULINGS DS
            INNER JOIN $TABLE_SCHEDULINGS S ON DS.scheduling_idx = S.idx
            INNER JOIN $TABLE_GROUPS G ON S.group_idx = G.idx
            WHERE G.codec_idx = ?
            ORDER BY DS.idx ASC
            """,
                arrayOf(codecIdx.toString())
            )

            var ordIdx = 0
            while (cursor.moveToNext()) {
                val idx = cursor.getInt(cursor.getColumnIndexOrThrow("idx"))

                val values = ContentValues().apply {
                    put("ord_idx", ordIdx)
                }
                db.update(TABLE_DEVICE_SCHEDULINGS, values, "idx = ?", arrayOf(idx.toString()))

                ordIdx++
            }
        } catch (e: SQLException) {
            e.printStackTrace()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Checks if a codec identity already exists in the database.
     * 
     * Verifies whether a codec with the specified identity string is already
     * registered in the system. This is useful for preventing duplicate codec
     * registrations and validating codec identity uniqueness.
     * 
     * @param identity The codec identity string to check for existence
     * @return true if a codec with this identity exists, false otherwise or on error
     */
    fun existsCodecIdentity(identity: String): Boolean {
        return try {
            val db = this.readableDatabase
            val cursor: Cursor = db.rawQuery(
                "SELECT 1 FROM $TABLE_CODECS WHERE identity = ? LIMIT 1",
                arrayOf(identity)
            )
            val exists = cursor.moveToFirst()
            cursor.close()
            exists
        } catch (e: SQLException) {
            e.printStackTrace()
            false
        }
    }

    /**
     * Retrieves the database ID of a fertilizer device within a specific group.
     * 
     * Searches for a device of type Ferti (fertilizer) within the specified group.
     * This is useful for identifying the fertilizer device that should be used
     * for fertilizer operations within a particular irrigation group.
     * 
     * @param groupIdx Database ID of the group to search within
     * @return Database ID of the fertilizer device if found, null otherwise or on error
     */
    fun getFertiDeviceIdxByGroup(groupIdx: Int): Int? {
        val type = MeshDeviceFragment.DevType.Ferti.value
        return try {
            val db = this.readableDatabase
            val cursor: Cursor = db.rawQuery(
                """
                SELECT d.idx
                FROM $TABLE_DEVICES d
                JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
                WHERE m.group_idx = ? AND d.type = ?
                LIMIT 1
                """.trimIndent(),
                arrayOf(groupIdx.toString(), type.toString())
            )
            val idx: Int? = if (cursor.moveToFirst()) cursor.getInt(0) else null
            cursor.close()
            idx
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        }
    }

    /**
     * Retrieves the database ID of a backwash device within a specific group.
     * 
     * Searches for a device of type Backwash within the specified group.
     * Backwash devices are used for cleaning irrigation filters and maintaining
     * system cleanliness. This method helps identify which device should handle
     * backwash operations for a particular irrigation group.
     * 
     * @param groupIdx Database ID of the group to search within
     * @return Database ID of the backwash device if found, null otherwise or on error
     */
    fun getDeviceIdxByMeshWithBackwash(groupIdx: Int): Int? {
        val type = MeshDeviceFragment.DevType.Backwash.value
        return try {
            val db = this.readableDatabase
            val cursor: Cursor = db.rawQuery(
                """
            SELECT d.idx
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            WHERE m.group_idx = ? AND d.type = ?
            LIMIT 1
            """.trimIndent(),
                arrayOf(groupIdx.toString(), type.toString())
            )

            val idx: Int? = if (cursor.moveToFirst()) {
                cursor.getInt(cursor.getColumnIndexOrThrow("idx"))
            } else {
                null
            }

            cursor.close()
            idx
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        }
    }

    /**
     * Retrieves level pump device configurations for a specific codec.
     * 
     * Returns information about level pump devices and their associated mesh devices
     * within the specified codec. Level pumps are used to maintain water levels in
     * irrigation systems. The method returns compact data using abbreviated keys
     * for efficient communication protocols.
     * 
     * Only returns devices where level pump functionality is enabled (level_pump_enable = 1)
     * and the mesh device type is Level (typically used for level monitoring).
     * 
     * @param codecIdx Database ID of the codec to get level pump devices for
     * @return List of maps with keys: "li" (device_ord_idx), "pi" (level_pump_ord_idx), 
     *         "wt" (level_pump_working_time). Empty list on error.
     */
    fun getLevelPumpDevices(codecIdx: Int): MutableList<MutableMap<String, Any>> {
        val db = this.readableDatabase
        val deviceList = mutableListOf<MutableMap<String, Any>>()
        var cursor: Cursor? = null
        val type = MeshDeviceFragment.MainType.Level.value
        return try {
            val query = """
            SELECT
                d.idx                AS device_id,
                d.mesh_idx           AS device_mesh_idx,
                d.identity           AS device_identity,
                d.type               AS device_type,
                d.ord_idx            AS device_ord_idx,
                dp.ord_idx           AS level_pump_ord_idx,
                md.idx               AS mesh_id,
                md.identity          AS mesh_identity,
                md.level_pump_idx    AS mesh_level_pump_idx,
                md.level_pump_working_time AS level_pump_working_time,
                md.name              AS mesh_name
            FROM Mesh_Devices md
            JOIN Devices d 
                ON d.mesh_idx = md.idx
            LEFT JOIN Devices dp
                ON dp.idx = md.level_pump_idx
            WHERE md.type = ? AND md.level_pump_enable = 1 AND md.codec_idx = ?
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(type.toString(), codecIdx.toString()))

            if (cursor.moveToFirst()) {
                do {
                    val item = mutableMapOf<String, Any>()

                    item["li"] = cursor.getInt(cursor.getColumnIndexOrThrow("device_ord_idx"))
                    item["pi"] = cursor.getInt(cursor.getColumnIndexOrThrow("level_pump_ord_idx"))
                    item["wt"] = cursor.getInt(cursor.getColumnIndexOrThrow("level_pump_working_time"))

                    deviceList.add(item)
                } while (cursor.moveToNext())
            }

            deviceList
        } catch (e: SQLException) {
            e.printStackTrace()
            mutableListOf()
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves the total count of schedulings in the database.
     * 
     * Returns the number of scheduling records currently stored in the database.
     * This is useful for system statistics, capacity planning, and user interface
     * displays showing system utilization.
     * 
     * @return Total number of schedulings, or 0 on error
     */
    fun getSchedulingsCount(): Int {
        return try {
            val db = this.readableDatabase
            val cursor = db.rawQuery("SELECT COUNT(*) FROM $TABLE_SCHEDULINGS", null)
            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    /**
     * Retrieves the count of groups belonging to enabled codecs.
     * 
     * Returns the number of groups that are associated with enabled codecs.
     * This provides an active group count, excluding groups that belong to
     * disabled codecs and are therefore not operational.
     * 
     * @return Number of groups with enabled codecs, or 0 on error
     */
    fun getGroupsCount(): Int {
        return try {
            val db = this.readableDatabase
            val cursor = db.rawQuery("""
            SELECT COUNT(*)
            FROM $TABLE_GROUPS g
            JOIN $TABLE_CODECS c ON g.codec_idx = c.idx
            WHERE c.enabled = 1
        """.trimIndent(), null)

            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    /**
     * Retrieves the count of sector devices belonging to enabled codecs.
     * 
     * Returns the number of sector devices (type = 0) that are associated with
     * enabled codecs. Sectors represent irrigation zones or areas that can be
     * individually controlled. Only counts sectors from active codecs.
     * 
     * @return Number of active sector devices, or 0 on error
     */
    fun getSectorsCount(): Int {
        return try {
            val db = this.readableDatabase
            val cursor = db.rawQuery("""
            SELECT COUNT(*)
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            JOIN $TABLE_CODECS c ON m.codec_idx = c.idx
            WHERE d.type = 0 AND c.enabled = 1
        """.trimIndent(), null)

            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    /**
     * Retrieves the total count of devices belonging to enabled codecs.
     * 
     * Returns the number of all devices (regardless of type) that are associated
     * with enabled codecs. This provides the total active device count across the
     * entire irrigation system, excluding devices from disabled codecs.
     * 
     * @return Total number of active devices, or 0 on error
     */
    fun getDevicesCount(): Int {
        return try {
            val db = this.readableDatabase
            val cursor = db.rawQuery("""
            SELECT COUNT(*)
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            JOIN $TABLE_CODECS c ON m.codec_idx = c.idx
            WHERE c.enabled = 1
        """.trimIndent(), null)

            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    fun getDevicesCountByCodec(codecIdx: Int): Int {
        return try {
            val db = this.readableDatabase
            val cursor = db.rawQuery("""
            SELECT COUNT(*)
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            JOIN $TABLE_CODECS c ON m.codec_idx = c.idx
            WHERE c.enabled = 1 AND c.idx = ?
        """.trimIndent(), arrayOf(codecIdx.toString()))

            var count = 0
            if (cursor.moveToFirst()) {
                count = cursor.getInt(0)
            }
            cursor.close()
            count
        } catch (e: SQLException) {
            e.printStackTrace()
            0
        }
    }

    /**
     * Retrieves the codec ID associated with a scheduling.
     * 
     * Traverses the relationship from scheduling → group → codec to find the codec
     * that manages the specified scheduling. This is essential for determining which
     * communication hub should handle commands related to a specific scheduling.
     * 
     * @param schedulingIdx Database ID of the scheduling
     * @return The codec database ID if found, null if scheduling doesn't exist or on error
     */
    fun getCodecIdxByScheduling(schedulingIdx: Int): Int? {
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT c.idx AS codec_idx
            FROM $TABLE_SCHEDULINGS s
            JOIN $TABLE_GROUPS g ON s.group_idx = g.idx
            JOIN $TABLE_CODECS c ON g.codec_idx = c.idx
            WHERE s.idx = ?
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(schedulingIdx.toString()))
            if (cursor.moveToFirst()) {
                cursor.getInt(cursor.getColumnIndexOrThrow("codec_idx"))
            } else {
                null
            }
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves the codec ID associated with a group.
     * 
     * Returns the codec that manages the specified group. Groups are logical
     * collections of mesh devices that are controlled by a single codec.
     * This relationship is fundamental to the system's hierarchical structure.
     * 
     * @param groupIdx Database ID of the group
     * @return The codec database ID if found, null if group doesn't exist or on error
     */
    fun getCodecIdxByGroup(groupIdx: Int): Int? {
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT c.idx AS codec_idx
            FROM $TABLE_GROUPS g
            JOIN $TABLE_CODECS c ON g.codec_idx = c.idx
            WHERE g.idx = ?
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(groupIdx.toString()))
            if (cursor.moveToFirst()) {
                cursor.getInt(cursor.getColumnIndexOrThrow("codec_idx"))
            } else {
                null
            }
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Retrieves the codec ID associated with a device.
     * 
     * Traverses the relationship from device → mesh device → codec to find the codec
     * that manages the specified device. This is crucial for routing device commands
     * to the correct communication hub and understanding device hierarchy.
     * 
     * @param deviceIdx Database ID of the device
     * @return The codec database ID if found, null if device doesn't exist or on error
     */
    fun getCodecIdxByDevice(deviceIdx: Int): Int? {
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT c.idx AS codec_idx
            FROM $TABLE_DEVICES d
            JOIN $TABLE_MESH_DEVICES m ON d.mesh_idx = m.idx
            JOIN $TABLE_CODECS c ON m.codec_idx = c.idx
            WHERE d.idx = ?
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(deviceIdx.toString()))

            if (cursor.moveToFirst()) {
                cursor.getInt(cursor.getColumnIndexOrThrow("codec_idx"))
            } else {
                null
            }
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Checks if level pump functionality is enabled for a specific device.
     * 
     * Determines whether the level pump feature is enabled for the mesh device
     * that uses the specified device as its level pump. This is used to verify
     * if automatic level management is active for a particular pump device.
     * 
     * @param deviceIdx Database ID of the device that serves as a level pump
     * @return true if level pump is enabled, false if disabled, null if device not found or on error
     */
    fun getLevelPumpEnableByDevice(deviceIdx: Int): Boolean? {
        val db = this.readableDatabase
        var cursor: Cursor? = null

        return try {
            val query = """
            SELECT level_pump_enable 
            FROM $TABLE_MESH_DEVICES 
            WHERE level_pump_idx = ?
        """.trimIndent()

            cursor = db.rawQuery(query, arrayOf(deviceIdx.toString()))

            if (cursor.moveToFirst()) {
                cursor.getInt(cursor.getColumnIndexOrThrow("level_pump_enable")) == 1
            } else {
                null
            }
        } catch (e: SQLException) {
            e.printStackTrace()
            null
        } finally {
            cursor?.close()
            db.close()
        }
    }

    /**
     * Updates the level pump enable status for a specific device.
     * 
     * Enables or disables level pump functionality for the mesh device that uses
     * the specified device as its level pump. This controls whether automatic
     * level management will be active for the associated mesh device.
     * 
     * @param deviceIdx Database ID of the device that serves as a level pump
     * @param enabled true to enable level pump functionality, false to disable
     * @return Number of rows updated (should be 1 if successful), -1 on error
     */
    fun updateLevelPumpEnableByDevice(deviceIdx: Int, enabled: Boolean): Int {
        val db = this.writableDatabase
        return try {
            val values = ContentValues().apply {
                put("level_pump_enable", if (enabled) 1 else 0)
            }

            db.update(
                TABLE_MESH_DEVICES,
                values,
                "level_pump_idx = ?",
                arrayOf(deviceIdx.toString())
            )
        } catch (e: SQLException) {
            e.printStackTrace()
            -1
        } finally {
            db.close()
        }
    }

    /**
     * Checks if a time interval conflicts with existing schedulings in a group.
     * 
     * Validates whether a proposed scheduling time interval overlaps with any existing
     * schedulings for the same group and days of the week. This prevents scheduling
     * conflicts that could cause irrigation system malfunctions or resource conflicts.
     * 
     * The method checks for overlaps by testing if either the start or end time of the
     * proposed interval falls within any existing scheduling's time range, and if the
     * days of the week have any common bits set (using bitwise AND operation).
     * 
     * @param schedulingIdxToIgnore Database ID of a scheduling to exclude from conflict checking
     *                              (used when updating an existing scheduling)
     * @param groupIdx Database ID of the group to check conflicts within
     * @param startTime Start time of the proposed interval (in minutes from midnight)
     * @param endTime End time of the proposed interval (in minutes from midnight)
     * @param daysOfWeek Bitmask representing days of the week (bit 0=Sunday, bit 1=Monday, etc.)
     * @return true if the time interval conflicts with existing schedulings, false otherwise
     */
    fun isTimeInsideExistingInterval(schedulingIdxToIgnore: Int, groupIdx: Int, startTime: Int, endTime: Int, daysOfWeek: Int): Boolean {
        val db = this.readableDatabase

        val query = """
        SELECT COUNT(*) FROM $TABLE_SCHEDULINGS
        WHERE group_idx = ?
          AND (
              (? >= start_time AND ? <= end_time)
              OR
              (? >= start_time AND ? <= end_time)
          )
          AND (days_of_week & ?) != 0 AND idx != ?
    """.trimIndent()

        val cursor = db.rawQuery(query, arrayOf(
            groupIdx.toString(),
            startTime.toString(), startTime.toString(),
            endTime.toString(), endTime.toString(),
            daysOfWeek.toString(), schedulingIdxToIgnore.toString()
        ))

        cursor.use {
            if (it.moveToFirst()) {
                val count = it.getInt(0)
                return count > 0
            }
        }

        return false
    }

    // ==============================================
    // UTILITY EXTENSION METHODS
    // ==============================================

    /**
     * Extension method for Cursor to safely get nullable integer values.
     * 
     * This method checks if the column value is NULL in the database before
     * attempting to retrieve it as an integer. This prevents crashes when
     * dealing with nullable integer columns.
     * 
     * @param columnIndex The zero-based index of the target column
     * @return The integer value if not NULL, null otherwise
     */
    fun Cursor.getIntOrNull(columnIndex: Int): Int? {
        return if (isNull(columnIndex)) null else getInt(columnIndex)
    }
}