syntax = "proto3";

package codec.in.devices;

message DevicesData {
  int32 idx              = 1;  // index in the array (device slot)
  int32 mesh_id          = 2;  // mesh ID
  int32 device_id        = 3;  // device ID
  int32 device_type      = 4;  // tipo de dispositivo
  int32 out1             = 5;  // saída 1
  int32 out2             = 6;  // saída 2
  int32 input            = 7;  // entrada
  int32 mode             = 8;  // modo de operação
  int32 sector           = 9;  // setor
  int32 group_idx        = 10; // grupo
  int32 power            = 11; // potência se estiver usando inversor
  int32 equipment        = 12; // tipo de equipamento
}

message DevicesPackage {
  repeated DevicesData data = 1;      // list of mesh devices
}
