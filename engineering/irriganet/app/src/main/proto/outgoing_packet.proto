syntax = "proto3";

import "info.proto";
import "status.proto";
import "scheduling_report.proto";
import "automation_report.proto";
import "ack.proto";
import "raw.proto";

package codec.out;

message OutgoingPacket {
  uint64 id = 1;
  oneof payload {
    codec.out.info.InfoPackage info = 2;
    codec.out.status.SystemStatusPackage status = 3;
    codec.out.scheduling_report.SchedulingReportPackage scheduling_report = 4;
    codec.out.automation_report.AutomationReportPackage automation_report = 5;
    codec.out.ack.AckPackage ack = 6;
    codec.out.raw.RawPackage raw = 7;
  }
}