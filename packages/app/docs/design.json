{"designSystem": {"name": "Mobile App Design System", "version": "1.0.0", "colorPalette": {"primary": {"50": "#f0f9f0", "100": "#dcf2dc", "200": "#bbe5bb", "300": "#8fd68f", "400": "#5cbd5c", "500": "#3ba73b", "600": "#2d8a2d", "700": "#256d25", "800": "#1e5a1e", "900": "#194719"}, "neutral": {"50": "#f9f9f9", "100": "#f0f0f0", "200": "#e4e4e4", "300": "#d1d1d1", "400": "#b4b4b4", "500": "#9a9a9a", "600": "#818181", "700": "#6a6a6a", "800": "#5a5a5a", "900": "#4a4a4a"}, "semantic": {"success": "#10b981", "warning": "#f59e0b", "error": "#ef4444", "info": "#3b82f6"}, "background": {"primary": "#ffffff", "secondary": "#f8f9fa", "accent": "#f0f9f0"}}, "typography": {"fontFamily": {"primary": "Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"}, "fontSize": {"xs": "12px", "sm": "14px", "base": "16px", "lg": "18px", "xl": "20px", "2xl": "24px", "3xl": "30px", "4xl": "36px"}, "fontWeight": {"light": 300, "normal": 400, "medium": 500, "semibold": 600, "bold": 700}, "lineHeight": {"tight": 1.2, "normal": 1.5, "relaxed": 1.75}}, "spacing": {"xs": "4px", "sm": "8px", "md": "16px", "lg": "24px", "xl": "32px", "2xl": "48px", "3xl": "64px"}, "borderRadius": {"sm": "4px", "md": "8px", "lg": "12px", "xl": "16px", "2xl": "24px", "full": "9999px"}, "shadows": {"sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"}, "components": {"button": {"primary": {"backgroundColor": "primary.500", "color": "white", "borderRadius": "lg", "padding": "md lg", "fontSize": "base", "fontWeight": "medium", "minHeight": "48px", "states": {"hover": {"backgroundColor": "primary.600"}, "active": {"backgroundColor": "primary.700"}, "disabled": {"backgroundColor": "neutral.300", "color": "neutral.500"}}}, "secondary": {"backgroundColor": "transparent", "color": "primary.500", "border": "1px solid primary.500", "borderRadius": "lg", "padding": "md lg", "fontSize": "base", "fontWeight": "medium", "minHeight": "48px"}, "ghost": {"backgroundColor": "transparent", "color": "primary.500", "borderRadius": "lg", "padding": "md lg", "fontSize": "base", "fontWeight": "medium"}}, "card": {"default": {"backgroundColor": "background.primary", "borderRadius": "xl", "padding": "lg", "shadow": "md", "border": "1px solid neutral.100"}, "elevated": {"backgroundColor": "background.primary", "borderRadius": "xl", "padding": "lg", "shadow": "lg"}}, "input": {"default": {"backgroundColor": "background.primary", "border": "1px solid neutral.200", "borderRadius": "lg", "padding": "md", "fontSize": "base", "minHeight": "48px", "states": {"focus": {"borderColor": "primary.500", "boxShadow": "0 0 0 3px rgba(59, 167, 59, 0.1)"}, "error": {"borderColor": "semantic.error"}}}}, "navigation": {"header": {"backgroundColor": "background.primary", "borderBottom": "1px solid neutral.100", "padding": "md lg", "minHeight": "56px"}, "tabBar": {"backgroundColor": "background.primary", "borderTop": "1px solid neutral.100", "padding": "sm lg", "minHeight": "60px"}}, "list": {"item": {"padding": "md lg", "borderBottom": "1px solid neutral.100", "backgroundColor": "background.primary", "states": {"hover": {"backgroundColor": "background.secondary"}}}}, "icon": {"sizes": {"sm": "16px", "md": "20px", "lg": "24px", "xl": "32px"}, "colors": {"primary": "primary.500", "secondary": "neutral.500", "accent": "primary.600"}}, "badge": {"default": {"backgroundColor": "primary.100", "color": "primary.700", "borderRadius": "full", "padding": "xs sm", "fontSize": "xs", "fontWeight": "medium"}, "success": {"backgroundColor": "semantic.success", "color": "white", "borderRadius": "full", "padding": "xs sm", "fontSize": "xs", "fontWeight": "medium"}}, "progressIndicator": {"linear": {"backgroundColor": "neutral.200", "height": "4px", "borderRadius": "full", "fill": {"backgroundColor": "primary.500", "borderRadius": "full"}}, "circular": {"size": "24px", "strokeWidth": "2px", "color": "primary.500"}}}, "layout": {"container": {"maxWidth": "100%", "padding": "0 lg", "margin": "0 auto"}, "grid": {"columns": 12, "gap": "md"}, "section": {"padding": "xl 0"}}, "breakpoints": {"mobile": "375px", "tablet": "768px", "desktop": "1024px"}, "animations": {"duration": {"fast": "150ms", "normal": "300ms", "slow": "500ms"}, "easing": {"easeInOut": "cubic-bezier(0.4, 0, 0.2, 1)", "easeOut": "cubic-bezier(0, 0, 0.2, 1)", "easeIn": "cubic-bezier(0.4, 0, 1, 1)"}}, "patterns": {"screenLayout": {"structure": ["header", "content", "footer"], "contentPadding": "md lg"}, "formLayout": {"fieldSpacing": "lg", "labelSpacing": "sm", "buttonSpacing": "xl"}, "cardGrid": {"gap": "md", "columnsPerRow": {"mobile": 1, "tablet": 2, "desktop": 3}}, "listLayout": {"itemSpacing": "0", "groupSpacing": "lg", "iconSpacing": "md"}}, "states": {"loading": {"opacity": 0.6, "cursor": "wait"}, "disabled": {"opacity": 0.4, "cursor": "not-allowed"}, "interactive": {"cursor": "pointer", "transition": "all 150ms ease"}}}}