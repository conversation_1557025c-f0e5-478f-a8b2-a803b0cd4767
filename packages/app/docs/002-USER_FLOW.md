# Irriga+ App User Flow

## Overview

This document outlines the complete user flow for the Irriga+ irrigation management application, detailing all user pathways from initial login through irrigation system configuration and control.

## 1. Application Entry & Authentication

### 1.1 Start

- **Entry Point**: User launches the Irriga+ app
- **Initial Action**: Directed to login screen

### 1.2 Login Screen

- **Components**:
  - Username field
  - Password field
  - Submit button
- **Action**: User enters credentials and submits
- **Next Step**: Account Check process

### 1.3 Account Check Decision Point

The system validates user credentials and determines account status:

**Path A: No Account Available**

- **Trigger**: Invalid credentials or no associated account
- **Destination**: "No Account" error page
- **User Action**: Must resolve account issues

**Path B: Success with One Account**

- **Trigger**: Valid credentials with single account
- **Destination**: Direct to Property Check

**Path C: Success with Many Accounts**

- **Trigger**: Valid credentials with multiple accounts
- **Destination**: Accounts selection page

## 2. Account Management

### 2.1 Multiple Accounts Flow

- **Screen**: Accounts listing page
- **Components**:
  - List of available accounts
  - Account selection interface
- **User Action**: Select desired account
- **Next Step**: Property Check for selected account

### 2.2 Single Account Flow

- **Process**: Automatic account selection
- **Next Step**: Direct to Property Check

## 3. Property Management

### 3.1 Property Check Decision Point

System evaluates properties associated with selected account:

**Path A: No Property**

- **Trigger**: Account has no associated properties
- **Destination**: "No Property" page
- **Available Action**: "Create Property" button

**Path B: One Property**

- **Trigger**: Account has single property
- **Destination**: Direct to property details view

**Path C: Many Properties**

- **Trigger**: Account has multiple properties
- **Destination**: Properties listing page

### 3.2 Property Creation Flow

**Triggered from**: "No Property" page → "Create Property" button

**New Property Form Components**:

- Name field
- Address/Location field
- Additional property details
- Save action
- **Next Step**: Property created and selected

### 3.3 Property Selection Flow

**Properties Page Components**:

- List of available properties
- Property selection interface
- **User Action**: Select desired property
- **Next Step**: Property details view

## 4. Property Details & Device Management

### 4.1 Property Overview

**Screen Components**:

- Property title/header
- Property information display
- Navigation elements (Home, Properties, Devices)
- Device listings and controls

### 4.2 Device Listing

**Components**:

- List of irrigation devices
- Device status indicators
- Device selection options
- Search functionality

### 4.3 Device Details View

**Screen Features**:

- Device-specific information
- Control interfaces
- Configuration options
- Status monitoring

## 5. Irrigation Control Interfaces

### 5.1 Temperature Monitoring

**Dashboard Components**:

- Temperature display (25°C shown in example)
- Environmental monitoring
- Status indicators
- Navigation controls (Home, Properties, Devices)

### 5.2 Irrigation System Control

**Control Interface**:

- System status display
- Manual control options
- Scheduling interface
- Field/zone management

## 6. Advanced Configuration

### 6.1 Field Configuration

**Multi-level Configuration Screens**:

**Initial Field Setup**:

- Field selection interface
- Basic field parameters
- Navigation to detailed configuration

**Detailed Field Configuration**:

- Multiple configuration sections
- Field-specific parameters
- Save/Cancel options
- Advanced settings access

### 6.2 Sector Management

**Sector Configuration Flow**:

**Sector Overview**:

- List of irrigation sectors
- Sector status display
- Individual sector access

**Individual Sector Configuration**:

- Sector-specific settings
- Timer configuration
- Duration controls
- Operational parameters

**Sector Details Dialog**:

- Comprehensive sector settings
- Multiple configuration sections (Sectors 1, 2, 3, etc.)
- Duration settings (30 min example shown)
- Timing field options
- Advanced sector controls

### 6.3 Duration & Timing Controls

**Duration Configuration**:

- Time duration settings
- Sector-specific timing
- Schedule management
- Save/Cancel options

**Advanced Timing Options**:

- Multiple sector coordination
- Sequential timing controls
- Override capabilities
- Status monitoring

## 7. System Navigation & Controls

### 7.1 Global Navigation

**Consistent Navigation Elements**:

- Home button (house icon)
- Properties section (building icon)
- Devices section (device icon)
- Context-specific actions

### 7.2 Action Controls

**Standard Interface Elements**:

- Save buttons (green)
- Cancel buttons (red/gray)
- Edit capabilities
- Status indicators
- Confirmation dialogs

### 7.3 Data Persistence

**Save/Cancel Pattern**:

- All configuration screens include save/cancel options
- Changes require explicit user confirmation
- Cancel option preserves previous settings
- Success feedback provided

## 8. Error Handling & Edge Cases

### 8.1 No Account Scenario

- Clear error messaging
- Path to account resolution
- Support contact options

### 8.2 No Property Scenario

- Guided property creation flow
- Clear instructions for setup
- Minimal friction property addition

### 8.3 Configuration Validation

- Real-time validation feedback
- Error prevention mechanisms
- Clear correction guidance

## 9. User Experience Flow Summary

The complete user journey follows this hierarchical structure:

1. **Authentication** → Account validation and selection
2. **Property Management** → Property selection or creation
3. **Device Access** → Irrigation system identification
4. **Control Interface** → Basic irrigation controls
5. **Advanced Configuration** → Detailed system setup
6. **Operational Management** → Ongoing irrigation control

Each level provides appropriate fallback options and error handling to ensure users can successfully navigate to functional irrigation control regardless of their account/property configuration state.
