# Account User Tree Debouncing

## Overview

The `apiService.account.getAccountUserTree` API call has been enhanced with a debouncing mechanism to prevent excessive requests when the account user ID changes rapidly. This is particularly important in scenarios where multiple components might trigger data fetching in quick succession.

## Implementation Details

### Debounce Configuration

- **Delay**: 300ms (configurable via `ACCOUNT_USER_TREE_DEBOUNCE_DELAY`)
- **Location**: `app/src/store/data.ts`

### Key Features

1. **Request Debouncing**: Multiple rapid calls to `fetchSelectedAccountUserTreeAtom` within 300ms will be debounced, with only the last call being executed.

2. **Account ID Change Handling**: If the account ID changes during the debounce period, the previous request is cancelled and a new one is scheduled.

3. **Duplicate Request Prevention**: If a request for the same account ID is already pending, subsequent requests will wait for the existing one to complete.

4. **Cleanup on Unassign**: When an account is unassigned, any pending debounced requests are automatically cancelled.

### Usage

The debouncing is transparent to consumers of the store. Simply use the existing atoms as before:

```typescript
import { useSet<PERSON>tom } from 'jotai';
import { refetchDataAtom } from '@/store';

const MyComponent = () => {
  const refetchData = useSetAtom(refetchDataAtom);
  
  // This will be automatically debounced
  const handleRefresh = () => {
    refetchData();
  };
  
  return <button onClick={handleRefresh}>Refresh</button>;
};
```

### Debugging

The implementation includes console logging to help debug debouncing behavior:

- Request initiation: `fetchSelectedAccountUserTreeAtom - Requesting data for account {id}`
- Request execution: `fetchSelectedAccountUserTreeAtom - Executing debounced request for account {id}`
- Request completion: `fetchSelectedAccountUserTreeAtom - Successfully loaded data for account {id}`
- Request cancellation: `fetchSelectedAccountUserTreeAtom - Account ID changed during debounce`

### Manual Control

For testing or special scenarios, you can manually clear pending requests:

```typescript
import { useSetAtom } from 'jotai';
import { clearPendingAccountUserTreeRequestsAtom } from '@/store';

const clearPendingRequests = useSetAtom(clearPendingAccountUserTreeRequestsAtom);

// Clear any pending debounced requests
clearPendingRequests();
```

## Benefits

1. **Reduced API Load**: Prevents unnecessary API calls when account selection changes rapidly
2. **Better Performance**: Reduces network traffic and server load
3. **Improved UX**: Prevents loading states from flickering during rapid changes
4. **Resource Efficiency**: Cancels outdated requests automatically

## Configuration

The debounce delay can be adjusted by modifying the `ACCOUNT_USER_TREE_DEBOUNCE_DELAY` constant in `app/src/store/data.ts`. The current value of 300ms provides a good balance between responsiveness and efficiency.
