# Modal Components Documentation

This document describes the reusable modal and notification components created to replace browser-native dialogs (`window.confirm`, `window.alert`, `window.prompt`).

## Components

### 1. ConfirmModal

A specialized modal for confirmation dialogs with customizable variants.

#### Props

```typescript
interface ConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
  variant?: "danger" | "warning" | "info";
  children?: React.ReactNode;
}
```

#### Usage

```tsx
import { ConfirmModal } from "../../components";

function MyComponent() {
  const [showModal, setShowModal] = useState(false);

  const handleConfirm = () => {
    // Handle confirmation
    setShowModal(false);
  };

  return (
    <>
      <button onClick={() => setShowModal(true)}>Delete Item</button>

      <ConfirmModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onConfirm={handleConfirm}
        title="Delete Item"
        message="Are you sure you want to delete this item?"
        confirmText="Delete"
        cancelText="Cancel"
        variant="danger"
      />
    </>
  );
}
```

You can also provide custom content via `children`. When `children` is present it is rendered instead of `message`.

```tsx
<ConfirmModal
  isOpen={showModal}
  onClose={() => setShowModal(false)}
  onConfirm={handleConfirm}
  title="Custom Confirmation"
  confirmText="Proceed"
  cancelText="Back"
>
  <div className="space-y-2">
    <p className="text-gray-700">This action affects multiple items.</p>
    <ul className="list-disc list-inside text-sm text-gray-600">
      <li>Item A</li>
      <li>Item B</li>
    </ul>
  </div>
</ConfirmModal>
```

#### Variants

- **danger**: Red color scheme for destructive actions
- **warning**: Yellow color scheme for important warnings
- **info**: Blue color scheme for informational confirmations

### 2. Modal

A generic modal component for custom content.

#### Props

```typescript
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: "sm" | "md" | "lg" | "xl";
  showCloseButton?: boolean;
  closable?: boolean;
}
```

#### Usage

```tsx
import { Modal } from "../../components";

function MyComponent() {
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <button onClick={() => setShowModal(true)}>Open Modal</button>

      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="Custom Modal"
        size="lg"
      >
        <div className="p-4">
          <p>Custom modal content goes here</p>
          <button
            onClick={() => setShowModal(false)}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded"
          >
            Close
          </button>
        </div>
      </Modal>
    </>
  );
}
```

### 3. Toast Notifications

A centralized toast notification system using global state management for showing alerts and messages throughout the application.

#### Setup

The toast system is automatically set up in the main App component and uses Jotai for state management:

```tsx
// In App.tsx (already configured)
import { ToastContainer } from "./components";

function App() {
  return (
    <StoreProvider>
      {/* Other components */}
      <ToastContainer />
    </StoreProvider>
  );
}
```

#### useToast Hook

```tsx
import { useToast } from "../../components";

function MyComponent() {
  const { showSuccess, showError, showWarning, showInfo, removeAllToasts } =
    useToast();

  const handleSuccess = () => {
    showSuccess({
      title: "Success!",
      message: "Operation completed successfully!",
    });
  };

  const handleError = () => {
    showError({
      title: "Error",
      message: "Something went wrong!",
      duration: 0, // No auto-dismiss
      actionLabel: "Retry",
      onAction: () => {
        // Handle retry logic
      },
    });
  };

  const handleWarning = () => {
    showWarning({
      message: "This is a simple warning message",
    });
  };

  return (
    <div>
      <button onClick={handleSuccess}>Show Success</button>
      <button onClick={handleError}>Show Error</button>
      <button onClick={handleWarning}>Show Warning</button>
      <button onClick={removeAllToasts}>Clear All Toasts</button>
    </div>
  );
}
```

#### Toast Methods

The `useToast` hook provides the following methods:

- `showSuccess(options)`: Display a green success toast
- `showError(options)`: Display a red error toast
- `showWarning(options)`: Display a yellow warning toast
- `showInfo(options)`: Display a blue info toast
- `addToast(toastData)`: Add a custom toast with full control
- `removeToast(id)`: Remove a specific toast by ID
- `removeAllToasts()`: Clear all active toasts

#### Toast Options

```typescript
interface ToastOptions {
  message: string; // Required: Toast message
  title?: string; // Optional: Toast title
  duration?: number; // Auto-dismiss delay in ms, 0 = no auto-dismiss (default: 5000)
  actionLabel?: string; // Optional: Action button text
  onAction?: () => void; // Optional: Action button click handler
}

// Full ToastData interface (for addToast)
interface ToastData {
  id: string;
  title?: string;
  message: string;
  type?: "success" | "error" | "warning" | "info";
  duration?: number;
  actionLabel?: string;
  onAction?: () => void;
}
```

#### Toast Container Configuration

The `ToastContainer` component can be configured with different positions:

```tsx
<ToastContainer position="top-right" /> // Default
<ToastContainer position="top-left" />
<ToastContainer position="top-center" />
<ToastContainer position="bottom-right" />
<ToastContainer position="bottom-left" />
<ToastContainer position="bottom-center" />
```

#### Advanced Examples

```tsx
// Simple message toast
showSuccess({ message: "Saved successfully!" });

// Toast with title and action
showError({
  title: "Connection Failed",
  message: "Unable to connect to the server.",
  actionLabel: "Retry",
  onAction: () => {
    // Retry connection logic
    console.log("Retrying connection...");
  },
});

// Persistent toast (manual dismiss only)
showWarning({
  title: "Important Notice",
  message: "Please review the updated terms of service.",
  duration: 0, // Won't auto-dismiss
  actionLabel: "Review Now",
  onAction: () => {
    // Navigate to terms page
  },
});

// Custom duration toast
showInfo({
  message: "This message will stay for 10 seconds",
  duration: 10000,
});

// Using addToast for full control
const { addToast } = useToast();

const customToast = addToast({
  type: "success",
  title: "Custom Toast",
  message: "This toast was created with addToast",
  duration: 8000,
  actionLabel: "Learn More",
  onAction: () => {
    showInfo({ message: "Action clicked!" });
  },
});
```

## Migration from Browser Dialogs

### window.confirm → ConfirmModal

```tsx
// Before
const confirmDelete = window.confirm("Are you sure?");
if (confirmDelete) {
  deleteItem();
}

// After
const [showConfirm, setShowConfirm] = useState(false);

const handleDelete = () => {
  setShowConfirm(true);
};

const handleConfirm = () => {
  deleteItem();
  setShowConfirm(false);
};

return (
  <>
    <button onClick={handleDelete}>Delete</button>
    <ConfirmModal
      isOpen={showConfirm}
      onClose={() => setShowConfirm(false)}
      onConfirm={handleConfirm}
      message="Are you sure?"
      variant="danger"
    />
  </>
);
```

### window.alert → Toast

```tsx
// Before
window.alert("Success!");

// After
const { showSuccess } = useToast();

const handleSuccess = () => {
  showSuccess({ message: "Success!" });
};

// Or with more details
const handleDetailedSuccess = () => {
  showSuccess({
    title: "Operation Complete",
    message: "Your changes have been saved successfully.",
  });
};
```

### window.prompt → Modal + Form

```tsx
// Before
const name = window.prompt("Enter your name:");
if (name) {
  setUserName(name);
}

// After
const [showPrompt, setShowPrompt] = useState(false);
const [inputValue, setInputValue] = useState("");

const handleSubmit = () => {
  if (inputValue.trim()) {
    setUserName(inputValue);
    setShowPrompt(false);
    setInputValue("");
  }
};

return (
  <>
    <button onClick={() => setShowPrompt(true)}>Enter Name</button>
    <Modal
      isOpen={showPrompt}
      onClose={() => setShowPrompt(false)}
      title="Enter Your Name"
    >
      <div className="p-4">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          className="w-full px-3 py-2 border rounded"
          placeholder="Your name"
        />
        <div className="flex gap-2 mt-4">
          <button
            onClick={() => setShowPrompt(false)}
            className="px-4 py-2 bg-gray-200 rounded"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded"
          >
            Submit
          </button>
        </div>
      </div>
    </Modal>
  </>
);
```

## Features

### Toast System Features

- **Global State Management**: Uses Jotai for centralized toast state
- **No Prop Drilling**: Access toast functionality from any component
- **Auto-Setup**: ToastContainer is configured at the app level
- **Flexible Positioning**: 6 different position options
- **Action Support**: Add interactive buttons to toasts
- **Custom Durations**: Control auto-dismiss timing or make persistent
- **Type Safety**: Full TypeScript support with proper interfaces
- **Performance**: Efficient state updates and minimal re-renders

### Accessibility

- Keyboard navigation (Escape to close)
- Focus management
- ARIA attributes for screen readers

### UX Improvements

- Backdrop click to close (when closable)
- Smooth animations
- Consistent styling
- Mobile responsive
- Customizable variants and sizes

### Developer Experience

- TypeScript support
- Intuitive API
- Reusable components
- Hook-based toast management
- Flexible styling with Tailwind CSS
