// src/store/data.ts
import { apiService } from "@/api";
import type {
  AccountUserTree,
  AccountUserWithAccount,
  AUTAccount,
  AUTDevice,
  AUTIrrigationPlan,
  AUTProject,
  AUTProperty,
  AUTPropertyDevice,
  AUTReservoir,
  AUTWaterPump,
} from "@/api/queries/account";
import type { CurrentProjectState } from "@/api/model/current-project-state";
import type { CurrentIrrigationPlanState } from "@/api/model/current-irrigation-plan-state";
import type { CurrentLICPacket } from "@/api/model/current-lic-packet";
import type { LicState } from "@/api/model/lic-state";
import type { CurrentReservoirState } from "@/api/model/current-reservoir-state";
import { loadCurrentLICPacketsForDevices } from "@/api/queries/current-lic-packet";
import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";
import { withOperationHandlingAtom } from "./operations";
import { TimeoutTimer } from "@/utils/timer";
import { buildRainfallSummary } from "@/utils";

// Debouncing configuration
export const ACCOUNT_USER_TREE_DEBOUNCE_DELAY = 100; // 100ms
// Interval for polling data
const DATA_FETCH_INTERVAL_MILLIS = 20000; // 10s

// Account selection atoms
export const availableAccountsAtom = atom<AccountUserWithAccount[]>([]);
availableAccountsAtom.debugLabel = "availableAccountsAtom";
const internalSelectedAccountUserIdAtom = atomWithStorage<string | null>(
  "selectedAccountUserId",
  null,
  undefined,
  {
    getOnInit: true, // Load from storage on initialization
  }
);
internalSelectedAccountUserIdAtom.debugLabel =
  "internalSelectedAccountUserIdAtom";
export const selectedAccountUserIdAtom = atom(null, (get) => {
  const id = get(internalSelectedAccountUserIdAtom);
  if (!id) {
    console.warn("No account user ID selected");
  }
  return id;
});
selectedAccountUserIdAtom.debugLabel = "selectedAccountUserIdAtom";

export const accountUserTreeAtom = atom<AccountUserTree | null>(null);
accountUserTreeAtom.debugLabel = "accountUserTreeAtom";

// Selection atoms for navigation with persistence
export const selectedPropertyIdAtom = atomWithStorage<string | null>(
  "selectedPropertyId",
  null,
  undefined,
  {
    getOnInit: true, // Load from storage on initialization
  }
);
selectedPropertyIdAtom.debugLabel = "selectedPropertyIdAtom";

// Derived atoms for current selections
export const currentAccountAtom = atom<AUTAccount | null>((get) => {
  const tree = get(accountUserTreeAtom);
  return tree?.account || null;
});
currentAccountAtom.debugLabel = "currentAccountAtom";

export const propertiesAtom = atom<AUTProperty[]>((get) => {
  const account = get(currentAccountAtom);
  return account?.properties || [];
});
propertiesAtom.debugLabel = "propertiesAtom";

export const selectedPropertyAtom = atom<AUTProperty | null>((get) => {
  const properties = get(propertiesAtom);
  const selectedId = get(selectedPropertyIdAtom);
  return properties.find((p) => p.id === selectedId) || null;
});
selectedPropertyAtom.debugLabel = "selectedPropertyAtom";

export const projectsAtom = atom<AUTProject[]>((get) => {
  const property = get(selectedPropertyAtom);
  return property?.projects || [];
});
projectsAtom.debugLabel = "projectsAtom";

export const waterPumpsAtom = atom<AUTWaterPump[]>((get) => {
  const property = get(selectedPropertyAtom);
  return property?.water_pumps || [];
});
waterPumpsAtom.debugLabel = "waterPumpsAtom";

export const reservoirsAtom = atom<AUTReservoir[]>((get) => {
  const property = get(selectedPropertyAtom);
  return property?.reservoirs || [];
});
reservoirsAtom.debugLabel = "reservoirsAtom";

export const devicesAtom = atom<AUTDevice[]>((get) => {
  const property = get(selectedPropertyAtom);
  return property?.devices?.map((pd) => pd.device).filter(Boolean) || [];
});
devicesAtom.debugLabel = "devicesAtom";

export const licDevicesAtom = atom<AUTDevice[]>((get) => {
  const devices = get(devicesAtom);
  return (
    devices
      .filter((d) => d.model === "LIC")
      .map((d) => d)
      .filter(Boolean) || []
  );
});
licDevicesAtom.debugLabel = "licDevicesAtom";

// Enhanced devices atom that includes mesh device mapping information
export const propertyDevicesAtom = atom<AUTPropertyDevice[]>((get) => {
  const property = get(selectedPropertyAtom);
  return property?.devices || [];
});
propertyDevicesAtom.debugLabel = "propertyDevicesAtom";

// -----------------------------------------------------------------------------
// Rainfall aggregation atoms (derived from current LIC status packets)
// -----------------------------------------------------------------------------

export const rainfallDataByPropertyAtom = atom((get) => {
  const packets = get(currentLICPacketsByPropertyAtom);
  const property = get(selectedPropertyAtom);
  const pds = get(propertyDevicesAtom);
  return buildRainfallSummary(packets, property, pds);
});
rainfallDataByPropertyAtom.debugLabel = "rainfallDataByPropertyAtom";

export const licsWithRainGaugeAtom = atom<number>((get) => {
  const rainfall = get(rainfallDataByPropertyAtom);
  return rainfall.gaugeCount;
});
licsWithRainGaugeAtom.debugLabel = "licsWithRainGaugeAtom";

// Device to LIC mapping atom - maps device IDs to their LIC device IDs
export const deviceToLicMapAtom = atom<Map<string, string | null>>((get) => {
  const propertyDevices = get(propertyDevicesAtom);
  const map = new Map<string, string | null>();

  propertyDevices.forEach((pd) => {
    if (pd.device?.id) {
      const licPropertyDeviceId =
        pd.current_mesh_device_mapping?.lic_property_device;

      // Find the LIC property_device using the ID
      let licDeviceId: string | null = null;
      if (licPropertyDeviceId) {
        const licPropertyDevice = propertyDevices.find((lpd) => {
          if (
            typeof licPropertyDeviceId === "object" &&
            licPropertyDeviceId !== null &&
            "id" in licPropertyDeviceId
          ) {
            return lpd.id === (licPropertyDeviceId as { id: string }).id;
          } else if (typeof licPropertyDeviceId === "string") {
            return lpd.id === licPropertyDeviceId;
          }
          return false;
        });

        // Get the device ID from the LIC property_device
        if (licPropertyDevice?.device?.id) {
          licDeviceId = licPropertyDevice.device.id;
        }
      }

      map.set(pd.device.id, licDeviceId);
    }
  });

  return map;
});
deviceToLicMapAtom.debugLabel = "deviceToLicMapAtom";

export const irrigationPlansAtom = atom<AUTIrrigationPlan[]>((get) => {
  const projects = get(projectsAtom);
  return projects.flatMap((project) => project.irrigation_plans || []);
});
irrigationPlansAtom.debugLabel = "irrigationPlansAtom";

// Property finder atom - finds a property by ID from current properties
export const propertyByIdAtom = atom(
  (get) =>
    (propertyId: string): AUTProperty | null => {
      const properties = get(propertiesAtom);
      return properties.find((p) => p.id === propertyId) || null;
    }
);
propertyByIdAtom.debugLabel = "propertyByIdAtom";

// Project finder atom - finds a project by ID from current projects
export const projectByIdAtom = atom(
  (get) =>
    (projectId: string): AUTProject | null => {
      const projects = get(projectsAtom);
      return projects.find((p) => p.id === projectId) || null;
    }
);
projectByIdAtom.debugLabel = "projectByIdAtom";

export const irrigationPlanByIdAtom = atom(
  (get) =>
    (planId: string): AUTIrrigationPlan | null => {
      const plans = get(irrigationPlansAtom);
      return plans.find((p) => p.id === planId) || null;
    }
);
irrigationPlanByIdAtom.debugLabel = "irrigationPlanByIdAtom";

export const devicesByIdAtom = atom(
  (get) =>
    (deviceId: string): AUTDevice | null => {
      const devices = get(devicesAtom);
      return devices.find((d) => d.id === deviceId) || null;
    }
);
devicesByIdAtom.debugLabel = "devicesByIdAtom";

export const waterPumpsByIdAtom = atom(
  (get) =>
    (pumpId: string): AUTWaterPump | null => {
      const pumps = get(waterPumpsAtom);
      return pumps.find((p) => p.id === pumpId) || null;
    }
);
waterPumpsByIdAtom.debugLabel = "waterPumpsByIdAtom";

export const reservoirsByIdAtom = atom(
  (get) =>
    (reservoirId: string): AUTReservoir | null => {
      const reservoirs = get(reservoirsAtom);
      return reservoirs.find((r) => r.id === reservoirId) || null;
    }
);
reservoirsByIdAtom.debugLabel = "reservoirsByIdAtom";

// -----------------------------------------------------------------------------
// Current Reservoir State atoms
// -----------------------------------------------------------------------------

export const currentReservoirStatesAtom = atom<
  CurrentReservoirState<{ reservoir: string }>[]
>([]);
currentReservoirStatesAtom.debugLabel = "currentReservoirStatesAtom";

export const currentReservoirStatesLoadingAtom = atom<boolean>(false);
currentReservoirStatesLoadingAtom.debugLabel =
  "currentReservoirStatesLoadingAtom";
export const currentReservoirStatesErrorAtom = atom<string | null>(null);
currentReservoirStatesErrorAtom.debugLabel = "currentReservoirStatesErrorAtom";

export const currentReservoirStatesByPropertyAtom = atom((get) => {
  const selectedPropertyId = get(selectedPropertyIdAtom);
  const states = get(currentReservoirStatesAtom);
  if (!selectedPropertyId) return [] as CurrentReservoirState[];
  return states as unknown as CurrentReservoirState[];
});
currentReservoirStatesByPropertyAtom.debugLabel =
  "currentReservoirStatesByPropertyAtom";

export const currentReservoirStateByReservoirIdAtom = atom(
  (get) =>
    (
      reservoirId: string
    ): CurrentReservoirState<{ reservoir: string }> | null => {
      const states = get(currentReservoirStatesAtom);
      return (
        states.find(
          (s) => (s.reservoir as unknown as string) === reservoirId
        ) || null
      );
    }
);
currentReservoirStateByReservoirIdAtom.debugLabel =
  "currentReservoirStateByReservoirIdAtom";

export const fetchCurrentReservoirStatesAtom = atom(null, async (get, set) => {
  const selectedPropertyId = get(selectedPropertyIdAtom);
  if (!selectedPropertyId) return;
  await set(
    withOperationHandlingAtom,
    {
      operation: "fetchCurrentReservoirStates",
      message: "Carregando estado atual dos reservatórios...",
      displayLoading: false,
      displayError: true,
    },
    async () => {
      set(currentReservoirStatesLoadingAtom, true);
      set(currentReservoirStatesErrorAtom, null);
      try {
        const states = await apiService.currentReservoirState.getByProperty(
          selectedPropertyId
        );
        set(currentReservoirStatesAtom, states);
        return states;
      } catch (e) {
        const msg = e instanceof Error ? e.message : "Unknown error";
        set(currentReservoirStatesErrorAtom, msg);
        throw e;
      } finally {
        set(currentReservoirStatesLoadingAtom, false);
      }
    }
  );
});
fetchCurrentReservoirStatesAtom.debugLabel = "fetchCurrentReservoirStatesAtom";
fetchCurrentReservoirStatesAtom.onMount = (dispatch) => {
  const timer = new TimeoutTimer();
  timer.start(DATA_FETCH_INTERVAL_MILLIS, async () => {
    await dispatch();
  });
  return () => timer.stop();
};

export const sectorsByProjectIdAtom = atom(
  (get) =>
    (projectId: string): AUTProject["sectors"] => {
      const projects = get(projectsAtom);
      const project = projects.find((p) => p.id === projectId);
      return (
        project?.sectors?.sort((a, b) => a.name.localeCompare(b.name)) || []
      );
    }
);
sectorsByProjectIdAtom.debugLabel = "sectorsByProjectIdAtom";

export const irrigationPlansByProjectIdAtom = atom(
  (get) =>
    (projectId: string): AUTIrrigationPlan[] => {
      const projectById = get(projectByIdAtom);
      const project = projectById(projectId);
      return (
        project?.irrigation_plans?.sort((a, b) =>
          a.start_time.localeCompare(b.start_time)
        ) || []
      );
    }
);
irrigationPlansByProjectIdAtom.debugLabel = "irrigationPlansByProjectIdAtom";

// Current project state atoms
export const currentProjectStatesAtom = atom<
  CurrentProjectState<{ project: string }>[]
>([]);
currentProjectStatesAtom.debugLabel = "currentProjectStatesAtom";
export const fetchCurrentProjectStatesAtom = atom(null, async (get, set) => {
  const selectedPropertyId = get(selectedPropertyIdAtom);
  if (!selectedPropertyId) {
    console.log("fetchCurrentProjectStatesAtom - No property ID selected");
    return;
  }

  console.log(
    `fetchCurrentProjectStatesAtom - Fetching current project states for property ${selectedPropertyId}`
  );
  // TODO: Implement minPacketDate
  const currentProjectStates =
    await apiService.currentProjectState.getByProperty(
      selectedPropertyId,
      null
    );
  console.log(
    `fetchCurrentProjectStatesAtom - Fetched current project states: ${JSON.stringify(
      currentProjectStates
    )}`
  );
  set(currentProjectStatesAtom, currentProjectStates);
});
fetchCurrentProjectStatesAtom.debugLabel = "fetchCurrentProjectStatesAtom";
fetchCurrentProjectStatesAtom.onMount = (dispatch) => {
  // dispatch();
  const timer = new TimeoutTimer();
  console.log("Starting timer");
  timer.start(DATA_FETCH_INTERVAL_MILLIS, async () => {
    await dispatch();
  });
  return () => {
    console.log("Stopping timer");
    timer.stop();
  };
};

export const currentProjectStatesByPropertyAtom = atom<
  CurrentProjectState<{ project: string }>[]
>((get) => {
  const selectedPropertyId = get(selectedPropertyIdAtom);
  const currentProjectStates = get(currentProjectStatesAtom);

  if (!selectedPropertyId) {
    return [];
  }

  return currentProjectStates.filter((state) => {
    // Filter by property through project relationship
    const project = state.project;

    // If project is just an ID, we need to find it in the projects
    const projects = get(projectsAtom);
    const projectObj = projects.find((p) => p.id === project);
    return projectObj?.property === selectedPropertyId;
  });
});
currentProjectStatesByPropertyAtom.debugLabel =
  "currentProjectStatesByPropertyAtom";

export const currentProjectStateByProjectIdAtom = atom(
  (get) =>
    (projectId: string): CurrentProjectState<{ project: string }> | null => {
      const currentProjectStates = get(currentProjectStatesAtom);
      return (
        currentProjectStates.find((state) => {
          const project = state.project;
          return project === projectId;
        }) || null
      );
    }
);
currentProjectStateByProjectIdAtom.debugLabel =
  "currentProjectStateByProjectIdAtom";

// Loading and error states for current project states
export const currentProjectStatesLoadingAtom = atom<boolean>(false);
currentProjectStatesLoadingAtom.debugLabel = "currentProjectStatesLoadingAtom";
export const currentProjectStatesErrorAtom = atom<string | null>(null);
currentProjectStatesErrorAtom.debugLabel = "currentProjectStatesErrorAtom";

// -----------------------------------------------------------------------------
// Current Irrigation Plan State atoms
// -----------------------------------------------------------------------------

// Base atom holding all current irrigation plan states (for the active property)
export const currentIrrigationPlanStatesAtom = atom<
  CurrentIrrigationPlanState<{ irrigation_plan: string }>[]
>([]);
currentIrrigationPlanStatesAtom.debugLabel = "currentIrrigationPlanStatesAtom";

// Loading and error atoms
export const currentIrrigationPlanStatesLoadingAtom = atom<boolean>(false);
currentIrrigationPlanStatesLoadingAtom.debugLabel =
  "currentIrrigationPlanStatesLoadingAtom";
export const currentIrrigationPlanStatesErrorAtom = atom<string | null>(null);
currentIrrigationPlanStatesErrorAtom.debugLabel =
  "currentIrrigationPlanStatesErrorAtom";

// Derived: filter by selected property (through irrigation_plan -> project -> property)
export const currentIrrigationPlanStatesByPropertyAtom = atom((get) => {
  const selectedPropertyId = get(selectedPropertyIdAtom);
  const states = get(currentIrrigationPlanStatesAtom);
  if (!selectedPropertyId) return [] as CurrentIrrigationPlanState[];
  // The fetch already filters by property; simply expose states when a property is selected
  return states as unknown as CurrentIrrigationPlanState[];
});
currentIrrigationPlanStatesByPropertyAtom.debugLabel =
  "currentIrrigationPlanStatesByPropertyAtom";

// Finder: by irrigation plan id
export const currentIrrigationPlanStateByPlanIdAtom = atom(
  (get) =>
    (
      irrigationPlanId: string
    ): CurrentIrrigationPlanState<{ irrigation_plan: string }> | null => {
      const states = get(currentIrrigationPlanStatesAtom);
      return (
        states.find(
          (s) => (s.irrigation_plan as unknown as string) === irrigationPlanId
        ) || null
      );
    }
);
currentIrrigationPlanStateByPlanIdAtom.debugLabel =
  "currentIrrigationPlanStateByPlanIdAtom";

// Fetch with polling for current irrigation plan states
export const fetchCurrentIrrigationPlanStatesAtom = atom(
  null,
  async (get, set) => {
    const selectedPropertyId = get(selectedPropertyIdAtom);
    if (!selectedPropertyId) {
      console.log(
        "fetchCurrentIrrigationPlanStatesAtom - No property ID selected"
      );
      return;
    }

    await set(
      withOperationHandlingAtom,
      {
        operation: "fetchCurrentIrrigationPlanStates",
        message: "Carregando estado atual dos planos de irrigação...",
        displayLoading: false,
        displayError: true,
      },
      async () => {
        set(currentIrrigationPlanStatesLoadingAtom, true);
        set(currentIrrigationPlanStatesErrorAtom, null);
        try {
          const states = await apiService.currentIrrigationPlanState.query({
            fields: ["*"],
            filter: {
              irrigation_plan: {
                project: { property: { _eq: selectedPropertyId } },
              },
            } as any,
            sort: ["-packet_date"],
          });
          set(currentIrrigationPlanStatesAtom, states as any);
          return states;
        } catch (error) {
          const msg = error instanceof Error ? error.message : String(error);
          set(currentIrrigationPlanStatesErrorAtom, msg);
          console.error(
            "Failed to fetch current irrigation plan states:",
            error
          );
          throw error;
        } finally {
          set(currentIrrigationPlanStatesLoadingAtom, false);
        }
      }
    );
  }
);
fetchCurrentIrrigationPlanStatesAtom.debugLabel =
  "fetchCurrentIrrigationPlanStatesAtom";
fetchCurrentIrrigationPlanStatesAtom.onMount = (dispatch) => {
  const timer = new TimeoutTimer();
  timer.start(DATA_FETCH_INTERVAL_MILLIS, async () => {
    await dispatch();
  });
  return () => timer.stop();
};

// -----------------------------------------------------------------------------
// Current LIC Packet atoms
// -----------------------------------------------------------------------------

// Base atom holding all current LIC packets (for devices under the active property)
export const currentLICPacketsAtom = atom<CurrentLICPacket[]>([]);
currentLICPacketsAtom.debugLabel = "currentLICPacketsAtom";

// Loading and error atoms
export const currentLICPacketsLoadingAtom = atom<boolean>(false);
currentLICPacketsLoadingAtom.debugLabel = "currentLICPacketsLoadingAtom";
export const currentLICPacketsErrorAtom = atom<string | null>(null);
currentLICPacketsErrorAtom.debugLabel = "currentLICPacketsErrorAtom";

// Derived: packets for current property (based on LIC devices in property)
export const currentLICPacketsByPropertyAtom = atom<CurrentLICPacket[]>(
  (get) => {
    const packets = get(currentLICPacketsAtom);
    const licDevices = get(licDevicesAtom);
    if (!licDevices || licDevices.length === 0) return [];
    const licIds = new Set(licDevices.map((d) => d.id));
    return packets.filter((p) => licIds.has(p.device as unknown as string));
  }
);
currentLICPacketsByPropertyAtom.debugLabel = "currentLICPacketsByPropertyAtom";

// Finder: by packet id
export const currentLICPacketByIdAtom = atom(
  (get) =>
    (id: number): CurrentLICPacket | null => {
      const packets = get(currentLICPacketsAtom);
      return packets.find((p) => p.id === id) || null;
    }
);
currentLICPacketByIdAtom.debugLabel = "currentLICPacketByIdAtom";

// Derived: latest packet by device and payload type
export const latestLICPacketByDeviceAndTypeAtom = atom(
  (get) =>
    (
      deviceId: string,
      payloadType: CurrentLICPacket["payload_type"]
    ): CurrentLICPacket | null => {
      const packets = get(currentLICPacketsAtom);
      const filtered = packets.filter(
        (p) =>
          (p.device as unknown as string) === deviceId &&
          p.payload_type === payloadType
      );
      return (
        filtered.sort((a, b) =>
          b.packet_date.localeCompare(a.packet_date)
        )[0] || null
      );
    }
);
latestLICPacketByDeviceAndTypeAtom.debugLabel =
  "latestLICPacketByDeviceAndTypeAtom";

// Fetch with polling for current LIC packets
export const fetchCurrentLICPacketsAtom = atom(null, async (get, set) => {
  const selectedPropertyId = get(selectedPropertyIdAtom);
  if (!selectedPropertyId) {
    console.log("fetchCurrentLICPacketsAtom - No property ID selected");
    return;
  }

  const licDevices = get(licDevicesAtom);
  const deviceIds = licDevices.map((d) => d.id);
  if (deviceIds.length === 0) {
    set(currentLICPacketsAtom, []);
    return;
  }

  await set(
    withOperationHandlingAtom,
    {
      operation: "fetchCurrentLICPackets",
      message: "Carregando pacotes LIC...",
      displayLoading: false,
      displayError: true,
    },
    async () => {
      set(currentLICPacketsLoadingAtom, true);
      set(currentLICPacketsErrorAtom, null);
      try {
        const packets = await loadCurrentLICPacketsForDevices(
          apiService,
          deviceIds
        );
        set(currentLICPacketsAtom, packets);
        return packets;
      } catch (error) {
        const msg = error instanceof Error ? error.message : String(error);
        set(currentLICPacketsErrorAtom, msg);
        console.error("Failed to fetch current LIC packets:", error);
        throw error;
      } finally {
        set(currentLICPacketsLoadingAtom, false);
      }
    }
  );
});
fetchCurrentLICPacketsAtom.debugLabel = "fetchCurrentLICPacketsAtom";
fetchCurrentLICPacketsAtom.onMount = (dispatch) => {
  const timer = new TimeoutTimer();
  timer.start(DATA_FETCH_INTERVAL_MILLIS, async () => {
    await dispatch();
  });
  return () => timer.stop();
};

// -----------------------------------------------------------------------------
// LIC State atoms
// -----------------------------------------------------------------------------

// Base atom holding lic_state rows (for LIC devices under the active property)
export const licStatesAtom = atom<LicState<{ device: string }>[]>([]);
licStatesAtom.debugLabel = "licStatesAtom";

// Loading and error atoms
export const licStatesLoadingAtom = atom<boolean>(false);
licStatesLoadingAtom.debugLabel = "licStatesLoadingAtom";
export const licStatesErrorAtom = atom<string | null>(null);
licStatesErrorAtom.debugLabel = "licStatesErrorAtom";

// Derived: states for current property based on device ids
export const licStatesByPropertyAtom = atom<LicState[]>((get) => {
  const states = get(licStatesAtom);
  const licDevices = get(licDevicesAtom);
  if (!licDevices || licDevices.length === 0) return [];
  const licIds = new Set(licDevices.map((d) => d.id));
  return states.filter((s) => licIds.has(s.device as unknown as string));
});
licStatesByPropertyAtom.debugLabel = "licStatesByPropertyAtom";

// Finder: by device id
export const licStateByDeviceIdAtom = atom(
  (get) =>
    (deviceId: string): LicState<{ device: string }> | null => {
      const states = get(licStatesAtom);
      return (
        states.find((s) => (s.device as unknown as string) === deviceId) || null
      );
    }
);
licStateByDeviceIdAtom.debugLabel = "licStateByDeviceIdAtom";

// Fetch with polling for LIC states
export const fetchLicStatesAtom = atom(null, async (get, set) => {
  const selectedPropertyId = get(selectedPropertyIdAtom);
  if (!selectedPropertyId) {
    console.log("fetchLicStatesAtom - No property ID selected");
    return;
  }

  const licDevices = get(licDevicesAtom);
  const deviceIds = licDevices.map((d) => d.id);
  if (deviceIds.length === 0) {
    set(licStatesAtom, []);
    return;
  }

  await set(
    withOperationHandlingAtom,
    {
      operation: "fetchLicStates",
      message: "Carregando estado LIC...",
      displayLoading: false,
      displayError: true,
    },
    async () => {
      set(licStatesLoadingAtom, true);
      set(licStatesErrorAtom, null);
      try {
        const states = await apiService.licState.query({
          fields: ["*"],
          filter: {
            device: { _in: deviceIds },
          } as any,
          sort: ["-date_updated"],
        });
        set(licStatesAtom, states as any);
        return states;
      } catch (error) {
        const msg = error instanceof Error ? error.message : String(error);
        set(licStatesErrorAtom, msg);
        console.error("Failed to fetch LIC states:", error);
        throw error;
      } finally {
        set(licStatesLoadingAtom, false);
      }
    }
  );
});
fetchLicStatesAtom.debugLabel = "fetchLicStatesAtom";
fetchLicStatesAtom.onMount = (dispatch) => {
  const timer = new TimeoutTimer();
  timer.start(DATA_FETCH_INTERVAL_MILLIS, async () => {
    await dispatch();
  });
  return () => timer.stop();
};

// Data fetching actions
export const fetchAccountsAtom = atom(null, async (get, set) => {
  const result = await set(
    withOperationHandlingAtom,
    {
      operation: "fetchAccounts",
      message: "Carregando contas...",
      displayLoading: true,
      displayError: true,
    },
    async () => {
      const accounts = await apiService.account.listAccountsWithUsers();
      set(availableAccountsAtom, accounts);
      return accounts;
    }
  );

  return result;
});
fetchAccountsAtom.debugLabel = "fetchAccountsAtom";

// Debouncing state for account user tree fetching
let fetchAccountUserTreeTimeout: NodeJS.Timeout | null = null;
let pendingAccountUserId: string | null = null;

// Helper function to clear the debounce timeout
const clearFetchAccountUserTreeTimeout = () => {
  if (fetchAccountUserTreeTimeout) {
    clearTimeout(fetchAccountUserTreeTimeout);
    fetchAccountUserTreeTimeout = null;
  }
  pendingAccountUserId = null;
};

const fetchSelectedAccountUserTreeAtom = atom(null, async (get, set) => {
  const targetId = get(internalSelectedAccountUserIdAtom);
  if (!targetId) {
    console.log(
      "fetchSelectedAccountUserTreeAtom - No account user ID provided"
    );
    throw new Error("No account user ID provided");
  }

  console.log(
    `fetchSelectedAccountUserTreeAtom - Requesting data for account ${targetId} (debounce: ${ACCOUNT_USER_TREE_DEBOUNCE_DELAY}ms)`
  );

  // If the same account ID is already pending, return early to avoid duplicate requests
  if (pendingAccountUserId === targetId && fetchAccountUserTreeTimeout) {
    console.log(
      `fetchSelectedAccountUserTreeAtom - Request for account ${targetId} already pending, skipping`
    );
    return new Promise((resolve) => {
      // Wait for the existing request to complete
      const checkInterval = setInterval(() => {
        if (!fetchAccountUserTreeTimeout || pendingAccountUserId !== targetId) {
          clearInterval(checkInterval);
          resolve({ success: true });
        }
      }, 50);
    });
  }

  // Clear any existing timeout to debounce the request
  clearFetchAccountUserTreeTimeout();
  pendingAccountUserId = targetId;

  // Return a promise that resolves after the debounce delay
  return new Promise((resolve, reject) => {
    fetchAccountUserTreeTimeout = setTimeout(async () => {
      try {
        // Double-check that the account ID hasn't changed during the debounce delay
        const currentTargetId = get(internalSelectedAccountUserIdAtom);
        if (currentTargetId !== targetId) {
          console.log(
            `fetchSelectedAccountUserTreeAtom - Account ID changed during debounce (${targetId} -> ${currentTargetId}), cancelling request`
          );
          clearFetchAccountUserTreeTimeout();
          resolve({ success: false, reason: "Account ID changed" });
          return;
        }

        console.log(
          `fetchSelectedAccountUserTreeAtom - Executing debounced request for account ${targetId}`
        );

        const result = await set(
          withOperationHandlingAtom,
          {
            operation: "fetchAccountUserTree",
            message: "Carregando dados da conta...",
            displayLoading: true,
            displayError: true,
          },
          async () => {
            const tree = await apiService.account.getAccountUserTree(targetId);
            set(accountUserTreeAtom, tree);
            console.log(
              `fetchSelectedAccountUserTreeAtom - Successfully loaded data for account ${targetId}`
            );
            Object.assign(window, {
              accountUserTree: tree, // For debugging purposes
            }); // For debugging purposes

            return tree;
          }
        );

        // Clear the pending state after successful completion
        clearFetchAccountUserTreeTimeout();
        resolve(result);
      } catch (error) {
        // Clear the pending state on error
        clearFetchAccountUserTreeTimeout();
        reject(error);
      }
    }, ACCOUNT_USER_TREE_DEBOUNCE_DELAY);
  });
});
fetchSelectedAccountUserTreeAtom.debugLabel =
  "fetchSelectedAccountUserTreeAtom";

// Helper atom to refetch data after mutations
export const refetchDataAtom = atom(null, async (get, set) => {
  const selectedAccountUserId = get(internalSelectedAccountUserIdAtom);
  if (selectedAccountUserId) {
    // Refresh account tree and also refresh live data tied to current property
    await set(fetchSelectedAccountUserTreeAtom);
    const selectedPropertyId = get(selectedPropertyIdAtom);
    if (selectedPropertyId) {
      try {
        await Promise.all([
          set(fetchCurrentProjectStatesAtom),
          set(fetchCurrentIrrigationPlanStatesAtom),
          set(fetchCurrentLICPacketsAtom),
          set(fetchLicStatesAtom),
        ]);
      } catch (e) {
        // Errors are already handled by withOperationHandlingAtom
      }
    }
    return { success: true } as const;
  }
  return { success: true };
});
refetchDataAtom.debugLabel = "refetchDataAtom";

// Helper atom to manually clear any pending debounced requests (useful for testing)
export const clearPendingAccountUserTreeRequestsAtom = atom(null, () => {
  clearFetchAccountUserTreeTimeout();
  console.log("🧹 Manually cleared pending account user tree requests");
});
clearPendingAccountUserTreeRequestsAtom.debugLabel =
  "clearPendingAccountUserTreeRequestsAtom";

// Fetch current project states for the selected property
// export const fetchCurrentProjectStatesByPropertyAtom = atom(
//   null,
//   async (get, set) => {
//     const selectedPropertyId = get(selectedPropertyIdAtom);

//     if (!selectedPropertyId) {
//       console.log(
//         "🚫 No property selected, skipping current project states fetch"
//       );
//       return [];
//     }

//     const result = await set(
//       withOperationHandlingAtom,
//       {
//         operation: "fetchCurrentProjectStates",
//         message: "Carregando estados dos projetos...",
//         displayLoading: false, // Don't show global loading for this
//         displayError: true,
//       },
//       async () => {
//         set(currentProjectStatesLoadingAtom, true);
//         set(currentProjectStatesErrorAtom, null);

//         try {
//           //TODO: Implement minPacketDate filtering
//           const projectStates =
//             await apiService.currentProjectState.getByProperty(
//               selectedPropertyId,
//               null
//             );
//           set(currentProjectStatesAtom, projectStates);
//           console.log(
//             `📊 Loaded ${projectStates.length} current project states for property ${selectedPropertyId}`
//           );
//           return projectStates;
//         } catch (error) {
//           const errorMessage =
//             error instanceof Error ? error.message : "Unknown error";
//           set(currentProjectStatesErrorAtom, errorMessage);
//           console.error("❌ Failed to fetch current project states:", error);
//           throw error;
//         } finally {
//           set(currentProjectStatesLoadingAtom, false);
//         }
//       }
//     );

//     return result;
//   }
// );

// Account assignment atom - handles account selection, data loading, and navigation
export const assignAccountAtom = atom(
  null,
  async (
    get,
    set,
    {
      accountUserId,
      navigate,
      nextLocation = "/app/dashboard",
    }: {
      accountUserId: string;
      nextLocation?: string;
      navigate: (path: string) => void;
    }
  ) => {
    // if (accountUserId === get(selectedAccountUserIdAtom)) {
    //   console.log("🔄 Already assigned to account:", accountUserId);
    //   // If already assigned, just navigate to the app dashboard
    //   navigate("/app/dashboard");
    //   return { success: true };
    // }
    try {
      console.log(
        "🔄 assignAccountAtom - Assigning current account:",
        accountUserId
      );

      // Step 1: Assign current account
      set(internalSelectedAccountUserIdAtom, accountUserId);

      // Step 2: Load current account data
      await set(fetchSelectedAccountUserTreeAtom);

      console.log(
        "✅ assignAccountAtom - Account data loaded successfully - redirecting to app"
      );

      // Step 3: Navigate to select property

      const selectedProperty = get(selectedPropertyAtom);

      if (selectedProperty) {
        // If a property is already selected, navigate to the app dashboard
        console.log(
          `🔄 assignAccountAtom - Property already selected: ${selectedProperty.id}, navigating to ${nextLocation}`
        );
        navigate(nextLocation);
        return { success: true };
      }

      // This will be handled by the existing route guards in AppShell
      console.log("🔄 No property selected - redirecting to select property");
      navigate("/select-property");

      return { success: true };
    } catch (error) {
      console.error("❌ Failed to load account data:", error);
      // On error, redirect to account selection
      navigate("/select-account");
      throw error;
    }
  }
);
assignAccountAtom.debugLabel = "assignAccountAtom";

export const unassignAccountAtom = atom(null, (get, set) => {
  console.log("🔄 Unassigning current account");
  // Clear any pending debounced fetch requests
  clearFetchAccountUserTreeTimeout();
  // Clear the selected account user ID
  set(internalSelectedAccountUserIdAtom, null);
  // Clear the account user tree
  set(accountUserTreeAtom, null);
  // Clear the selected property ID
  set(selectedPropertyIdAtom, null);
});
unassignAccountAtom.debugLabel = "unassignAccountAtom";
