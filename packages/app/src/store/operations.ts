// src/store/operations.ts
import { isDirectusError } from "@directus/sdk";
import { atom } from "jotai";
import { atomWithStorage } from "jotai/utils";

// Types for loading and error operations
export interface LoadingOperation {
  operation: string;
  message?: string;
  displayLoading?: boolean;
}

export interface ErrorOperation {
  operation: string;
  error: any;
  message: string;
  displayError?: boolean;
}

// Operation helper options
export interface OperationOptions {
  operation: string;
  message?: string;
  displayLoading?: boolean;
  displayError?: boolean;
}

// Global loading and error state atoms
export const loadingOperationsAtom = atom<LoadingOperation[]>([]);
export const errorOperationsAtom = atomWithStorage<ErrorOperation[]>(
  "errorOperations",
  []
);

// Derived atoms for UI display
export const displayLoadingAtom = atom((get) => {
  const operations = get(loadingOperationsAtom);
  return operations.filter((op) => op.displayLoading);
});

export const displayErrorsAtom = atom((get) => {
  const errors = get(errorOperationsAtom);
  // Automatically filter out errors with displayError: false
  return errors.filter((error) => error.displayError !== false);
});

// Helper function to add loading operation
export const addLoadingOperationAtom = atom(
  null,
  (get, set, operation: LoadingOperation) => {
    const current = get(loadingOperationsAtom);
    set(loadingOperationsAtom, [...current, operation]);
  }
);

// Helper function to remove loading operation
export const removeLoadingOperationAtom = atom(
  null,
  (get, set, operationName: string) => {
    const current = get(loadingOperationsAtom);
    set(
      loadingOperationsAtom,
      current.filter((op) => op.operation !== operationName)
    );
  }
);

// Helper function to add error operation
export const addErrorOperationAtom = atom(
  null,
  (get, set, error: ErrorOperation) => {
    const current = get(errorOperationsAtom);
    set(errorOperationsAtom, [...current, error]);
  }
);

// Helper function to remove error operation
export const removeErrorOperationAtom = atom(
  null,
  (get, set, operationName: string) => {
    const current = get(errorOperationsAtom);
    set(
      errorOperationsAtom,
      current.filter((error) => error.operation !== operationName)
    );
  }
);

// Helper function to clear all errors with displayError: false
export const cleanupErrorsAtom = atom(null, (get, set) => {
  const current = get(errorOperationsAtom);
  const filtered = current.filter((error) => error.displayError !== false);
  set(errorOperationsAtom, filtered);
});

export type OperationResult<T> = {
  success: boolean;
  data?: T;
  error?: string;
};

// Main wrapper function for API operations
export const withOperationHandlingAtom = atom(
  null,
  async <T>(
    get: any,
    set: any,
    options: OperationOptions,
    fn: () => Promise<T>
  ): Promise<OperationResult<T>> => {
    const {
      operation,
      message,
      displayLoading = true,
      displayError = true,
    } = options;

    try {
      // Add loading operation
      set(addLoadingOperationAtom, {
        operation,
        message,
        displayLoading,
      });

      // Execute the API call
      const result = await fn();

      return { success: true, data: result };
    } catch (error) {
      // Create error message
      const errorMessage = getOperationErrorMessage(options, error);

      // A very detailed log
      // A very detailed error log
      logOperationError(operation, message, error, errorMessage);

      // Add error operation
      set(addErrorOperationAtom, {
        operation,
        error,
        message: message ? `${message}: ${errorMessage}` : errorMessage,
        displayError,
      });

      return { success: false, error: errorMessage };
    } finally {
      // Remove loading operation
      set(removeLoadingOperationAtom, operation);
    }
  }
);

// Helper to determine if error is network-related
export const isNetworkError = (error: any): boolean => {
  if (!error) return false;

  return (
    error.name === "NetworkError" ||
    error.code === "NETWORK_ERROR" ||
    error.message?.includes("fetch") ||
    error.message?.includes("network") ||
    error.message?.includes("offline") ||
    error.status === 0 ||
    !navigator.onLine
  );
};

// Check if any loading operation is currently active
export const hasActiveLoadingAtom = atom((get) => {
  const operations = get(loadingOperationsAtom);
  return operations.some((op) => op.displayLoading);
});

// Get current loading message for display
export const currentLoadingMessageAtom = atom((get) => {
  const operations = get(displayLoadingAtom);
  return operations.length > 0
    ? operations[0].message || operations[0].operation
    : null;
});

function logOperationError(
  operation: string,
  message: string | undefined,
  error: unknown,
  errorMessage: string
) {
  console.group(`🚨 Operation Error: ${operation}`);
  console.error("Operation:", operation);
  console.error("Custom Message:", message || "No custom message provided");
  console.error("Error Type:", error?.constructor?.name || "Unknown");
  console.error("Error Message:", errorMessage);
  console.error("Full Error Object:", error);
  console.error(
    "Stack Trace:",
    error instanceof Error
      ? error?.stack || "No stack trace available"
      : "Not an Error instance"
  );
  console.error("Timestamp:", new Date().toISOString());
  console.error("User Agent:", navigator.userAgent);
  console.error("Online Status:", navigator.onLine);
  console.error("Current URL:", window.location.href);

  if (isDirectusError(error)) {
    console.error("Directus Error:", error);
    console.error("Error Details:", error.errors);
    console.error(
      "Error Messages:",
      error.errors.map((e) => e.message)
    );
    console.error(
      "Error Extensions:",
      error.errors.map((e) => e.extensions)
    );
    console.error("Error Response:", error.response);
  }

  console.error("Is Network Error:", isNetworkError(error));
  if (error && typeof error === "object" && "response" in error) {
    console.error("HTTP Status:", (error as any).response.status);
    console.error("HTTP Status Text:", (error as any).response.statusText);
    console.error("Response Headers:", (error as any).response.headers);
    console.error("Response Data:", (error as any).response.data);
  }
  if (error && typeof error === "object" && "request" in error) {
    console.error("Request Config:", (error as any).request);
  }
  console.groupEnd();
}

function getOperationErrorMessage(
  operationOptions: OperationOptions,
  error: any
): string {
  // Check for network errors first
  if (isNetworkError(error)) {
    return "Erro de conexão. Verifique sua conexão com a internet e tente novamente.";
  }

  // Check for Directus errors
  if (isDirectusError(error)) {
    if (error.errors && error.errors.length > 0) {
      const firstError = error.errors[0];

      // Handle specific Directus error types
      switch (firstError.extensions?.code) {
        case "INVALID_CREDENTIALS":
          return "Credenciais inválidas. Verifique seu email e senha.";
        case "FORBIDDEN":
          return "Você não tem permissão para realizar esta operação.";
        case "ITEM_NOT_FOUND":
          return "Item não encontrado.";
        case "VALIDATION_FAILED":
          return `Erro de validação: ${firstError.message}`;
        case "RECORD_NOT_UNIQUE":
          // Extract field and collection from extensions for a more specific message
          const field = firstError.extensions?.field;
          const collection = firstError.extensions?.collection;
          if (field && collection) {
            return `Registro duplicado: Já existe um registro com este valor para o campo "${field}" na coleção "${collection}". Use um valor diferente.`;
          }
          return "Registro duplicado:Já existe um registro com esses dados. Use valores diferentes.";

        default:
          return firstError.message || "Erro interno do servidor.";
      }
    }
    return "Erro interno do servidor.";
  }

  // Check for HTTP errors
  if (error && typeof error === "object" && "response" in error) {
    const response = (error as any).response;

    switch (response?.status) {
      case 400:
        return "Dados inválidos enviados para o servidor.";
      case 401:
        return "Você não está autenticado. Faça login novamente.";
      case 403:
        return "Você não tem permissão para realizar esta operação.";
      case 404:
        return "Recurso não encontrado.";
      case 408:
        return "Tempo limite de conexão excedido. Tente novamente.";
      case 422:
        return "Dados fornecidos são inválidos ou incompletos.";
      case 429:
        return "Muitas tentativas. Aguarde um momento e tente novamente.";
      case 500:
        return "Erro interno do servidor. Tente novamente mais tarde.";
      case 502:
      case 503:
      case 504:
        return "Serviço temporariamente indisponível. Tente novamente mais tarde.";
      default:
        return response?.statusText || "Erro desconhecido do servidor.";
    }
  }

  // Check for JavaScript Error instances
  if (error instanceof Error) {
    // Handle specific error types
    if (error.name === "TypeError") {
      return "Erro de tipo de dados. Verifique os dados fornecidos.";
    }
    if (error.name === "ReferenceError") {
      return "Erro de referência interna da aplicação.";
    }
    if (error.name === "SyntaxError") {
      return "Erro de sintaxe na resposta do servidor.";
    }
    if (error.name === "TimeoutError") {
      return "Tempo limite excedido. Tente novamente.";
    }

    return error.message || "Erro desconhecido.";
  }

  // Handle string errors
  if (typeof error === "string") {
    return error;
  }

  // Handle objects with message property
  if (error && typeof error === "object" && "message" in error) {
    return (error as any).message || "Erro desconhecido.";
  }

  // Fallback for unknown error types
  return "Erro desconhecido. Tente novamente ou entre em contato com o suporte.";
}
