// src/store/auth.ts
import { atom } from "jotai";
import { apiService } from "@/api";
import { User } from "@/api/model/user";
import { withOperationHandlingAtom, isNetworkError } from "./operations";

export type AuthUser = Pick<
  User,
  "id" | "first_name" | "last_name" | "email" | "role" | "avatar"
> & {
  cpf?: string | null;
  phone_number?: string | null;
};

// Auth state atoms
export const authUserAtom = atom<AuthUser | null>(null);
authUserAtom.debugLabel = "authUserAtom";
export const isAuthenticatedAtom = atom((get) => {
  const user = get(authUserAtom);
  return !!user;
});
isAuthenticatedAtom.debugLabel = "isAuthenticatedAtom";

// Legacy atoms for backwards compatibility (deprecated)
export const authLoadingAtom = atom(false);
authLoadingAtom.debugLabel = "authLoadingAtom";
export const authErrorAtom = atom<string | null>(null);
authErrorAtom.debugLabel = "authErrorAtom";
export const authNetworkErrorAtom = atom<boolean>(false);
authNetworkErrorAtom.debugLabel = "authNetworkErrorAtom";

// Auth actions
export const loginAtom = atom(
  null,
  async (
    get,
    set,
    { email, password }: { email: string; password: string }
  ) => {
    const result = await set(
      withOperationHandlingAtom,
      {
        operation: "login",
        message: "Fazendo login...",
        displayLoading: true,
        displayError: true,
      },
      async () => {
        await apiService.auth.login(email, password);
        const user = await apiService.auth.fetchUser();
        set(authUserAtom, user as any);
        return user;
      }
    );

    return result;
  }
);
loginAtom.debugLabel = "loginAtom";

export const logoutAtom = atom(null, async (get, set) => {
  const result = await set(
    withOperationHandlingAtom,
    {
      operation: "logout",
      message: "Fazendo logout...",
      displayLoading: false,
      displayError: false,
    },
    async () => {
      await apiService.auth.logout();
    }
  );

  // Always clear user state on logout, regardless of API call result
  set(authUserAtom, null);

  return result;
});
logoutAtom.debugLabel = "logoutAtom";

export const initializeAuthAtom = atom(null, async (get, set) => {
  const result = await set(
    withOperationHandlingAtom,
    {
      operation: "initializeAuth",
      message: "Verificando autenticação...",
      displayLoading: false,
      displayError: false,
    },
    async () => {
      const user = await apiService.auth.fetchUser();
      set(authUserAtom, user as any);
      return user;
    }
  );

  // Handle network errors separately for auth initialization
  if (!result.success && result.error) {
    // Create a mock error object to check if it's a network error
    const error = new Error(result.error);
    if (isNetworkError(error)) {
      // For network errors during initialization, we might want to set a flag
      // but don't clear the user state (they might be offline but still authenticated)
      console.error("Network error during auth initialization:", result.error);
    } else {
      // For other errors, clear the user state (invalid token, etc.)
      set(authUserAtom, null);
    }
  }

  return result;
});
initializeAuthAtom.debugLabel = "initializeAuthAtom";

// Retry auth initialization (useful for network error recovery)
export const retryAuthInitializationAtom = atom(null, async (get, set) => {
  return await set(initializeAuthAtom);
});
retryAuthInitializationAtom.debugLabel = "retryAuthInitializationAtom";

// Update profile (first/last name, cpf, phone)
export const updateProfileAtom = atom(
  null,
  async (
    get,
    set,
    data: Partial<{
      first_name: string | null;
      last_name: string | null;
      cpf: string | null;
      phone_number: string | null;
    }>
  ) => {
    const result = await set(
      withOperationHandlingAtom,
      {
        operation: "updateProfile",
        message: "Salvando perfil...",
        displayLoading: true,
        displayError: true,
      },
      async () => {
        await apiService.auth.updateProfile(data);
        const user = await apiService.auth.fetchUser();
        set(authUserAtom, user as any);
        return user;
      }
    );
    return result;
  }
);
updateProfileAtom.debugLabel = "updateProfileAtom";
