import type {
  DirectusRelationArrayType,
  DirectusRelationFieldType,
} from "@/utils/types";
import type { Point } from "geojson";
import type { Account } from "./account";
import type { Model } from "./common";
import type { Project } from "./project";
import type { PropertyDevice } from "./property-device";
import { Reservoir } from "./reservoir";
import type { WaterPump } from "./water-pump";

export type PropertyRelationsTypes = {
  account: DirectusRelationFieldType<Account>;
  water_pumps: DirectusRelationFieldType<WaterPump>;
  projects: DirectusRelationFieldType<Project>;
  devices: DirectusRelationFieldType<PropertyDevice>;
  reservoirs: DirectusRelationFieldType<Reservoir>;
};

export type PropertyDefaultRelationsTypes = PropertyRelationsTypes;

export interface Property<
  Types extends Partial<PropertyRelationsTypes> = PropertyDefaultRelationsTypes
> extends Model {
  account: Types["account"];
  name: string;
  timezone: string;
  point: Point | null;
  address_postal_code: string | null;
  address_street_name: string | null;
  address_street_number: string | null;
  address_complement: string | null;
  address_neighborhood: string | null;
  address_city: string | null;
  address_state: string | null;
  address_country: string | null;
  backwash_duration_minutes: number | null;
  backwash_period_minutes: number | null;
  backwash_delay_seconds: number | null;
  rain_gauge_enabled: boolean;
  rain_gauge_resolution_mm: number | null;
  precipitation_volume_limit_mm: number | null;
  precipitation_suspended_duration_hours: number | null;
  water_pumps: DirectusRelationArrayType<Types["water_pumps"]>;
  projects: DirectusRelationArrayType<Types["projects"]>;
  devices: DirectusRelationArrayType<Types["devices"]>;
  reservoirs: DirectusRelationArrayType<Types["reservoirs"]>;
}
