// Auto-generated from Directus schema

import type { DirectusRelationFieldType } from "@/utils/types";
import type { Model } from "./common";
import type { PropertyDevice } from "./property-device";

export type MeshDeviceMappingRelationsTypes = {
  mesh_property_device: DirectusRelationFieldType<PropertyDevice>;
  lic_property_device: DirectusRelationFieldType<PropertyDevice>;
};

export type MeshDeviceMappingDefaultRelationsTypes = MeshDeviceMappingRelationsTypes;

export interface MeshDeviceMapping<
  Types extends Partial<MeshDeviceMappingRelationsTypes> = MeshDeviceMappingDefaultRelationsTypes
> extends Model {
  mesh_property_device: Types["mesh_property_device"];
  lic_property_device: Types["lic_property_device"];
  start_date: string;
  end_date: string | null;
}
