import type { DirectusRelationFieldType } from "@/utils/types";
import type { Device } from "./device";
import type {
  LICPacketPayloadData,
  LICPacketPayloadType,
} from "./current-lic-packet";

// Hypertable model (read-only via SDK); not wired in client/service
export type LicPacketRelations = {
  device: DirectusRelationFieldType<Device>;
};

export type LicPacketDefaultRelations = {
  device: string;
};

export interface LicPacket<
  Types extends Partial<LicPacketRelations> = LicPacketDefaultRelations
> {
  id: number;
  date_created: string;
  device: string | Types["device"];
  packet_date: string;
  payload_type: LICPacketPayloadType;
  payload_data: LICPacketPayloadData<LICPacketPayloadType>;
}
