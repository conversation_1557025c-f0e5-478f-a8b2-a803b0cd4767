import type { DirectusRelationFieldType } from "@/utils/types";
import type { Project } from "./project";
import type { ProjectStateStatus, SectorState } from "./current-project-state";

// Hypertable model (read-only via SDK); not wired in client/service
export type ProjectStateRelations = {
  project: DirectusRelationFieldType<Project>;
};

export type ProjectStateDefaultRelations = {
  project: string;
};

export interface ProjectState<
  Types extends Partial<ProjectStateRelations> = ProjectStateDefaultRelations
> {
  id: number;
  project: Types["project"];
  date_created: string;
  date_updated: string;
  packet_date: string;
  irrigation_status: ProjectStateStatus;
  fertigation_status: ProjectStateStatus;
  backwash_status: ProjectStateStatus;
  sectors: SectorState[];
}
