import type {
  DirectusRelationArrayType,
  DirectusRelationFieldType,
} from "@/utils/types";
import type { Model } from "./common";
import type { Device } from "./device";
import type { Project } from "./project";
import type { Property } from "./property";

export const WATER_PUMP_TYPE_VALUES = [
  "IRRIGATION",
  "FERTIGATION",
  "SERVICE",
] as const;

export type WaterPumpType = (typeof WATER_PUMP_TYPE_VALUES)[number];

export const WATER_PUMP_TYPE_LABELS: Record<WaterPumpType, string> = {
  IRRIGATION: "Irrigação",
  FERTIGATION: "Fertirrigação",
  SERVICE: "Bomba de Serviço",
};

export const WATER_PUMP_MODE_VALUES = [
  "PULSE",
  "CONTINUOUS",
] as const;

export type WaterPumpMode = (typeof WATER_PUMP_MODE_VALUES)[number];

export const WATER_PUMP_MODE_LABELS: Record<WaterPumpMode, string> = {
  PULSE: "Pulso",
  CONTINUOUS: "Contínuo",
};

export type WaterPumpRelationsTypes = {
  property: DirectusRelationFieldType<Property>;
  water_pump_controller: DirectusRelationFieldType<Device>;
  irrigation_projects: DirectusRelationFieldType<Project>;
  fertigation_projects: DirectusRelationFieldType<Project>;
};

export type WaterPumpDefaultRelationsTypes = WaterPumpRelationsTypes;

export interface WaterPump<
  Types extends WaterPumpRelationsTypes = WaterPumpDefaultRelationsTypes,
> extends Model {
  property: Types["property"];
  water_pump_controller: Types["water_pump_controller"] | null;
  label: string;
  identifier: string;
  pump_type: WaterPumpType;
  pump_model: string;
  has_frequency_inverter: boolean;
  monitor_operation: boolean;
  flow_rate_lh: number | null;
  mode: WaterPumpMode;
  irrigation_projects: DirectusRelationArrayType<Types["irrigation_projects"]>;
  fertigation_projects: DirectusRelationArrayType<
    Types["fertigation_projects"]
  >;
}
