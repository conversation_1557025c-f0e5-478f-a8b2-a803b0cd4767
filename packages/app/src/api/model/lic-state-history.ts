import type { <PERSON><PERSON><PERSON> } from "./common";

// Hypertable model (read-only via SDK); not wired in client/service
export interface LicStateHistory {
  device: string;
  state_date: string; // ISO timestamp
  lic: Any<PERSON>son | null;
  groups: <PERSON><PERSON><PERSON> | null;
  devices: <PERSON><PERSON><PERSON> | null;
  mesh_devices: <PERSON><PERSON>son | null;
  schedules: AnyJson | null;
  sector_schedules: Any<PERSON>son | null;
  device_schedules: AnyJson | null;
  last_devices_request: number | null;
  last_scheduling_request: number | null;
  last_dev_scheduling_request: number | null;
  last_automation_request: number | null;
  last_config_request: number | null;
  current_devices_timestamp: number | null;
  current_scheduling_timestamp: number | null;
  current_dev_scheduling_timestamp: number | null;
  current_automation_timestamp: number | null;
  current_config_timestamp: number | null;
}

