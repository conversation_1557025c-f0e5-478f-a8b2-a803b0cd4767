import type { DirectusRelationFieldType } from "@/utils/types";
import type { IrrigationPlan } from "./irrigation-plan";

// Hypertable model (read-only via SDK); not wired in client/service
export type IrrigationPlanStateRelations = {
  irrigation_plan: DirectusRelationFieldType<IrrigationPlan>;
};

export type IrrigationPlanStateDefaultRelations = {
  irrigation_plan: string;
};

export interface IrrigationPlanState<
  Types extends Partial<IrrigationPlanStateRelations> = IrrigationPlanStateDefaultRelations
> {
  id: number;
  irrigation_plan: Types["irrigation_plan"];
  packet_date: string;
  start_time: string | null;
  end_time: string | null;
  activated_steps: string[];
  activated_ferti_steps: string[];
  waterpump_working: boolean;
  backwash_start_time: string | null;
  uses_waterpump: boolean;
  uses_ferti: boolean;
  date_created: string;
}
