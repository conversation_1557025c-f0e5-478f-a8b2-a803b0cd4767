import type { DirectusRelationFieldType } from "@/utils/types";
import type { Model } from "./common";
import type { Reservoir } from "./reservoir";

export type CurrentReservoirStateRelations = {
  reservoir: DirectusRelationFieldType<Reservoir>;
};

export type CurrentReservoirStateDefaultRelations = {
  reservoir: DirectusRelationFieldType<Reservoir>;
};

export interface CurrentReservoirState<
  Types extends Partial<CurrentReservoirStateRelations> = CurrentReservoirStateDefaultRelations
> extends Pick<Model, "id" | "date_created" | "date_updated"> {
  reservoir: Types["reservoir"];
  packet_date: string;
  start_time: string;
  restart_time: string | null;
  end_time: string | null;
}

