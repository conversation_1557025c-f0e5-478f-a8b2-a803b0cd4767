import type { DirectusRelationFieldType } from "@/utils/types";
import type { Reservoir } from "./reservoir";

export type ReservoirStateRelations = {
  reservoir: DirectusRelationFieldType<Reservoir>;
};

export type ReservoirStateDefaultRelations = {
  reservoir: string;
};

export interface ReservoirState<
  Types extends Partial<ReservoirStateRelations> = ReservoirStateDefaultRelations
> {
  id: number;
  reservoir: Types["reservoir"];
  packet_date: string;
  start_time: string | null;
  restart_time: string | null;
  end_time: string | null;
  date_created: string;
}

