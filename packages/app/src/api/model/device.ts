import type {
  DirectusRelationArrayType,
  DirectusRelationFieldType,
} from "@/utils/types";
import type { Model } from "./common";
import type { Project } from "./project";
import type { PropertyDevice } from "./property-device";
import type { Sector } from "./sector";
import type { WaterPump } from "./water-pump";

export const DEVICE_MODEL_VALUES = [
  "LIC",
  "WPC-PL10",
  "WPC-PL50",
  "VC",
  "RM",
] as const;

export type DeviceModel = (typeof DEVICE_MODEL_VALUES)[number];

export type DeviceRelationsTypes = {
  water_pumps: DirectusRelationFieldType<WaterPump>;
  properties: DirectusRelationFieldType<PropertyDevice>;
  valve_sectors: DirectusRelationFieldType<Sector>;
  localized_irrigation_controller_projects: DirectusRelationFieldType<Project>;
};

export type DeviceDefaultRelationsTypes = {
  water_pumps: DirectusRelationFieldType<WaterPump>;
  properties: DirectusRelationFieldType<PropertyDevice>;
  valve_sectors: DirectusRelationFieldType<Sector>;
  localized_irrigation_controller_projects: DirectusRelationFieldType<Project>;
};

export interface Device<
  Types extends Partial<DeviceRelationsTypes> = DeviceDefaultRelationsTypes
> extends Model {
  id: string;
  identifier: string;
  model: DeviceModel;
  water_pumps: DirectusRelationArrayType<Types["water_pumps"]>;
  properties: DirectusRelationArrayType<Types["properties"]>;
  valve_sectors: DirectusRelationArrayType<Types["valve_sectors"]>;
  localized_irrigation_controller_projects: DirectusRelationArrayType<
    Types["localized_irrigation_controller_projects"]
  >;
}

export type LICDeviceMetadata = {
  wifiSSID?: string;
  wifiPassword?: string;
};
