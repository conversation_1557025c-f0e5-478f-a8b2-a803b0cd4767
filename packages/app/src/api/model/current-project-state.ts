import { DirectusRelationFieldType } from "@/utils/types";
import type { Model } from "./common";
import type { Project } from "./project";

export type CurrentProjectStateRelationsTypes = {
  project: DirectusRelationFieldType<Project>;
};

export type CurrentProjectStateDefaultRelationsTypes = {
  project: DirectusRelationFieldType<Project>;
};

export type ProjectStateStatus = "active" | "inactive" | "error";

export type SectorState = {
  sector: string;
  sector_name: string;
  status: ProjectStateStatus;
};

export interface CurrentProjectState<
  Types extends Partial<CurrentProjectStateRelationsTypes> = CurrentProjectStateDefaultRelationsTypes
> extends Pick<Model, "id" | "date_created" | "date_updated"> {
  project: Types["project"];
  packet_date: string;
  irrigation_status: ProjectStateStatus;
  fertigation_status: ProjectStateStatus;
  backwash_status: ProjectStateStatus;
  sectors: SectorState[];
}
