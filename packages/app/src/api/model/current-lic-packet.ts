import type { DirectusRelationFieldType } from "@/utils/types";
import type { Device } from "./device";
import { codec } from "proto";
export type CurrentLICPacketRelationsTypes = {
  device: DirectusRelationFieldType<Device>;
};

export type CurrentLICPacketDefaultRelationsTypes = {
  device: DirectusRelationFieldType<Device>;
};

const LIC_PACKET_PAYLOAD_TYPES = [
  "status",
  "info",
  "scheduling_report",
  "automation_report",
  "ack",
  "raw",
] as const;

export type LICPacketPayloadType = Exclude<
  keyof codec.out.IOutgoingPacket,
  "id"
>;

export type LICPacketPayloadData<P extends LICPacketPayloadType> = {
  id: NonNullable<codec.out.IOutgoingPacket["id"]>;
} & {
  [k in P]: NonNullable<codec.out.IOutgoingPacket[P]>;
};

export interface CurrentLICPacket<
  Types extends Partial<CurrentLICPacketRelationsTypes> = CurrentLICPacketDefaultRelationsTypes
> {
  id: number;
  date_created: string;
  device: string | Types["device"];
  packet_date: string;
  payload_type: LICPacketPayloadType;
  payload_data: LICPacketPayloadData<LICPacketPayloadType>;
}
