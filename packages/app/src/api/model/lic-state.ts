import type { DirectusRelationFieldType } from "@/utils/types";
import type { AnyJson, Model } from "./common";
import type { Device } from "./device";

export type LicStateRelations = {
  device: DirectusRelationFieldType<Device>;
};

export type LicStateDefaultRelations = {
  device: DirectusRelationFieldType<Device>;
};

export interface LicState<
  Types extends Partial<LicStateRelations> = LicStateDefaultRelations
> extends Model {
  device: Types["device"];
  lic: AnyJson | null;
  groups: AnyJson | null;
  devices: AnyJson | null;
  mesh_devices: AnyJson | null;
  schedules: AnyJson | null;
  sector_schedules: AnyJson | null;
  device_schedules: AnyJson | null;
  last_devices_request: number | null;
  last_scheduling_request: number | null;
  last_dev_scheduling_request: number | null;
  last_automation_request: number | null;
  last_config_request: number | null;
  current_devices_timestamp: number | null;
  current_scheduling_timestamp: number | null;
  current_dev_scheduling_timestamp: number | null;
  current_automation_timestamp: number | null;
  current_config_timestamp: number | null;
}

