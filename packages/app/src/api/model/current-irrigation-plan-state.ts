import type { DirectusRelationFieldType } from "@/utils/types";
import type { Model } from "./common";
import type { IrrigationPlan } from "./irrigation-plan";

export type CurrentIrrigationPlanStateRelations = {
  irrigation_plan: DirectusRelationFieldType<IrrigationPlan>;
};

export type CurrentIrrigationPlanStateDefaultRelations = {
  irrigation_plan: DirectusRelationFieldType<IrrigationPlan>;
};

export interface CurrentIrrigationPlanState<
  Types extends Partial<CurrentIrrigationPlanStateRelations> = CurrentIrrigationPlanStateDefaultRelations
> extends Pick<Model, "id" | "date_created" | "date_updated"> {
  irrigation_plan: Types["irrigation_plan"];
  packet_date: string;
  start_time: string;
  end_time: string | null;
  activated_steps: string[];
  activated_ferti_steps: string[];
  waterpump_working: boolean;
  backwash_start_time: string | null;
  uses_waterpump: boolean;
  uses_ferti: boolean;
}

