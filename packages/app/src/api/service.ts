import {
  CollectionType,
  createItem,
  createItems,
  deleteItem,
  NestedPartial,
  passwordRequest,
  passwordReset,
  Query,
  QueryFilter,
  readAssetRaw,
  readItem,
  readItems,
  readMe,
  registerUser,
  registerUserVerify,
  updateItem,
  updateItemsBatch,
  updateMe,
} from "@directus/sdk";

import type { OmitAndMerge } from "@/utils/types";
import type { AppDirectusClient, AppSchema } from "./client";
import { createDirectusInstance } from "./client";
import { getRequestUrl } from "./get-request-url";
import {
  AccountUserTree,
  AccountUserWithAccount,
  getAccountUserTree,
  listAccountsWithUsers,
} from "./queries/account";
import { CurrentProjectState } from "./model/current-project-state";
import { appConfig } from "@/utils/config";

type CollectionKey = keyof AppSchema;
type CollectionItem<K extends CollectionKey = CollectionKey> = CollectionType<
  AppSchema,
  K
>;

type CRUD<C extends CollectionKey> = {
  create: (
    data: NestedPartial<CollectionItem<C>>
  ) => Promise<CollectionItem<C>>;
  createBatch: (
    items: NestedPartial<CollectionItem<C>>[]
  ) => Promise<CollectionItem<C>[]>;
  update: (
    id: CollectionItem<C>["id"],
    data: Partial<CollectionItem<C>>
  ) => Promise<CollectionItem<C>>;
  updateBatch: (
    items: Partial<CollectionItem<C>>[]
  ) => Promise<CollectionItem<C>[]>;
  getOne: (id: CollectionItem<C>["id"]) => Promise<CollectionItem<C>>;
  getMany: (
    ids: Array<CollectionItem<C>["id"]>
  ) => Promise<CollectionItem<C>[]>;
  query: (
    query: Query<AppSchema, CollectionItem<C>>
  ) => Promise<CollectionItem<C>[]>;
  del: (id: CollectionItem<C>["id"]) => Promise<void>;
};

export class DirectusApiService {
  private static instance: DirectusApiService;

  private constructor(protected readonly client: AppDirectusClient) {}

  public static getInstance(
    client: AppDirectusClient = createDirectusInstance()
  ): DirectusApiService {
    if (!DirectusApiService.instance) {
      DirectusApiService.instance = new DirectusApiService(client);
    }
    return DirectusApiService.instance;
  }

  protected crud<C extends CollectionKey>(collection: C): CRUD<C> {
    const crud: CRUD<C> = {
      create: async (data: NestedPartial<CollectionItem<C>>) => {
        return (await this.client.request(
          createItem(collection, data as any, { fields: ["*"] })
        )) as CollectionItem<C>;
      },
      createBatch: async (items: NestedPartial<CollectionItem<C>>[]) => {
        return (await this.client.request(
          createItems(collection, items as any, { fields: ["*"] })
        )) as CollectionItem<C>[];
      },
      update: async (
        id: CollectionItem<C>["id"],
        data: Partial<CollectionItem<C>>
      ) => {
        return (await this.client.request(
          updateItem(collection, id, data, { fields: ["*"] })
        )) as CollectionItem<C>;
      },
      updateBatch: async (items: Partial<CollectionItem<C>>[]) => {
        return (await this.client.request(
          updateItemsBatch(collection, items as any, { fields: ["*"] })
        )) as CollectionItem<C>[];
      },
      getOne: async (id: CollectionItem<C>["id"]) => {
        return (await this.client.request(
          readItem(collection, id, {
            fields: ["*"],
          })
        )) as CollectionItem<C>;
      },
      getMany: async (IDs: Array<CollectionItem<C>["id"]>) => {
        return (await this.client.request(
          readItems(collection, {
            fields: ["*"],
            filter: {
              id: { _in: IDs },
            } as QueryFilter<AppSchema, CollectionItem<C>>,
          })
        )) as CollectionItem<C>[];
      },
      query: async (query: Query<AppSchema, CollectionItem<C>>) => {
        return (await this.client.request(
          readItems(collection, query)
        )) as CollectionItem<C>[];
      },
      del: async (id: CollectionItem<C>["id"]) => {
        return await this.client.request(deleteItem(collection, id));
      },
    };
    return crud;
  }

  public readonly asset = {
    resolveUrl: (assetId: string) => {
      if (!this.client) throw new Error("Directus client is not initialized");
      const asset = readAssetRaw(assetId);
      const requestOptions = asset();
      return getRequestUrl(
        this.client.url,
        requestOptions.path,
        requestOptions.params
      );
    },
  };

  public readonly account = {
    listAccountsWithUsers: async (): Promise<AccountUserWithAccount[]> => {
      return await listAccountsWithUsers(this.client);
    },
    getAccountUserTree: async (
      accountUserId: string
    ): Promise<AccountUserTree> => {
      return await getAccountUserTree(this.client, accountUserId);
    },
  };

  public readonly auth = {
    login: async (email: string, password: string) => {
      return await this.client.login({ email, password });
    },
    logout: async () => {
      return await this.client.logout();
    },
    register: async (
      email: string,
      password: string,
      firstName: string,
      lastName: string
      // cpf: string,
      // phone_number: string
    ) => {
      // Directus does not have a built-in registration endpoint.
      // You might need to create a custom endpoint or use the SDK to create a user.
      return await this.client.request(
        registerUser(email, password, {
          first_name: firstName,
          last_name: lastName,
          verification_url: appConfig.auth.verificationUrl,
          // cpf,
          // phone_number,
        } as any)
      );
    },
    verifyEmail: async (token: string) => {
      return await this.client.request(registerUserVerify(token));
    },
    requestPasswordReset: async (email: string) => {
      return await this.client.request(
        passwordRequest(email, appConfig.auth.resetUrl)
      );
    },
    /**
     * Reset user password using a token received via email.
     * @param token - Token to verify the user
     * @param newPassword - New password for the user
     * @returns
     */
    resetPassword: async (token: string, newPassword: string) => {
      return await this.client.request(passwordReset(token, newPassword));
    },
    changePassword: async (newPassword: string) => {
      // Directus does not have a built-in change password endpoint using a token.
      // You might need to create a custom endpoint or use the SDK to change the password.
      // This is a placeholder implementation and may need to be adjusted based on your Directus setup.
      return await this.client.request(updateMe({ password: newPassword }));
    },
    fetchUser: async () => {
      const token = await this.client.getToken();
      if (!token) throw new Error("No token found");
      // Try to fetch the user using the SDK (assuming "users" collection)
      const rawUser = await this.client.request(
        readMe({
          fields: ([
            "email",
            "first_name",
            "last_name",
            "avatar",
            "id",
            "role",
            // Custom profile fields
            "cpf",
            "phone_number",
          ] as any), // Adjust fields as needed
        })
      );
      return (
        (rawUser as OmitAndMerge<
          typeof rawUser,
          "avatar" | "role",
          { avatar: string | null; role: string | null } & {
            cpf?: string | null;
            phone_number?: string | null;
          }
        >) ?? null
      );
    },
    updateProfile: async (
      data: Partial<{
        first_name: string | null;
        last_name: string | null;
        cpf: string | null;
        phone_number: string | null;
      }>
    ) => {
      return await this.client.request(updateMe(data as any));
    },
    getToken: async () => {
      return await this.client.getToken();
    },
    setToken: async (token: string) => {
      return await this.client.setToken(token);
    },
  };

  public readonly project = this.crud("project");

  public readonly property = this.crud("property");

  public readonly propertyDevice = this.crud("property_device");

  public readonly device = this.crud("device");

  public readonly deviceMessageRequest = this.crud("device_message_request");

  public readonly reservoir = this.crud("reservoir");

  public readonly sector = this.crud("sector");

  public readonly waterPump = this.crud("water_pump");

  public readonly irrigationPlan = this.crud("irrigation_plan");

  public readonly meshDeviceMapping = this.crud("mesh_device_mapping");

  public readonly currentLICPacket = this.crud("current_lic_packet");

  public readonly currentProjectState = {
    ...this.crud("current_project_state"),

    /**
     * Get current project states by property ID
     * @param propertyId - Property ID to filter by
     * @returns Promise resolving to array of current project states
     */
    getByProperty: async (propertyId: string, minPacketDate: Date | null) => {
      const now = new Date();
      return await this.client.request(
        readItems("current_project_state", {
          fields: ["*"],
          filter: Object.assign(
            {},
            minPacketDate && {
              packet_date: { _gt: minPacketDate > now ? now : minPacketDate },
            },
            { project: { property: { _eq: propertyId } } }
          ) as QueryFilter<AppSchema, CurrentProjectState>,
        })
      );
    },
  };

  public readonly currentIrrigationPlanState = this.crud(
    "current_irrigation_plan_state"
  );

  public readonly currentReservoirState = {
    ...this.crud("current_reservoir_state"),
    getByProperty: async (propertyId: string) => {
      return (await this.client.request(
        readItems("current_reservoir_state", {
          fields: ["*"],
          filter: {
            reservoir: { property: { _eq: propertyId } },
          } as QueryFilter<AppSchema, any>,
          limit: -1,
        })
      )) as any;
    },
  };

  public readonly licState = this.crud("lic_state");

  public readonly irrigationPlanStep = {
    ...this.crud("irrigation_plan_step"),
    swapOrders: async (
      step1: Pick<CollectionItem<"irrigation_plan_step">, "id" | "order">,
      step2: Pick<CollectionItem<"irrigation_plan_step">, "id" | "order">
    ) => {
      return await this.client.request(
        updateItemsBatch("irrigation_plan_step", [
          { id: step1.id, order: step2.order },
          { id: step2.id, order: step1.order },
        ])
      );
    },
  };
}

export const apiService = DirectusApiService.getInstance();
