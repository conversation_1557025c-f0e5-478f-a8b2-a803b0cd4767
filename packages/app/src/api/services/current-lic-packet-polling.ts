import type { AppDirectusClient } from "../client";
import { loadCurrentLICPacketsForProperty } from "../queries/current-lic-packet";
import type { CurrentLICPacket } from "../model/current-lic-packet";

export interface CurrentLICPacketPollingConfig {
  /** Polling interval in milliseconds. Default: 5000 (5 seconds) */
  interval?: number;
  /** Whether polling is enabled. Default: true */
  enabled?: boolean;
  /** Callback when data is fetched successfully */
  onData?: (packets: CurrentLICPacket[]) => void;
  /** Callback when an error occurs during polling */
  onError?: (error: Error) => void;
}

export class CurrentLICPacketPollingService {
  private intervalId: NodeJS.Timeout | null = null;
  private isPolling = false;
  private config: Required<CurrentLICPacketPollingConfig>;
  private currentPropertyId: string | null = null;

  constructor(
    private readonly directus: AppDirectusClient,
    config: CurrentLICPacketPollingConfig = {}
  ) {
    this.config = {
      interval: 5000, // 5 seconds default
      enabled: true,
      onData: () => {}, // no-op default
      onError: () => {}, // no-op default
      ...config,
    };
  }

  /**
   * Start polling for a specific property
   */
  startPolling(propertyId: string, config?: Partial<CurrentLICPacketPollingConfig>): void {
    // Stop existing polling if running
    this.stopPolling();

    // Update config if provided
    if (config) {
      this.config = { ...this.config, ...config };
    }

    if (!this.config.enabled) {
      console.log("CurrentLICPacketPollingService: Polling is disabled");
      return;
    }

    this.currentPropertyId = propertyId;
    this.isPolling = true;

    console.log(`CurrentLICPacketPollingService: Starting polling for property ${propertyId} every ${this.config.interval}ms`);

    // Initial fetch
    this.fetchData();

    // Set up interval
    this.intervalId = setInterval(() => {
      this.fetchData();
    }, this.config.interval);
  }

  /**
   * Stop polling
   */
  stopPolling(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isPolling = false;
    this.currentPropertyId = null;
    console.log("CurrentLICPacketPollingService: Polling stopped");
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<CurrentLICPacketPollingConfig>): void {
    this.config = { ...this.config, ...config };

    // If polling is running and interval changed, restart with new interval
    if (this.isPolling && config.interval && this.currentPropertyId) {
      this.startPolling(this.currentPropertyId, { interval: config.interval });
    }
  }

  /**
   * Get current polling status
   */
  getStatus(): {
    isPolling: boolean;
    propertyId: string | null;
    interval: number;
    enabled: boolean;
  } {
    return {
      isPolling: this.isPolling,
      propertyId: this.currentPropertyId,
      interval: this.config.interval,
      enabled: this.config.enabled,
    };
  }

  /**
   * Manually fetch data (without affecting polling schedule)
   */
  async fetchDataManually(): Promise<CurrentLICPacket[]> {
    if (!this.currentPropertyId) {
      throw new Error("No property ID set for polling");
    }

    return await loadCurrentLICPacketsForProperty(this.directus, this.currentPropertyId);
  }

  /**
   * Internal method to fetch data and handle callbacks
   */
  private async fetchData(): Promise<void> {
    if (!this.currentPropertyId) {
      console.warn("CurrentLICPacketPollingService: No property ID set, skipping fetch");
      return;
    }

    try {
      const packets = await loadCurrentLICPacketsForProperty(this.directus, this.currentPropertyId);
      this.config.onData(packets);
    } catch (error) {
      console.error("CurrentLICPacketPollingService: Error fetching data:", error);
      this.config.onError(error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * Cleanup and dispose
   */
  dispose(): void {
    this.stopPolling();
    this.config.onData = () => {};
    this.config.onError = () => {};
  }
}