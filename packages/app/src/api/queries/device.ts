import { PickAndMerge } from "@/utils/types";
import { Device } from "../model/device";
import { PropertyDevice } from "../model/property-device";
import { DirectusApiService } from "../service";

export type DeviceOverview = PickAndMerge<
  Device,
  "id" | "identifier" | "date_created" | "model",
  {
    properties: Array<
      PickAndMerge<
        PropertyDevice,
        "id" | "start_date" | "end_date" | "device",
        {}
      >
    >;
  }
>;

export async function findDeviceOverviewByIdentifier(
  service: DirectusApiService,
  identifier: string
): Promise<DeviceOverview | undefined> {
  const device = await service.device.query({
    fields: [
      "id",
      "identifier",
      "date_created",
      "model",
      {
        properties: ["id", "start_date", "end_date", "device"],
      },
    ],
    filter: {
      identifier: {
        _eq: identifier,
      },
    },
  });
  return device.at(0) as DeviceOverview | undefined;
}
