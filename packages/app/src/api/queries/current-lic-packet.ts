import { readItems, Query } from "@directus/sdk";
import type { AppDirectusClient } from "../client";
import type { CurrentLICPacket } from "../model/current-lic-packet";
import { DirectusApiService } from "../service";

/**
 * Fetch current LIC packets for all LIC devices in a property
 * @param directus - Directus client instance
 * @param propertyId - Property ID to filter devices
 * @returns Array of current LIC packets
 */
export async function loadCurrentLICPacketsForProperty(
  directus: AppDirectusClient,
  propertyId: string
): Promise<CurrentLICPacket[]> {
  // First, get all LIC device IDs for the property
  const propertyDevicesResponse = await directus.request(
    readItems("property_device", {
      fields: ["*", { device: ["id", "model"] }],
      filter: {
        property: { _eq: propertyId },
        device: { model: { _eq: "LIC" } },
      },
    })
  );

  // Extract device IDs
  const licDeviceIds = propertyDevicesResponse
    .map((pd) => (pd.device as any)?.id)
    .filter(Boolean);

  if (licDeviceIds.length === 0) {
    return [];
  }

  // Fetch current LIC packets for these devices
  const query: Query<any, CurrentLICPacket> = {
    fields: ["*"],
    filter: {
      device: { _in: licDeviceIds },
    },
    sort: ["-packet_date"], // Most recent first
  };

  const packets = await directus.request(
    readItems("current_lic_packet", query)
  );

  return packets as unknown as CurrentLICPacket[];
}

/**
 * Fetch current LIC packets for a specific device
 * @param directus - Directus client instance
 * @param deviceId - Device ID to filter packets
 * @returns Array of current LIC packets for the device
 */
export async function loadCurrentLICPacketsForDevice(
  directus: AppDirectusClient,
  deviceId: string
): Promise<CurrentLICPacket[]> {
  const query: Query<any, CurrentLICPacket> = {
    fields: ["*"],
    filter: {
      device: { _eq: deviceId },
    },
    sort: ["-packet_date"], // Most recent first
  };

  const packets = await directus.request(
    readItems("current_lic_packet", query)
  );

  return packets as unknown as CurrentLICPacket[];
}

/**
 * Fetch current LIC packets for a specific device
 * @param service - DirectusApiService instance
 * @param deviceId - Device ID to filter packets
 * @returns Array of current LIC packets for the device
 */
export async function loadCurrentLICPacketsForDevices(
  service: DirectusApiService,
  deviceIds: string[]
): Promise<CurrentLICPacket[]> {
  const packets = await service.currentLICPacket.query({
    filter: {
      device: { _in: deviceIds },
    },
    sort: ["-packet_date"], // Most recent first
  });

  return packets;
}

/**
 * Fetch current LIC packet for a specific device and payload type
 * @param directus - Directus client instance
 * @param deviceId - Device ID
 * @param payloadType - Payload type to filter
 * @returns Single current LIC packet or null if not found
 */
export async function loadCurrentLICPacketForDeviceAndType(
  service: DirectusApiService,
  deviceId: string,
  payloadType: CurrentLICPacket["payload_type"]
): Promise<CurrentLICPacket | null> {
  const query: Query<any, CurrentLICPacket> = {
    fields: ["*"],
    filter: {
      device: { _eq: deviceId },
      payload_type: { _eq: payloadType },
    },
    limit: 1,
  };

  const packets = await service.currentLICPacket.query(query);
  return packets.length > 0 ? packets[0] : null;
}
