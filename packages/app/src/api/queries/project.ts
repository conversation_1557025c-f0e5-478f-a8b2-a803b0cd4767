import { readItems, Query } from "@directus/sdk";
import type { AppDirectusClient } from "../client";
import type { CurrentProjectState } from "../model/current-project-state";
import { DirectusApiService } from "../service";

/**
 * Fetch current project states for all projects in a property
 * @param directus - Directus client instance
 * @param propertyId - Property ID to filter projects
 * @returns Array of current project states
 */
export async function loadCurrentProjectStatesByProperty(
  directus: AppDirectusClient,
  propertyId: string
): Promise<CurrentProjectState[]> {
  const query: Query<any, CurrentProjectState> = {
    fields: [
      "*",
      {
        project: ["id", "name", "property"],
      },
    ],
    filter: {
      project: {
        property: { _eq: propertyId },
      },
    },
    sort: ["project.name"], // Sort by project name
  };

  const projectStates = await directus.request(
    readItems("current_project_state", query)
  );

  return projectStates as unknown as CurrentProjectState[];
}

/**
 * Fetch current project states for all projects in a property using service
 * @param service - DirectusApiService instance
 * @param propertyId - Property ID to filter projects
 * @returns Array of current project states
 */
export async function loadCurrentProjectStatesByPropertyService(
  service: DirectusApiService,
  propertyId: string
): Promise<CurrentProjectState[]> {
  return await service.currentProjectState.getByProperty(propertyId, null);
}

/**
 * Fetch current project state for a specific project
 * @param directus - Directus client instance
 * @param projectId - Project ID to filter
 * @returns Single current project state or null if not found
 */
export async function loadCurrentProjectStateByProject(
  directus: AppDirectusClient,
  projectId: string
): Promise<CurrentProjectState | null> {
  const query: Query<any, CurrentProjectState> = {
    fields: [
      "*",
      {
        project: ["id", "name", "property"],
      },
    ],
    filter: {
      project: { _eq: projectId },
    },
    limit: 1,
  };

  const projectStates = await directus.request(
    readItems("current_project_state", query)
  );

  return projectStates.length > 0 ? (projectStates[0] as unknown as CurrentProjectState) : null;
}

/**
 * Fetch current project state for a specific project using service
 * @param service - DirectusApiService instance
 * @param projectId - Project ID to filter
 * @returns Single current project state or null if not found
 */
export async function loadCurrentProjectStateByProjectService(
  service: DirectusApiService,
  projectId: string
): Promise<CurrentProjectState | null> {
  const query: Query<any, CurrentProjectState> = {
    fields: [
      "*",
      {
        project: ["id", "name", "property"],
      },
    ],
    filter: {
      project: { _eq: projectId },
    },
    limit: 1,
  };

  const projectStates = await service.currentProjectState.query(query);
  return projectStates.length > 0 ? projectStates[0] : null;
}

/**
 * Fetch current project states for multiple projects
 * @param service - DirectusApiService instance
 * @param projectIds - Array of project IDs to filter
 * @returns Array of current project states
 */
export async function loadCurrentProjectStatesByProjects(
  service: DirectusApiService,
  projectIds: string[]
): Promise<CurrentProjectState[]> {
  if (projectIds.length === 0) {
    return [];
  }

  const query: Query<any, CurrentProjectState> = {
    fields: [
      "*",
      {
        project: ["id", "name", "property"],
      },
    ],
    filter: {
      project: { _in: projectIds },
    },
    sort: ["project.name"], // Sort by project name
  };

  return await service.currentProjectState.query(query);
}
