import React, { useEffect } from "react";
import "./Toast.css";
import { use<PERSON>tom, useSet<PERSON>tom } from "jotai";
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from "lucide-react";
import { Button } from "./ui/Button";
import {
  toasts<PERSON>tom,
  removeToast<PERSON>tom,
  addToast<PERSON>tom,
  removeAllToasts<PERSON>tom,
  showSuccessToast<PERSON>tom,
  showErrorToast<PERSON>tom,
  showWarningToast<PERSON>tom,
  showInfoToast<PERSON>tom,
  type ToastData,
  type ToastPosition,
} from "@/store";

interface ToastProps {
  toast: ToastData;
}

function Toast({ toast }: ToastProps) {
  const removeToast = useSetAtom(removeToastAtom);
  const {
    id,
    title,
    message,
    type = "info",
    duration = 5000,
    actionLabel,
    onAction,
    attention = false,
  } = toast;

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        removeToast(id);
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [id, duration, removeToast]);

  const getTypeStyles = () => {
    switch (type) {
      case "success":
        return {
          container: attention
            ? "bg-green-100 border-green-400 text-green-900 ring-2 ring-green-500/40 shadow-lg"
            : "bg-green-50 border-green-200 text-green-900",
          icon: (
            <CheckCircle
              className={`${
                attention ? "h-6 w-6 text-green-600" : "h-5 w-5 text-green-500"
              }`}
            />
          ),
        };
      case "error":
        return {
          container: attention
            ? "bg-red-100 border-red-400 text-red-900 ring-2 ring-red-500/40 shadow-lg"
            : "bg-red-50 border-red-200 text-red-900",
          icon: (
            <AlertCircle
              className={`${
                attention ? "h-6 w-6 text-red-600" : "h-5 w-5 text-red-500"
              }`}
            />
          ),
        };
      case "warning":
        return {
          container: attention
            ? "bg-yellow-100 border-yellow-400 text-yellow-900 ring-2 ring-yellow-500/40 shadow-lg"
            : "bg-yellow-50 border-yellow-200 text-yellow-900",
          icon: (
            <AlertTriangle
              className={`${
                attention
                  ? "h-6 w-6 text-yellow-600"
                  : "h-5 w-5 text-yellow-500"
              }`}
            />
          ),
        };
      case "info":
      default:
        return {
          container: attention
            ? "bg-blue-100 border-blue-400 text-blue-900 ring-2 ring-blue-500/40 shadow-lg"
            : "bg-blue-50 border-blue-200 text-blue-900",
          icon: (
            <Info
              className={`${
                attention ? "h-6 w-6 text-blue-600" : "h-5 w-5 text-blue-500"
              }`}
            />
          ),
        };
    }
  };

  const styles = getTypeStyles();

  const getProgressBarStyles = () => {
    switch (type) {
      case "success":
        return {
          background: attention ? "bg-green-300" : "bg-green-200",
          progress: attention ? "bg-green-700" : "bg-green-600",
        };
      case "error":
        return {
          background: attention ? "bg-red-300" : "bg-red-200",
          progress: attention ? "bg-red-700" : "bg-red-600",
        };
      case "warning":
        return {
          background: attention ? "bg-yellow-300" : "bg-yellow-200",
          progress: attention ? "bg-yellow-700" : "bg-yellow-600",
        };
      case "info":
      default:
        return {
          background: attention ? "bg-blue-300" : "bg-blue-200",
          progress: attention ? "bg-blue-700" : "bg-blue-600",
        };
    }
  };

  const progressStyles = getProgressBarStyles();

  return (
    <div
      className={`relative p-4 mb-3 rounded-lg border ${
        attention ? "shadow-lg animate-[toast-pop_180ms_ease-out]" : "shadow-sm"
      } ${styles.container}`}
      data-attention={attention || undefined}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {styles.icon}
          <div>
            {title && <p className="text-sm font-semibold mb-1">{title}</p>}
            <p
              className={`text-sm ${
                attention
                  ? "font-semibold"
                  : title
                  ? "font-normal"
                  : "font-medium"
              }`}
            >
              {message}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {actionLabel && onAction && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onAction}
              className="text-sm font-medium underline hover:no-underline"
            >
              {actionLabel}
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => removeToast(id)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Progress bar for auto-dismiss countdown */}
      {duration > 0 && (
        <div className="mt-3">
          <div
            className={`${progressStyles.background} rounded-full ${
              attention ? "h-1.5" : "h-1"
            }`}
          >
            <div
              className={`${progressStyles.progress} h-1 rounded-full`}
              style={{
                animation: `shrink ${duration}ms linear forwards`,
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}

interface ToastContainerProps {
  position?: ToastPosition;
}

function ToastContainer({ position = "top-center" }: ToastContainerProps) {
  const [toasts] = useAtom(toastsAtom);

  if (toasts.length === 0) return null;

  const getPositionClasses = (pos: ToastPosition) => {
    switch (pos) {
      case "top-left":
        return "top-4 left-4";
      case "top-center":
        return "top-4 left-1/2 transform -translate-x-1/2";
      case "top-right":
        return "top-4 right-4";
      case "bottom-left":
        return "bottom-4 left-4";
      case "bottom-center":
        return "bottom-4 left-1/2 transform -translate-x-1/2";
      case "bottom-right":
        return "bottom-4 right-4";
      case "center":
        return "top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2";
      default:
        return "top-4 right-4";
    }
  };

  // Group toasts by their effective position (toast.position overrides prop default)
  const positions: ToastPosition[] = [
    "top-left",
    "top-center",
    "top-right",
    "bottom-left",
    "bottom-center",
    "bottom-right",
    "center",
  ];

  const grouped = new Map<ToastPosition, ToastData[]>();
  for (const t of toasts) {
    const pos = (t.position ?? position) as ToastPosition;
    const list = grouped.get(pos) ?? [];
    list.push(t);
    grouped.set(pos, list);
  }

  return (
    <>
      {positions
        .filter((pos) => grouped.has(pos))
        .map((pos) => (
          <div
            key={pos}
            className={`fixed z-70 max-w-sm w-full ${getPositionClasses(pos)}`}
          >
            {grouped.get(pos)!.map((toast) => (
              <Toast key={toast.id} toast={toast} />
            ))}
          </div>
        ))}
    </>
  );
}

// Hook for managing toasts (using store)
export function useToast() {
  const [toasts] = useAtom(toastsAtom);
  const addToast = useSetAtom(addToastAtom);
  const removeToast = useSetAtom(removeToastAtom);
  const removeAllToasts = useSetAtom(removeAllToastsAtom);
  const showSuccess = useSetAtom(showSuccessToastAtom);
  const showError = useSetAtom(showErrorToastAtom);
  const showWarning = useSetAtom(showWarningToastAtom);
  const showInfo = useSetAtom(showInfoToastAtom);

  return {
    toasts,
    addToast,
    removeToast,
    removeAllToasts,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };
}

export { Toast, ToastContainer };
export default ToastContainer;
