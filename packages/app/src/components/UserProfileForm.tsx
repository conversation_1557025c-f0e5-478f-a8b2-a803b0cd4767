import React, { useMemo, useState } from "react";
import Button from "@/components/ui/Button";
import {
  cleanCPF,
  cleanPhoneBR,
  formatCPF,
  formatPhoneBR,
  validateCPF,
  validatePhoneBR,
} from "@/utils/br";

export type UserProfileFormValues = {
  first_name?: string | null;
  last_name?: string | null;
  cpf: string;
  phone_number: string;
  email?: string | null;
};

export interface UserProfileFormProps {
  initialValues?: Partial<UserProfileFormValues>;
  onSubmit: (data: {
    first_name?: string | null;
    last_name?: string | null;
    cpf: string | null;
    phone_number: string | null;
  }) => Promise<void> | void;
  onCancel?: () => void;
  submitLabel?: string;
  showNameFields?: boolean;
  email?: string | null;
}

export default function UserProfileForm({
  initialValues,
  onSubmit,
  onCancel,
  submitLabel = "Salvar",
  showNameFields = true,
  email,
}: UserProfileFormProps) {
  const [firstName, setFirstName] = useState(initialValues?.first_name ?? "");
  const [lastName, setLastName] = useState(initialValues?.last_name ?? "");
  const [cpf, setCpf] = useState(() => formatCPF(initialValues?.cpf ?? ""));
  const [phone, setPhone] = useState(() =>
    formatPhoneBR(initialValues?.phone_number ?? "")
  );
  const [errors, setErrors] = useState<{
    [K in keyof UserProfileFormValues]?: string;
  }>({});
  const [submitting, setSubmitting] = useState(false);

  const canSubmit = useMemo(() => {
    return cpf.trim().length > 0 && phone.trim().length > 0;
  }, [cpf, phone]);

  function validate() {
    const errs: typeof errors = {};
    if (!validateCPF(cpf)) errs.cpf = "CPF inválido";
    if (!validatePhoneBR(phone)) errs.phone_number = "Telefone inválido";
    setErrors(errs);
    return Object.keys(errs).length === 0;
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!validate()) return;
    setSubmitting(true);
    try {
      await onSubmit({
        first_name: showNameFields ? firstName || null : undefined,
        last_name: showNameFields ? lastName || null : undefined,
        cpf: cleanCPF(cpf),
        phone_number: cleanPhoneBR(phone),
      });
    } finally {
      setSubmitting(false);
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {showNameFields && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {email && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                E-mail:
                <span className="w-full text-lg px-4 py-3 rounded-xl text-gray-600 cursor-not-allowed">
                  {email}
                </span>
              </label>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome
            </label>
            <input
              type="text"
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              placeholder="Seu nome"
              autoComplete="given-name"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sobrenome
            </label>
            <input
              type="text"
              className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              placeholder="Seu sobrenome"
              autoComplete="family-name"
            />
          </div>
        </div>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          CPF
        </label>
        <input
          type="text"
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
          value={cpf}
          onChange={(e) => setCpf(formatCPF(e.target.value))}
          placeholder="000.000.000-00"
          maxLength={14}
          inputMode="numeric"
        />
        {errors.cpf && (
          <p className="mt-1 text-xs text-red-600">{errors.cpf}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Telefone
        </label>
        <input
          type="tel"
          className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
          value={phone}
          onChange={(e) => setPhone(formatPhoneBR(e.target.value))}
          placeholder="(00) 00000-0000"
          inputMode="tel"
        />
        {errors.phone_number && (
          <p className="mt-1 text-xs text-red-600">{errors.phone_number}</p>
        )}
      </div>

      <div className="flex gap-2 pt-2">
        {onCancel && (
          <Button
            type="button"
            variant="secondary"
            className="flex-1"
            onClick={onCancel}
            disabled={submitting}
          >
            Cancelar
          </Button>
        )}
        <Button
          type="submit"
          variant="primary"
          className="flex-1"
          disabled={!canSubmit || submitting}
          loading={submitting}
        >
          {submitLabel}
        </Button>
      </div>
    </form>
  );
}
