import React, { forwardRef } from "react";
import clsx from "clsx";
import { getDesignClass } from "@/utils/design-system";
import { makeOutline, makeGhost } from "./utils";
import { twMerge } from "tailwind-merge";

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: keyof typeof variantClasses;
  size?: "xs" | "sm" | "md" | "lg";
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
  fullWidth?: boolean;
  outline?: boolean;
  ghost?: boolean;
}

const sizeClasses: Record<NonNullable<ButtonProps["size"]>, string> = {
  xs: "px-2 py-1 text-xs min-h-4",
  sm: "px-3 py-1.5 text-sm min-h-8",
  md: "px-6 py-3 text-base min-h-12",
  lg: "px-8 py-4 text-lg min-h-14",
};

const baseFocus =
  "focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2";

const variantClasses = {
  primary:
    // align with report: green-600/700/800, disabled neutral, rounded-lg, transition 150
    "bg-green-600 hover:bg-green-700 active:bg-green-800 disabled:bg-neutral-300 disabled:text-neutral-500 text-white font-medium rounded-lg transition-all duration-150 ease-in-out",
  secondary:
    "bg-transparent border border-green-600 text-green-600 hover:bg-green-50 active:bg-green-100 disabled:bg-neutral-300 disabled:text-neutral-500 disabled:border-neutral-300 font-medium rounded-lg transition-all duration-150 ease-in-out",
  ghost:
    "bg-transparent text-green-600 hover:bg-green-50 active:bg-green-100 disabled:text-neutral-400 font-medium rounded-lg transition-all duration-150 ease-in-out",
  destructive:
    "bg-red-600 hover:bg-red-700 active:bg-red-800 disabled:bg-neutral-300 text-white font-medium rounded-lg transition-all duration-150 ease-in-out",
  warning:
    "bg-yellow-500 hover:bg-yellow-600 active:bg-yellow-700 disabled:bg-neutral-300 disabled:text-neutral-500 text-white font-medium rounded-lg transition-all duration-150 ease-in-out",
  info: "bg-blue-500 hover:bg-blue-600 active:bg-blue-700 disabled:bg-neutral-300 disabled:text-neutral-500 text-white font-medium rounded-lg transition-all duration-150 ease-in-out",
  neutral:
    "bg-neutral-600 hover:bg-neutral-700 active:bg-neutral-800 disabled:bg-neutral-300 disabled:text-neutral-500 text-white font-medium rounded-lg transition-all duration-150 ease-in-out",
} as const;

function Spinner({ className }: { className?: string }) {
  return (
    <svg
      className={clsx("animate-spin", className)}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      />
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
      />
    </svg>
  );
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = "primary",
      size = "md",
      loading = false,
      icon,
      iconPosition = "left",
      fullWidth = false,
      outline = false,
      ghost = false,
      className,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || loading;

    const variantBase =
      getDesignClass("button", variant as string) || variantClasses[variant];

    const baseWithStyle = outline
      ? makeOutline(variantBase)
      : ghost
      ? makeGhost(variantBase)
      : variantBase;

    const classes = clsx(
      baseWithStyle,
      sizeClasses[size],
      baseFocus,
      "inline-flex items-center justify-center gap-2 select-none",
      fullWidth && "w-full",
      isDisabled && "disabled:opacity-50 disabled:cursor-not-allowed",
      className
    );

    // Normalize icon sizing by size
    const iconSize =
      size === "xs"
        ? "h-2 w-2"
        : size === "sm"
        ? "h-4 w-4"
        : size === "lg"
        ? "h-6 w-6"
        : "h-5 w-5";

    return (
      <button
        ref={ref}
        className={twMerge(classes)}
        disabled={isDisabled}
        aria-busy={loading || undefined}
        {...props}
      >
        {loading && <Spinner className={clsx(iconSize)} />}

        {icon && iconPosition === "left" && !loading && (
          <span className={clsx(iconSize, "inline-flex items-center")}>
            {icon}
          </span>
        )}

        {children && <span className="whitespace-nowrap">{children}</span>}

        {icon && iconPosition === "right" && !loading && (
          <span className={clsx(iconSize, "inline-flex items-center")}>
            {icon}
          </span>
        )}
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
