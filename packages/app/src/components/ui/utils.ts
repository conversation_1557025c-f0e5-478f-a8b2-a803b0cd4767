export function makeOutline(classNames: string): string {
  const parts = classNames.split(/\s+/);
  const bgByState: Record<string, string[]> = {};
  const seenBorder: Record<string, boolean> = {};

  for (const p of parts) {
    const m = p.match(/^(?:(hover|active|disabled):)?(bg-[^\s:]+)/);
    if (m) {
      const state = m[1] || "base";
      const bg = m[2];
      (bgByState[state] ||= []).push(bg);
    }
    const b = p.match(/^(?:(hover|active|disabled):)?(border(?:-[^\s:]+)?)$/);
    if (b) {
      const state = b[1] || "base";
      seenBorder[state] = true;
    }
  }

  const stateOrder = ["base", "hover", "active", "disabled"] as const;
  const chosen: Record<string, string | null> = {};
  for (const s of stateOrder) {
    chosen[s] = bgByState[s]?.[0] || null;
  }

  const out: string[] = [];
  for (const p of parts) {
    const mm = p.match(/^(?:(hover|active|disabled):)?(.+)$/);
    if (!mm) {
      out.push(p);
      continue;
    }
    const state = (mm[1] as keyof typeof chosen) || "base";
    let core = mm[2];

    if (/^bg-/.test(core)) {
      core = "bg-transparent";
      out.push((mm[1] ? mm[1] + ":" : "") + core);
      continue;
    }

    if (/^text-/.test(core) || /^border(?:-[^\s:]+)?$/.test(core)) {
      const bg = chosen[state] || chosen.base;
      if (bg) {
        const color = bg.replace(/^bg-/, "");
        core = /^text-/.test(core) ? `text-${color}` : `border-${color}`;
        out.push((mm[1] ? mm[1] + ":" : "") + core);
        if (!/^text-/.test(core)) seenBorder[state] = true;
        continue;
      }
    }

    out.push(p);
  }

  let anyBorder = false;
  for (const s of stateOrder) {
    const bg = chosen[s] || chosen.base;
    if (!bg) continue;
    if (seenBorder[s]) {
      anyBorder = true;
      continue;
    }
    const color = bg.replace(/^bg-/, "");
    const prefix = s === "base" ? "" : `${s}:`;
    out.push(`${prefix}border-${color}`);
    anyBorder = true;
  }

  if (anyBorder && !parts.some(p => /^border$/.test(p))) {
    out.push("border");
  }

  return out.join(" ");
}

export function makeGhost(classNames: string): string {
  const parts = classNames.split(/\s+/);
  const bgByState: Record<string, string[]> = {};

  for (const p of parts) {
    const m = p.match(/^(?:(hover|active|disabled):)?(bg-[^\s:]+)/);
    if (m) {
      const state = m[1] || "base";
      const bg = m[2];
      (bgByState[state] ||= []).push(bg);
    }
  }

  const stateOrder = ["base", "hover", "active", "disabled"] as const;
  const chosen: Record<string, string | null> = {};
  for (const s of stateOrder) {
    chosen[s] = bgByState[s]?.[0] || null;
  }

  const out: string[] = [];
  for (const p of parts) {
    const mm = p.match(/^(?:(hover|active|disabled):)?(.+)$/);
    if (!mm) {
      out.push(p);
      continue;
    }
    const state = (mm[1] as keyof typeof chosen) || "base";
    let core = mm[2];

    if (/^bg-/.test(core)) {
      core = "bg-transparent";
      out.push((mm[1] ? mm[1] + ":" : "") + core);
      continue;
    }

    if (/^text-/.test(core)) {
      const bg = chosen[state] || chosen.base;
      if (bg) {
        const color = bg.replace(/^bg-/, "");
        core = `text-${color}`;
        out.push((mm[1] ? mm[1] + ":" : "") + core);
        continue;
      }
    }

    // drop border classes entirely
    if (/^border(?:-[^\s:]+)?$/.test(core)) {
      continue;
    }

    out.push(p);
  }

  return out.join(" ");
}
