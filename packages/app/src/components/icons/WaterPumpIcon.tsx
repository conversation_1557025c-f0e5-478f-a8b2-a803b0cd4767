interface WaterPumpIconProps {
  size?: number;
  className?: string;
  color?: string;
}

function WaterPumpIcon({
  size = 24,
  className = "",
  color = "currentColor",
}: WaterPumpIconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`lucide lucide-water-pump ${className}`}
    >
      <g transform="scale(1, 1.2) translate(0, -2.4)">
        <path
          fill="currentColor"
          d="M2.5 17.5h5.175c-0.48335 -0.38335 -0.91665 -0.82915 -1.3 -1.3375 -0.38335 -0.50835 -0.69165 -1.0625 -0.925 -1.6625H2.5v3Zm9.49475 0c1.52015 0 2.81775 -0.53575 3.89275 -1.60725 1.075 -1.0715 1.6125 -2.36735 1.6125 -3.8875 0 -1.52015 -0.53575 -2.81775 -1.60725 -3.89275 -1.0715 -1.075 -2.36735 -1.6125 -3.8875 -1.6125 -1.52015 0 -2.81775 0.53575 -3.89275 1.60725 -1.075 1.0715 -1.6125 2.36735 -1.6125 3.8875 0 1.52015 0.53575 2.81775 1.60725 3.89275 1.0715 1.075 2.36735 1.6125 3.8875 1.6125ZM18.55 9.5H21.5v-3H16.325c0.48335 0.38335 0.91665 0.82915 1.3 1.3375 0.38335 0.50835 0.69165 1.0625 0.925 1.6625ZM1 20V12h1.5v1h2.575c-0.03335 -0.16665 -0.05415 -0.32915 -0.0625 -0.4875C5.00415 12.35415 5 12.18335 5 12c0 -1.95 0.67915 -3.60415 2.0375 -4.9625C8.39585 5.67915 10.05 5 12 5h9.5V4h1.5v8h-1.5v-1h-2.575c0.03335 0.16665 0.05415 0.32915 0.0625 0.4875 0.00835 0.15835 0.0125 0.32915 0.0125 0.5125 0 1.95 -0.67915 3.60415 -2.0375 4.9625C15.60415 18.32085 13.95 19 12 19H2.5v1H1Zm10.99775 -5.4c-0.48185 0 -0.8936 -0.17135 -1.23525 -0.514 -0.34165 -0.34285 -0.5125 -0.75485 -0.5125 -1.236 0 -0.38335 0.05835 -0.70835 0.175 -0.975 0.11665 -0.26665 0.375 -0.7 0.775 -1.3L12 9.4l0.8 1.175c0.4 0.6 0.65835 1.03335 0.775 1.3 0.11665 0.26665 0.175 0.59165 0.175 0.975 0 0.48115 -0.1716 0.89315 -0.51475 1.236 -0.34315 0.34265 -0.75565 0.514 -1.2375 0.514Z"
          strokeWidth="0.5"
        />
      </g>
    </svg>
  );
}

export default WaterPumpIcon;
