import { createDeviceMessageRequestAtom } from "@/store/crud";
import { deviceToLicMapAtom } from "@/store/data";
import { useAtomValue, useSetAtom } from "jotai";
import { useCallback } from "react";
import { useToast } from "./Toast";
import Button from "./ui/Button";

type DeviceActivationPanelProps = {
  elementType: "pump" | "valve";
  elementId: string;
  // Need to find the LIC
  elementControllerId: string;
  elementVariation?: "backwash" | undefined;
  turnOnDurationMinutes: number;
  title?: string;
};

export function DeviceActivationPanel({
  elementType,
  elementId,
  elementControllerId,
  elementVariation,
  turnOnDurationMinutes,
  title,
}: DeviceActivationPanelProps) {
  const createDeviceMessageRequest = useSetAtom(createDeviceMessageRequestAtom);
  const deviceToLicMap = useAtomValue(deviceToLicMapAtom);
  const { showInfo, showWarning } = useToast();

  const handleTestActivation = useCallback(
    async (turnOn: boolean) => {
      const lic = deviceToLicMap.get(elementControllerId);
      if (!lic) {
        showWarning({
          message: `Controlador de Irrigação não encontrado para o ${
            elementType === "pump"
              ? "controlador de bomba"
              : "controlador de válvula"
          }.`,
        });
        return;
      }
      await createDeviceMessageRequest({
        device: lic,
        payload_type: "control",
        payload_data: {
          elementType,
          elementId,
          elementVariation,
          turnOn,
          durationMinutes: turnOn ? turnOnDurationMinutes : undefined,
        },
      });
      showInfo({
        message: `Ativação de ${
          elementType === "pump" ? "bomba" : "válvula"
        } enviada com sucesso.`,
      });
    },
    [
      elementType,
      elementId,
      elementControllerId,
      elementVariation,
      turnOnDurationMinutes,
      createDeviceMessageRequest,
      deviceToLicMap,
    ]
  );

  return (
    <div className="flex flex-col border-gray-200 border rounded-lg p-2">
      <div className="text-sm font-medium text-gray-700 text-center">
        {title ?? "Testar Acionamento"}
      </div>
      <div className="flex gap-2 justify-center mt-2">
        <Button
          variant="info"
          outline
          type="button"
          className="flex-1"
          onClick={() => handleTestActivation(true)}
        >
          Ligar
        </Button>
        <Button
          variant="destructive"
          outline
          type="button"
          className="flex-1"
          onClick={() => handleTestActivation(false)}
        >
          Desligar
        </Button>
      </div>
    </div>
  );
}
