import React, { useMemo, useState } from "react";
import { useAtomValue } from "jotai";
import { ChevronDown } from "lucide-react";
import {
  rainfallDataByPropertyAtom,
  selectedPropertyAtom,
} from "@/store";
import { dayjs } from "@/utils/date";

function formatMm(v: number | null | undefined) {
  if (v == null || Number.isNaN(v)) return "—";
  return `${v.toFixed(1)} mm`;
}

export const RainfallCard: React.FC = () => {
  const [expanded, setExpanded] = useState(false);
  const property = useAtomValue(selectedPropertyAtom);
  const rainfall = useAtomValue(rainfallDataByPropertyAtom);

  const summary = useMemo(() => {
    return {
      rainingLabel: rainfall.rainingAny ? "sim" : "não",
      avgLabel:
        rainfall.averageRainfallMm != null
          ? formatMm(rainfall.averageRainfallMm)
          : "—",
      gaugeCount: rainfall.gaugeCount,
    };
  }, [rainfall]);

  const isDisabled = !property?.rain_gauge_enabled;

  return (
    <div className="rounded-xl shadow-sm border border-gray-200 overflow-hidden bg-white">
      <button
        type="button"
        className="w-full p-4 text-left transition-colors flex items-start justify-between gap-4"
        onClick={() => setExpanded((e) => !e)}
        aria-expanded={expanded}
      >
        <div className="min-w-0 flex-1">
          <div className="flex items-center justify-between gap-3">
            <h3 className="text-base font-semibold text-gray-900 truncate">
              Chuva (24h)
            </h3>
            <ChevronDown
              className={`w-5 h-5 text-gray-500 transition-transform ${
                expanded ? "rotate-180" : "rotate-0"
              }`}
            />
          </div>

          {isDisabled ? (
            <div className="mt-2 text-sm text-gray-600">
              Pluviômetro desativado na propriedade.
            </div>
          ) : (
            <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-2">
              <div className="text-sm text-gray-700">
                <span className="block text-xs text-gray-500">Chovendo</span>
                <span className="text-gray-900">{summary.rainingLabel}</span>
              </div>
              <div className="text-sm text-gray-700">
                <span className="block text-xs text-gray-500">Acumulado 24h</span>
                <span className="text-gray-900">{summary.avgLabel}</span>
              </div>
              <div className="text-sm text-gray-700">
                <span className="block text-xs text-gray-500">Pluviômetros</span>
                <span className="text-gray-900">{summary.gaugeCount}</span>
              </div>
            </div>
          )}
        </div>
      </button>

      {expanded && !isDisabled && (
        <div className="border-t border-gray-100">
          {rainfall.details.length === 0 ? (
            <div className="p-4 text-sm text-gray-600">
              Nenhum dado de chuva disponível.
            </div>
          ) : (
            <ul className="divide-y divide-gray-100">
              {rainfall.details.map((d) => (
                <li key={d.deviceId} className="flex items-center justify-between p-3">
                  <div className="min-w-0">
                    <div className="text-sm font-medium text-gray-900 truncate">
                      {d.label}
                    </div>
                    <div className="text-xs text-gray-500">
                      Atualizado {dayjs(d.packetDate).calendar()}
                    </div>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <span
                      className={`px-2 py-1 rounded-md border text-xs ${
                        d.raining
                          ? "bg-blue-50 text-blue-700 border-blue-200"
                          : "bg-gray-50 text-gray-700 border-gray-200"
                      }`}
                    >
                      {d.raining ? "Chovendo" : "Sem chuva"}
                    </span>
                    <span className="text-gray-900">{formatMm(d.rainfallMm)}</span>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

export default RainfallCard;

