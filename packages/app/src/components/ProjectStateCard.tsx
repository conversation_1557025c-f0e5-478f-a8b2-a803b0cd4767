import React, { useMemo, useState } from "react";
import type { AUTProject } from "@/api/queries/account";
import { useAtomValue } from "jotai";
import {
  currentProjectStateByProjectIdAtom,
  sectorsByProjectIdAtom,
} from "@/store/data";
import { dayjs } from "@/utils/date";
import { ChevronDown } from "lucide-react";

type Status = "active" | "inactive" | "error";

function statusColor(status: Status | null | undefined) {
  switch (status) {
    case "active":
      return "bg-green-100 text-green-700 border-green-200";
    case "error":
      return "bg-red-100 text-red-700 border-red-200";
    case "inactive":
    default:
      return "bg-gray-100 text-gray-700 border-gray-200";
  }
}

function statusLabel(
  status: Status | null | undefined,
  statusType: "pump" | "sector"
) {
  switch (status) {
    case "active":
      return statusType === "pump" ? "Ativa" : "Ativo";
    case "error":
      return "Erro";
    case "inactive":
    default:
      return statusType === "pump" ? "Inativa" : "Inativo";
  }
}

export interface ProjectStateCardProps {
  project: AUTProject;
}

export const ProjectStateCard: React.FC<ProjectStateCardProps> = ({
  project,
}) => {
  const [expanded, setExpanded] = useState(false);
  const getCurrentState = useAtomValue(currentProjectStateByProjectIdAtom);
  const projectSectors = useAtomValue(
    useMemo(() => sectorsByProjectIdAtom, [])
  )(project.id);

  const state = getCurrentState(project.id);

  // Build sector status map from state; default non-listed to inactive for counts/UI
  const sectorStatusMap = useMemo(() => {
    const map = new Map<string, Status>();
    state?.sectors?.forEach((s) => map.set(s.sector, s.status as Status));
    return map;
  }, [state]);

  const sectorsWithStatus = useMemo(() => {
    return (projectSectors || []).map((sec) => {
      const status = sectorStatusMap.get(sec.id) || ("inactive" as Status);
      return {
        id: sec.id,
        name: sec.name,
        status,
      };
    });
  }, [projectSectors, sectorStatusMap]);

  const counts = useMemo(() => {
    let active = 0,
      inactive = 0,
      error = 0;
    for (const s of sectorsWithStatus) {
      if (s.status === "active") active++;
      else if (s.status === "error") error++;
      else inactive++;
    }
    return { active, inactive, error, total: sectorsWithStatus.length };
  }, [sectorsWithStatus]);

  const lastActivity = state?.packet_date || null;

  return (
    <div
      className={`${
        state?.irrigation_status === "active" ? "bg-green-50" : "bg-white"
      } rounded-xl shadow-sm border border-gray-200 overflow-hidden"}`}
      role="region"
      aria-label={`Estado do projeto ${project.name}`}
    >
      {/* Header / Summary */}
      <button
        type="button"
        className={`${
          state?.irrigation_status === "active" ? "bg-green-50" : "bg-white"
        } w-full p-4 text-left transition-colors flex items-start justify-between gap-4`}
        onClick={() => setExpanded((e) => !e)}
        aria-expanded={expanded}
      >
        <div
          className={`${
            state?.irrigation_status === "active" ? "bg-green-50" : "bg-white"
          } min-w-0 flex-1`}
        >
          <div className="flex items-center justify-between gap-3">
            <h3 className="text-base font-semibold text-gray-900 truncate">
              Projeto {project.name}
            </h3>
            <ChevronDown
              className={`w-5 h-5 text-gray-500 transition-transform ${
                expanded ? "rotate-180" : "rotate-0"
              }`}
            />
          </div>

          <div className="mt-2 grid grid-cols-1 md:grid-cols-4 gap-2">
            <div className="text-sm text-gray-600">
              <span className="block text-xs text-gray-500">
                Última atividade
              </span>
              <span className="text-gray-800">
                {lastActivity ? dayjs(lastActivity).calendar() : "—"}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <span
                className={`px-2 py-1 rounded-md border text-xs ${statusColor(
                  state?.irrigation_status as Status
                )}`}
              >
                Irrigação:{" "}
                {statusLabel(state?.irrigation_status as Status, "pump")}
              </span>
              <span
                className={`px-2 py-1 rounded-md border text-xs ${statusColor(
                  state?.fertigation_status as Status
                )}`}
              >
                Fertirrigação:{" "}
                {statusLabel(state?.fertigation_status as Status, "pump")}
              </span>
              <span
                className={`px-2 py-1 rounded-md border text-xs ${statusColor(
                  state?.backwash_status as Status
                )}`}
              >
                Retrolavagem:{" "}
                {statusLabel(state?.backwash_status as Status, "pump")}
              </span>
            </div>

            <div className="flex items-center gap-2 text-sm text-gray-700">
              <span className="text-sm text-gray-900">Setores:</span>
              <span className="px-2 py-1 rounded-md bg-green-50 text-green-700 border border-green-200 text-xs">
                Ativos: {counts.active}
              </span>
              <span className="px-2 py-1 rounded-md bg-gray-50 text-gray-700 border border-gray-200 text-xs">
                Inativos: {counts.inactive}
              </span>
              <span className="px-2 py-1 rounded-md bg-red-50 text-red-700 border border-red-200 text-xs">
                Erros: {counts.error}
              </span>
            </div>
          </div>
        </div>
      </button>

      {/* Details: sectors list */}
      {expanded && (
        <div className="border-t border-gray-100">
          {sectorsWithStatus.length === 0 ? (
            <div className="p-4 text-sm text-gray-600">
              Nenhum setor cadastrado.
            </div>
          ) : (
            <ul className="divide-y divide-gray-100">
              {sectorsWithStatus.map((s) => (
                <li
                  key={s.id}
                  className="flex items-center justify-between p-3"
                >
                  <span className="text-sm text-gray-800 truncate">
                    {s.name}
                  </span>
                  <span
                    className={`px-2 py-1 rounded-md border text-xs ${statusColor(
                      s.status
                    )}`}
                  >
                    {statusLabel(s.status, "sector")}
                  </span>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};

export default ProjectStateCard;
