import { X } from "lucide-react";
import React, { useCallback } from "react";
import { Button } from "@/components/ui/Button";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: "sm" | "md" | "lg" | "xl";
  showCloseButton?: boolean;
  closable?: boolean;
}

function Modal({
  isOpen,
  onClose,
  title,
  children,
  size = "md",
  showCloseButton = true,
  closable = true,
}: ModalProps) {
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (closable && e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (closable && e.key === "Escape") {
        onClose();
      }
    },
    [closable, onClose]
  );

  React.useEffect(() => {
    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, handleKeyDown]);

  const getSizeClasses = () => {
    switch (size) {
      case "sm":
        return "max-w-sm";
      case "md":
        return "max-w-md";
      case "lg":
        return "max-w-lg";
      case "xl":
        return "max-w-xl";
      default:
        return "max-w-md";
    }
  };
  if (!isOpen) return null;
  return (
    <div
      className="fixed inset-0 z-60 flex items-center justify-center p-4 bg-black/50"
      onClick={handleBackdropClick}
    >
      <div
        className={`bg-white rounded-lg shadow-xl w-full max-h-[96vh] ${getSizeClasses()}`}
        style={{ display: "flex", flexDirection: "column" }}
      >
        {/* Header */}
        {(title || showCloseButton) && (
          <div className="flex items-center justify-between p-4 border-b border-gray-200 ">
            {title && (
              <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            )}
            {showCloseButton && closable && (
              <Button
                onClick={onClose}
                variant="ghost"
                size="sm"
                icon={<X className="h-5 w-5" />}
              />
            )}
          </div>
        )}

        {/* Content */}
        <div
          className={
            title || showCloseButton
              ? "p-4 flex-1 overflow-auto"
              : "flex-1 overflow-auto"
          }
          style={{ minHeight: 0 }}
        >
          {children}
        </div>
      </div>
    </div>
  );
}

export default Modal;
