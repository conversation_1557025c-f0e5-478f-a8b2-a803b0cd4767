import React, { useMemo } from "react";
import { useAtomValue } from "jotai";
import { dayjs } from "@/utils/date";
import {
  currentReservoirStateByReservoirIdAtom,
  reservoirsByIdAtom,
} from "@/store/data";
import { Clock } from "lucide-react";
import WaterPumpIcon from "./icons/WaterPumpIcon";

export function ReservoirStateCard({ reservoirId }: { reservoirId: string }) {
  const reservoir = useAtomValue(reservoirsByIdAtom)(reservoirId);
  const getState = useAtomValue(currentReservoirStateByReservoirIdAtom);
  const state = getState(reservoirId);
  const isRunning = useMemo(() => {
    return !!state?.start_time && !state?.end_time;
  }, [state]);
  const showRestartTime = !!state?.restart_time;

  if (!reservoir) return null;

  return (
    <div
      className={` rounded-xl shadow-sm border border-gray-200 p-4 ${
        isRunning ? "bg-green-100" : "bg-white"
      }`}
    >
      <div className="flex items-center justify-between">
        <h3 className="text-base font-semibold text-gray-900">
          Reservatório {reservoir.name}
        </h3>
        <div className="text-sm text-gray-600">
          <span className="text-xs text-gray-500 mr-1">Último pacote:</span>
          <span>
            {state?.packet_date ? dayjs(state.packet_date).calendar() : "—"}
          </span>
        </div>
      </div>
      {isRunning && (
        <div className="rounded-md p-2 border border-green-800">
          <div className="text-xs text-green-800 flex items-center">
            <WaterPumpIcon className="w-4 h-4 inline-block mr-1" />
            Ativo agora
          </div>
        </div>
      )}
      <div
        className={`mt-3 grid ${
          showRestartTime ? "grid-cols-3" : "grid-cols-2"
        } gap-3 text-sm`}
      >
        <div className="rounded-md bg-gray-50 p-2 border border-gray-200">
          <div className="text-xs text-gray-500">Início</div>
          <div className="text-gray-800">
            {state?.start_time ? dayjs(state.start_time).calendar() : "—"}
          </div>
        </div>
        {showRestartTime && (
          <div className="rounded-md bg-gray-50 p-2 border border-gray-200">
            <div className="text-xs text-gray-500">Reinício</div>
            <div className="text-gray-800">
              {state?.restart_time ? dayjs(state.restart_time).calendar() : "—"}
            </div>
          </div>
        )}
        <div className="rounded-md bg-gray-50 p-2 border border-gray-200">
          <div className="text-xs text-gray-500">Fim</div>
          <div className="text-gray-800">
            {state?.end_time ? dayjs(state.end_time).calendar() : "—"}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ReservoirStateCard;
