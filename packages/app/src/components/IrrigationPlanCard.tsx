import React, { useState } from "react";
import { Clock, ListChe<PERSON>, ChevronDown } from "lucide-react";
import { formatDays } from "@/utils/format";
import type { AUTIrrigationPlan } from "@/api/queries/account";
import { useAtomValue } from "jotai";
import { currentIrrigationPlanStateByPlanIdAtom } from "@/store";
import { dayjs } from "@/utils/date";

export interface IrrigationPlanCardColorScheme {
  bg: string;
  icon: string;
  border?: string;
}

interface IrrigationPlanCardProps {
  plan: AUTIrrigationPlan;
  projectId: string;
  colorScheme: IrrigationPlanCardColorScheme;
  onClick: () => void;
}

export default function IrrigationPlanCard({
  plan,
  projectId: _projectId,
  colorScheme,
  onClick,
}: IrrigationPlanCardProps) {
  const getState = useAtomValue(currentIrrigationPlanStateByPlanIdAtom);
  const state = getState(plan.id);
  const isRunning = !!state && state.end_time == null;
  const formatIso = (s: string | null | undefined) =>
    s ? dayjs(s).format("L LT") : "—";
  const [expanded, setExpanded] = useState(false);
  return (
    <div
      key={plan.id}
      className={`${
        isRunning ? "bg-green-50 border-green-200" : "bg-white border-gray-200"
      } rounded-xl border p-5 transition-all duration-200 hover:shadow-md hover:border-green-300 cursor-pointer`}
      onClick={onClick}
    >
      <div className="flex items-start gap-4">
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-3">
            <div
              className={`w-12 h-12 rounded-xl flex-shrink-0 flex items-center justify-center ${colorScheme.bg}`}
            >
              <Clock className={`w-6 h-6 ${colorScheme.icon}`} />
            </div>
            <div className="flex-1 min-w-0 ml-3">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {plan.name}
              </h3>
              <p className="text-sm text-gray-500 mt-1">
                {formatDays(plan.days_of_week)}
              </p>
            </div>
            <span
              className={`px-2 py-1 text-xs font-medium rounded-full flex-shrink-0 ${
                plan.is_enabled
                  ? "bg-green-100 text-green-700"
                  : "bg-gray-100 text-gray-600"
              }`}
            >
              {plan.is_enabled ? "Ativo" : "Inativo"}
            </span>
          </div>

          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-400" />
              <span>{plan.start_time}</span>
            </div>
            <div className="flex items-center gap-2">
              <ListChecks className="h-4 w-4 text-gray-400" />
              <span>{plan.steps?.length || 0} etapas</span>
            </div>
          </div>

          {/* Collapsible last execution section */}
          <div className="pt-3 mt-2 border-t border-gray-100">
            {!expanded ? (
              <div className="mt-2 text-sm text-gray-700">
                <span className="text-gray-500">Última execução: </span>
                {isRunning ? (
                  <span className="font-bold text-green-600">Agora</span>
                ) : (
                  <span className="font-medium">
                    {formatIso(state?.start_time ?? null)}
                  </span>
                )}
                <div className="mt-2 flex justify-center">
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setExpanded(true);
                    }}
                    aria-expanded={expanded}
                    aria-label="Mostrar detalhes da última execução"
                    className="p-1  w-full text-center rounded hover:bg-gray-50 flex items-center justify-center gap-2"
                  >
                    <ChevronDown className="w-7 h-7 text-gray-400 hover:text-gray-600" />
                    <span className="text-sm">Mostrar detalhes</span>
                  </button>
                </div>
              </div>
            ) : (
              <>
                <div className="mt-3 grid grid-cols-2 gap-4 text-sm text-gray-700 mb-2">
                  <div>
                    <span className="text-xs text-gray-500 block">Início</span>
                    <span>{formatIso(state?.start_time ?? null)}</span>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block">Término</span>
                    <span>
                      {state
                        ? state.end_time
                          ? formatIso(state.end_time)
                          : "Em execução"
                        : "—"}
                    </span>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block">
                      Setores ativados
                    </span>
                    <span>{state?.activated_steps?.length ?? 0}</span>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block">
                      Ferti. ativadas
                    </span>
                    <span>{state?.activated_ferti_steps?.length ?? 0}</span>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block">
                      Funcionamento da bomba
                    </span>
                    <span>
                      {state
                        ? state.waterpump_working
                          ? "Ligada"
                          : "Desligada"
                        : "—"}
                    </span>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500 block">
                      Início da retrolavagem
                    </span>
                    <span>{formatIso(state?.backwash_start_time ?? null)}</span>
                  </div>
                </div>

                <div className="text-xs text-gray-500 pt-3 border-t border-gray-100">
                  Última atualização:{" "}
                  {state ? (
                    <span
                      title={state.packet_date}
                      className="font-medium text-gray-700"
                    >
                      {dayjs(state.packet_date).calendar()}
                    </span>
                  ) : (
                    "—"
                  )}
                </div>
                <div className="mt-2 flex justify-center">
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setExpanded(false);
                    }}
                    aria-expanded={expanded}
                    aria-label="Ocultar detalhes da última execução"
                    className="p-1  w-full text-center rounded hover:bg-gray-50 flex items-center justify-center gap-2"
                  >
                    <ChevronDown className="w-7 h-7 text-gray-400 hover:text-gray-600 rotate-180" />
                    <span className="text-sm">Ocultar detalhes</span>
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
