import { currentA<PERSON>unt<PERSON><PERSON> } from "@/store";
import { useGeolocation } from "@uidotdev/usehooks";
import { useAtomValue } from "jotai";
import { ArrowLeft, ArrowRight, Check, MapIcon } from "lucide-react";
import React, { useCallback, useState } from "react";
import { Button } from "@/components/ui/Button";

export interface Point {
  type: "Point";
  coordinates: [number, number]; // [longitude, latitude]
}

export interface PropertyWizardData {
  account: string;
  name: string;
  timezone: string;
  point: Point | null;
  address_postal_code: string | null;
  address_street_name: string | null;
  address_street_number: string | null;
  address_complement: string | null;
  address_neighborhood: string | null;
  address_city: string | null;
  address_state: string | null;
  address_country: string | null;
  backwash_duration_minutes: number | null;
  backwash_period_minutes: number | null;
  backwash_delay_seconds: number | null;
  rain_gauge_enabled: boolean;
  rain_gauge_resolution_mm: number | null;
  precipitation_volume_limit_mm: number | null;
  precipitation_suspended_duration_hours: number | null;
}

interface PropertyWizardProps {
  initialData?: Partial<PropertyWizardData>;
  isEditMode?: boolean;
  isLoading?: boolean;
  onSubmit: (data: PropertyWizardData) => Promise<void>;
  onCancel: () => void;
  submitButtonText?: string;
}

const defaultWizardData: PropertyWizardData = {
  account: "",
  name: "",
  timezone: "America/Sao_Paulo",
  point: null,
  address_postal_code: null,
  address_street_name: null,
  address_street_number: null,
  address_complement: null,
  address_neighborhood: null,
  address_city: null,
  address_state: null,
  address_country: "Brasil",
  backwash_duration_minutes: null,
  backwash_period_minutes: null,
  backwash_delay_seconds: null,
  rain_gauge_enabled: false,
  rain_gauge_resolution_mm: 0.2,
  precipitation_volume_limit_mm: 2,
  precipitation_suspended_duration_hours: 24,
};

const wizardSteps = [
  {
    number: 1,
    title: "Informações Básicas",
    description: "Nome, fuso horário e localização",
  },
  {
    number: 2,
    title: "Endereço",
    description: "Informações de endereço da propriedade",
  },
  {
    number: 3,
    title: "Retrolavagem",
    description: "Configurações de retrolavagem",
  },
  {
    number: 4,
    title: "Pluviômetro",
    description: "Configurações do pluviômetro",
  },
  {
    number: 5,
    title: "Revisão",
    description: "Revisar e confirmar as informações",
  },
];

function GeoLocation({
  onLocation,
}: {
  onLocation: (location: { latitude: number; longitude: number }) => void;
}): React.JSX.Element {
  const state = useGeolocation();

  if (state.loading) {
    return (
      <p className="mt-1 text-xs text-gray-500">
        Acessando localização... (você pode precisar habilitar permissões)
      </p>
    );
  }

  if (state.error) {
    return (
      <p className="mt-1 text-xs text-red-600">
        Habilite permissões para acessar seus dados de localização
      </p>
    );
  }

  const { latitude, longitude } = state;

  if (latitude != null && longitude != null) {
    return (
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={() => onLocation({ latitude, longitude })}
        icon={<MapIcon className="h-4 w-4" />}
        iconPosition="left"
      >
        Usar localização atual
      </Button>
    );
  } else {
    return <p>Localização não disponível</p>;
  }
}

export function PropertyWizard({
  initialData = {},
  isEditMode = false,
  isLoading = false,
  onSubmit,
  onCancel,
  submitButtonText,
}: PropertyWizardProps): React.JSX.Element {
  const currentAccount = useAtomValue(currentAccountAtom);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<PropertyWizardData>({
    ...defaultWizardData,
    ...initialData,
    account: currentAccount?.id || "",
  });
  const [errors, setErrors] = useState<Partial<PropertyWizardData>>({});

  const handleInputChange = useCallback(
    (
      e: React.ChangeEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
    ) => {
      const { name, value, type } = e.target as
        | HTMLInputElement
        | HTMLTextAreaElement
        | HTMLSelectElement;

      // Handle checkbox inputs
      if (type === "checkbox") {
        const checked = (e.target as HTMLInputElement).checked;
        setFormData((prev) => ({ ...prev, [name]: checked }));
      } else {
        // Convert empty strings to null for nullable fields, except for required fields
        const requiredFields = [
          "name",
          "timezone",
          "address_city",
          "address_state",
          "address_country",
          "address_postal_code",
        ];
        const processedValue =
          !requiredFields.includes(name) && value.trim() === "" ? null : value;

        setFormData((prev) => ({ ...prev, [name]: processedValue }));
      }

      // Clear error when user starts typing
      if (errors[name as keyof PropertyWizardData]) {
        setErrors((prev) => ({ ...prev, [name]: undefined }));
      }
    },
    [errors]
  );

  const validateCurrentStep = useCallback((): boolean => {
    const newErrors: Partial<PropertyWizardData> = {};

    switch (currentStep) {
      case 1: // Basic Information
        if (!formData.name.trim()) {
          newErrors.name = "Nome da propriedade é obrigatório";
        }
        break;
      case 2: // Address
        if (!formData.address_city?.trim()) {
          newErrors.address_city = "Cidade é obrigatória";
        }
        if (!formData.address_state?.trim()) {
          newErrors.address_state = "Estado é obrigatório";
        }
        if (!formData.address_country?.trim()) {
          newErrors.address_country = "País é obrigatório";
        }
        if (!formData.address_postal_code?.trim()) {
          newErrors.address_postal_code = "CEP é obrigatório";
        }
        break;
      // Steps 3, 4, and 5 don't have required fields
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [currentStep, formData]);

  const handleNext = useCallback(() => {
    if (validateCurrentStep() && currentStep < wizardSteps.length) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, validateCurrentStep]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!validateCurrentStep()) {
        return;
      }

      try {
        await onSubmit(formData);
      } catch (error) {
        console.error("Property wizard submission error:", error);
      }
    },
    [formData, onSubmit, validateCurrentStep]
  );

  const defaultSubmitText = isEditMode ? "Salvar" : "Criar";

  // Step 1: Basic Information (Name, timezone, point)
  const renderStep1 = () => (
    <>
      {/* Property Name */}
      <div>
        <label
          htmlFor="name"
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          Nome da Propriedade *
        </label>
        <input
          type="text"
          id="name"
          name="name"
          value={formData.name}
          onChange={handleInputChange}
          placeholder="Ex: Fazenda São José"
          className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
            errors.name ? "border-red-300" : "border-gray-300"
          }`}
        />
        {errors.name && (
          <p className="mt-1 text-sm text-red-600">{errors.name}</p>
        )}
      </div>

      {/* Timezone */}
      <div>
        <label
          htmlFor="timezone"
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          Fuso Horário
        </label>
        <select
          id="timezone"
          name="timezone"
          value={formData.timezone}
          onChange={handleInputChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
        >
          <option value="America/Sao_Paulo">São Paulo (UTC-3)</option>
          <option value="America/Manaus">Manaus (UTC-4)</option>
          <option value="America/Rio_Branco">Rio Branco (UTC-5)</option>
        </select>
      </div>

      {/* Coordinates */}
      <div>
        <label
          htmlFor="coordinates"
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          Coordenadas (Latitude, Longitude)
        </label>
        <div className="grid grid-cols-2 gap-2">
          <input
            type="number"
            placeholder="Latitude"
            step="any"
            value={formData.point?.coordinates[1] || ""}
            onChange={(e) => {
              const lat = parseFloat(e.target.value);
              if (!isNaN(lat)) {
                const lng = formData.point?.coordinates[0] || 0;
                setFormData((prev) => ({
                  ...prev,
                  point: { type: "Point", coordinates: [lng, lat] },
                }));
              } else {
                setFormData((prev) => ({ ...prev, point: null }));
              }
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          />
          <input
            type="number"
            placeholder="Longitude"
            step="any"
            value={formData.point?.coordinates[0] || ""}
            onChange={(e) => {
              const lng = parseFloat(e.target.value);
              if (!isNaN(lng)) {
                const lat = formData.point?.coordinates[1] || 0;
                setFormData((prev) => ({
                  ...prev,
                  point: { type: "Point", coordinates: [lng, lat] },
                }));
              } else {
                setFormData((prev) => ({ ...prev, point: null }));
              }
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          />
        </div>
        <GeoLocation
          onLocation={({ latitude, longitude }) => {
            setFormData((prev) => ({
              ...prev,
              point: {
                type: "Point",
                coordinates: [longitude, latitude],
              },
            }));
          }}
        />
      </div>
    </>
  );

  // Step 2: Address
  const renderStep2 = () => (
    <>
      {/* Postal Code and Country */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label
            htmlFor="address_postal_code"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            CEP *
          </label>
          <input
            type="text"
            id="address_postal_code"
            name="address_postal_code"
            value={formData.address_postal_code || ""}
            onChange={handleInputChange}
            placeholder="00000-000"
            className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
              errors.address_postal_code ? "border-red-300" : "border-gray-300"
            }`}
          />
          {errors.address_postal_code && (
            <p className="mt-1 text-sm text-red-600">
              {errors.address_postal_code}
            </p>
          )}
        </div>

        <div>
          <label
            htmlFor="address_country"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            País *
          </label>
          <input
            type="text"
            id="address_country"
            name="address_country"
            value={formData.address_country || ""}
            onChange={handleInputChange}
            placeholder="Ex: Brasil"
            className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
              errors.address_country ? "border-red-300" : "border-gray-300"
            }`}
          />
          {errors.address_country && (
            <p className="mt-1 text-sm text-red-600">
              {errors.address_country}
            </p>
          )}
        </div>
      </div>

      {/* State and City */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label
            htmlFor="address_state"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Estado *
          </label>
          <input
            type="text"
            id="address_state"
            name="address_state"
            value={formData.address_state || ""}
            onChange={handleInputChange}
            placeholder="Ex: SP"
            className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
              errors.address_state ? "border-red-300" : "border-gray-300"
            }`}
          />
          {errors.address_state && (
            <p className="mt-1 text-sm text-red-600">{errors.address_state}</p>
          )}
        </div>

        <div>
          <label
            htmlFor="address_city"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Cidade *
          </label>
          <input
            type="text"
            id="address_city"
            name="address_city"
            value={formData.address_city || ""}
            onChange={handleInputChange}
            placeholder="Ex: São Paulo"
            className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
              errors.address_city ? "border-red-300" : "border-gray-300"
            }`}
          />
          {errors.address_city && (
            <p className="mt-1 text-sm text-red-600">{errors.address_city}</p>
          )}
        </div>
      </div>

      {/* Street Name and Number */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label
            htmlFor="address_street_name"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Nome da Rua
          </label>
          <input
            type="text"
            id="address_street_name"
            name="address_street_name"
            value={formData.address_street_name || ""}
            onChange={handleInputChange}
            placeholder="Ex: Rua das Flores"
            className={`w-full px-3 py-2 border rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
              errors.address_street_name ? "border-red-300" : "border-gray-300"
            }`}
          />
          {errors.address_street_name && (
            <p className="mt-1 text-sm text-red-600">
              {errors.address_street_name}
            </p>
          )}
        </div>

        <div>
          <label
            htmlFor="address_street_number"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Número
          </label>
          <input
            type="text"
            id="address_street_number"
            name="address_street_number"
            value={formData.address_street_number || ""}
            onChange={handleInputChange}
            placeholder="123"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          />
        </div>
      </div>

      {/* Complement and Neighborhood */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label
            htmlFor="address_complement"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Complemento
          </label>
          <input
            type="text"
            id="address_complement"
            name="address_complement"
            value={formData.address_complement || ""}
            onChange={handleInputChange}
            placeholder="Ex: Bloco A, Apto 101"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          />
        </div>

        <div>
          <label
            htmlFor="address_neighborhood"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Bairro
          </label>
          <input
            type="text"
            id="address_neighborhood"
            name="address_neighborhood"
            value={formData.address_neighborhood || ""}
            onChange={handleInputChange}
            placeholder="Ex: Centro"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          />
        </div>
      </div>
    </>
  );

  // Step 3: Backwash
  const renderStep3 = () => (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label
            htmlFor="backwash_duration_minutes"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Duração da Retrolavagem (minutos)
          </label>
          <input
            type="number"
            id="backwash_duration_minutes"
            name="backwash_duration_minutes"
            value={formData.backwash_duration_minutes || ""}
            onChange={handleInputChange}
            placeholder="Ex: 5"
            min="0"
            step="1"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          />
          <p className="mt-1 text-xs text-gray-500">
            Tempo para limpeza do sistema por reversão do fluxo
          </p>
        </div>

        <div>
          <label
            htmlFor="backwash_period_minutes"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Período entre Retrolavagens (minutos)
          </label>
          <input
            type="number"
            id="backwash_period_minutes"
            name="backwash_period_minutes"
            value={formData.backwash_period_minutes || ""}
            onChange={handleInputChange}
            placeholder="Ex: 120"
            min="0"
            step="1"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
          />
          <p className="mt-1 text-xs text-gray-500">
            Intervalo para manutenção automática
          </p>
        </div>
      </div>
    </>
  );

  // Step 4: Rain Gauge
  const renderStep4 = () => (
    <>
      <div>
        <div className="flex items-center space-x-3 mb-4">
          <input
            type="checkbox"
            id="rain_gauge_enabled"
            name="rain_gauge_enabled"
            checked={formData.rain_gauge_enabled}
            onChange={handleInputChange}
            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
          />
          <label
            htmlFor="rain_gauge_enabled"
            className="text-sm font-medium text-gray-700"
          >
            Habilitar Pluviômetro
          </label>
        </div>

        {formData.rain_gauge_enabled && (
          <div className="space-y-4 pl-7">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label
                  htmlFor="rain_gauge_resolution_mm"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Resolução (mm)
                </label>
                <input
                  type="number"
                  id="rain_gauge_resolution_mm"
                  name="rain_gauge_resolution_mm"
                  value={formData.rain_gauge_resolution_mm || ""}
                  onChange={handleInputChange}
                  placeholder="0.2"
                  min="0"
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Precisão do sensor de chuva
                </p>
              </div>

              <div>
                <label
                  htmlFor="precipitation_volume_limit_mm"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Limite de Precipitação (mm)
                </label>
                <input
                  type="number"
                  id="precipitation_volume_limit_mm"
                  name="precipitation_volume_limit_mm"
                  value={formData.precipitation_volume_limit_mm || ""}
                  onChange={handleInputChange}
                  placeholder="2"
                  min="0"
                  step="0.1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Volume que suspende a irrigação
                </p>
              </div>

              <div>
                <label
                  htmlFor="precipitation_suspended_duration_hours"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Duração da Suspensão (horas)
                </label>
                <input
                  type="number"
                  id="precipitation_suspended_duration_hours"
                  name="precipitation_suspended_duration_hours"
                  value={formData.precipitation_suspended_duration_hours || ""}
                  onChange={handleInputChange}
                  placeholder="24"
                  min="0"
                  step="1"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Tempo de pausa após chuva
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );

  // Step 5: Review
  const renderStep5 = () => (
    <div className="space-y-6">
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-lg font-medium text-gray-900 mb-4">
          Revisão das Informações
        </h4>

        {/* Basic Information */}
        <div className="mb-6">
          <h5 className="text-sm font-medium text-gray-700 mb-2">
            Informações Básicas
          </h5>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Nome:</span>
              <span className="ml-2 font-medium">{formData.name}</span>
            </div>
            <div>
              <span className="text-gray-600">Fuso Horário:</span>
              <span className="ml-2 font-medium">{formData.timezone}</span>
            </div>
            {formData.point && (
              <div className="md:col-span-2">
                <span className="text-gray-600">Coordenadas:</span>
                <span className="ml-2 font-medium">
                  {formData.point.coordinates[1]},{" "}
                  {formData.point.coordinates[0]}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Address */}
        <div className="mb-6">
          <h5 className="text-sm font-medium text-gray-700 mb-2">Endereço</h5>
          <div className="text-sm">
            <div className="mb-1">
              <span className="font-medium">
                {formData.address_street_name} {formData.address_street_number}
              </span>
            </div>
            {formData.address_complement && (
              <div className="mb-1 text-gray-600">
                {formData.address_complement}
              </div>
            )}
            <div className="text-gray-600">
              {formData.address_neighborhood &&
                `${formData.address_neighborhood}, `}
              {formData.address_city}, {formData.address_state}
            </div>
            <div className="text-gray-600">
              {formData.address_postal_code} - {formData.address_country}
            </div>
          </div>
        </div>

        {/* Backwash */}
        {(formData.backwash_duration_minutes ||
          formData.backwash_period_minutes) && (
          <div className="mb-6">
            <h5 className="text-sm font-medium text-gray-700 mb-2">
              Configurações de Retrolavagem
            </h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {formData.backwash_duration_minutes && (
                <div>
                  <span className="text-gray-600">Duração:</span>
                  <span className="ml-2 font-medium">
                    {formData.backwash_duration_minutes} minutos
                  </span>
                </div>
              )}
              {formData.backwash_period_minutes && (
                <div>
                  <span className="text-gray-600">Período:</span>
                  <span className="ml-2 font-medium">
                    {formData.backwash_period_minutes} minutos
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Rain Gauge */}
        <div className="mb-6">
          <h5 className="text-sm font-medium text-gray-700 mb-2">
            Pluviômetro
          </h5>
          {formData.rain_gauge_enabled ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Status:</span>
                <span className="ml-2 font-medium text-green-600">
                  Habilitado
                </span>
              </div>
              {formData.rain_gauge_resolution_mm && (
                <div>
                  <span className="text-gray-600">Resolução:</span>
                  <span className="ml-2 font-medium">
                    {formData.rain_gauge_resolution_mm} mm
                  </span>
                </div>
              )}
              {formData.precipitation_volume_limit_mm && (
                <div>
                  <span className="text-gray-600">Limite:</span>
                  <span className="ml-2 font-medium">
                    {formData.precipitation_volume_limit_mm} mm
                  </span>
                </div>
              )}
              {formData.precipitation_suspended_duration_hours && (
                <div>
                  <span className="text-gray-600">Duração da Suspensão:</span>
                  <span className="ml-2 font-medium">
                    {formData.precipitation_suspended_duration_hours} horas
                  </span>
                </div>
              )}
            </div>
          ) : (
            <div className="text-sm">
              <span className="text-gray-600">Status:</span>
              <span className="ml-2 font-medium text-gray-500">
                Desabilitado
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="flex flex-col h-full">
      {/* Header with Steps */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            {isEditMode ? "Editar Propriedade" : "Nova Propriedade"}
          </h2>
          <div className="text-sm text-gray-500">
            Passo {currentStep} de {wizardSteps.length}
          </div>
        </div>

        {/* Step Indicator */}
        <div className="flex items-center justify-between ">
          {wizardSteps.map((step, index) => (
            <div key={step.number} className="flex items-center w-full">
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  step.number < currentStep
                    ? "bg-green-500 text-white"
                    : step.number === currentStep
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-600"
                }`}
              >
                {step.number < currentStep ? (
                  <Check className="w-4 h-4" />
                ) : (
                  step.number
                )}
              </div>
              {index < wizardSteps.length - 1 && (
                <div
                  className={`flex-grow h-0.5 mx-2 ${
                    step.number < currentStep ? "bg-green-500" : "bg-gray-200"
                  }`}
                />
              )}
            </div>
          ))}
        </div>

        <div className="mt-4">
          <h3 className="text-lg font-medium text-gray-900">
            {wizardSteps[currentStep - 1].title}
          </h3>
          <p className="text-sm text-gray-600">
            {wizardSteps[currentStep - 1].description}
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-6">
        <form onSubmit={handleSubmit} className="max-w-2xl mx-auto">
          {/* Step Content */}
          <div className="space-y-6">
            {currentStep === 1 && renderStep1()}
            {currentStep === 2 && renderStep2()}
            {currentStep === 3 && renderStep3()}
            {currentStep === 4 && renderStep4()}
            {currentStep === 5 && renderStep5()}
          </div>
        </form>
      </div>

      {/* Footer with Navigation */}
      <div className="bg-white border-t border-gray-200 px-6 py-4">
        <div className="flex flex-wrap items-center justify-between gap-3 max-w-2xl mx-auto">
          <Button
            type="button"
            variant={currentStep === 1 ? "secondary" : "secondary"}
            size="md"
            onClick={currentStep === 1 ? onCancel : handlePrevious}
            icon={<ArrowLeft className="h-4 w-4" />}
            iconPosition="left"
            className="w-full sm:w-auto flex-1 sm:flex-none"
          >
            {currentStep === 1 ? "Cancelar" : "Anterior"}
          </Button>

          {currentStep < wizardSteps.length ? (
            <Button
              type="button"
              variant="primary"
              size="md"
              onClick={handleNext}
              icon={<ArrowRight className="h-4 w-4" />}
              iconPosition="right"
              className="w-full sm:w-auto flex-1 sm:flex-none"
            >
              Próximo
            </Button>
          ) : (
            <Button
              type="submit"
              variant="primary"
              size="md"
              loading={isLoading}
              icon={isLoading ? undefined : <Check className="h-4 w-4" />}
              iconPosition="left"
              disabled={isLoading}
              onClick={handleSubmit}
              className="w-full sm:w-auto flex-1 sm:flex-none"
            >
              {isLoading
                ? "Salvando..."
                : submitButtonText || defaultSubmitText}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
