import { useEffect } from "react";
import Button, { type ButtonProps } from "@/components/ui/Button";
import type { AUTDevice } from "@/api/queries/account";
import { useToast } from "@/components";
import { useLICCommunicationTest } from "@/hooks/useLICCommunicationTest";

export interface TestLICCommunicationButtonProps
  extends Omit<ButtonProps, "children" | "onClick"> {
  device: AUTDevice;
  pollingInterval?: number;
  timeout?: number;
}

export function TestLICCommunicationButton({
  device,
  pollingInterval = 2000,
  timeout = 30000,
  loading: externalLoading = false,
  disabled: externalDisabled = false,
  ...buttonProps
}: TestLICCommunicationButtonProps) {
  const { showError, showSuccess } = useToast();
  
  const { status, error, startTest, isPolling } = useLICCommunicationTest({
    deviceId: device.id,
    pollingInterval,
    timeout,
  });

  // Handle status change notifications
  useEffect(() => {
    if (status === 'acknowledged') {
      showSuccess({
        message: "Teste de comunicação realizado com sucesso!",
      });
    } else if (status === 'failed' && error) {
      showError({
        message: `Teste de comunicação falhou: ${error.message}`,
      });
    }
  }, [status, error, showSuccess, showError]);

  const getButtonText = () => {
    switch (status) {
      case 'idle':
        return "Testar Comunicação";
      case 'created':
      case 'pending':
      case 'sent':
        return "Testando";
      case 'failed':
        return error ? `Erro: ${error.message}` : "Erro";
      case 'acknowledged':
        return "Sucesso";
      default:
        return "Testar Comunicação";
    }
  };

  const handleClick = () => {
    console.log("Testing communication for device:", device.identifier);
    startTest();
  };

  const isDisabled = externalDisabled || externalLoading || isPolling;
  const loading = isPolling || externalLoading;

  return (
    <Button
      {...buttonProps}
      loading={loading}
      disabled={isDisabled}
      onClick={handleClick}
    >
      {getButtonText()}
    </Button>
  );
}

export default TestLICCommunicationButton;
