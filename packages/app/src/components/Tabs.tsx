import type { LucideIcon } from "lucide-react";
import { useCallback, useEffect } from "react";
import { Button } from "./ui/Button";

interface TabItem<T extends string = string> {
  key: T;
  label?: string;
  icon?: LucideIcon;
  disabled?: boolean;
}

interface TabsProps<T extends string> {
  tabs: readonly TabItem<T>[];
  activeTab: T;
  onTabChange: (tab: T) => void;
  className?: string;
}

/**
 * Reusable Tabs component with full type safety
 *
 * @example
 * ```tsx
 * const tabs = [
 *   { key: 'devices', label: 'Dispositivos', icon: Cpu },
 *   { key: 'pumps', label: 'Bombas', icon: Wrench }
 * ] as const;
 *
 * <Tabs
 *   tabs={tabs}
 *   activeTab={activeTab}
 *   onTabChange={setActiveTab}
 * />
 * ```
 */
function Tabs<T extends string>({
  tabs,
  activeTab,
  onTabChange,
  className = "",
}: TabsProps<T>) {
  const handleTabClick = useCallback(
    (tab: T) => {
      const tabItem = tabs.find((t) => t.key === tab);
      if (!tabItem?.disabled) {
        onTabChange(tab);
      }
    },
    [tabs, onTabChange]
  );
  useEffect(() => {
    const tabItem = tabs.find((t) => t.key === activeTab);
    if (!tabItem) {
      onTabChange(tabs[0].key);
    }
  }, [activeTab]);

  return (
    <div className={`flex bg-gray-100 rounded-lg p-1 ${className}`}>
      {tabs.map((tab) => {
        const isActive = activeTab === tab.key;
        const Icon = tab.icon;

        return (
          <Button
            key={tab.key}
            onClick={() => handleTabClick(tab.key)}
            disabled={tab.disabled}
            variant="neutral"
            ghost={!isActive}
            size="md"
            className={`flex-1 flex items-center justify-center gap-2 ${
              tab.disabled ? "text-gray-400 cursor-not-allowed" : ""
            }`}
            aria-pressed={isActive}
            aria-disabled={tab.disabled}
            icon={Icon && <Icon size={16} />}
          >
            {tab.label && <span>{tab.label}</span>}
          </Button>
        );
      })}
    </div>
  );
}

export default Tabs;
export type { TabItem, TabsProps };
