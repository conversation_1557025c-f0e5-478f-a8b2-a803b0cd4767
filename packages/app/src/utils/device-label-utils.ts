import type { AUTPropertyDevice, AUTProperty } from "@/api/queries/account";
import type { PropertyDeviceMetadata } from "@/api/model/property-device";

/**
 * Device display information extracted from property_device metadata
 */
export interface DeviceDisplayInfo {
  label: string | null;
  identifier: string;
  model: string;
  id: string;
}

/**
 * Extended device display info with LIC mapping information
 */
export interface ExtendedDeviceDisplayInfo extends DeviceDisplayInfo {
  licInfo?: DeviceDisplayInfo;
}

/**
 * Find a property device by device UUID
 * @param deviceUuid - The device UUID to find
 * @param propertyDevices - Array of property devices to search in
 * @returns The matching property device or null if not found
 */
export function findPropertyDeviceByUuid(
  deviceUuid: string | null,
  propertyDevices: AUTPropertyDevice[]
): AUTPropertyDevice | null {
  if (!deviceUuid) return null;

  return propertyDevices.find((pd) => pd.device.id === deviceUuid) || null;
}

/**
 * Extract device display information from a property device
 * @param propertyDevice - The property device to extract info from
 * @returns Device display information
 */
export function getDeviceDisplayInfo(
  propertyDevice: AUTPropertyDevice | null
): DeviceDisplayInfo | null {
  if (!propertyDevice) return null;

  const metadata = propertyDevice.metadata as PropertyDeviceMetadata | null;

  return {
    label: metadata?.label || null,
    identifier: propertyDevice.device.identifier,
    model: propertyDevice.device.model,
    id: propertyDevice.device.id,
  };
}

/**
 * Resolve a device UUID to display information with optional LIC mapping
 * @param deviceUuid - The device UUID to resolve
 * @param propertyDevices - Array of property devices to search in
 * @returns Extended device display information including LIC if available
 */
export function resolveDeviceDisplayInfo(
  deviceUuid: string | null,
  propertyDevices: AUTPropertyDevice[]
): ExtendedDeviceDisplayInfo | null {
  const propertyDevice = findPropertyDeviceByUuid(deviceUuid, propertyDevices);
  if (!propertyDevice) return null;

  const displayInfo = getDeviceDisplayInfo(propertyDevice);
  if (!displayInfo) return null;

  // Check if this device has a LIC mapping
  let licInfo: DeviceDisplayInfo | null = null;
  if (propertyDevice.current_mesh_device_mapping) {
    const licPropertyDevice = propertyDevices.find(
      (pd) =>
        pd.id === propertyDevice.current_mesh_device_mapping.lic_property_device
    );
    licInfo = getDeviceDisplayInfo(licPropertyDevice ?? null);
  }

  return {
    ...displayInfo,
    licInfo: licInfo || undefined,
  };
}

/**
 * Format device display text for UI
 * @param displayInfo - Device display information
 * @param fallbackText - Text to show when device info is not available
 * @returns Formatted display text
 */
export function formatDeviceDisplayText(
  displayInfo: DeviceDisplayInfo | null,
  fallbackText: string = "Não definido"
): string {
  if (!displayInfo) return fallbackText;

  const label = displayInfo.label;
  const identifier = displayInfo.identifier;

  if (label && identifier) {
    return `${label} (S/N: ${identifier})`;
  } else if (label) {
    return label;
  } else if (identifier) {
    return `S/N: ${identifier}`;
  }

  return fallbackText;
}

/**
 * Format LIC display text for UI
 * @param licInfo - LIC device display information
 * @returns Formatted LIC display text or null if not available
 */
export function formatLICDisplayText(
  licInfo: DeviceDisplayInfo | null
): string | null {
  if (!licInfo) return null;

  const label = licInfo.label;
  const identifier = licInfo.identifier;

  if (label && identifier) {
    return `${label} (S/N: ${identifier})`;
  } else if (label) {
    return label;
  } else if (identifier) {
    return `S/N: ${identifier}`;
  }

  return null;
}

/**
 * Resolve pump controller display information from a property
 * This is a convenience function specifically for pump controllers
 * @param pumpControllerUuid - The pump controller device UUID
 * @param property - The property containing all devices
 * @returns Extended device display information for the pump controller
 */
export function resolvePumpControllerDisplayInfo(
  pumpControllerUuid: string | null,
  property: AUTProperty
): ExtendedDeviceDisplayInfo | null {
  return resolveDeviceDisplayInfo(pumpControllerUuid, property.devices);
}
