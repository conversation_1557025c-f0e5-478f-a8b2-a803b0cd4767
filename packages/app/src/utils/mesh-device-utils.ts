import type { DeviceModel } from "@/api/model/device";
import type { AUTPropertyDevice } from "@/api/queries/account";

// Device type classification
export const MESH_DEVICE_MODELS: DeviceModel[] = [
  "WPC-PL10",
  "WPC-PL50",
  "VC",
  "RM",
] as const;
export const LIC_DEVICE_MODEL: DeviceModel = "LIC" as const;

export function isLICDevice(model: DeviceModel): boolean {
  return model === LIC_DEVICE_MODEL;
}

export function isMeshDevice(model: DeviceModel): boolean {
  return MESH_DEVICE_MODELS.includes(model);
}

// Device mapping status
export type DeviceMappingStatus = "coordinator" | "mapped" | "unmapped";

export function getDeviceMappingStatus(
  propertyDevice: AUTPropertyDevice
): DeviceMappingStatus {
  if (isLICDevice(propertyDevice.device.model)) {
    return "coordinator";
  }

  if (isMeshDevice(propertyDevice.device.model)) {
    return propertyDevice.current_mesh_device_mapping ? "mapped" : "unmapped";
  }

  return "unmapped";
}

// Enhanced device type with mapping information
export interface DeviceWithMapping extends AUTPropertyDevice {
  mappingStatus: DeviceMappingStatus;
  isLIC: boolean;
  isMesh: boolean;
  licDevice?: AUTPropertyDevice; // For mapped mesh devices
  meshDevices?: DeviceWithMapping[]; // For LIC devices
}

// Transform property devices to include mapping information
export function enhanceDevicesWithMapping(
  propertyDevices: AUTPropertyDevice[]
): DeviceWithMapping[] {
  // Create a map of LIC devices for quick lookup
  const licDevicesMap = new Map<string, AUTPropertyDevice>();
  propertyDevices.forEach((pd) => {
    if (isLICDevice(pd.device.model)) {
      licDevicesMap.set(pd.id, pd);
    }
  });

  // Enhance each device with mapping information
  const enhancedDevices: DeviceWithMapping[] = propertyDevices.map((pd) => {
    const mappingStatus = getDeviceMappingStatus(pd);
    const isLIC = isLICDevice(pd.device.model);
    const isMesh = isMeshDevice(pd.device.model);

    let licDevice: AUTPropertyDevice | undefined;
    if (isMesh && pd.current_mesh_device_mapping) {
      licDevice = licDevicesMap.get(
        pd.current_mesh_device_mapping.lic_property_device
      );
    }

    return {
      ...pd,
      mappingStatus,
      isLIC,
      isMesh,
      licDevice,
    };
  });

  // Add mesh devices to LIC devices
  enhancedDevices.forEach((device) => {
    if (device.isLIC) {
      device.meshDevices = enhancedDevices.filter(
        (d) => d.isMesh && d.licDevice?.id === device.id
      );
    }
  });

  return enhancedDevices;
}

// Sort devices for hierarchical display
export function sortDevicesForHierarchicalDisplay(
  devices: DeviceWithMapping[]
): DeviceWithMapping[] {
  const result: DeviceWithMapping[] = [];

  // First, add all LIC devices
  const licDevices = devices.filter((d) => d.isLIC);
  licDevices.sort((a, b) =>
    a.device.identifier.localeCompare(b.device.identifier)
  );

  for (const licDevice of licDevices) {
    result.push(licDevice);

    // Add mapped mesh devices for this LIC
    if (licDevice.meshDevices) {
      const sortedMeshDevices = licDevice.meshDevices.sort((a, b) =>
        a.device.identifier.localeCompare(b.device.identifier)
      );
      result.push(...sortedMeshDevices);
    }
  }

  // Finally, add unmapped mesh devices
  const unmappedDevices = devices.filter((d) => d.mappingStatus === "unmapped");
  unmappedDevices.sort((a, b) =>
    a.device.identifier.localeCompare(b.device.identifier)
  );
  result.push(...unmappedDevices);

  return result;
}

// Get device icon based on model
export function getDeviceIcon(model: DeviceModel): string {
  switch (model) {
    case "LIC":
      return "📡";
    case "WPC-PL10":
    case "WPC-PL50":
      return "💧";
    case "VC":
      return "🌱";
    case "RM":
      return "🏗️";
    default:
      return "🔧";
  }
}

// Format mapping date
export function formatMappingDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString("pt-BR", {
    day: "2-digit",
    month: "short",
    year: "numeric",
  });
}

// Get tree symbol for hierarchical display
export function getTreeSymbol(
  device: DeviceWithMapping,
  index: number,
  devices: DeviceWithMapping[]
): string {
  if (device.isLIC) {
    return "";
  }

  if (device.mappingStatus === "mapped" && device.licDevice) {
    // Check if this is the last mesh device for this LIC
    const licId = device.licDevice.id;
    const meshDevicesForThisLIC = devices.filter(
      (d) => d.mappingStatus === "mapped" && d.licDevice?.id === licId
    );
    const isLastForLIC =
      meshDevicesForThisLIC[meshDevicesForThisLIC.length - 1].id === device.id;

    return isLastForLIC ? "└─" : "├─";
  }

  return "";
}
