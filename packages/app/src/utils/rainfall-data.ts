import type { CurrentLICPacket } from "@/api/model/current-lic-packet";
import type { AUTProperty, AUTPropertyDevice } from "@/api/queries/account";

export type RainfallDetail = {
  deviceId: string;
  label: string;
  rainfallMm: number;
  raining: boolean;
  packetDate: string;
};

export type RainfallSummary = {
  gaugeCount: number;
  averageRainfallMm: number | null;
  rainingAny: boolean;
  details: RainfallDetail[];
};

function getLabelForDevice(
  deviceId: string,
  propertyDevices: AUTPropertyDevice[]
): string {
  const pd = propertyDevices.find((p) => p.device?.id === deviceId);
  const label = (pd?.metadata as any)?.label;
  return (label && String(label)) || deviceId;
}

// Select only latest status packet per device
export function latestStatusPacketsByDevice(
  packets: CurrentLICPacket[]
): Map<string, CurrentLICPacket> {
  const map = new Map<string, CurrentLICPacket>();
  for (const p of packets) {
    if (p.payload_type !== "status") continue;
    const deviceId = String(p.device as unknown as string);
    const prev = map.get(deviceId);
    if (!prev || prev.packet_date < p.packet_date) {
      map.set(deviceId, p);
    }
  }
  return map;
}

export function buildRainfallSummary(
  packets: CurrentLICPacket[],
  property: AUTProperty | null,
  propertyDevices: AUTPropertyDevice[]
): RainfallSummary {
  const latest = latestStatusPacketsByDevice(packets);
  const details: RainfallDetail[] = [];

  for (const [deviceId, p] of latest.entries()) {
    // payload_data for status always present when payload_type === 'status'
    const status: any = (p as any).payload_data?.status ?? {};
    // Only include devices that actually provide rain data in the packet
    const hasRainfall = Object.prototype.hasOwnProperty.call(status, "rainfall");
    const hasRaining = Object.prototype.hasOwnProperty.call(status, "raining");
    if (!hasRainfall && !hasRaining) {
      continue; // Ignore LICs without rain gauge data
    }

    const rainfall = Number(status.rainfall ?? 0);
    const raining = Number(status.raining ?? 0) === 1;

    details.push({
      deviceId,
      label: getLabelForDevice(deviceId, propertyDevices),
      rainfallMm: rainfall,
      raining,
      packetDate: p.packet_date,
    });
  }

  // If the property explicitly disables rain gauge, surface empty summary
  if (!property?.rain_gauge_enabled) {
    return {
      gaugeCount: 0,
      averageRainfallMm: null,
      rainingAny: false,
      details: [],
    };
  }

  const gaugeCount = details.length;
  const sum = details.reduce((acc, d) => acc + (Number.isFinite(d.rainfallMm) ? d.rainfallMm : 0), 0);
  const averageRainfallMm = gaugeCount > 0 ? sum / gaugeCount : null;
  const rainingAny = details.some((d) => d.raining);

  return { gaugeCount, averageRainfallMm, rainingAny, details };
}
