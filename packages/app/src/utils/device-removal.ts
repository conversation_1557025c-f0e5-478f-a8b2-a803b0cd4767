import type { AUTDevice, AUTProperty } from "@/api/queries/account";
import type { DeviceModel } from "@/api/model/device";
import type { DirectusApiService } from "@/api/service";

/**
 * Represents the analysis of what would be affected if a device is removed from a property
 */
export interface DeviceRemovalImpact {
  /** The device being analyzed for removal */
  device: AUTDevice;
  /** Whether the device can be safely removed */
  canRemove: boolean;
  /** Summary message about the removal impact */
  summary: string;
  /** Affected entities details */
  affectedEntities: {
    /** Projects that would lose their LIC controller */
    projects: Array<{
      id: string;
      name: string;
      description: string | null;
    }>;
    /** Sectors that would lose their valve controller */
    sectors: Array<{
      id: string;
      name: string;
      projectName: string;
      description: string | null;
    }>;
    /** Reservoirs that would lose their monitor */
    reservoirs: Array<{
      id: string;
      name: string;
      description: string | null;
    }>;
    /** Water pumps that would lose their controller */
    waterPumps: Array<{
      id: string;
      label: string;
      identifier: string;
      type: string;
    }>;
    /** Mesh devices that would be unmapped (for LIC removal) */
    meshDevices: Array<{
      id: string;
      identifier: string;
      model: DeviceModel;
      label?: string;
    }>;
  };
}

/**
 * Parameters for the device removal operation
 */
export interface DeviceRemovalParams {
  /** The device being removed */
  device: AUTDevice;
  /** The property the device is being removed from */
  propertyId: string;
  /** The complete property data with all related entities */
  propertyData: AUTProperty;
  /** The API service instance */
  apiService: DirectusApiService;
}

/**
 * Result of the device removal operation
 */
export interface DeviceRemovalResult {
  /** Whether the operation was successful */
  success: boolean;
  /** Error message if the operation failed */
  error?: string;
  /** Details about what was updated during the removal */
  updated: {
    /** Property-device relations that were ended */
    propertyDevicesEnded: string[];
    /** Mesh device mappings that were ended */
    meshMappingsEnded: string[];
    /** Projects that had their LIC controller set to NULL */
    projectsUpdated: string[];
    /** Sectors that had their valve controller set to NULL */
    sectorsUpdated: string[];
    /** Reservoirs that had their monitor set to NULL */
    reservoirsUpdated: string[];
    /** Water pumps that had their controller set to NULL */
    waterPumpsUpdated: string[];
  };
}

/**
 * Analyzes the impact of removing a device from a property
 * This function checks what entities would be affected by the device removal
 * without actually performing the removal
 */
export function analyzeDeviceRemovalImpact(
  device: AUTDevice,
  propertyData: AUTProperty
): DeviceRemovalImpact {
  const impact: DeviceRemovalImpact = {
    device,
    canRemove: true,
    summary: "",
    affectedEntities: {
      projects: [],
      sectors: [],
      reservoirs: [],
      waterPumps: [],
      meshDevices: [],
    },
  };

  // Find projects that reference this device as LIC controller
  const affectedProjects = propertyData.projects.filter(
    (project) => project.localized_irrigation_controller === device.id
  );

  // Find sectors that reference this device as valve controller
  const affectedSectors = propertyData.projects.flatMap((project) =>
    project.sectors
      .filter((sector) => sector.valve_controller === device.id)
      .map((sector) => ({
        ...sector,
        projectName: project.name,
      }))
  );

  // Find reservoirs that reference this device as monitor
  const affectedReservoirs = propertyData.reservoirs.filter(
    (reservoir) => reservoir.reservoir_monitor === device.id
  );

  // Find water pumps that reference this device as controller
  const affectedWaterPumps = propertyData.water_pumps.filter(
    (pump) => pump.water_pump_controller === device.id
  );

  // For LIC devices, find mapped mesh devices that would be unmapped
  let affectedMeshDevices: Array<{
    id: string;
    identifier: string;
    model: DeviceModel;
    label?: string;
  }> = [];

  if (device.model === "LIC") {
    affectedMeshDevices = propertyData.devices
      .filter(
        (pd) =>
          pd.current_mesh_device_mapping &&
          pd.current_mesh_device_mapping.lic_property_device ===
            // Find the property-device relation for this LIC
            propertyData.devices.find((licPd) => licPd.device.id === device.id)
              ?.id &&
          !pd.current_mesh_device_mapping.end_date
      )
      .map((pd) => ({
        id: pd.device.id,
        identifier: pd.device.identifier,
        model: pd.device.model,
        label: (pd.metadata as any)?.label || undefined,
      }));
  }

  // Populate affected entities
  impact.affectedEntities.projects = affectedProjects.map((project) => ({
    id: project.id,
    name: project.name,
    description: project.description,
  }));

  impact.affectedEntities.sectors = affectedSectors.map((sector) => ({
    id: sector.id,
    name: sector.name,
    projectName: sector.projectName,
    description: sector.description,
  }));

  impact.affectedEntities.reservoirs = affectedReservoirs.map((reservoir) => ({
    id: reservoir.id,
    name: reservoir.name,
    description: reservoir.description,
  }));

  impact.affectedEntities.waterPumps = affectedWaterPumps.map((pump) => ({
    id: pump.id,
    label: pump.label,
    identifier: pump.identifier,
    type: pump.pump_type,
  }));

  impact.affectedEntities.meshDevices = affectedMeshDevices;

  // Generate summary
  const totalAffected =
    affectedProjects.length +
    affectedSectors.length +
    affectedReservoirs.length +
    affectedWaterPumps.length +
    affectedMeshDevices.length;

  if (totalAffected === 0) {
    impact.summary = "Este dispositivo pode ser removido sem afetar outras entidades.";
  } else {
    const parts: string[] = [];
    if (affectedProjects.length > 0) {
      const licNote = device.model === "LIC" ? " (controladores e bombas de irrigação)" : "";
      parts.push(`${affectedProjects.length} projeto(s)${licNote}`);
    }
    if (affectedSectors.length > 0) {
      parts.push(`${affectedSectors.length} setor(es)`);
    }
    if (affectedReservoirs.length > 0) {
      parts.push(`${affectedReservoirs.length} reservatório(s)`);
    }
    if (affectedWaterPumps.length > 0) {
      parts.push(`${affectedWaterPumps.length} bomba(s) d'água`);
    }
    if (affectedMeshDevices.length > 0) {
      parts.push(`${affectedMeshDevices.length} dispositivo(s) mesh`);
    }

    impact.summary = `A remoção afetará: ${parts.join(", ")}. As referências serão definidas como nulas.`;
  }

  return impact;
}

/**
 * Performs the actual device removal operation
 * This includes ending property-device relations, mesh mappings, and setting device references to NULL
 */
export async function performDeviceRemoval({
  device,
  propertyId,
  propertyData,
  apiService,
}: DeviceRemovalParams): Promise<DeviceRemovalResult> {
  const result: DeviceRemovalResult = {
    success: false,
    updated: {
      propertyDevicesEnded: [],
      meshMappingsEnded: [],
      projectsUpdated: [],
      sectorsUpdated: [],
      reservoirsUpdated: [],
      waterPumpsUpdated: [],
    },
  };

  try {
    const now = new Date().toISOString();

    // 1. Find the property-device relation for this device
    const devicePropertyDevice = propertyData.devices.find(
      (pd) => pd.device.id === device.id && !pd.end_date
    );

    if (!devicePropertyDevice) {
      throw new Error("Device not found in property or already removed");
    }

    // 2. If it's a mesh device, end its current mapping first (if any)
    if (
      ["WPC-PL10", "WPC-PL50", "VC", "RM"].includes(device.model) &&
      devicePropertyDevice.current_mesh_device_mapping &&
      !devicePropertyDevice.current_mesh_device_mapping.end_date
    ) {
      await apiService.meshDeviceMapping.update(
        devicePropertyDevice.current_mesh_device_mapping.id,
        {
          end_date: now,
        }
      );
      result.updated.meshMappingsEnded.push(
        devicePropertyDevice.current_mesh_device_mapping.id
      );
    }

    // 3. If it's a LIC, end mappings for all mapped mesh devices
    if (device.model === "LIC") {
      const mappedDevices = propertyData.devices.filter(
        (pd) =>
          pd.current_mesh_device_mapping &&
          pd.current_mesh_device_mapping.lic_property_device ===
            devicePropertyDevice.id &&
          !pd.current_mesh_device_mapping.end_date
      );

      for (const mappedDevice of mappedDevices) {
        await apiService.meshDeviceMapping.update(
          mappedDevice.current_mesh_device_mapping.id,
          {
            end_date: now,
          }
        );
        result.updated.meshMappingsEnded.push(
          mappedDevice.current_mesh_device_mapping.id
        );
      }
    }

    // 4. Set device references to NULL in related entities
    
    // Update projects that reference this device as LIC controller
    const affectedProjects = propertyData.projects.filter(
      (project) => project.localized_irrigation_controller === device.id
    );
    
    for (const project of affectedProjects) {
      await apiService.project.update(project.id, {
        localized_irrigation_controller: null as any,
        // When LIC is removed, irrigation pump must also be set to null
        // since it depends on the LIC controller for mesh network connectivity
        irrigation_water_pump: null as any,
      });
      result.updated.projectsUpdated.push(project.id);
    }

    // Update sectors that reference this device as valve controller
    const affectedSectors = propertyData.projects.flatMap((project) =>
      project.sectors.filter((sector) => sector.valve_controller === device.id)
    );
    
    for (const sector of affectedSectors) {
      await apiService.sector.update(sector.id, {
        valve_controller: null as any,
        valve_controller_output: null as any,
      });
      result.updated.sectorsUpdated.push(sector.id);
    }

    // Update reservoirs that reference this device as monitor
    const affectedReservoirs = propertyData.reservoirs.filter(
      (reservoir) => reservoir.reservoir_monitor === device.id
    );
    
    for (const reservoir of affectedReservoirs) {
      await apiService.reservoir.update(reservoir.id, {
        reservoir_monitor: null as any,
      });
      result.updated.reservoirsUpdated.push(reservoir.id);
    }

    // Update water pumps that reference this device as controller
    const affectedWaterPumps = propertyData.water_pumps.filter(
      (pump) => pump.water_pump_controller === device.id
    );
    
    for (const pump of affectedWaterPumps) {
      await apiService.waterPump.update(pump.id, {
        water_pump_controller: null as any,
      });
      result.updated.waterPumpsUpdated.push(pump.id);
    }

    // 5. Finally, end the property_device relation for the selected device
    await apiService.propertyDevice.update(devicePropertyDevice.id, {
      end_date: now,
    });
    result.updated.propertyDevicesEnded.push(devicePropertyDevice.id);

    result.success = true;
    return result;
  } catch (error) {
    result.error =
      error instanceof Error ? error.message : "Unknown error occurred";
    return result;
  }
}