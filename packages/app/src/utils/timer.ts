export interface ITimer {
  start(delayMs: number, callback: () => Promise<void>): void;
  stop(): void;
}

/**
 * A timer that uses setTimeout for periodic execution.
 * This guarantees that the interval between the end of one execution and the start of the next is always the specified delay.
 */
export class TimeoutTimer implements ITimer {
  private timerId: NodeJS.Timeout | null = null;
  private active = false;

  start(delayMs: number, callback: () => Promise<void>) {
    if (this.active) {
      throw new Error("TimeoutTimer already started");
    }

    this.active = true;

    const run = async () => {
      // mark that there is no pending timeout while the callback runs
      this.timerId = null;
      try {
        await callback();
      } catch (err) {
        // Don't let a thrown error stop future runs. Surface to console for debugging.
        // eslint-disable-next-line no-console
        console.error("TimeoutTimer callback error:", err);
      }

      // If stop() was called while the callback was running, do not schedule another run.
      if (!this.active) return;

      // Schedule next run after delayMs
      this.timerId = setTimeout(run, Math.max(0, delayMs));
    };

    // Schedule the first run after delayMs
    this.timerId = setTimeout(run, Math.max(0, delayMs));
  }

  /**
   * Stop the timer.
   */
  stop() {
    // prevent scheduling of further runs
    this.active = false;

    if (this.timerId) {
      clearTimeout(this.timerId);
      this.timerId = null;
    }
  }
}

export class IntervalTimer implements ITimer {
  private timerId: NodeJS.Timeout | null = null;
  private active = false;

  start(delayMs: number, callback: () => Promise<void>) {
    if (this.active) {
      throw new Error("IntervalTimer already started");
    }

    this.active = true;

    this.timerId = setInterval(async () => {
      try {
        await callback();
      } catch (err) {
        // Don't let a thrown error stop future runs. Surface to console for debugging.
        // eslint-disable-next-line no-console
        console.error("IntervalTimer callback error:", err);
      }
    }, Math.max(0, delayMs));
  }

  stop() {
    this.active = false;

    if (this.timerId) {
      clearInterval(this.timerId);
      this.timerId = null;
    }
  }
}
