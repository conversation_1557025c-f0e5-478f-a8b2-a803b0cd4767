export function validatePassword(
  password: string,
  confirmPassword?: string
): { valid: true } | { valid: false; error: string } {
  if (password.length < 6) {
    return {
      valid: false,
      error: "A senha deve ter pelo menos 6 caracteres.",
    };
  }

  if (confirmPassword != null && password !== confirmPassword) {
    return {
      valid: false,
      error: "As senhas não coincidem.",
    };
  }
  return { valid: true };
}
