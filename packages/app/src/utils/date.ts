import Dayjs from "dayjs";
import updateLocale from "dayjs/plugin/updateLocale";
import relativeTime from "dayjs/plugin/relativeTime";
import localizedFormat from "dayjs/plugin/localizedFormat";
import duration, { type Duration } from "dayjs/plugin/duration";
import utc from "dayjs/plugin/utc";
import isBetween from "dayjs/plugin/isBetween";
import localeData from "dayjs/plugin/localeData";
import customParseFormat from "dayjs/plugin/customParseFormat";
import "dayjs/locale/pt-br";
import calendar from "dayjs/plugin/calendar";

Dayjs.locale("pt-br");
Dayjs.extend(updateLocale);
Dayjs.extend(relativeTime);
Dayjs.extend(localizedFormat);
Dayjs.extend(duration);
Dayjs.extend(utc);
Dayjs.extend(isBetween);
Dayjs.extend(localeData);
Dayjs.extend(customParseFormat);
Dayjs.extend(calendar);

export const dayjs = Dayjs;
dayjs.locale("pt-br");
dayjs.updateLocale("pt-br", {
  calendar: {
    lastDay: "[Ontem às] LT",
    sameDay: "[Hoje às] LT",
    nextDay: "[Amanhã às] LT",
    lastWeek: "[último] dddd [às] LT",
    nextWeek: "dddd [às] LT",
    sameElse: "L",
  },
});

export function formatDuration(d: Duration, fmt: "H[h]? mm[m]?") {
  if (fmt === "H[h]? mm[m]?") {
    const h = Math.floor(d.asHours());
    const m = d.minutes();
    if (!h) {
      return d.format("mm[m]");
    }
    if (!m) {
      return `${h}h`;
    }
    return d.format(`[${h}h] mm[m]`);
  }
}

export function toDate(d: Date | string | number) {
  return typeof d === "object" ? d : new Date(d);
}

export function dayjsTZ(
  d: string | number | Dayjs.Dayjs | Date,
  tz?: string | number
) {
  return tz != null ? dayjs(d).utcOffset(tz) : dayjs(d);
}

export type TimeStr =
  `${number}${number}:${number}${number}:${number}${number}`;

export type DateStr =
  `${number}${number}${number}${number}-${number}${number}-${number}${number}`;

export function getTimeFromDate(
  d: Date | string | number | Dayjs.Dayjs,
  tz: string | undefined
): TimeStr {
  const djs = dayjsTZ(d, tz);
  return djs.format("HH:mm:ss") as TimeStr;
}

export function setTimeInDate(
  d: Date | string | number | Dayjs.Dayjs,
  time: TimeStr,
  tz: string | undefined
) {
  const djs = dayjsTZ(d, tz);
  return dayjs(djs.format("YYYY-MM-DDT") + time + djs.format("ZZ"));
}

export function getDateStr(
  d: Date | string | number | Dayjs.Dayjs,
  tz?: string | number
): DateStr {
  return dayjsTZ(d, tz).format("YYYY-MM-DD") as DateStr;
}

export function isDateStr(d: string): d is DateStr {
  return /^\d{4}-\d{2}-\d{2}$/.test(d);
}
