import { use } from "./object";

export class AppConfig {
  public readonly auth = {
    verificationUrl: use(new URL(window.location.href), (url) => {
      url.hash = "/verify-email";
      return url.toString();
    }),
    resetUrl: use(new URL(window.location.href), (url) => {
      url.hash = "/reset-password";
      return url.toString();
    }),
  };
}

export const appConfig = new AppConfig();
console.log("AppConfig initialized:", appConfig);
