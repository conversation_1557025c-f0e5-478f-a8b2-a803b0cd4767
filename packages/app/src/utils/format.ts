import { DAY_OF_WEEK_VALUES, DayOfWeek } from "@/api/model/common";

export const formatDays = (days: DayOfWeek[]) => {
  const dayMap: Record<string, string> = {
    MON: "Seg",
    TUE: "Ter",
    WED: "Qua",
    THU: "Qui",
    FRI: "Sex",
    SAT: "Sáb",
    SUN: "Dom",
  };
  const allDays = DAY_OF_WEEK_VALUES;
  const weekdays: DayOfWeek[] = ["MON", "TUE", "WED", "THU", "FRI"];
  const weekend: DayOfWeek[] = ["SAT", "SUN"];

  // Ordena os dias conforme a semana
  const sortedDays = days
    .slice()
    .sort((a, b) => allDays.indexOf(a) - allDays.indexOf(b));
  const isWeekdays =
    weekdays.every((d) => sortedDays.includes(d)) && sortedDays.length === 5;
  const isWeekend =
    weekend.every((d) => sortedDays.includes(d)) && sortedDays.length === 2;
  const isEveryday =
    allDays.every((d) => sortedDays.includes(d)) && sortedDays.length === 7;

  if (isEveryday) return "Todos os dias";
  if (isWeekdays) return "Dias úteis";
  if (isWeekend) return "Fim de semana";

  return sortedDays.map((day) => dayMap[day] || day).join(", ");
};
