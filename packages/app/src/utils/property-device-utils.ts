import {
  DeviceOverview,
  findDeviceOverviewByIdentifier,
} from "@/api/queries/device";
import { DirectusApiService } from "@/api/service";

export type NewPropertyDeviceValidationResult =
  | {
      valid: true;
      deviceOverview: null;
      message: "DEVICE_NOT_FOUND";
    }
  | {
      valid: true;
      deviceOverview: DeviceOverview;
      message: "NOT_IN_USE";
    }
  | {
      valid: false;
      deviceOverview: DeviceOverview;
      message: "IN_USE";
    };

/**
 * Validates if a new property device can be created.
 *
 *
 * @param apiService
 * @param propertyId
 * @param deviceIdentifier
 * @param referenceDate
 * @returns
 */
export async function validateNewPropertyDevice(
  apiService: DirectusApiService,
  propertyId: string,
  deviceIdentifier: string,
  referenceDate = new Date()
): Promise<NewPropertyDeviceValidationResult> {
  const deviceOverview = await findDeviceOverviewByIdentifier(
    apiService,
    deviceIdentifier
  );
  // Device does not exist, it is valid for creation
  if (!deviceOverview) {
    return { valid: true, deviceOverview: null, message: "DEVICE_NOT_FOUND" };
  }
  if (!deviceOverview.properties || deviceOverview.properties.length === 0) {
    return { valid: true, deviceOverview, message: "NOT_IN_USE" };
  }
  // Check if the device is already mapped to the property
  const isInUse = deviceOverview.properties.some(
    (pd) =>
      (pd.start_date == null || new Date(pd.start_date) <= referenceDate) &&
      (pd.end_date == null || new Date(pd.end_date) >= referenceDate)
  );
  return isInUse
    ? {
        valid: false,
        deviceOverview,
        message: "IN_USE",
      }
    : {
        valid: true,
        deviceOverview,
        message: "NOT_IN_USE",
      };
}
