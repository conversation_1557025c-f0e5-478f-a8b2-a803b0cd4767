// Brazilian-specific validators and formatters

// Remove all non-digits
export const onlyDigits = (value: string) => value.replace(/\D+/g, "");

// CPF validation based on Brazilian rules
export function validateCPF(cpf: string): boolean {
  const clean = onlyDigits(cpf);
  if (clean.length !== 11) return false;
  if (/^(\d)\1{10}$/.test(clean)) return false; // same digits

  let sum = 0;
  for (let i = 0; i < 9; i++) sum += parseInt(clean.charAt(i)) * (10 - i);
  let remainder = 11 - (sum % 11);
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(clean.charAt(9))) return false;

  sum = 0;
  for (let i = 0; i < 10; i++) sum += parseInt(clean.charAt(i)) * (11 - i);
  remainder = 11 - (sum % 11);
  if (remainder === 10 || remainder === 11) remainder = 0;
  if (remainder !== parseInt(clean.charAt(10))) return false;

  return true;
}

export function formatCPF(value: string): string {
  const clean = onlyDigits(value).slice(0, 11);
  return clean
    .replace(/(\d{3})(\d)/, "$1.$2")
    .replace(/(\d{3})(\d)/, "$1.$2")
    .replace(/(\d{3})(\d{1,2})$/, "$1-$2");
}

export function cleanCPF(value: string): string {
  return onlyDigits(value).slice(0, 11);
}

// Brazilian phone: allow 10 or 11 digits
export function validatePhoneBR(phone: string): boolean {
  const clean = onlyDigits(phone);
  return clean.length === 10 || clean.length === 11;
}

export function formatPhoneBR(value: string): string {
  const clean = onlyDigits(value).slice(0, 11);
  if (clean.length <= 10) {
    return clean
      .replace(/(\d{2})(\d)/, "($1) $2")
      .replace(/(\d{4})(\d)/, "$1-$2");
  }
  return clean
    .replace(/(\d{2})(\d)/, "($1) $2")
    .replace(/(\d{5})(\d)/, "$1-$2");
}

export function cleanPhoneBR(value: string): string {
  return onlyDigits(value).slice(0, 11);
}

