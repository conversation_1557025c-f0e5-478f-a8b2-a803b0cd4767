import { useAtomValue } from "jotai";
import { useEffect } from "react";
import { Route, Router, Switch } from "wouter";
import { useMyHashLocation, useMySearchHook } from "./hooks/useMyHashLocation";
import AccountSelectionPage from "./pages/AccountSelectionPage";
import CheckEmailPage from "./pages/CheckEmailPage";
import CreatePropertyPage from "./pages/CreatePropertyPage";
import ForgotPasswordPage from "./pages/ForgotPasswordPage";
import LoginPage from "./pages/LoginPage";
import NoAccountPage from "./pages/NoAccountPage";
import NoPropertyPage from "./pages/NoPropertyPage";
import PropertySelectionPage from "./pages/PropertySelectionPage";
import ResetPasswordPage from "./pages/ResetPasswordPage";
import SignUpPage from "./pages/SignUpPage";
import VerifyEmailPage from "./pages/VerifyEmailPage";
import AppShell from "./pages/main/AppShell";
import { isAuthenticatedAtom } from "./store";

// Route Guard Components
const ProtectedRoute: React.FC<{
  component: React.ComponentType<any>;
  path: string;
  [key: string]: any;
}> = ({ component: Component, ...props }) => {
  const isAuthenticated = useAtomValue(isAuthenticatedAtom);
  const [, setLocation] = useMyHashLocation();

  if (!isAuthenticated) {
    setLocation("/login");
    return null;
  }

  return <Route {...props} component={Component} />;
};

const PublicRoute: React.FC<{
  component: React.ComponentType<any>;
  path: string;
  redirectAuthenticated?: string;
  [key: string]: any;
}> = ({
  component: Component,
  redirectAuthenticated = "/app/dashboard",
  ...props
}) => {
  const isAuthenticated = useAtomValue(isAuthenticatedAtom);
  const [, setLocation] = useMyHashLocation();

  if (isAuthenticated && redirectAuthenticated) {
    setLocation(redirectAuthenticated);
    return null;
  }

  return <Route {...props} component={Component} />;
};

function AppRouter() {
  const [location, setLocation] = useMyHashLocation();

  useEffect(() => {
    console.log(`AppRouter mounted - current location: ${location}`);
  }, [location]);

  return (
    <>
      {/* 
      Routes below are matched exclusively -
      the first matched route gets rendered
    */}
      <Router hook={useMyHashLocation} searchHook={useMySearchHook}>
        <Switch>
          {/* Public Routes - Redirect authenticated users */}
          <PublicRoute path="/login" component={LoginPage} />
          <PublicRoute path="/signup" component={SignUpPage} />
          <PublicRoute path="/forgot-password" component={ForgotPasswordPage} />
          <PublicRoute path="/reset-password" component={ResetPasswordPage} />
          <PublicRoute path="/check-email" component={CheckEmailPage} />
          <PublicRoute path="/verify-email" component={VerifyEmailPage} />

          {/* Protected Routes - Require authentication */}
          <ProtectedRoute
            path="/select-account"
            component={AccountSelectionPage}
          />
          <ProtectedRoute path="/no-account" component={NoAccountPage} />
          <ProtectedRoute
            path="/select-property"
            component={PropertySelectionPage}
          />
          <ProtectedRoute path="/no-property" component={NoPropertyPage} />
          <ProtectedRoute
            path="/property/create"
            component={CreatePropertyPage}
          />

          {/* Main Application Shell - Protected */}
          <ProtectedRoute path="/app/*" component={AppShell} />

          {/* Root path - Smart redirect based on auth status */}
          <Route
            path="/"
            component={() => {
              const isAuthenticated = useAtomValue(isAuthenticatedAtom);
              setLocation(isAuthenticated ? "/app/dashboard" : "/login");
              return null;
            }}
          />

          {/* Default route in a switch */}
          <Route>404: No such page!</Route>
        </Switch>
      </Router>
    </>
  );
}
export default AppRouter;
