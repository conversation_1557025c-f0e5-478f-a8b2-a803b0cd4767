import LogoSymbol from "@/assets/logo-symbol-transparent-79x96.png"; // Adjust the import path as needed
import LogoWordmark from "@/assets/logo-wordmark-256x48.png"; // Adjust the import path as needed

import { apiService } from "@/api/service";
import { useToast } from "@/components";
import Button from "@/components/ui/Button";
import { useAuth } from "@/store";
import React, { useState } from "react";
import { useLocation } from "wouter";
import { validatePassword } from "@/utils/validation";

function SignUpPage() {
  const [email, setEmail] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  // const [phone, setPhone] = useState("");
  // const [cpf, setCpf] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { showSuccess, showError } = useToast();
  const [, setLocation] = useLocation();

  const { login } = useAuth();

  // CPF validation function
  const validateCPF = (cpf: string): boolean => {
    const cleanCPF = cpf.replace(/[^\d]+/g, "");
    if (cleanCPF.length !== 11) return false;

    // Check for known invalid CPFs
    if (/^(\d)\1{10}$/.test(cleanCPF)) return false;

    // Validate first digit
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
    }
    let remainder = 11 - (sum % 11);
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cleanCPF.charAt(9))) return false;

    // Validate second digit
    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
    }
    remainder = 11 - (sum % 11);
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cleanCPF.charAt(10))) return false;

    return true;
  };

  // Format CPF input
  const formatCPF = (value: string): string => {
    const cleanValue = value.replace(/[^\d]+/g, "");
    if (cleanValue.length <= 11) {
      return cleanValue
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d)/, "$1.$2")
        .replace(/(\d{3})(\d{1,2})$/, "$1-$2");
    }
    return value;
  };

  // Format phone input
  const formatPhone = (value: string): string => {
    const cleanValue = value.replace(/[^\d]+/g, "");
    if (cleanValue.length <= 11) {
      return cleanValue
        .replace(/(\d{2})(\d)/, "($1) $2")
        .replace(/(\d{5})(\d)/, "$1-$2");
    }
    return value;
  };

  const handleSignUp = async () => {
    // Validation
    if (
      !email ||
      !firstName ||
      !lastName ||
      // !phone ||
      // !cpf ||
      !password ||
      !confirmPassword
    ) {
      showError({
        title: "Erro no cadastro",
        message: "Por favor, preencha todos os campos.",
      });
      return;
    }

    // if (!validateCPF(cpf)) {
    //   showError({
    //     title: "Erro no cadastro",
    //     message: "CPF inválido. Por favor, verifique o número digitado.",
    //   });
    //   return;
    // }

    const passwordValidation = validatePassword(password, confirmPassword);
    if (!passwordValidation.valid) {
      showError({
        title: "Erro no cadastro",
        message: passwordValidation.error,
      });
      return;
    }

    try {
      setIsLoading(true);

      // Clean CPF and phone formats for API
      // const cleanCPF = cpf.replace(/[^\d]+/g, "");
      // const cleanPhone = phone.replace(/[^\d]+/g, "");

      // Call API service to register user
      await apiService.auth.register(
        email,
        password,
        firstName,
        lastName
        // cleanCPF,
        // cleanPhone
      );

      showSuccess({
        title: "Cadastro realizado!",
        message:
          "Sua conta foi criada com sucesso. Verifique seu email para confirmar o cadastro.",
      });

      // Redirect to check email page instead of login
      setLocation("/check-email");
    } catch (error: any) {
      console.error("Signup error:", error);

      let errorMessage = "Ocorreu um erro ao criar sua conta. Tente novamente.";

      // Handle specific API errors
      if (error.message?.includes("email")) {
        errorMessage =
          "Este email já está cadastrado. Use outro email ou recupere sua senha.";
      } else if (error.message?.includes("cpf")) {
        errorMessage =
          "Este CPF já está cadastrado. Use outro CPF ou recupere sua senha.";
      } else if (error.message?.includes("validation")) {
        errorMessage = "Dados inválidos. Verifique as informações digitadas.";
      }

      showError({
        title: "Erro no cadastro",
        message: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await handleSignUp();
  };

  // const handleCPFChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const formatted = formatCPF(e.target.value);
  //   setCpf(formatted);
  // };

  // const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   const formatted = formatPhone(e.target.value);
  //   setPhone(formatted);
  // };

  return (
    <div
      className="h-full overflow-y-auto px-4 flex flex-col items-center justify-center"
      style={{
        background:
          "linear-gradient(0deg, #18181c 0%, #121b23 40%, #042438 100%)",
      }}
    >
      <div className="w-full max-w-md mt-4">
        {/* Logo and Header */}
        <div className="text-center mb-4">
          <div className="mx-auto flex flex-col items-center text-center">
            <img
              src={LogoWordmark}
              alt="Irriga+ Wordmark"
              className="h-8 mb-2"
            />
          </div>
          <p className="text-gray-50">Sistema de Controle de Irrigação</p>
        </div>

        {/* Sign Up Form */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 relative">
          <img
            src={LogoSymbol}
            alt="Irriga+ Symbol"
            className="absolute top-4 right-4 h-16 object-cover"
          />
          <h2 className="text-2xl font-semibold text-gray-900 mb-4 text-center">
            Crie sua conta
          </h2>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label
                  className="block text-sm font-medium text-gray-700 mb-2"
                  htmlFor="firstName"
                >
                  Nome
                </label>
                <input
                  id="firstName"
                  type="text"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
                  placeholder="Seu nome"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  required
                  autoComplete="given-name"
                />
              </div>

              <div>
                <label
                  className="block text-sm font-medium text-gray-700 mb-2"
                  htmlFor="lastName"
                >
                  Sobrenome
                </label>
                <input
                  id="lastName"
                  type="text"
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
                  placeholder="Seu sobrenome"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  required
                  autoComplete="family-name"
                />
              </div>
            </div>

            <div>
              <label
                className="block text-sm font-medium text-gray-700 mb-2"
                htmlFor="email"
              >
                Email
              </label>
              <input
                id="email"
                type="email"
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                autoComplete="email"
              />
            </div>

            {/* <div>
              <label
                className="block text-sm font-medium text-gray-700 mb-2"
                htmlFor="phone"
              >
                Telefone
              </label>
              <input
                id="phone"
                type="tel"
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
                placeholder="(00) 00000-0000"
                value={phone}
                onChange={handlePhoneChange}
                required
                autoComplete="tel"
              />
            </div> */}

            {/* <div>
              <label
                className="block text-sm font-medium text-gray-700 mb-2"
                htmlFor="cpf"
              >
                CPF
              </label>
              <input
                id="cpf"
                type="text"
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
                placeholder="000.000.000-00"
                value={cpf}
                onChange={handleCPFChange}
                required
                maxLength={14}
                autoComplete="off"
              />
            </div> */}

            <div>
              <label
                className="block text-sm font-medium text-gray-700 mb-2"
                htmlFor="password"
              >
                Senha
              </label>
              <input
                id="password"
                type="password"
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
                placeholder="Crie uma senha"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                autoComplete="new-password"
              />
            </div>

            <div>
              <label
                className="block text-sm font-medium text-gray-700 mb-2"
                htmlFor="confirmPassword"
              >
                Confirmar Senha
              </label>
              <input
                id="confirmPassword"
                type="password"
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
                placeholder="Confirme sua senha"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                required
                autoComplete="new-password"
              />
            </div>

            <Button
              type="submit"
              disabled={isLoading}
              variant="primary"
              className="w-full shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              loading={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Criando conta...
                </div>
              ) : (
                "Criar conta"
              )}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Já tem uma conta?{" "}
              <Button
                type="button"
                variant="ghost"
                className="font-medium"
                onClick={() => setLocation("/login")}
              >
                Faça login
              </Button>
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-xs text-gray-500">
          <p>Sistema de Gestão de Irrigação Automatizada</p>
          <p className="mt-1">
            Para propriedades rurais de pequeno e médio porte
          </p>
        </div>
      </div>
    </div>
  );
}

export default SignUpPage;
