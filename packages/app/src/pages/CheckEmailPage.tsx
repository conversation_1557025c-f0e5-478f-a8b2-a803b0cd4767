import LogoSymbol from "@/assets/logo-symbol-transparent-79x96.png";
import LogoWordmark from "@/assets/logo-wordmark-256x48.png";
import Button from "@/components/ui/Button";
import { useLocation } from "wouter";

function CheckEmailPage() {
  const [, setLocation] = useLocation();

  const handleGoToLogin = () => {
    setLocation("/login");
  };

  return (
    <div
      className="h-full overflow-y-auto px-4 flex flex-col items-center justify-center"
      style={{
        background:
          "linear-gradient(0deg, #18181c 0%, #121b23 40%, #042438 100%)",
      }}
    >
      <div className="w-full max-w-md mt-4">
        {/* Logo and Header */}
        <div className="text-center mb-4">
          <div className="mx-auto flex flex-col items-center text-center">
            <img
              src={LogoWordmark}
              alt="Irriga+ Wordmark"
              className="h-8 mb-2"
            />
          </div>
          <p className="text-gray-50">Sistema de Controle de Irrigação</p>
        </div>

        {/* Check Email Card */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 relative">
          <img
            src={LogoSymbol}
            alt="Irriga+ Symbol"
            className="absolute top-4 right-4 h-16 object-cover"
          />

          <div className="text-center mb-6">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
              <svg
                className="h-8 w-8 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              Verifique seu email
            </h2>
            <p className="text-gray-600">
              Enviamos um link de confirmação para o seu email. Clique no link
              para continuar o processo.
            </p>
          </div>

          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-blue-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-800">
                    <strong>Dica:</strong> Verifique também a pasta de spam ou
                    lixeira eletrônica caso não encontre o email em sua caixa de
                    entrada.
                  </p>
                </div>
              </div>
            </div>

            <Button
              type="button"
              variant="primary"
              className="w-full shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              onClick={handleGoToLogin}
            >
              Voltar para o login
            </Button>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-xs text-gray-500">
          <p>Sistema de Gestão de Irrigação Automatizada</p>
          <p className="mt-1">
            Para propriedades rurais de pequeno e médio porte
          </p>
        </div>
      </div>
    </div>
  );
}

export default CheckEmailPage;
