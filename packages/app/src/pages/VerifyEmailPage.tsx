import { apiService } from "@/api/service";
import LogoSymbol from "@/assets/logo-symbol-transparent-79x96.png";
import LogoWordmark from "@/assets/logo-wordmark-256x48.png";
import { useToast } from "@/components";
import Button from "@/components/ui/Button";
import { useEffect, useState } from "react";
import { useLocation, useSearchParams } from "wouter";

function VerifyEmailPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const { showSuccess, showError } = useToast();
  const [, setLocation] = useLocation();
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");

  useEffect(() => {
    if (token) {
      verifyEmail();
    } else {
      setIsLoading(false);
      showError({
        title: "Erro na verificação",
        message: "Token de verificação não encontrado na URL.",
      });
    }
  }, [token]);

  const verifyEmail = async () => {
    try {
      setIsLoading(true);
      await apiService.auth.verifyEmail(token!);
      setIsSuccess(true);

      showSuccess({
        title: "Email verificado!",
        message:
          "Seu email foi verificado com sucesso. Você será redirecionado para o login.",
      });

      // Auto-redirect to login after 3 seconds
      setTimeout(() => {
        setLocation("/login");
      }, 3000);
    } catch (error: any) {
      console.error("Email verification error:", error);

      let errorMessage =
        "Ocorreu um erro ao verificar seu email. Tente novamente.";

      if (error.message?.includes("expired")) {
        errorMessage =
          "Este link de verificação expirou. Solicite um novo email de verificação.";
      } else if (error.message?.includes("invalid")) {
        errorMessage =
          "Token de verificação inválido. Verifique o link ou solicite um novo.";
      }

      showError({
        title: "Erro na verificação",
        message: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendVerification = async () => {
    try {
      setIsResending(true);
      // Note: This would require implementing a resend verification endpoint
      // For now, we'll show a message indicating this feature is coming
      showSuccess({
        title: "Em breve",
        message:
          "Funcionalidade de reenvio de email será implementada em breve.",
      });
    } catch (error) {
      showError({
        title: "Erro",
        message: "Não foi possível reenviar o email de verificação.",
      });
    } finally {
      setIsResending(false);
    }
  };

  const handleGoToLogin = () => {
    setLocation("/login");
  };

  return (
    <div
      className="h-full overflow-y-auto px-4 flex flex-col items-center justify-center"
      style={{
        background:
          "linear-gradient(0deg, #18181c 0%, #121b23 40%, #042438 100%)",
      }}
    >
      <div className="w-full max-w-md mt-4">
        {/* Logo and Header */}
        <div className="text-center mb-4">
          <div className="mx-auto flex flex-col items-center text-center">
            <img
              src={LogoWordmark}
              alt="Irriga+ Wordmark"
              className="h-8 mb-2"
            />
          </div>
          <p className="text-gray-50">Sistema de Controle de Irrigação</p>
        </div>

        {/* Verification Card */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 relative">
          <img
            src={LogoSymbol}
            alt="Irriga+ Symbol"
            className="absolute top-4 right-4 h-16 object-cover"
          />

          {isLoading ? (
            <div className="text-center">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
                <svg
                  className="animate-spin h-8 w-8 text-blue-600"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                Verificando email...
              </h2>
              <p className="text-gray-600">
                Aguarde enquanto verificamos seu endereço de email.
              </p>
            </div>
          ) : isSuccess ? (
            <div className="text-center">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                <svg
                  className="h-8 w-8 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                Email verificado!
              </h2>
              <p className="text-gray-600 mb-4">
                Seu email foi verificado com sucesso. Você será redirecionado
                para o login em instantes...
              </p>
              <Button
                type="button"
                variant="primary"
                className="w-full shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                onClick={handleGoToLogin}
              >
                Ir para o login agora
              </Button>
            </div>
          ) : (
            <div className="text-center">
              <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
                <svg
                  className="h-8 w-8 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-gray-900 mb-2">
                Erro na verificação
              </h2>
              <p className="text-gray-600 mb-6">
                Não foi possível verificar seu email. O link pode estar expirado
                ou inválido.
              </p>
              <div className="space-y-3">
                <Button
                  type="button"
                  variant="primary"
                  className="w-full shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  onClick={verifyEmail}
                >
                  Tentar novamente
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  className="w-full"
                  onClick={handleResendVerification}
                  loading={isResending}
                >
                  Reenviar email de verificação
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  className="w-full"
                  onClick={handleGoToLogin}
                >
                  Voltar para o login
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-xs text-gray-500">
          <p>Sistema de Gestão de Irrigação Automatizada</p>
          <p className="mt-1">
            Para propriedades rurais de pequeno e médio porte
          </p>
        </div>
      </div>
    </div>
  );
}

export default VerifyEmailPage;
