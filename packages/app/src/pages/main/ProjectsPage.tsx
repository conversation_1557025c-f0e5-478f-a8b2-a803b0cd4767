import type { AUTProject, AUTProperty } from "@/api/queries/account";
import { appShell } from "@/store";
import { projectsAtom, selectedPropertyAtom } from "@/store/data";
import { dayjs } from "@/utils/date";
import { useAtomValue, useSetAtom } from "jotai";
import React, { useEffect, useState } from "react";
import { useLocation } from "wouter";
import ProjectCard, { type ProjectDisplayData } from "./components/ProjectCard";
import { FAB } from "@/components/ui/FAB";

// Transform AUTProject to ProjectDisplayData
function transformProjectData(
  project: AUTProject,
  property: AUTProperty | null
): ProjectDisplayData {
  const sectorsCount = project.sectors?.length || 0;
  const irrigationPlansCount = project.irrigation_plans?.length || 0;
  const hasIrrigation = !!project.irrigation_water_pump;
  const hasFertigation = !!project.fertigation_water_pump;

  // Calculate total area from sectors
  const totalArea =
    project.sectors?.reduce((sum, sector) => {
      return sum + (sector.area || 0);
    }, 0) || 0;

  // Determine status based on dates and data
  const now = dayjs();
  const startDate = project.start_date ? dayjs(project.start_date) : null;
  const endDate = project.end_date ? dayjs(project.end_date) : null;

  let status: "active" | "planning" | "inactive" = "planning";

  if (startDate && endDate) {
    if (now.isBefore(startDate)) {
      status = "planning";
    } else if (now.isAfter(endDate)) {
      status = "inactive";
    } else if (sectorsCount > 0 && irrigationPlansCount > 0) {
      status = "active";
    }
  } else if (sectorsCount > 0 && irrigationPlansCount > 0) {
    status = "active";
  }

  // Find last activity from irrigation plans
  const lastActivity = project.irrigation_plans?.reduce((latest, plan) => {
    if (!plan.start_date) return latest;
    const planDate = dayjs(plan.start_date);
    return !latest || planDate.isAfter(dayjs(latest))
      ? plan.start_date
      : latest;
  }, null as string | null);

  return {
    id: project.id,
    name: project.name,
    description: project.description || "",
    startDate: project.start_date || "",
    endDate: project.end_date || "",
    sectorsCount,
    hasIrrigation,
    hasFertigation,
    status,
    area: totalArea,
    irrigationPlansCount,
    lastActivity: lastActivity || null,
    pipeWashTimeMinutes:
      project.pipe_wash_time_seconds != null
        ? Math.round(project.pipe_wash_time_seconds / 60)
        : null,
    // Use global property values for backwash settings
    backwashDurationMinutes: property?.backwash_duration_minutes || null,
    backwashPeriodMinutes: property?.backwash_period_minutes || null,
  };
}

function ProjectsPage() {
  const setBackButton = useSetAtom(appShell.backButtonAtom);
  useEffect(() => {
    setBackButton(false);
  });

  const [, setLocation] = useLocation();
  const rawProjects = useAtomValue(projectsAtom);
  const property = useAtomValue(selectedPropertyAtom);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "active" | "planning" | "inactive"
  >("all");

  // Transform projects data and apply filters in a single useMemo
  const projects = React.useMemo(() => {
    return rawProjects.map((project) =>
      transformProjectData(project, property)
    );
  }, [rawProjects, property]);

  const filteredProjects = React.useMemo(() => {
    return projects.filter((project) => {
      const matchesSearch =
        project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter =
        filterStatus === "all" || project.status === filterStatus;
      return matchesSearch && matchesFilter;
    });
  }, [projects, searchTerm, filterStatus]);

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Ativo";
      case "planning":
        return "Planejamento";
      case "inactive":
        return "Inativo";
      default:
        return "Desconhecido";
    }
  };

  return (
    <div className="p-4 pb-20 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Projetos</h1>
        <p className="text-gray-600">Gerencie seus projetos de irrigação</p>
      </div>

      {/* Search and Filter Controls */}
      <div className="mb-6 space-y-4">
        {projects.length > 4 && (
          <div className="relative">
            <svg
              className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
            <input
              type="text"
              placeholder="Buscar projetos..."
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        )}

        {false && (
          <div className="flex gap-2 overflow-x-auto">
            {["all", "active", "planning", "inactive"].map((status) => (
              <button
                key={status}
                onClick={() => setFilterStatus(status as any)}
                className={`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors ${
                  filterStatus === status
                    ? "bg-green-600 text-white"
                    : "bg-white text-gray-600 border border-gray-200 hover:bg-gray-50"
                }`}
              >
                {status === "all" ? "Todos" : getStatusText(status)}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Projects List */}
      {filteredProjects.length === 0 ? (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-gray-400 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
            />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-1">
            Nenhum projeto encontrado
          </h3>
          <p className="text-gray-500">
            {searchTerm || filterStatus !== "all"
              ? "Tente ajustar os filtros de busca"
              : "Crie seu primeiro projeto de irrigação"}
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredProjects.map((project) => (
            <ProjectCard compact={true} key={project.id} project={project} />
          ))}
        </div>
      )}

      {/* Floating Action Button */}
      <FAB
        className="bottom-20 right-4"
        onClick={() => {
          setLocation("/app/projects/new");
        }}
      />
    </div>
  );
}

export default ProjectsPage;
