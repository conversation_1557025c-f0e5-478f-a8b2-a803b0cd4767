import { PropertyOverview } from "@/components/PropertyOverview";
import { appShell, propertiesAtom, selectedPropertyAtom } from "@/store";
import { projectsAtom } from "@/store/data";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect, useMemo } from "react";
import ProjectStateCard from "@/components/ProjectStateCard";
import { RainfallCard } from "@/components";
import { rainfallDataByPropertyAtom } from "@/store";
import ReservoirStateCard from "@/components/ReservoirStateCard";
import { reservoirsAtom, fetchCurrentReservoirStatesAtom } from "@/store/data";

function DashboardPage() {
  const setBackButton = useSetAtom(appShell.backButtonAtom);
  useEffect(() => {
    setBackButton(false);
  });
  const selectedProperty = useAtomValue(selectedPropertyAtom);
  const properties = useAtomValue(propertiesAtom);
  const hasManyProperties = useMemo(() => properties.length > 1, [properties]);

  if (!selectedProperty) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Propriedade não encontrada
          </h2>
          <p className="text-gray-600">
            A propriedade selecionada não foi encontrada.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      {hasManyProperties && (
        <h1 className="text-2xl font-bold text-gray-900 mb-2 ">
          {selectedProperty.name}
        </h1>
      )}

      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-2">
        <PropertyOverview
          showTitle={false}
          showAddress={false}
          propertyId={selectedProperty.id}
        />
      </div>
      {/* Rainfall */}
      <RainfallSection />
      {/* Current Projects State */}
      <ProjectsCurrentStateSection />

      {/* Current Reservoirs State */}
      <ReservoirsCurrentStateSection />
    </div>
  );
}

export default DashboardPage;

function ProjectsCurrentStateSection() {
  const projects = useAtomValue(projectsAtom);

  if (!projects || projects.length === 0) return null;

  return (
    <div className="mt-4 space-y-3">
      <h2 className="text-lg font-semibold text-gray-900">
        Estado dos Projetos
      </h2>
      <div className="grid grid-cols-1 gap-3">
        {projects.map((project) => (
          <ProjectStateCard key={project.id} project={project} />
        ))}
      </div>
    </div>
  );
}

function ReservoirsCurrentStateSection() {
  const reservoirs = useAtomValue(reservoirsAtom);
  if (!reservoirs || reservoirs.length === 0) return null;
  return (
    <div className="mt-4 space-y-3">
      <h2 className="text-lg font-semibold text-gray-900">
        Estado dos Reservatórios
      </h2>
      <div className="grid grid-cols-1 gap-3">
        {reservoirs.map((r) => (
          <ReservoirStateCard key={r.id} reservoirId={r.id} />
        ))}
      </div>
    </div>
  );
}

function RainfallSection() {
  const selectedProperty = useAtomValue(selectedPropertyAtom);
  const rainfall = useAtomValue(rainfallDataByPropertyAtom);
  if (!selectedProperty) return null;
  // If disabled or no LIC provided rain data, hide the section
  if (!selectedProperty.rain_gauge_enabled || rainfall.gaugeCount === 0) {
    return null;
  }
  return (
    <div className="mt-4 space-y-3">
      <h2 className="text-lg font-semibold text-gray-900">Chuva</h2>
      <div className="grid grid-cols-1 gap-3">
        <RainfallCard />
      </div>
    </div>
  );
}
