// import LogoSymbol from "@/assets/logo-symbol-transparent-79x96.png"; // Adjust the import path as needed
import LogoSymbol from "@/assets/logo-symbol-byagro-32x32.png"; // Adjust the import path as needed
// import LogoWordmark from "@/assets/logo-wordmark-256x48.png"; // Adjust the import path as needed
import LogoWordmark from "@/assets/logo-wordmark-byagro-2.svg"; // Adjust the import path as needed
import { logoutAtom, selectedPropertyAtom, refetchDataAtom } from "@/store";
import {
  Menu,
  MenuButton,
  MenuDivider,
  MenuHeader,
  MenuItem,
} from "@szhsin/react-menu";
import "@szhsin/react-menu/dist/index.css";
import "@szhsin/react-menu/dist/transitions/zoom.css";
import { useDebounce } from "@uidotdev/usehooks";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import {
  ArrowLeft,
  Bell,
  CloudCog,
  Droplets,
  EllipsisVertical,
  Folders,
  Home,
  LogOut,
  RefreshCw,
  <PERSON>hapes,
  SquareGanttChart,
  User,
} from "lucide-react";
import { Link, useLocation } from "wouter";
import Button from "@/components/ui/Button";

export type MenuItemType =
  | "profile"
  | "projects"
  | "equipment"
  | "reports"
  | "property"
  | "reservoirs";

interface HeaderProps {
  showSettingsModal?: boolean;
  onMenuClicked: (item: MenuItemType) => void;
  backButton?: boolean | string | URL;
}

function Header({
  showSettingsModal = false,
  onMenuClicked,
  backButton,
}: HeaderProps) {
  const [location, setLocation] = useLocation();
  const logout = useSetAtom(logoutAtom);
  const refetchData = useSetAtom(refetchDataAtom);

  // Get current property and projects from store
  const currentProperty = useAtomValue(selectedPropertyAtom);

  // Determine if we should show the back button
  const shouldShowBackButton = useDebounce(backButton, 50);

  const handleBack = () => {
    if (backButton && typeof backButton !== "boolean") {
      return setLocation(backButton);
    }
    window.history.back();
  };

  const handleLogout = async () => {
    await logout();
    setLocation("/login");
  };

  const handleRefreshData = async () => {
    try {
      await refetchData();
    } catch (error) {
      console.error("Failed to refetch data:", error);
    }
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="px-1 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            {shouldShowBackButton ? (
              <Button
                variant="ghost"
                size="sm"
                icon={<ArrowLeft className="w-5 h-5" />}
                onClick={handleBack}
              />
            ) : (
              <div className="p-2 ">
                <div className="w-5 h-5" />
              </div>
            )}
            <Link to="/app/dashboard" className="flex items-center gap-3 ml-3">
              {/* <div className="w-12 h-12 p-1 flex items-center justify-center  ">
                <img
                  className="rounded-full h-10 w-10"
                  src={LogoSymbol}
                  alt="Irriga+ Logo"
                />
              </div> */}
              <div>
                <img
                  src={LogoWordmark}
                  alt="Irriga+ Wordmark"
                  className="h-10"
                />
                {/* <p className="text-xs text-gray-500">
                  {currentProperty?.name || "Selecione uma propriedade"}
                </p> */}
              </div>
            </Link>
          </div>

          <div className="flex items-center space-x-2">
            {/* Notifications */}
            <Button
              variant="neutral"
              ghost
              size="sm"
              className="text-center"
              icon={<Bell className="w-6 h-6" />}
            />

            {/* Menu */}
            {/* <button className=" text-gray-600 hover:text-gray-600 rounded-lg hover:bg-gray-100">
              <EllipsisVertical className="w-5 h-5" />
            </button> */}
            <Menu
              menuButton={
                <MenuButton>
                  <EllipsisVertical className="w-6 h-6" />
                </MenuButton>
              }
              transition
            >
              <MenuItem onClick={() => setLocation("/app/dashboard")}>
                <Home className="w-5 h-5" />
                <span className="ml-2">Início</span>
              </MenuItem>
              <MenuItem onClick={() => onMenuClicked("profile")}>
                <User className="w-5 h-5" />
                <span className="ml-2">Perfil</span>
              </MenuItem>
              <MenuItem onClick={() => onMenuClicked("reports")}>
                <SquareGanttChart className="w-5 h-5" />
                <span className="ml-2">Relatórios</span>
              </MenuItem>
              <MenuDivider />
              <MenuHeader>Configurações</MenuHeader>
              <MenuItem onClick={() => onMenuClicked("property")}>
                <CloudCog className="w-5 h-5" />
                <span className="ml-2">Propriedade</span>
              </MenuItem>
              <MenuItem onClick={() => onMenuClicked("projects")}>
                <Folders className="w-5 h-5" />
                <span className="ml-2">Projetos</span>
              </MenuItem>
              <MenuItem onClick={() => onMenuClicked("equipment")}>
                <Shapes className="w-5 h-5" />
                <span className="ml-2">Equipamentos</span>
              </MenuItem>
              <MenuItem onClick={() => onMenuClicked("reservoirs")}>
                <Droplets className="w-5 h-5" />
                <span className="ml-2">Reservatórios</span>
              </MenuItem>
              <MenuDivider />
              <MenuItem onClick={handleRefreshData}>
                <RefreshCw className="w-5 h-5" />
                <span className="ml-2">Atualizar dados</span>
              </MenuItem>
              <MenuItem onClick={handleLogout}>
                <LogOut className="w-5 h-5" />
                <span className="ml-2">Sair</span>
              </MenuItem>
            </Menu>
          </div>
        </div>
      </div>
    </header>
  );
}

export default Header;
