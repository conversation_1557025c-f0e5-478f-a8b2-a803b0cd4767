import type { AUTWaterPump, AUTPropertyDevice, AUTProperty } from '@/api/queries/account';
import PumpListItem from './PumpListItem';
import EmptyPumpState from './EmptyPumpState';

interface PumpListProps {
  pumps: AUTWaterPump[];
  propertyDevices: AUTPropertyDevice[];
  selectedProperty: AUTProperty | null;
  onPumpClick: (pump: AUTWaterPump) => void;
  hasSearchQuery: boolean;
}

export default function PumpList({ pumps, propertyDevices, selectedProperty, onPumpClick, hasSearchQuery }: PumpListProps) {
  if (pumps.length === 0) {
    return <EmptyPumpState hasSearchQuery={hasSearchQuery} />;
  }

  return (
    <div className="space-y-3">
      {pumps.map((pump) => (
        <PumpListItem
          key={pump.id}
          pump={pump}
          propertyDevices={propertyDevices}
          selectedProperty={selectedProperty}
          onClick={onPumpClick}
        />
      ))}
    </div>
  );
}