import type { DeviceModel } from "@/api/model/device";
import { MODEL_LABELS } from "@/utils/device-model";
import type { AUTDevice, AUTProperty } from "@/api/queries/account";
import { Modal, useToast } from "@/components";
import ConfirmModal from "@/components/ConfirmModal";
import Button from "@/components/ui/Button";
import { ModalFooter } from "@/components/ui/ModalFooter";
import {
  createPropertyDeviceAtom,
  refetchDataAtom,
  selectedPropertyIdAtom,
  updatePropertyDeviceAtom,
} from "@/store";
import { updateMeshDeviceMappingBatchAtom } from "@/store/crud";
import { useAtomValue, useSetAtom } from "jotai";
import { Eye, EyeOff, Link, AlertTriangle } from "lucide-react";
import { useEffect, useState } from "react";
import type { DeviceWithMapping } from "@/utils/mesh-device-utils";
import {
  getDeviceIcon,
  formatMappingDate,
  isMeshDevice,
} from "@/utils/mesh-device-utils";
import { getDeviceModelLabel } from "@/utils/device-model";
import { validateNewPropertyDevice } from "@/utils/property-device-utils";
import {
  analyzeDeviceRemovalImpact,
  performDeviceRemoval,
  type DeviceRemovalImpact,
} from "@/utils/device-removal";
import { apiService } from "@/api";

interface DeviceDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  device: AUTDevice | null;
  enhancedDevice?: DeviceWithMapping | null; // Optional enhanced device info for mesh mapping display
  mode: "create" | "edit";
  onOpenAssignToLIC?: (device: DeviceWithMapping) => void; // Callback to open assign to LIC modal
  propertyData?: AUTProperty | null; // Property data needed for device removal impact analysis
}

function DeviceDetailModal({
  isOpen,
  onClose,
  device,
  enhancedDevice,
  mode,
  onOpenAssignToLIC,
  propertyData,
}: DeviceDetailModalProps) {
  const updatePropertyDevice = useSetAtom(updatePropertyDeviceAtom);
  const createPropertyDevice = useSetAtom(createPropertyDeviceAtom);
  const selectedPropertyId = useAtomValue(selectedPropertyIdAtom);
  const refetchData = useSetAtom(refetchDataAtom);
  const { showWarning, showError, showSuccess } = useToast();
  const updateMeshDeviceMappingBatch = useSetAtom(
    updateMeshDeviceMappingBatchAtom
  );

  const [formData, setFormData] = useState({
    identifier: "",
    model: "LIC" as DeviceModel,
    label: "",
    wifiSSID: "",
    wifiPassword: "",
  });
  const [showWifiPassword, setShowWifiPassword] = useState(false);
  const [identifierError, setIdentifierError] = useState<string | null>(null);
  const [isModelLocked, setIsModelLocked] = useState(false);
  const [showExcludeConfirm, setShowExcludeConfirm] = useState(false);
  const [excluding, setExcluding] = useState(false);
  const [removalImpact, setRemovalImpact] =
    useState<DeviceRemovalImpact | null>(null);

  // Validation functions
  const validateIdentifier = (
    identifier: string,
    model: DeviceModel
  ): string | null => {
    if (!identifier.trim()) {
      return "Identificador é obrigatório";
    }

    const hexPattern = /^[0-9A-F]+$/;
    if (!hexPattern.test(identifier)) {
      return "Identificador deve conter apenas caracteres hexadecimais (0-9, A-F)";
    }

    const expectedLength = model === "LIC" ? 12 : 6;
    if (identifier.length !== expectedLength) {
      return `Identificador deve ter ${expectedLength} caracteres para dispositivos ${model}`;
    }

    return null;
  };

  const handleIdentifierChange = (value: string) => {
    // Automatically capitalize and filter only hex characters
    const processedValue = value.toUpperCase().replace(/[^0-9A-F]/g, "");

    setFormData({ ...formData, identifier: processedValue });
    // When the user edits the identifier, unlock model until next blur validation
    if (isModelLocked) setIsModelLocked(false);

    // Validate the identifier
    const error = validateIdentifier(processedValue, formData.model);
    setIdentifierError(error);
  };

  const handleModelChange = (model: DeviceModel) => {
    setFormData({ ...formData, model });

    // Re-validate identifier with new model
    if (formData.identifier) {
      const error = validateIdentifier(formData.identifier, model);
      setIdentifierError(error);
    }
  };

  // On blur, validate against backend rules; if an existing device is found, lock model to that device's model
  const handleIdentifierBlur = async () => {
    // Only validate on create flow with a selected property. For other cases, fall back to local validation.
    if (mode !== "create" || !selectedPropertyId) {
      const fallbackError = validateIdentifier(
        formData.identifier,
        formData.model
      );
      setIdentifierError(fallbackError);
      return;
    }

    try {
      const validation = await validateNewPropertyDevice(
        apiService,
        selectedPropertyId,
        formData.identifier
      );

      if (!validation.valid) {
        // Device already in use elsewhere
        setIsModelLocked(false);
        setIdentifierError("Dispositivo já em uso.");
        return;
      }

      if (validation.deviceOverview) {
        // Existing device found: adopt its model and lock the select
        setFormData((prev) => ({
          ...prev,
          model: validation.deviceOverview!.model as DeviceModel,
        }));
        setIsModelLocked(true);
        setIdentifierError(null);
      } else {
        // Device not found: keep model editable and run local validation now
        setIsModelLocked(false);
        const localError = validateIdentifier(
          formData.identifier,
          formData.model
        );
        setIdentifierError(localError);
      }
    } catch (error) {
      // Backend validation failed: unlock model and fallback to local validation
      console.error("Identifier blur validation failed:", error);
      setIsModelLocked(false);
      const localError = validateIdentifier(
        formData.identifier,
        formData.model
      );
      setIdentifierError(localError ?? "Erro ao validar dispositivo.");
    }
  };

  useEffect(() => {
    if (device && mode === "edit") {
      // For LIC devices, get WIFI configuration from PropertyDevice metadata instead of Device metadata
      const metadata =
        device.model === "LIC" && enhancedDevice?.metadata
          ? (enhancedDevice.metadata as any)
          : (device.metadata as any);

      setFormData({
        identifier: device.identifier,
        model: device.model,
        label: metadata?.label || "",
        wifiSSID: metadata?.wifiSSID || "",
        wifiPassword: metadata?.wifiPassword || "",
      });
      setIdentifierError(null);
    } else if (mode === "create") {
      setFormData({
        identifier: "",
        model: "LIC" as DeviceModel,
        label: "",
        wifiSSID: "",
        wifiPassword: "",
      });
      setIdentifierError(null);
    }
  }, [device, mode, enhancedDevice]);

  const handleSave = async () => {
    if (!selectedPropertyId) {
      console.error("No property selected for the device.");
      showWarning({
        message: "Nenhuma propriedade selecionada para o dispositivo.",
      });
      return;
    }

    // Validate identifier before saving
    const identifierValidationError = validateIdentifier(
      formData.identifier,
      formData.model
    );
    if (identifierValidationError) {
      setIdentifierError(identifierValidationError);
      showWarning({
        message: "Por favor, corrija os erros no formulário antes de salvar.",
      });
      return;
    }

    // Prepare metadata for LIC devices
    const metadata =
      formData.model === "LIC"
        ? {
            wifiSSID: formData.wifiSSID || null,
            wifiPassword: formData.wifiPassword || null,
            label: formData.label || null,
          }
        : {
            label: formData.label || null,
          };

    try {
      if (mode === "create") {
        const validation = await validateNewPropertyDevice(
          apiService,
          selectedPropertyId,
          formData.identifier
        );
        if (!validation.valid) {
          showError({
            message:
              validation.message === "IN_USE"
                ? "Dispositivo já em uso."
                : "Erro ao validar dispositivo.",
            duration: 5000,
            position: "center",
            attention: true,
          });
          return;
        }
        // TODO: Must check if the device exists or needs to be created
        await createPropertyDevice({
          property: selectedPropertyId,
          device:
            validation.deviceOverview?.id ??
            ({
              identifier: formData.identifier,
              model: formData.model,
            } as any), // Type assertion to bypass strict typing for nested creation
          metadata,
          start_date: new Date().toISOString(),
        });
      } else if (mode === "edit" && device && enhancedDevice) {
        await updatePropertyDevice({
          id: enhancedDevice.id, // PropertyDevice ID
          data: {
            device: {
              id: device.id, // Directus ID of the device
              identifier: formData.identifier,
              model: formData.model,
            } as any,
            metadata,
          },
        });
      }
      onClose();
    } catch (error) {
      console.error("Error saving device:", error);
      showWarning({
        message: "Erro ao salvar dispositivo. Tente novamente.",
      });
    }
  };

  const handleCancel = () => {
    onClose();
  };

  // Exclude (end relation) handlers
  const handleExcludeClick = () => {
    if (!device || !propertyData) {
      showWarning({
        message: "Dados insuficientes para analisar o impacto da remoção.",
      });
      return;
    }

    // Analyze the removal impact before showing confirmation
    const impact = analyzeDeviceRemovalImpact(device, propertyData);
    setRemovalImpact(impact);
    setShowExcludeConfirm(true);
  };

  const confirmExclude = async () => {
    if (!device || !selectedPropertyId || !propertyData || mode !== "edit")
      return;

    try {
      setExcluding(true);

      const result = await performDeviceRemoval({
        device,
        propertyId: selectedPropertyId,
        propertyData,
        apiService,
      });

      if (result.success) {
        showSuccess({
          message: "Dispositivo excluído com sucesso.",
          duration: 3000,
        });
        onClose();
      } else {
        showError({
          message: `Erro ao excluir dispositivo: ${result.error}`,
          duration: 5000,
        });
      }
    } catch (error) {
      console.error("Error excluding device:", error);
      showError({
        message: "Erro inesperado ao excluir dispositivo.",
        duration: 5000,
      });
    } finally {
      setExcluding(false);
      setRemovalImpact(null);
      await refetchData();
    }
  };

  const getModelOptions = () =>
    Object.entries(MODEL_LABELS).map(([value, label]) => ({ value, label }));

  // Determine if model select should be disabled (LIC with mapped mesh devices)
  const isLICWithMappedDevices = (() => {
    if (
      mode === "edit" &&
      device &&
      device.model === "LIC" &&
      enhancedDevice &&
      Array.isArray(enhancedDevice.meshDevices) &&
      enhancedDevice.meshDevices.length > 0
    ) {
      return true;
    }
    return false;
  })();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === "create" ? "Novo Dispositivo" : "Editar Dispositivo"}
      size="md"
    >
      <div className="space-y-6">
        {/* Model */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Modelo
          </label>
          <select
            value={formData.model}
            onChange={(e) => handleModelChange(e.target.value as DeviceModel)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none disabled:bg-gray-100 disabled:text-gray-500"
            disabled={isLICWithMappedDevices || isModelLocked}
            title={
              isLICWithMappedDevices
                ? "Não é possível alterar o modelo de um LIC que possui dispositivos mesh mapeados."
                : isModelLocked
                ? "Modelo bloqueado pois o dispositivo já existe e seu modelo foi detectado automaticamente."
                : undefined
            }
          >
            {getModelOptions().map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          {isLICWithMappedDevices && (
            <p className="mt-1 text-xs text-yellow-700">
              Não é possível alterar o modelo de um LIC que possui dispositivos
              mesh mapeados.
            </p>
          )}
        </div>

        {/* Serial Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            S/N
          </label>
          <input
            type="text"
            value={formData.identifier}
            onChange={(e) => handleIdentifierChange(e.target.value)}
            onBlur={handleIdentifierBlur}
            className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none ${
              identifierError ? "border-red-300" : "border-gray-300"
            }`}
            placeholder={formData.model === "LIC" ? "XXXXXXXXXXXX" : "XXXXXX"}
            maxLength={formData.model === "LIC" ? 12 : 6}
          />
          {identifierError && (
            <p className="mt-1 text-sm text-red-600">{identifierError}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            {formData.model === "LIC"
              ? "12 caracteres hexadecimais (0-9, A-F)"
              : "6 caracteres hexadecimais (0-9, A-F)"}
          </p>
        </div>

        {/* Label */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Rótulo (Opcional)
          </label>
          <input
            type="text"
            value={formData.label}
            onChange={(e) =>
              setFormData({ ...formData, label: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            placeholder="Nome personalizado para o dispositivo"
          />
          <p className="mt-1 text-xs text-gray-500">
            Rótulo personalizado para identificar facilmente este dispositivo
          </p>
        </div>

        {/* Wi-Fi Configuration - Only for LIC devices */}
        {formData.model === "LIC" && (
          <>
            {/* Wi-Fi SSID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                SSID do Wi-Fi
              </label>
              <input
                type="text"
                value={formData.wifiSSID}
                onChange={(e) =>
                  setFormData({ ...formData, wifiSSID: e.target.value })
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
                placeholder="Nome da rede Wi-Fi"
              />
            </div>

            {/* Wi-Fi Password with visibility toggle */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Senha do Wi-Fi
              </label>
              <div className="relative">
                <input
                  type={showWifiPassword ? "text" : "password"}
                  value={formData.wifiPassword}
                  onChange={(e) =>
                    setFormData({ ...formData, wifiPassword: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none pr-12"
                  placeholder="Senha da rede Wi-Fi"
                />
                <Button
                  type="button"
                  aria-label={
                    showWifiPassword ? "Ocultar senha" : "Mostrar senha"
                  }
                  onClick={() => setShowWifiPassword((v) => !v)}
                  variant="ghost"
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                  tabIndex={0}
                >
                  {showWifiPassword ? (
                    <EyeOff className="h-5 w-5" />
                  ) : (
                    <Eye className="h-5 w-5" />
                  )}
                </Button>
              </div>
            </div>
          </>
        )}

        {/* Mesh Device Mapping Information - Only for mesh devices in edit mode */}
        {mode === "edit" &&
          enhancedDevice &&
          isMeshDevice(enhancedDevice.device.model) && (
            <div className="border-t border-gray-200 pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
                <span>{getDeviceIcon(enhancedDevice.device.model)}</span>
                Mapeamento de Rede Mesh
              </h3>

              {enhancedDevice.mappingStatus === "mapped" &&
              enhancedDevice.licDevice ? (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <Link className="text-green-600" size={16} />
                      <span className="font-medium text-green-800">
                        Mapeado para LIC
                      </span>
                    </div>
                    {onOpenAssignToLIC && (
                      <Button
                        onClick={() => onOpenAssignToLIC(enhancedDevice)}
                        variant="primary"
                        size="sm"
                        className="px-3 py-1.5"
                      >
                        Alterar Mapeamento
                      </Button>
                    )}
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">📡</span>
                      <span className="font-medium">
                        {enhancedDevice.licDevice.device.identifier}
                      </span>
                      <span className="text-gray-600">
                        (
                        {getDeviceModelLabel(
                          enhancedDevice.licDevice.device.model
                        )}
                        )
                      </span>
                    </div>
                    {enhancedDevice.current_mesh_device_mapping && (
                      <div className="text-gray-600">
                        Mapeado desde:{" "}
                        {formatMappingDate(
                          enhancedDevice.current_mesh_device_mapping.start_date
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="text-yellow-600" size={16} />
                      <span className="font-medium text-yellow-800">
                        Não Mapeado
                      </span>
                    </div>
                    {onOpenAssignToLIC && (
                      <Button
                        onClick={() => onOpenAssignToLIC(enhancedDevice)}
                        variant="primary"
                        size="sm"
                        className="px-3 py-1.5"
                      >
                        Mapear
                      </Button>
                    )}
                  </div>
                  <p className="text-sm text-yellow-700">
                    Este dispositivo não está associado a nenhum LIC. Clique em
                    "Mapear" para associá-lo a um coordenador.
                  </p>
                </div>
              )}
            </div>
          )}

        {/* Action Buttons */}
        <ModalFooter>
          <Button
            variant="primary"
            className="flex-1"
            onClick={handleSave}
            disabled={excluding}
          >
            Salvar
          </Button>

          {mode === "edit" && (
            <Button
              variant="destructive"
              className="flex-1"
              onClick={handleExcludeClick}
              disabled={excluding}
              loading={excluding}
            >
              Excluir
            </Button>
          )}

          <Button
            variant="secondary"
            className="flex-1"
            onClick={handleCancel}
            disabled={excluding}
          >
            Cancelar
          </Button>
        </ModalFooter>

        {/* Exclude Confirmation Modal with Impact Analysis */}
        <ConfirmModal
          isOpen={showExcludeConfirm}
          onClose={() => {
            setShowExcludeConfirm(false);
            setRemovalImpact(null);
          }}
          onConfirm={confirmExclude}
          title="Excluir dispositivo"
          message={
            removalImpact
              ? undefined
              : "Tem certeza que deseja excluir este dispositivo?"
          }
          confirmText="Excluir"
          cancelText="Cancelar"
          variant="danger"
        >
          {removalImpact && (
            <div className="space-y-4">
              <p>Tem certeza que deseja excluir este dispositivo?</p>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-medium text-yellow-800 mb-2">
                  Impacto da Remoção:
                </h4>
                <p className="text-sm text-yellow-700 mb-3">
                  {removalImpact.summary}
                </p>

                {/* Show affected entities if any */}
                {(removalImpact.affectedEntities.projects.length > 0 ||
                  removalImpact.affectedEntities.sectors.length > 0 ||
                  removalImpact.affectedEntities.reservoirs.length > 0 ||
                  removalImpact.affectedEntities.waterPumps.length > 0 ||
                  removalImpact.affectedEntities.meshDevices.length > 0) && (
                  <div className="space-y-2">
                    {removalImpact.affectedEntities.projects.length > 0 && (
                      <div>
                        <strong className="text-xs text-yellow-800">
                          Projetos afetados:
                        </strong>
                        <ul className="text-xs text-yellow-700 ml-2">
                          {removalImpact.affectedEntities.projects.map(
                            (project) => (
                              <li key={project.id}>• {project.name}</li>
                            )
                          )}
                        </ul>
                      </div>
                    )}

                    {removalImpact.affectedEntities.sectors.length > 0 && (
                      <div>
                        <strong className="text-xs text-yellow-800">
                          Setores afetados:
                        </strong>
                        <ul className="text-xs text-yellow-700 ml-2">
                          {removalImpact.affectedEntities.sectors.map(
                            (sector) => (
                              <li key={sector.id}>
                                • {sector.name} ({sector.projectName})
                              </li>
                            )
                          )}
                        </ul>
                      </div>
                    )}

                    {removalImpact.affectedEntities.reservoirs.length > 0 && (
                      <div>
                        <strong className="text-xs text-yellow-800">
                          Reservatórios afetados:
                        </strong>
                        <ul className="text-xs text-yellow-700 ml-2">
                          {removalImpact.affectedEntities.reservoirs.map(
                            (reservoir) => (
                              <li key={reservoir.id}>• {reservoir.name}</li>
                            )
                          )}
                        </ul>
                      </div>
                    )}

                    {removalImpact.affectedEntities.waterPumps.length > 0 && (
                      <div>
                        <strong className="text-xs text-yellow-800">
                          Bombas d'água afetadas:
                        </strong>
                        <ul className="text-xs text-yellow-700 ml-2">
                          {removalImpact.affectedEntities.waterPumps.map(
                            (pump) => (
                              <li key={pump.id}>
                                • {pump.label} ({pump.identifier})
                              </li>
                            )
                          )}
                        </ul>
                      </div>
                    )}

                    {removalImpact.affectedEntities.meshDevices.length > 0 && (
                      <div>
                        <strong className="text-xs text-yellow-800">
                          Dispositivos mesh que serão desmapeados:
                        </strong>
                        <ul className="text-xs text-yellow-700 ml-2">
                          {removalImpact.affectedEntities.meshDevices.map(
                            (meshDevice) => (
                              <li key={meshDevice.id}>
                                • {meshDevice.label || meshDevice.identifier} (
                                {meshDevice.model})
                              </li>
                            )
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </ConfirmModal>
      </div>
    </Modal>
  );
}

export default DeviceDetailModal;
