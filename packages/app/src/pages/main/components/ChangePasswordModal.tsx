import React, { useState } from "react";
import { Button } from "@/components/ui/Button";
import { validatePassword } from "@/utils/validation";
import { apiService } from "@/api/service";
import { useSet<PERSON>tom } from "jotai";
import { logout<PERSON>tom } from "@/store";
import ConfirmModal from "@/components/ConfirmModal";
import { isDirectusError } from "@directus/sdk";
import { useToast } from "@/components";

interface ChangePasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
}

function ChangePasswordModal({ isOpen, onClose }: ChangePasswordModalProps) {
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const logout = useSetAtom(logoutAtom);
  const { showSuccess } = useToast();

  const handleConfirm = async () => {
    setError(null);

    // Validate new password
    const validation = validatePassword(newPassword, confirmPassword);
    if (!validation.valid) {
      setError(validation.error);
      return false;
    }

    setIsLoading(true);
    let token: string | null = null;
    try {
      // First, verify current password by attempting to login
      const userResponse = await apiService.auth.fetchUser();
      const email = userResponse.email;

      if (!email) {
        throw new Error("Email do usuário não encontrado");
      }

      token = await apiService.auth.getToken();
      // Verify current password
      await apiService.auth.login(email, currentPassword);

      // Change password
      await apiService.auth.changePassword(newPassword);

      // Show success message (could be implemented with a toast notification)
      showSuccess({
        message: "Senha alterada com sucesso! Por favor, faça login novamente.",
        attention: true,
        position: "center",
        duration: 8000,
      });

      // Success - close modal and logout
      onClose();
      await logout();
    } catch (err) {
      if (isDirectusError(err)) {
        if (
          err.errors.some(
            (e) =>
              e.message.includes("Invalid user credentials") ||
              e.extensions?.code === "INVALID_CREDENTIALS"
          )
        ) {
          setError("Senha atual incorreta.");
        } else {
          setError(
            `Erro ao alterar senha: ${
              err.errors.map((e) => e.message).join(", ") || err.message
            }`
          );
        }
        if (err.response.status === 401 && token) {
          // If invalid password, restore the token
          await apiService.auth.setToken(token);
        }
      } else if (err instanceof Error) {
        if (err.message.includes("Invalid user credentials")) {
          setError("Senha atual incorreta.");
        } else {
          setError(err.message || "Erro ao alterar senha. Tente novamente.");
        }
      } else {
        setError("Erro ao alterar senha. Tente novamente.");
      }
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    // Reset form state
    setCurrentPassword("");
    setNewPassword("");
    setConfirmPassword("");
    setError(null);
    setIsLoading(false);
    onClose();
  };

  return (
    <ConfirmModal
      isOpen={isOpen}
      onClose={handleClose}
      onConfirm={handleConfirm}
      title="Alterar Senha"
      confirmText="Alterar Senha"
      cancelText="Cancelar"
      variant="primary"
    >
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg mb-4">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      <div className="space-y-3">
        <div>
          <label
            htmlFor="currentPassword"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Senha Atual
          </label>
          <input
            id="currentPassword"
            type="password"
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 outline-none transition-all"
            required
            disabled={isLoading}
          />
        </div>

        <div>
          <label
            htmlFor="newPassword"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Nova Senha
          </label>
          <input
            id="newPassword"
            type="password"
            value={newPassword}
            onChange={(e) => setNewPassword(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 outline-none transition-all"
            required
            disabled={isLoading}
            minLength={6}
          />
          <p className="text-xs text-gray-500 mt-1">Mínimo 6 caracteres</p>
        </div>

        <div>
          <label
            htmlFor="confirmPassword"
            className="block text-sm font-medium text-gray-700 mb-1"
          >
            Confirmar Nova Senha
          </label>
          <input
            id="confirmPassword"
            type="password"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 outline-none transition-all"
            required
            disabled={isLoading}
            minLength={6}
          />
        </div>
      </div>
    </ConfirmModal>
  );
}

export default ChangePasswordModal;
