import { useLocation } from "wouter";

export interface ProjectDisplayData {
  id: string;
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  sectorsCount: number;
  hasIrrigation: boolean;
  hasFertigation: boolean;
  status: "active" | "planning" | "inactive";
  area: number;
  irrigationPlansCount: number;
  lastActivity: string | null;
  // Irrigation system configuration (optional display fields)
  pipeWashTimeMinutes: number | null;
  backwashDurationMinutes: number | null;
  backwashPeriodMinutes: number | null;
}

interface ProjectCardProps {
  project: ProjectDisplayData;
}

function ProjectCardFull({ project }: ProjectCardProps) {
  const [, setLocation] = useLocation();

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-700";
      case "planning":
        return "bg-blue-100 text-blue-700";
      case "inactive":
        return "bg-gray-100 text-gray-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Ativo";
      case "planning":
        return "Planejamento";
      case "inactive":
        return "Inativo";
      default:
        return "Desconhecido";
    }
  };

  return (
    <div
      className="bg-white rounded-xl border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
      onClick={() => {
        setLocation(`/app/projects/${project.id}`);
      }}
    >
      {/* Project Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {project.name}
          </h3>
          <p className="text-gray-600 text-sm">{project.description}</p>
        </div>
        <div className="flex items-center gap-2 ml-4">
          <span
            className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
              project.status
            )}`}
          >
            {getStatusText(project.status)}
          </span>
          <svg
            className="h-5 w-5 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </div>
      </div>

      {/* Project Stats */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-2xl font-bold text-gray-900">
            {project.sectorsCount}
          </div>
          <div className="text-sm text-gray-600">Setores</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-2xl font-bold text-gray-900">
            {project.irrigationPlansCount}
          </div>
          <div className="text-sm text-gray-600">Planos</div>
        </div>
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-2xl font-bold text-gray-900">
            {project.area} <span className="text-xs text-gray-600">ha</span>
          </div>
          <div className="text-sm text-gray-600">Área</div>
        </div>
      </div>

      {/* Features and Info */}
      <div className="space-y-3">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Período:</span>
          <span className="font-medium text-gray-900">
            {new Date(project.startDate).toLocaleDateString()} -{" "}
            {new Date(project.endDate).toLocaleDateString()}
          </span>
        </div>

        {project.lastActivity && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Última atividade:</span>
            <span className="font-medium text-gray-900">
              {new Date(project.lastActivity).toLocaleDateString()}
            </span>
          </div>
        )}

        {/* Irrigation System Configuration */}
        {(project.pipeWashTimeMinutes != null ||
          project.backwashDurationMinutes != null ||
          project.backwashPeriodMinutes != null) && (
          <div className="border-t pt-3 mt-3">
            <h4 className="text-sm font-medium text-gray-700 mb-2">
              Configuração do Sistema
            </h4>
            <div className="grid grid-cols-3 gap-2 text-xs">
              {project.pipeWashTimeMinutes !== undefined &&
                project.pipeWashTimeMinutes !== null && (
                  <div className="bg-blue-50 rounded p-2">
                    <div className="font-medium text-blue-900">
                      {project.pipeWashTimeMinutes}min
                    </div>
                    <div className="text-blue-600">Lavagem</div>
                  </div>
                )}
              {project.backwashDurationMinutes !== undefined &&
                project.backwashDurationMinutes !== null && (
                  <div className="bg-green-50 rounded p-2">
                    <div className="font-medium text-green-900">
                      {project.backwashDurationMinutes}min
                    </div>
                    <div className="text-green-600">Retrolavagem</div>
                  </div>
                )}
              {project.backwashPeriodMinutes !== undefined &&
                project.backwashPeriodMinutes !== null && (
                  <div className="bg-purple-50 rounded p-2">
                    <div className="font-medium text-purple-900">
                      {project.backwashPeriodMinutes}min
                    </div>
                    <div className="text-purple-600">Período</div>
                  </div>
                )}
            </div>
          </div>
        )}

        <div className="flex items-center gap-4 pt-2">
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                project.hasIrrigation ? "bg-blue-500" : "bg-gray-300"
              }`}
            ></div>
            <span
              className={`text-xs ${
                project.hasIrrigation ? "text-blue-700" : "text-gray-500"
              }`}
            >
              Irrigação
            </span>
          </div>
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                project.hasFertigation ? "bg-green-500" : "bg-gray-300"
              }`}
            ></div>
            <span
              className={`text-xs ${
                project.hasFertigation ? "text-green-700" : "text-gray-500"
              }`}
            >
              Fertirrigação
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

function ProjectCardCompact({ project }: ProjectCardProps) {
  const [, setLocation] = useLocation();

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-700";
      case "planning":
        return "bg-blue-100 text-blue-700";
      case "inactive":
        return "bg-gray-100 text-gray-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Ativo";
      case "planning":
        return "Planejamento";
      case "inactive":
        return "Inativo";
      default:
        return "Desconhecido";
    }
  };

  return (
    <div
      className="bg-white rounded-lg border border-gray-200 p-3 hover:shadow-md transition-shadow cursor-pointer"
      onClick={() => {
        setLocation(`/app/projects/${project.id}`);
      }}
    >
      {/* Project Header */}
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-base font-semibold text-gray-900 truncate flex-1 pr-2">
          {project.name}
        </h3>
        <div className="flex items-center gap-1 flex-shrink-0">
          <span
            className={`px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
              project.status
            )}`}
          >
            {getStatusText(project.status)}
          </span>
          <svg
            className="h-4 w-4 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </div>
      </div>

      {/* Compact Stats and Features */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3 text-sm">
          <div className="flex items-center gap-1">
            <span className="font-medium text-gray-900">
              {project.sectorsCount}
            </span>
            <span className="text-gray-600">setores</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="font-medium text-gray-900">
              {project.irrigationPlansCount}
            </span>
            <span className="text-gray-600">planos</span>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <div
              className={`w-1.5 h-1.5 rounded-full ${
                project.hasIrrigation ? "bg-blue-500" : "bg-gray-300"
              }`}
            ></div>
            <span
              className={`text-xs ${
                project.hasIrrigation ? "text-blue-700" : "text-gray-500"
              }`}
            >
              I
            </span>
          </div>
          <div className="flex items-center gap-1">
            <div
              className={`w-1.5 h-1.5 rounded-full ${
                project.hasFertigation ? "bg-green-500" : "bg-gray-300"
              }`}
            ></div>
            <span
              className={`text-xs ${
                project.hasFertigation ? "text-green-700" : "text-gray-500"
              }`}
            >
              F
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

function ProjectCard({
  project,
  compact,
}: ProjectCardProps & { compact?: boolean }) {
  return compact ? (
    <ProjectCardCompact project={project} />
  ) : (
    <ProjectCardFull project={project} />
  );
}

export default ProjectCard;
