import WaterPumpIcon from "@/components/icons/WaterPumpIcon";
import {
  BarChart3,
  CalendarClock,
  FolderOpen,
  Home,
  Monitor,
} from "lucide-react";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/Button";

function BottomTabNavigation() {
  const [location, setLocation] = useLocation();

  const navigation = [
    {
      name: "Home",
      path: "/app/dashboard",
      icon: <Home className="w-8 h-8" />,
      visible: true,
    },
    {
      name: "Projetos",
      path: "/app/projects",
      icon: <FolderOpen className="w-8 h-8" />,
      visible: false,
    },
    {
      name: "Programação",
      path: "/app/irrigation-plans",
      icon: <CalendarClock className="w-8 h-8" />,
      visible: true,
    },
    {
      name: "Hardware",
      path: "/app/devices",
      icon: <Monitor className="w-8 h-8" />,
      visible: false,
    },
    {
      name: "<PERSON>lat<PERSON><PERSON><PERSON>",
      path: "/app/reports",
      icon: <BarChart3 className="w-8 h-8" />,
      visible: false,
    },
    {
      name: "Bomb<PERSON>",
      path: "/app/pumps",
      icon: <WaterPumpIcon className="w-8 h-8" />,
      visible: true,
    },
  ];

  return (
    <nav className="bg-white border-t border-neutral-100 z-50 safe-area-pb relative">
      <div className="flex justify-around items-center min-h-[60px] px-6 py-2">
        {navigation
          .filter((item) => item.visible)
          .map((item) => {
            const isActive = location === item.path;
            const isProgramacao = item.name === "Programação";

            if (isProgramacao) {
              return (
                <Button
                  key={item.name}
                  onClick={() => setLocation(item.path)}
                  variant={isActive ? "primary" : "neutral"}
                  ghost
                  className="focus:ring-0 flex flex-col items-center justify-center flex-1 py-2 px-1 transition-all duration-150 ease-in-out cursor-pointer relative"
                >
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div
                      className={`w-16 h-16 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 ease-in-out ${
                        isActive
                          ? "text-primary-200 bg-primary-600 scale-110"
                          : "bg-white text-neutral-500 hover:text-neutral-700 border-2"
                      }`}
                    >
                      {item.icon}
                    </div>
                  </div>
                  {/* <span
                  className={`text-xs font-medium mt-6 ${
                    isActive ? "text-primary-500" : "text-neutral-500"
                  }`}
                >
                  {item.name}
                </span> */}
                </Button>
              );
            }

            return (
              <Button
                key={item.name}
                onClick={() => setLocation(item.path)}
                variant={isActive ? "primary" : "neutral"}
                ghost
                className="focus:ring-0"
              >
                <div
                  className={`mb-1 ${
                    isActive ? "scale-110" : ""
                  } transition-transform duration-150 ease-in-out`}
                >
                  {item.icon}
                </div>
                {/* <span
                  className={`text-xs font-medium ${
                    isActive ? "text-primary-500" : "text-neutral-500"
                  }`}
                >
                  {item.name}
                </span> */}
              </Button>
            );
          })}
      </div>
    </nav>
  );
}

export default BottomTabNavigation;
