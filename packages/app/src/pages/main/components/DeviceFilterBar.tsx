import { ChevronDown } from "lucide-react";
import { useState } from "react";
import Button from "@/components/ui/Button";

export type DeviceFilter =
  | "all"
  | "coordinators"
  | "mapped"
  | "unmapped"
  | "lic"
  | "wpc"
  | "vc"
  | "rm";

interface DeviceFilterBarProps {
  selectedFilter: DeviceFilter;
  onFilterChange: (filter: DeviceFilter) => void;
  deviceCounts: {
    all: number;
    coordinators: number;
    mapped: number;
    unmapped: number;
    lic: number;
    wpc: number;
    vc: number;
    rm: number;
  };
}

const FILTER_OPTIONS = [
  { key: "all" as const, label: "Todos os Dispositivos", icon: "🔧" },
  { key: "coordinators" as const, label: "Coordenadores (LIC)", icon: "📡" },
  { key: "mapped" as const, label: "Mapeados", icon: "🔗" },
  { key: "unmapped" as const, label: "Não Mapeados", icon: "⚠️" },
  { key: "lic" as const, label: "LIC", icon: "📡" },
  { key: "wpc" as const, label: "WPC", icon: "💧" },
  { key: "vc" as const, label: "VC", icon: "🌱" },
  { key: "rm" as const, label: "RM", icon: "🏗️" },
];

function DeviceFilterBar({
  selectedFilter,
  onFilterChange,
  deviceCounts,
}: DeviceFilterBarProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const selectedOption =
    FILTER_OPTIONS.find((option) => option.key === selectedFilter) ||
    FILTER_OPTIONS[0];

  const handleFilterSelect = (filter: DeviceFilter) => {
    onFilterChange(filter);
    setIsDropdownOpen(false);
  };

  return (
    <div className="mb-4">
      {/* Main Filter Dropdown */}
      <div className="relative">
        <Button
          variant="ghost"
          className="w-full flex items-center justify-between px-4 py-3 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          icon={<ChevronDown className="h-4 w-4" />}
        >
          <div className="flex items-center gap-2">
            <span className="text-lg">{selectedOption.icon}</span>
            <span className="font-medium text-gray-900">
              {selectedOption.label}
            </span>
            <span className="text-sm text-gray-500">
              ({deviceCounts[selectedFilter]})
            </span>
          </div>
        </Button>

        {/* Dropdown Menu */}
        {isDropdownOpen && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
            {FILTER_OPTIONS.map((option) => (
              <button
                key={option.key}
                className={`w-full flex items-center justify-between px-4 py-3 text-left hover:bg-gray-50 transition-colors first:rounded-t-lg last:rounded-b-lg ${
                  selectedFilter === option.key
                    ? "bg-green-50 text-green-700"
                    : "text-gray-900"
                }`}
                onClick={() => handleFilterSelect(option.key)}
              >
                <div className="flex items-center gap-2">
                  <span className="text-lg">{option.icon}</span>
                  <span className="font-medium">{option.label}</span>
                </div>
                <span className="text-sm text-gray-500">
                  ({deviceCounts[option.key]})
                </span>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Status Summary */}
      {selectedFilter === "all" && (
        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
          <div className="text-xs text-gray-600 mb-1">Resumo da Rede</div>
          <div className="flex gap-4 text-sm">
            <span className="text-green-600">
              📡 {deviceCounts.coordinators} LIC
              {deviceCounts.coordinators !== 1 ? "s" : ""}
            </span>
            <span className="text-blue-600">
              🔗 {deviceCounts.mapped} Mapeado
              {deviceCounts.mapped !== 1 ? "s" : ""}
            </span>
            <span className="text-orange-600">
              ⚠️ {deviceCounts.unmapped} Não mapeado
              {deviceCounts.unmapped !== 1 ? "s" : ""}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

export default DeviceFilterBar;
