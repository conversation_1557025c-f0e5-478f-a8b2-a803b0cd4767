import { useEffect } from "react";
import Button from "@/components/ui/Button";
import Toggle from "@/components/ui/Toggle";
import { IrrigationPlanFormData } from "../IrrigationPlanWizardPage";

interface IrrigationPlanConfigPanelProps {
  data: IrrigationPlanFormData;
  onChange: (data: Partial<IrrigationPlanFormData>) => void;
  readOnly?: boolean;
  project?: {
    fertigation_water_pump?: any;
    backwash_pump_type?: string | null;
  };
}

const DAYS_OF_WEEK = [
  { value: "MON", label: "Seg", fullLabel: "Segunda" },
  { value: "TUE", label: "Ter", fullLabel: "Terça" },
  { value: "WED", label: "Qua", fullLabel: "Quarta" },
  { value: "THU", label: "Qui", fullLabel: "Quinta" },
  { value: "FRI", label: "Sex", fullLabel: "Sex<PERSON>" },
  { value: "SAT", label: "Sáb", fullLabel: "S<PERSON>bado" },
  { value: "SUN", label: "Dom", fullLabel: "Domingo" },
] as const;

function IrrigationPlanConfigPanel({
  data,
  onChange,
  readOnly = false,
  project,
}: IrrigationPlanConfigPanelProps) {
  const handleDayToggle = (day: (typeof DAYS_OF_WEEK)[number]["value"]) => {
    const newDays = data.daysOfWeek.includes(day)
      ? data.daysOfWeek.filter((d) => d !== day)
      : [...data.daysOfWeek, day];
    onChange({ daysOfWeek: newDays });
  };

  // Check if the project has a fertigation water pump
  const hasfertigationWaterPump =
    project?.fertigation_water_pump !== null &&
    project?.fertigation_water_pump !== undefined;

  // Check if the project has a backwash pump configured
  const hasBackwashPump =
    project?.backwash_pump_type !== null &&
    project?.backwash_pump_type !== undefined &&
    project?.backwash_pump_type !== "";

  // Automatically disable fertigation if the project doesn't have a fertigation water pump
  useEffect(() => {
    if (!hasfertigationWaterPump && data.fertigationEnabled) {
      onChange({ fertigationEnabled: false });
    }
  }, [hasfertigationWaterPump, data.fertigationEnabled, onChange]);

  // Automatically disable backwash if the project doesn't have a backwash pump
  useEffect(() => {
    if (!hasBackwashPump && data.backwashEnabled) {
      onChange({ backwashEnabled: false });
    }
  }, [hasBackwashPump, data.backwashEnabled, onChange]);

  const formatTimeForInput = (timeString: string) => {
    // Ensure time is in HH:MM format for input
    return timeString || "06:00";
  };

  const formatDateForInput = (dateString: string) => {
    // Convert date string to YYYY-MM-DD format for input
    if (!dateString) return "";
    return dateString.split("T")[0];
  };

  return (
    <div className="p-4 space-y-6">
      {/* Enabled Toggle */}
      <div className="bg-white rounded-xl border border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Status</h3>
            <p className="text-sm text-gray-600 mt-1">
              Defina se o cronograma estará ativo
            </p>
          </div>
          <Toggle
            ariaLabel="Habilitar cronograma"
            checked={data.isEnabled}
            onChange={(checked) =>
              !readOnly && onChange({ isEnabled: checked })
            }
            disabled={readOnly}
          />
        </div>
        {data.isEnabled && (
          <div className="mt-3 text-sm text-green-600 font-medium">
            Habilitado
          </div>
        )}
      </div>

      {/* Fertigation Toggle */}
      <div className="bg-white rounded-xl border border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Fertirrigação
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {hasfertigationWaterPump
                ? "Habilite para incluir fertirrigação neste plano de irrigação"
                : "Este projeto não possui bomba de fertirrigação configurada"}
            </p>
          </div>
          <Toggle
            ariaLabel="Habilitar fertirrigação"
            checked={data.fertigationEnabled}
            onChange={(checked) =>
              !readOnly &&
              hasfertigationWaterPump &&
              onChange({ fertigationEnabled: checked })
            }
            disabled={readOnly || !hasfertigationWaterPump}
            color={hasfertigationWaterPump ? "green" : "gray"}
          />
        </div>
        {data.fertigationEnabled && hasfertigationWaterPump && (
          <div className="mt-3 text-sm text-green-600 font-medium">
            Fertirrigação Habilitada
          </div>
        )}
        {!hasfertigationWaterPump && (
          <div className="mt-3 text-sm text-orange-600 font-medium">
            Fertirrigação indisponível
          </div>
        )}
      </div>

      {/* Backwash Toggle */}
      <div className="bg-white rounded-xl border border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Retrolavagem
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {hasBackwashPump
                ? "Habilite para incluir retrolavagem neste plano de irrigação"
                : "Este projeto não possui bomba de retrolavagem configurada"}
            </p>
          </div>
          <Toggle
            ariaLabel="Habilitar retrolavagem"
            checked={data.backwashEnabled}
            onChange={(checked) =>
              !readOnly &&
              hasBackwashPump &&
              onChange({ backwashEnabled: checked })
            }
            disabled={readOnly || !hasBackwashPump}
            color={hasBackwashPump ? "green" : "gray"}
          />
        </div>
        {data.backwashEnabled && hasBackwashPump && (
          <div className="mt-3 text-sm text-green-600 font-medium">
            Retrolavagem Habilitada
          </div>
        )}
        {!hasBackwashPump && (
          <div className="mt-3 text-sm text-orange-600 font-medium">
            Retrolavagem indisponível
          </div>
        )}
      </div>

      {/* Basic Information */}
      <div className="bg-white rounded-xl border border-gray-200 p-4 space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Informações Básicas
        </h3>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nome *
          </label>
          <input
            type="text"
            placeholder="Ex: Tomatos Field III"
            value={data.name}
            onChange={(e) => !readOnly && onChange({ name: e.target.value })}
            disabled={readOnly}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Descrição
          </label>
          <textarea
            placeholder="Descrição opcional do cronograma"
            value={data.description}
            onChange={(e) =>
              !readOnly && onChange({ description: e.target.value })
            }
            disabled={readOnly}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
          />
        </div>
      </div>

      {/* Schedule Configuration */}
      <div className="bg-white rounded-xl border border-gray-200 p-4 space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Agendamento</h3>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Horário de Início *
          </label>
          <input
            type="time"
            value={formatTimeForInput(data.startTime)}
            onChange={(e) =>
              !readOnly && onChange({ startTime: e.target.value })
            }
            disabled={readOnly}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Dias da Semana *
          </label>
          <div className="grid grid-cols-7 gap-2">
            {DAYS_OF_WEEK.map((day) => (
              <Button
                key={day.value}
                type="button"
                onClick={() => !readOnly && handleDayToggle(day.value)}
                disabled={readOnly}
                size="sm"
                variant={
                  data.daysOfWeek.includes(day.value) ? "primary" : "secondary"
                }
                className={`py-3 px-2 text-center text-sm font-medium transition-colors ${
                  data.daysOfWeek.includes(day.value)
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                <div className="font-semibold">{day.label}</div>
              </Button>
            ))}
          </div>
          {data.daysOfWeek.length === 0 && (
            <p className="text-sm text-red-600 mt-2">
              Selecione pelo menos um dia da semana
            </p>
          )}
        </div>
      </div>

      {/* Date Range */}
      <div className="bg-white rounded-xl border border-gray-200 p-4 space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Período de Validade
        </h3>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Data de Início
            </label>
            <div className="relative">
              <input
                type="date"
                value={formatDateForInput(data.startDate)}
                onChange={(e) =>
                  !readOnly && onChange({ startDate: e.target.value })
                }
                disabled={readOnly}
                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
              />
              {!readOnly && data.startDate && (
                <Button
                  type="button"
                  onClick={() => onChange({ startDate: "" })}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 p-1"
                  title="Limpar data de início"
                  variant="ghost"
                  size="sm"
                  icon={
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  }
                />
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Data de Fim
            </label>
            <div className="relative">
              <input
                type="date"
                value={formatDateForInput(data.endDate)}
                onChange={(e) =>
                  !readOnly && onChange({ endDate: e.target.value })
                }
                disabled={readOnly}
                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-50 disabled:text-gray-500"
              />
              {!readOnly && data.endDate && (
                <Button
                  type="button"
                  onClick={() => onChange({ endDate: "" })}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 p-1"
                  title="Limpar data de fim"
                  variant="ghost"
                  size="sm"
                  icon={
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  }
                />
              )}
            </div>
          </div>
        </div>

        <div className="text-sm text-gray-600">
          <p>
            Deixe as datas em branco para um cronograma sem data de validade.
          </p>
        </div>
      </div>
    </div>
  );
}

export default IrrigationPlanConfigPanel;
