import {
  devicesByIdAtom,
  reservoirsByIdAtom,
  waterPumpsByIdAtom,
} from "@/store";
import { useAtomValue } from "jotai";
import { Droplets, Monitor, Zap, Clock } from "lucide-react";

interface ReservoirCardProps {
  reservoirId: string; // TODO: Fix AUTReservoir type
  onClick: () => void;
}

export function ReservoirCard({ reservoirId, onClick }: ReservoirCardProps) {
  const reservoirsById = useAtomValue(reservoirsByIdAtom);
  const reservoir = reservoirsById(reservoirId);

  const formatCapacity = (capacity: number | null) => {
    if (!capacity) return "N/A";
    if (capacity >= 1000) {
      return `${(capacity / 1000).toFixed(1)}k L`;
    }
    return `${capacity.toLocaleString()} L`;
  };

  if (!reservoir) return <div>Reservoir not found</div>;

  const waterPumpById = useAtomValue(waterPumpsByIdAtom);
  const waterPump = reservoir.water_pump
    ? waterPumpById(reservoir.water_pump as string)
    : null;

  const deviceById = useAtomValue(devicesByIdAtom);
  const device = reservoir.reservoir_monitor
    ? deviceById(reservoir.reservoir_monitor as string)
    : null;

  return (
    <div
      className="bg-white rounded-xl border border-gray-200 p-4 hover:shadow-md transition-all cursor-pointer hover:-translate-y-1"
      onClick={onClick}
    >
      <div className="flex items-center mb-3">
        <Droplets className="w-5 h-5 text-blue-500 mr-2" />
        <h3 className="text-lg font-semibold text-gray-900 truncate">
          {reservoir.name}
        </h3>
      </div>

      {reservoir.description && (
        <p className="text-sm text-gray-600 mb-3 truncate">
          {reservoir.description}
        </p>
      )}

      <div className="space-y-2">
        {reservoir.capacity && (
          <div className="flex items-center text-sm text-gray-600">
            <Droplets className="w-4 h-4 mr-2" />
            <span>Capacidade: {formatCapacity(reservoir.capacity)}</span>
          </div>
        )}

        {/* Always show safety time; display a fallback when not set */}
        {(() => {
          const safetyTime = (reservoir as any).safety_time_minutes;
          return (
            <div className="flex items-center text-sm text-gray-600">
              <Clock className="w-4 h-4 mr-2" />
              <span>
                Tempo de Segurança:{" "}
                {safetyTime != null ? `${safetyTime} min` : "N/A"}
              </span>
            </div>
          );
        })()}

        {device && (
          <div className="flex items-center text-sm text-gray-600">
            <Monitor className="w-4 h-4 mr-2" />
            <span>Monitor: {device.identifier}</span>
          </div>
        )}

        {waterPump && (
          <div className="flex items-center text-sm text-gray-600">
            <Zap className="w-4 h-4 mr-2" />
            <span>Bomba de Serviço: {waterPump.label}</span>
          </div>
        )}
      </div>

      <div className="mt-4 flex justify-end">
        <span
          className={`px-2 py-1 text-xs font-medium rounded-full ${
            reservoir.enabled
              ? "bg-green-100 text-green-700"
              : "bg-gray-100 text-gray-700"
          }`}
        >
          {reservoir.enabled ? "Ativo" : "Inativo"}
        </span>
      </div>
    </div>
  );
}
