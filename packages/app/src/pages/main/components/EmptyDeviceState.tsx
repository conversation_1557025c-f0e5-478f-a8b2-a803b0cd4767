interface EmptyDeviceStateProps {
  hasSearchQuery: boolean;
  hasFilter: boolean;
}

export default function EmptyDeviceState({ hasSearchQuery, hasFilter }: EmptyDeviceStateProps) {
  return (
    <div className="text-center py-12 text-gray-500">
      <div className="text-4xl mb-4">🔧</div>
      <p className="text-lg font-medium mb-2">
        Nenhum dispositivo encontrado
      </p>
      <p className="text-sm">
        {hasSearchQuery || hasFilter
          ? 'Tente ajustar os filtros ou termo de busca'
          : 'Adicione dispositivos para começar'}
      </p>
    </div>
  );
}