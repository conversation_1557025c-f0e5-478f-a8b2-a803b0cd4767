import { useMemo } from 'react';
import { useAtomValue } from 'jotai';
import { propertyDevicesAtom, selectedPropertyAtom } from '@/store';
import { enhanceDevicesWithMapping, sortDevicesForHierarchicalDisplay } from '@/utils/mesh-device-utils';
import { useDeviceFiltering } from '@/hooks/useDeviceFiltering';
import DeviceFilterBar from './DeviceFilterBar';
import DeviceList from './DeviceList';

interface DevicesTabProps {
  searchQuery: string;
  onDeviceClick: (device: any) => void;
  onManageNetwork: (device: any) => void;
}

export default function DevicesTab({ 
  searchQuery, 
  onDeviceClick, 
  onManageNetwork 
}: DevicesTabProps) {
  const propertyDevices = useAtomValue(propertyDevicesAtom);
  const property = useAtomValue(selectedPropertyAtom);

  // Enhanced devices with mapping information
  const enhancedDevices = useMemo(() => {
    const enhanced = enhanceDevicesWithMapping(propertyDevices);
    return sortDevicesForHierarchicalDisplay(enhanced);
  }, [propertyDevices]);

  const { deviceFilter, setDeviceFilter, filteredDevices, deviceCounts } = 
    useDeviceFiltering(enhancedDevices);

  // Apply search query filter
  const searchFilteredDevices = useMemo(() => {
    if (!searchQuery.trim()) return filteredDevices;
    
    return filteredDevices.filter(
      (device) =>
        device.device.identifier
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        device.device.model.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [filteredDevices, searchQuery]);

  return (
    <>
      <DeviceFilterBar
        selectedFilter={deviceFilter}
        onFilterChange={setDeviceFilter}
        deviceCounts={deviceCounts}
      />
      
      <DeviceList
        devices={searchFilteredDevices}
        property={property}
        onDeviceClick={onDeviceClick}
        onManageNetwork={onManageNetwork}
        hasSearchQuery={!!searchQuery.trim()}
        hasFilter={deviceFilter !== 'all'}
      />
    </>
  );
}