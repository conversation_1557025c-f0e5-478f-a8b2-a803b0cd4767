import {
  WATER_PUMP_MODE_LABELS,
  WATER_PUMP_MODE_VALUES,
  WATER_PUMP_TYPE_LABELS,
  WATER_PUMP_TYPE_VALUES,
  type WaterPumpMode,
  type WaterPumpType,
} from "@/api/model/water-pump";
import type { AUTWaterPump } from "@/api/queries/account";
import { Modal, useToast } from "@/components";
import { DeviceActivationPanel } from "@/components/DeviceActivationPanel";
import { ModalFooter } from "@/components/ui/ModalFooter";
import { selectedPropertyIdAtom, waterPumpsAtom } from "@/store";
import {
  createDeviceMessageRequestAtom,
  createWaterPumpAtom,
  updateWaterPumpAtom,
} from "@/store/crud";
import { deviceToLicMapAtom, propertyDevicesAtom } from "@/store/data";
import { getDeviceModelCommercialName } from "@/utils/device-model";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useCallback, useEffect, useMemo, useState } from "react";

interface PumpDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  pump: AUTWaterPump | null;
  mode: "create" | "edit";
}

function PumpDetailModal({
  isOpen,
  onClose,
  pump,
  mode,
}: PumpDetailModalProps) {
  const createWaterPump = useSetAtom(createWaterPumpAtom);
  const updateWaterPump = useSetAtom(updateWaterPumpAtom);
  const createDeviceMessageRequest = useSetAtom(createDeviceMessageRequestAtom);
  const deviceToLicMap = useAtomValue(deviceToLicMapAtom);
  const selectedPropertyId = useAtomValue(selectedPropertyIdAtom);
  const propertyDevices = useAtomValue(propertyDevicesAtom);
  const waterPumps = useAtomValue(waterPumpsAtom);

  const { showWarning, showInfo } = useToast();

  const [formData, setFormData] = useState({
    label: "",
    identifier: "",
    pump_type: "" as WaterPumpType | "",
    pump_model: "",
    water_pump_controller: "" as string,
    has_frequency_inverter: false,
    monitor_operation: false,
    flow_rate_lh: null as number | null,
    mode: "CONTINUOUS" as WaterPumpMode,
  });

  useEffect(() => {
    if (pump && mode === "edit") {
      setFormData({
        label: pump.label,
        identifier: pump.identifier,
        pump_type: pump.pump_type,
        pump_model: pump.pump_model,
        water_pump_controller: pump.water_pump_controller || "",
        has_frequency_inverter: pump.has_frequency_inverter || false,
        monitor_operation: pump.monitor_operation || false,
        flow_rate_lh: pump.flow_rate_lh || null,
        mode: pump.mode || "CONTINUOUS",
      });
    } else if (mode === "create") {
      setFormData({
        label: "",
        identifier: "",
        pump_type: "",
        pump_model: "",
        water_pump_controller: "",
        has_frequency_inverter: false,
        monitor_operation: false,
        flow_rate_lh: null,
        mode: "CONTINUOUS",
      });
    }
  }, [pump, mode]);

  // Filter devices to only show WPC-PL10 and WPC-PL50 controllers
  const availableControllers = useMemo(() => {
    return propertyDevices.filter(
      (pd) => pd.device.model === "WPC-PL10" || pd.device.model === "WPC-PL50"
    );
  }, [propertyDevices]);

  // Fast lookup map from controller id to device
  const deviceById = useMemo(() => {
    return new Map(propertyDevices.map((d) => [d.id, d] as const));
  }, [propertyDevices]);

  // Currently selected controller (if any) and mode constraint
  const selectedController = useMemo(() => {
    return (
      availableControllers.find(
        (d) => d.device.id === formData.water_pump_controller
      ) || null
    );
  }, [availableControllers, formData.water_pump_controller]);

  // Determine when mode must be forced to CONTINUOUS
  const isModeForcedContinuous = useMemo(() => {
    if (!selectedController) return false;

    // Always forced for WPC-PL10
    if (selectedController.device.model === "WPC-PL10") return true;

    // WPC-PL50 specific rules
    if (selectedController.device.model === "WPC-PL50") {
      // When configuring a fertigation with WPC-PL50, mode must be CONTINUOUS
      if (formData.pump_type === "FERTIGATION") return true;

      // When configuring an irrigation with a WPC-PL50 that is already
      // associated to a fertigation pump, force CONTINUOUS
      if (formData.pump_type === "IRRIGATION") {
        const controllerId = selectedController.id;
        const fertigationUsingController = waterPumps.some(
          (p) =>
            p.id !== pump?.id &&
            p.pump_type === "FERTIGATION" &&
            p.water_pump_controller === controllerId
        );
        if (fertigationUsingController) return true;
      }
    }

    return false;
  }, [selectedController, formData.pump_type, waterPumps, pump?.id]);

  // Reason to show when mode is being forced to CONTINUOUS
  const forcedModeReason = useMemo(() => {
    if (!selectedController) return "";
    if (selectedController.device.model === "WPC-PL10") {
      return "O controlador WPC-PL10 exige modo CONTINUOUS.";
    }
    if (selectedController.device.model === "WPC-PL50") {
      if (formData.pump_type === "FERTIGATION") {
        return "WPC-PL50 em fertirrigação exige modo CONTINUOUS.";
      }
      if (formData.pump_type === "IRRIGATION") {
        const controllerId = selectedController.id;
        const fertigationUsingController = waterPumps.some(
          (p) =>
            p.id !== pump?.id &&
            p.pump_type === "FERTIGATION" &&
            p.water_pump_controller === controllerId
        );
        if (fertigationUsingController) {
          return "WPC-PL50 compartilhado com fertirrigação exige modo CONTINUOUS.";
        }
      }
    }
    return "";
  }, [selectedController, formData.pump_type, waterPumps, pump?.id]);

  // If controller is WPC-PL10, enforce CONTINUOUS mode
  useEffect(() => {
    if (isModeForcedContinuous && formData.mode !== "CONTINUOUS") {
      setFormData((prev) => ({
        ...prev,
        mode: "CONTINUOUS" as WaterPumpMode,
      }));
    }
  }, [isModeForcedContinuous, formData.mode]);

  // Get controllers that are disabled based on assignment rules
  const getDisabledControllers = useMemo(() => {
    if (!formData.pump_type) return new Set<string>();

    const currentPumpId = pump?.id;
    const disabledControllerIds = new Set<string>();

    // Get all pumps except the current one being edited
    const otherPumps = waterPumps.filter((p) => p.id !== currentPumpId);

    otherPumps.forEach((otherPump) => {
      const controllerId = otherPump.water_pump_controller; // Now always a string

      if (!controllerId) return; // Skip pumps without controllers

      // Apply filtering rules based on pump types
      if (formData.pump_type === "SERVICE") {
        // SERVICE pumps cannot use controllers assigned to any other pump
        disabledControllerIds.add(controllerId);
      } else if (otherPump.pump_type === "SERVICE") {
        // Controllers assigned to SERVICE pumps cannot be used by any other pump
        disabledControllerIds.add(controllerId);
      } else if (formData.pump_type === otherPump.pump_type) {
        // Same type pumps cannot share controllers
        disabledControllerIds.add(controllerId);
      } else if (
        (formData.pump_type === "IRRIGATION" &&
          otherPump.pump_type === "FERTIGATION") ||
        (formData.pump_type === "FERTIGATION" &&
          otherPump.pump_type === "IRRIGATION")
      ) {
        // IRRIGATION and FERTIGATION can share controllers, so don't disable
        // This is explicitly allowed by the requirements
      }
    });

    // Additional WPC-PL50 rule:
    // When configuring a fertigation pump, a WPC-PL50 already associated to an
    // irrigation pump in PULSE mode cannot be available
    if (formData.pump_type === "FERTIGATION") {
      otherPumps.forEach((otherPump) => {
        const controllerId = otherPump.water_pump_controller;
        if (!controllerId) return;
        if (otherPump.pump_type !== "IRRIGATION") return;
        if (otherPump.mode !== "PULSE") return;
        const ctrl = deviceById.get(controllerId);
        if (ctrl?.device?.model === "WPC-PL50") {
          disabledControllerIds.add(controllerId);
        }
      });
    }

    return disabledControllerIds;
  }, [formData.pump_type, waterPumps, pump?.id, deviceById]);

  // Handle pump type change - reset controller when type changes
  const handlePumpTypeChange = (newPumpType: WaterPumpType | "") => {
    setFormData((prev) => ({
      ...prev,
      pump_type: newPumpType,
      water_pump_controller: "", // Reset controller when pump type changes
    }));
  };

  const handleSave = async () => {
    if (!selectedPropertyId) {
      console.error("No property selected for the pump.");
      showWarning({
        message: "Nenhuma propriedade selecionada para a bomba.",
      });
      return;
    }

    // Guard: When configuring a fertigation with WPC-PL50, if controller is already
    // associated to an irrigation pump of mode PULSE, block the assignment
    if (
      formData.pump_type === "FERTIGATION" &&
      selectedController?.device?.model === "WPC-PL50"
    ) {
      const controllerId = selectedController.id;
      const irrigationPulseExists = waterPumps.some(
        (p) =>
          p.id !== pump?.id &&
          p.pump_type === "IRRIGATION" &&
          p.mode === "PULSE" &&
          p.water_pump_controller === controllerId
      );
      if (irrigationPulseExists) {
        showWarning({
          message:
            "Este WPC-PL50 está associado a uma bomba de irrigação em modo PULSE e não pode ser usado na fertirrigação.",
        });
        return;
      }
    }
    const data = {
      ...formData,
      identifier: formData.identifier.trim() || formData.label.trim(),
      property: selectedPropertyId, // Ensure property ID is set
      pump_type: formData.pump_type || undefined, // Convert empty string to undefined
      water_pump_controller: formData.water_pump_controller || null, // Convert empty string to null
    };
    // TODO: Implement save logic with API call
    if (mode === "edit" && pump) {
      await updateWaterPump({
        id: pump.id,
        data: data as any, // Cast to avoid type mismatch with DirectusRelationFieldType
      });
    } else if (mode === "create") {
      // Create new water pump
      await createWaterPump(data as any); // Cast to avoid type mismatch with DirectusRelationFieldType
    }

    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  const handleTestActivation = useCallback(
    async (turnOn: boolean) => {
      if (!pump?.water_pump_controller) {
        showWarning({
          message: "Nenhum controlador de bomba selecionado.",
        });
        return;
      }
      const lic = deviceToLicMap.get(pump.water_pump_controller);
      if (!lic) {
        showWarning({
          message:
            "Controlador de Irrigação não encontrado para o controlador de bomba.",
        });
        return;
      }
      await createDeviceMessageRequest({
        device: lic,
        payload_type: "control",
        payload_data: {
          elementType: "pump",
          elementId: pump?.id,
          turnOn: turnOn,
          durationMinutes: turnOn ? 1 : undefined,
        },
      });
      showInfo({
        message: "Ativação de bomba enviada com sucesso.",
      });
    },
    [pump, createDeviceMessageRequest, deviceToLicMap]
  );

  const handleTestBackwash = useCallback(
    async (turnOn: boolean) => {
      if (!pump?.water_pump_controller) {
        showWarning({
          message: "Nenhum controlador de bomba selecionado.",
        });
        return;
      }
      const lic = deviceToLicMap.get(pump.water_pump_controller);
      if (!lic) {
        showWarning({
          message:
            "Controlador de Irrigação não encontrado para o controlador de bomba.",
        });
        return;
      }
      await createDeviceMessageRequest({
        device: lic,
        payload_type: "control",
        payload_data: {
          elementType: "pump",
          elementId: pump?.id,
          elementVariation: "backwash",
          turnOn: turnOn,
          durationMinutes: turnOn ? 1 : undefined,
        },
      });
      showInfo({
        message: "Ativação de retro-lavagem enviada com sucesso.",
      });
    },
    [pump, createDeviceMessageRequest, deviceToLicMap]
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === "create" ? "Nova Bomba" : "Editar Bomba"}
      size="md"
    >
      <div className="space-y-6">
        {/* Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nome
          </label>
          <input
            type="text"
            value={formData.label}
            onChange={(e) =>
              setFormData({ ...formData, label: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            placeholder="Nome da Bomba"
          />
        </div>

        {/* Water Pump Serial Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            S/N
          </label>
          <input
            type="text"
            value={formData.identifier}
            onChange={(e) =>
              setFormData({ ...formData, identifier: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            placeholder="S/N XXXXXXX"
          />
        </div>

        {/* Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tipo
          </label>
          <select
            value={formData.pump_type}
            onChange={(e) =>
              handlePumpTypeChange(e.target.value as WaterPumpType | "")
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
          >
            <option value="">Selecione o tipo da bomba</option>
            {WATER_PUMP_TYPE_VALUES.map((type) => (
              <option key={type} value={type}>
                {WATER_PUMP_TYPE_LABELS[type]}
              </option>
            ))}
          </select>
        </div>

        {/* Water Pump Controller */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Controlador
          </label>
          <select
            value={formData.water_pump_controller}
            onChange={(e) =>
              setFormData({
                ...formData,
                water_pump_controller: e.target.value,
              })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            disabled={!formData.pump_type}
          >
            <option value="">
              {!formData.pump_type
                ? "Selecione primeiro o tipo da bomba"
                : "Selecione o controlador"}
            </option>
            {availableControllers.map((controller) => {
              const isDisabled = getDisabledControllers.has(controller.id);

              // Find which pump is using this controller to provide better context
              let disabledReason = "";
              if (isDisabled) {
                const usingPump = waterPumps.find((p) => {
                  const controllerId = p.water_pump_controller; // Now always a string
                  return controllerId === controller.id && p.id !== pump?.id;
                });

                if (usingPump) {
                  const pumpTypeLabel =
                    WATER_PUMP_TYPE_LABELS[usingPump.pump_type];
                  disabledReason = ` (Usado por bomba ${pumpTypeLabel})`;
                } else {
                  disabledReason = " (Em uso)";
                }
              }

              return (
                <option
                  key={controller.device.id}
                  value={controller.device.id}
                  disabled={isDisabled}
                >
                  {controller.metadata?.label
                    ? `${controller.metadata.label} - ${controller.device.identifier}`
                    : controller.device.identifier}{" "}
                  - {getDeviceModelCommercialName(controller.device.model)}
                  {disabledReason}
                </option>
              );
            })}
          </select>
        </div>

        {/* Model */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Modelo
          </label>
          <input
            type="text"
            value={formData.pump_model}
            onChange={(e) =>
              setFormData({ ...formData, pump_model: e.target.value })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            placeholder="Modelo da bomba"
          />
        </div>

        {/* Mode */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Modo
          </label>
          <div className="flex gap-4">
            {WATER_PUMP_MODE_VALUES.map((modeValue) => (
              <label key={modeValue} className="flex items-center gap-1">
                <input
                  type="radio"
                  name="pump-mode"
                  value={modeValue}
                  checked={formData.mode === modeValue}
                  onChange={() =>
                    setFormData({
                      ...formData,
                      mode: modeValue as WaterPumpMode,
                    })
                  }
                  className="text-green-600 focus:ring-green-500"
                  disabled={isModeForcedContinuous}
                />
                <span className="text-sm">
                  {WATER_PUMP_MODE_LABELS[modeValue]}
                </span>
              </label>
            ))}
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Modo de operação da bomba
          </p>
          {isModeForcedContinuous && (
            <p className="mt-1 text-xs text-gray-500">
              {forcedModeReason ||
                "O modo está sendo forçado para CONTINUOUS pelas regras do controlador."}
            </p>
          )}
        </div>

        {/* Frequency Inverter */}
        <div>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={formData.has_frequency_inverter}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  has_frequency_inverter: e.target.checked,
                })
              }
              className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
            />
            <span className="text-sm font-medium text-gray-700">
              Possui inversor de frequência
            </span>
          </label>
        </div>

        {/* Monitor Operation: Only show if pump_type is not 'FERTIGATION' */}
        {formData.pump_type !== "FERTIGATION" && (
          <div>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={formData.monitor_operation}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    monitor_operation: e.target.checked,
                  })
                }
                className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
              />
              <span className="text-sm font-medium text-gray-700">
                Monitorar operação da bomba
              </span>
            </label>
          </div>
        )}

        {/* Flow Rate */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Vazão (L/h)
          </label>
          <input
            type="number"
            value={formData.flow_rate_lh || ""}
            onChange={(e) =>
              setFormData({
                ...formData,
                flow_rate_lh: e.target.value
                  ? parseFloat(e.target.value)
                  : null,
              })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            placeholder="1000"
            min="0"
            step="0.1"
          />
          <p className="mt-1 text-xs text-gray-500">
            Taxa de fluxo da bomba em litros por hora
          </p>
        </div>

        <div className="flex flex-col gap-0.5">
          {pump?.water_pump_controller && (
            <>
              <DeviceActivationPanel
                elementType="pump"
                elementId={pump.id}
                elementControllerId={pump.water_pump_controller}
                turnOnDurationMinutes={1}
                title="Testar Acionamento da Bomba"
              />
              <DeviceActivationPanel
                elementType="pump"
                elementId={pump.id}
                elementControllerId={pump.water_pump_controller}
                elementVariation="backwash"
                turnOnDurationMinutes={1}
                title="Testar Acionamento da Retrolavagem"
              />
            </>
          )}

          {/* <Button
            variant="secondary"
            className="mt-4"
            type="button"
            onClick={handleTestBackwash}
          >
            Testar Retrolavagem
          </Button> */}
        </div>

        {/* Action Buttons */}
        <ModalFooter
          primaryAction={{
            label: "Salvar",
            onClick: handleSave,
          }}
          secondaryAction={{
            label: "Cancelar",
            onClick: handleCancel,
          }}
        />
      </div>
    </Modal>
  );
}

export default PumpDetailModal;
