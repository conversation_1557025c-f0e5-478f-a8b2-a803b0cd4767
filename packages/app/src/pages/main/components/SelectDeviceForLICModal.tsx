import { Modal, useToast } from "@/components";
import Button from "@/components/ui/Button";
import { ChevronDown, Plus, Check } from "lucide-react";
import { useState } from "react";
import type { DeviceWithMapping } from "@/utils/mesh-device-utils";
import { getDeviceIcon } from "@/utils/mesh-device-utils";
import { getDeviceModelLabel } from "@/utils/device-model";
import clsx from "clsx";

interface SelectDeviceForLICModalProps {
  isOpen: boolean;
  onClose: () => void;
  licDevice: DeviceWithMapping | null;
  availableDevices: DeviceWithMapping[]; // All mesh devices (unmapped and mapped to other LICs)
  onSelectDevices?: (
    items: Array<{
      meshDeviceId: string;
      licDeviceId: string;
      startDate: string;
    }>
  ) => void;
}

function SelectDeviceForLICModal({
  isOpen,
  onClose,
  licDevice,
  availableDevices,
  onSelectDevices,
}: SelectDeviceForLICModalProps) {
  const { showWarning } = useToast();
  const [selectedDeviceIds, setSelectedDeviceIds] = useState<string[]>([]);
  const [startDate, setStartDate] = useState<string>(new Date().toISOString());
  const [isDeviceDropdownOpen, setIsDeviceDropdownOpen] = useState(false);

  if (!licDevice || !licDevice.isLIC) {
    return null;
  }

  const selectedDevices = availableDevices.filter((d) =>
    selectedDeviceIds.includes(d.id)
  );

  const handleSelectDevice = () => {
    if (selectedDeviceIds.length === 0) {
      showWarning({
        message:
          "Por favor, selecione pelo menos um dispositivo para adicionar à rede.",
      });
      return;
    }

    if (!startDate) {
      showWarning({
        message: "Por favor, selecione uma data de início.",
      });
      return;
    }

    if (onSelectDevices) {
      const startDateISO = new Date(startDate).toISOString();
      onSelectDevices(
        selectedDeviceIds.map((id) => ({
          meshDeviceId: id,
          licDeviceId: licDevice.id,
          startDate: startDateISO,
        }))
      );
      handleCancel();
    } else {
      showWarning({
        message: "Funcionalidade de adição ainda não implementada.",
      });
    }
  };

  const handleCancel = () => {
    setSelectedDeviceIds([]);
    setStartDate(new Date().toISOString());
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Adicionar Dispositivo à Rede"
      size="md"
    >
      <div className="space-y-4">
        {/* LIC Device Info */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-xl">
              {getDeviceIcon(licDevice.device.model)}
            </span>
            <span className="font-medium text-gray-900">
              {licDevice.device.identifier}
            </span>
          </div>
          <p className="text-sm text-gray-600 mb-1">
            {getDeviceModelLabel(licDevice.device.model)}
          </p>
          <p className="text-sm text-green-600">
            Rede com {licDevice.meshDevices?.length || 0} dispositivo
            {(licDevice.meshDevices?.length || 0) !== 1 ? "s" : ""}
          </p>
        </div>

        {/* Device Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Selecionar Dispositivos
          </label>

          {availableDevices.length === 0 ? (
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg text-center text-gray-500">
              <p>Nenhum dispositivo disponível</p>
              <p className="text-sm">Não há dispositivos mesh para mapear</p>
            </div>
          ) : (
            <div className="relative">
              <Button
                type="button"
                variant="ghost"
                className="w-full flex items-center justify-start px-4 py-3 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                onClick={() => setIsDeviceDropdownOpen(!isDeviceDropdownOpen)}
                icon={
                  <ChevronDown
                    className={`text-gray-400 transition-transform ${
                      isDeviceDropdownOpen ? "rotate-180" : ""
                    }`}
                    size={20}
                  />
                }
              >
                <div className="flex items-center gap-2">
                  {selectedDevices.length > 0 ? (
                    <>
                      <span className="text-lg">
                        {getDeviceIcon(selectedDevices[0].device.model)}
                      </span>
                      <div className="flex flex-col">
                        <span className="font-medium text-left">
                          {selectedDevices.length === 1
                            ? selectedDevices[0].device.identifier
                            : `${selectedDevices[0].device.identifier} +${
                                selectedDevices.length - 1
                              }`}
                        </span>
                        <span className="text-xs text-gray-500">
                          {selectedDevices.length === 1
                            ? selectedDevices[0].mappingStatus === "unmapped"
                              ? "Não mapeado"
                              : selectedDevices[0].licDevice
                              ? `Mapeado para ${selectedDevices[0].licDevice.device.identifier}`
                              : "Status desconhecido"
                            : `${selectedDevices.length} dispositivos selecionados`}
                        </span>
                      </div>
                    </>
                  ) : (
                    <span className="text-gray-500">
                      Selecione dispositivos...
                    </span>
                  )}
                </div>
              </Button>

              {isDeviceDropdownOpen && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                  {availableDevices.map((device) => {
                    const isSelected = selectedDeviceIds.includes(device.id);
                    return (
                      <button
                        type="button"
                        key={device.id}
                        className={`border-b border-gray-200 w-full flex items-center justify-between px-4 py-3 text-left hover:bg-gray-50 transition-colors first:rounded-t-lg last:rounded-b-lg ${
                          isSelected
                            ? "bg-green-50 text-green-700"
                            : "text-gray-900"
                        }`}
                        onClick={() => {
                          setSelectedDeviceIds((prev) =>
                            prev.includes(device.id)
                              ? prev.filter((id) => id !== device.id)
                              : [...prev, device.id]
                          );
                          // Mantém o dropdown aberto para seleção múltipla
                        }}
                      >
                        <div className="flex items-center gap-2">
                          <span className="text-lg">
                            {getDeviceIcon(device.device.model)}
                          </span>
                          <div className="flex flex-col">
                            <span className="font-medium flex items-center gap-2">
                              {device.device.identifier}
                              {isSelected && (
                                <Check className="text-green-600" size={16} />
                              )}
                            </span>
                            <span
                              className={clsx("text-xs", {
                                "text-red-500":
                                  device.mappingStatus === "unmapped",
                                "text-green-500":
                                  device.mappingStatus === "mapped",
                              })}
                            >
                              {device.mappingStatus === "unmapped"
                                ? "Não mapeado"
                                : device.licDevice
                                ? `Mapeado para ${device.licDevice.device.identifier}`
                                : "Status desconhecido"}
                            </span>
                          </div>
                        </div>
                        <span className="text-sm text-gray-500">
                          {device.device.model}
                        </span>
                      </button>
                    );
                  })}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Start Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Data de Início
          </label>
          <input
            type="date"
            value={startDate}
            onChange={(e) => setStartDate(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
          />
        </div>

        {/* Information */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start gap-2">
            <Plus className="text-blue-600 mt-0.5" size={16} />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Adicionando à rede mesh:</p>
              <p>
                O dispositivo selecionado será associado a este LIC e poderá
                receber comandos através dele. Se o dispositivo já estiver
                mapeado para outro LIC, ele será transferido para esta rede. A
                associação será ativa a partir da data especificada.
              </p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-gray-200">
          <Button variant="secondary" className="flex-1" onClick={handleCancel}>
            Cancelar
          </Button>
          <Button
            variant="primary"
            className="flex-1"
            onClick={handleSelectDevice}
            disabled={
              selectedDeviceIds.length === 0 || availableDevices.length === 0
            }
          >
            Adicionar
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default SelectDeviceForLICModal;
