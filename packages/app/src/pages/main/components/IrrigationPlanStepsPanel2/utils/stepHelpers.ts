import {
  IrrigationPlanFormData,
  IrrigationPlanStepFormData,
} from "@/pages/main/IrrigationPlanWizardPage";
import { Sector } from "../types";

/**
 * Creates a new irrigation plan step for a given sector.
 * @param sector - The sector to create a step for
 * @param planData - The irrigation plan data containing fertigation settings
 * @returns A new irrigation plan step
 */
export const createNewStep = (
  sector: Sector,
  planData: IrrigationPlanFormData
): IrrigationPlanStepFormData => {
  return {
    id: `step-${Date.now()}-${sector.id}`,
    sectorId: sector.id,
    sectorName: sector.name,
    order: 1, // Will be updated by the parent component
    durationMinutes: 15,
    fertigationStartDelayMinutes: planData.fertigationEnabled ? 0 : undefined,
    fertigationDurationMinutes: planData.fertigationEnabled ? 5 : undefined,
  };
};

/**
 * Calculates the total duration of all steps in seconds.
 * @param steps - Array of irrigation plan steps
 * @returns Total duration in seconds
 */
export const calculateTotalDuration = (
  steps: IrrigationPlanStepFormData[]
): number => {
  return steps.reduce((total, step) => total + step.durationMinutes * 60, 0);
};
