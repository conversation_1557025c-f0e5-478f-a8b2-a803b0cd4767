import { useCallback, useState } from 'react';

/**
 * Custom hook for managing step selection state for bulk operations.
 * @returns Object containing selection state and manipulation functions
 */
export const useStepSelection = () => {
  const [selectedSteps, setSelectedSteps] = useState<Set<string>>(new Set());

  /**
   * Toggles the selection state of a step.
   * @param stepId - ID of the step to toggle
   */
  const toggleStepSelect = useCallback((stepId: string) => {
    setSelectedSteps((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(stepId)) {
        newSet.delete(stepId);
      } else {
        newSet.add(stepId);
      }
      return newSet;
    });
  }, []);

  /**
   * Clears all selected steps.
   */
  const clearSelection = useCallback(() => {
    setSelectedSteps(new Set());
  }, []);

  /**
   * Selects all provided step IDs.
   * @param stepIds - Array of step IDs to select
   */
  const selectAll = useCallback((stepIds: string[]) => {
    setSelectedSteps(new Set(stepIds));
  }, []);

  return {
    selectedSteps,
    toggleStepSelect,
    clearSelection,
    selectAll,
  };
};
