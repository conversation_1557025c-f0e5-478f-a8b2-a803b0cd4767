import { ListChecks, ListOrdered } from "lucide-react";
import { useState } from "react";
import ConfirmModal from "../../../../components/ConfirmModal";
import BulkActionBar from "./components/BulkActionBar";
import ChooseSectorsDialog from "./components/dialogs/ChooseSectorsDialog";
import StepEditDialog from "./components/dialogs/StepEditDialog";
import StepEditManyDialog from "./components/dialogs/StepEditManyDialog";
import StepReorderAssistant from "./components/dialogs/StepReorderAssistant";
import StepList from "./components/StepList";
import SummarySection from "./components/SummarySection";
import { useSectorManagement } from "./hooks/useSectorManagement";
import { useStepActions } from "./hooks/useStepActions";
import { useStepSelection } from "./hooks/useStepSelection";
import { IrrigationPlanStepsPanel2Props } from "./types";
import Button from "@/components/ui/Button";

/**
 * IrrigationPlanStepsPanel2 - Modular implementation of irrigation plan steps management
 *
 * This component provides a card-based layout with bulk operations and sector selection dialog.
 * It has been refactored into a modular structure for better maintainability and reusability.
 */
function IrrigationPlanStepsPanel2({
  projectId,
  planData,
  steps,
  sectors,
  onAddStep,
  onRemoveStep,
  onSaveStep,
  onUpdateStep,
  onMoveStep,
  onBulkRemoveSteps,
  onReorderSteps,
  readOnly = false,
  loading = false,
  projectData,
  hasFrequencyInverter = false,
}: IrrigationPlanStepsPanel2Props) {
  // Dialog state
  const [showSectorDialog, setShowSectorDialog] = useState(false);
  const [showReorderAssistant, setShowReorderAssistant] = useState(false);
  const [editingStep, setEditingStep] = useState<string | null>(null);
  const [showBulkEditDialog, setShowBulkEditDialog] = useState(false);
  const [confirmModal, setConfirmModal] = useState({
    isOpen: false,
    stepId: "",
    sectorName: "",
  });

  // Custom hooks for state management
  const { selectedSteps, toggleStepSelect, clearSelection, selectAll } =
    useStepSelection();

  const { selectedSectorIds, handleSectorsChange } = useSectorManagement({
    steps,
    sectors,
    planData,
    onRemoveStep,
    onUpdateStep,
    onSaveStep,
    onBulkRemoveSteps,
  });

  // Confirmation handlers
  const handleConfirmSingleDelete = (stepId: string, sectorName: string) => {
    setConfirmModal({
      isOpen: true,
      stepId,
      sectorName,
    });
  };

  const { handleStepEdit, handleStepDelete, handleStepSave, handleBulkEdit } =
    useStepActions({
      steps,
      onUpdateStep,
      onRemoveStep,
      onSaveStep,
      onConfirmSingleDelete: handleConfirmSingleDelete,
    });

  // Event handlers
  const handleEditStep = (stepId: string) => {
    setEditingStep(stepId);
  };

  const handleStepSaveAndClose = (step: any) => {
    handleStepSave(step);
    setEditingStep(null);
  };

  const handleBulkEditClick = () => {
    setShowBulkEditDialog(true);
  };

  const handleBulkSave = (updates: any) => {
    const stepIds = Array.from(selectedSteps);
    handleBulkEdit(stepIds, updates);
    setShowBulkEditDialog(false);
    clearSelection();
  };

  // Handle step reordering
  const handleReorderSteps = (newOrder: any[]) => {
    if (onReorderSteps) {
      onReorderSteps(newOrder);
    }
    setShowReorderAssistant(false);
  };

  // Confirmation modal handlers
  const handleConfirmRemove = () => {
    onRemoveStep(confirmModal.stepId);
    setConfirmModal({
      isOpen: false,
      stepId: "",
      sectorName: "",
    });
  };

  const handleCancelRemove = () => {
    setConfirmModal({
      isOpen: false,
      stepId: "",
      sectorName: "",
    });
  };

  // Get the step being edited
  const editingStepData = editingStep
    ? steps.find((step) => step.id === editingStep) || null
    : null;

  // Get selected steps for bulk editing
  const selectedStepsData = steps.filter((step) => selectedSteps.has(step.id));

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      {/* Summary Section */}
      <SummarySection steps={steps} sectors={sectors} planData={planData} />

      {/* Choose Sectors Button */}
      <div className="flex justify-center">
        <Button
          onClick={() => setShowSectorDialog(true)}
          disabled={readOnly}
          icon={<ListChecks />}
          variant="primary"
          fullWidth
        >
          Escolher Setores
        </Button>
      </div>

      {/* Step Reordering Assistant Button */}
      {!readOnly && steps.length > 1 && (
        <div className="flex justify-center">
          <Button
            onClick={() => setShowReorderAssistant(true)}
            icon={<ListOrdered />}
            variant="info"
            outline
            fullWidth
          >
            Assistente de Ordenação
          </Button>
        </div>
      )}

      {/* Step List */}
      <StepList
        steps={steps}
        selectedSteps={selectedSteps}
        fertigationEnabled={planData.fertigationEnabled}
        readOnly={readOnly}
        sectors={sectors}
        hasFrequencyInverter={hasFrequencyInverter}
        onToggleStepSelect={toggleStepSelect}
        onSelectAll={selectAll}
        onClearSelection={clearSelection}
        onMoveStep={onMoveStep}
        onEditStep={handleEditStep}
        onDeleteStep={handleStepDelete}
      />

      {/* Bulk Action Bar */}
      <BulkActionBar
        selectedCount={selectedSteps.size}
        readOnly={readOnly}
        onBulkEdit={handleBulkEditClick}
      />

      {/* Choose Sectors Dialog */}
      <ChooseSectorsDialog
        isOpen={showSectorDialog}
        onClose={() => setShowSectorDialog(false)}
        sectors={sectors}
        selectedSectorIds={selectedSectorIds}
        onSectorsChange={handleSectorsChange}
      />

      {/* Step Edit Dialog */}
      <StepEditDialog
        isOpen={editingStep !== null}
        onClose={() => setEditingStep(null)}
        step={editingStepData}
        fertigationEnabled={planData.fertigationEnabled}
        projectData={projectData}
        onSave={handleStepSaveAndClose}
      />

      {/* Step Edit Many Dialog */}
      <StepEditManyDialog
        isOpen={showBulkEditDialog}
        onClose={() => setShowBulkEditDialog(false)}
        selectedSteps={selectedStepsData}
        fertigationEnabled={planData.fertigationEnabled}
        projectData={projectData}
        onSave={handleBulkSave}
      />

      {/* Step Reorder Assistant */}
      <StepReorderAssistant
        isOpen={showReorderAssistant}
        onClose={() => setShowReorderAssistant(false)}
        steps={steps}
        onReorderSteps={handleReorderSteps}
      />

      {/* Confirmation Modal */}
      <ConfirmModal
        isOpen={confirmModal.isOpen}
        onClose={handleCancelRemove}
        onConfirm={handleConfirmRemove}
        title="Confirmar exclusão"
        message={`Tem certeza que deseja excluir o setor "${confirmModal.sectorName}"?`}
        confirmText="Excluir"
        cancelText="Cancelar"
        variant="danger"
      />
    </div>
  );
}

export default IrrigationPlanStepsPanel2;
