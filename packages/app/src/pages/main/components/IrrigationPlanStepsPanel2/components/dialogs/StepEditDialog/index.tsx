import Modal from "@/components/Modal";
import { IrrigationPlanStepFormData } from "@/pages/main/IrrigationPlanWizardPage";
import { useEffect, useState } from "react";
import { StepEditDialogProps } from "../../../types";
import { validateFertigationTiming } from "../../../utils/validation";
import Button from "@/components/ui/Button";

/**
 * Dialog component for editing a single irrigation plan step.
 * Allows modification of duration and fertigation settings.
 */
function StepEditDialog({
  isOpen,
  onClose,
  step,
  fertigationEnabled,
  projectData,
  onSave,
}: StepEditDialogProps) {
  const [formData, setFormData] = useState<{
    durationMinutes: string;
    fertigationStartDelayMinutes: string;
    fertigationDurationMinutes: string;
  }>({
    durationMinutes: "15",
    fertigationStartDelayMinutes: "0",
    fertigationDurationMinutes: "5",
  });

  const [validationError, setValidationError] = useState<string | null>(null);

  // Get pipe wash time from project data, default to 0 if not available
  const pipeWashTimeSeconds = projectData?.pipe_wash_time_seconds || 0;

  // Update form data when step changes
  useEffect(() => {
    if (step) {
      setFormData({
        durationMinutes: step.durationMinutes.toString(),
        fertigationStartDelayMinutes: (
          step.fertigationStartDelayMinutes || 0
        ).toString(),
        fertigationDurationMinutes: (
          step.fertigationDurationMinutes || 5
        ).toString(),
      });
    }
  }, [step]);

  // Validate fertigation timing whenever form data changes
  useEffect(() => {
    if (!step || !fertigationEnabled) {
      setValidationError(null);
      return;
    }

    // Create a temporary step with current form values for validation
    const durationMinutes = parseInt(formData.durationMinutes);
    const fertigationStartDelayMinutes = parseInt(
      formData.fertigationStartDelayMinutes
    );
    const fertigationDurationMinutes = parseInt(
      formData.fertigationDurationMinutes
    );

    // Only validate if all values are valid numbers
    if (
      !isNaN(durationMinutes) &&
      !isNaN(fertigationStartDelayMinutes) &&
      !isNaN(fertigationDurationMinutes) &&
      durationMinutes > 0
    ) {
      const tempStep: IrrigationPlanStepFormData = {
        ...step,
        durationMinutes,
        fertigationStartDelayMinutes,
        fertigationDurationMinutes,
      };

      const validation = validateFertigationTiming(
        tempStep,
        pipeWashTimeSeconds
      );
      setValidationError(
        validation.isValid ? null : validation.errorMessage || null
      );
    } else {
      setValidationError(null);
    }
  }, [formData, step, fertigationEnabled, pipeWashTimeSeconds]);

  const handleSave = () => {
    if (!step) return;

    // Parse string values back to numbers, with validation
    const durationMinutes = parseInt(formData.durationMinutes);
    const fertigationStartDelayMinutes = parseInt(
      formData.fertigationStartDelayMinutes
    );
    const fertigationDurationMinutes = parseInt(
      formData.fertigationDurationMinutes
    );

    // Validate required fields
    if (isNaN(durationMinutes) || durationMinutes < 1) {
      return; // Don't save if duration is invalid
    }

    // Don't save if there's a validation error
    if (validationError) {
      return;
    }

    const updatedStep: IrrigationPlanStepFormData = {
      ...step,
      durationMinutes,
      fertigationStartDelayMinutes:
        fertigationEnabled && !isNaN(fertigationStartDelayMinutes)
          ? fertigationStartDelayMinutes
          : undefined,
      fertigationDurationMinutes:
        fertigationEnabled && !isNaN(fertigationDurationMinutes)
          ? fertigationDurationMinutes
          : undefined,
    };

    onSave(updatedStep);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  if (!step) return null;

  return (
    <Modal
      title="Editar Setor"
      isOpen={isOpen}
      onClose={handleCancel}
      showCloseButton={true}
    >
      <div className="space-y-4">
        {/* Sector Name (read-only) */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Setor
          </label>
          <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-900">
            {step.sectorName}
          </div>
        </div>

        {/* Duration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Duração (minutos)
          </label>
          <input
            type="number"
            min="1"
            max="1440"
            value={formData.durationMinutes}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                durationMinutes: e.target.value,
              }))
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>

        {/* Fertigation fields */}
        {fertigationEnabled && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Atraso da Fertirrigação (minutos)
              </label>
              <input
                type="number"
                min="0"
                max="1440"
                value={formData.fertigationStartDelayMinutes}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    fertigationStartDelayMinutes: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Duração da Fertirrigação (minutos)
              </label>
              <input
                type="number"
                min="0"
                max="1440"
                value={formData.fertigationDurationMinutes}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    fertigationDurationMinutes: e.target.value,
                  }))
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </>
        )}

        {/* Validation Error */}
        {validationError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-sm text-red-700">{validationError}</p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button onClick={handleCancel} variant="secondary">
            Cancelar
          </Button>
          <Button
            onClick={handleSave}
            disabled={!!validationError}
            variant="primary"
          >
            Salvar
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default StepEditDialog;
