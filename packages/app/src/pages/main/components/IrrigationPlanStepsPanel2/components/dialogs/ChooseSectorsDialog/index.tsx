import Modal from "@/components/Modal";
import { Search } from "lucide-react";
import { useEffect, useState } from "react";
import { ChooseSectorsDialogProps } from "../../../types";
import Button from "@/components/ui/Button";

/**
 * Dialog component for selecting sectors for irrigation plan steps.
 * Provides search functionality and multi-select interface.
 */
function ChooseSectorsDialog({
  isOpen,
  onClose,
  sectors,
  selectedSectorIds,
  onSectorsChange,
}: ChooseSectorsDialogProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [localSelectedIds, setLocalSelectedIds] =
    useState<string[]>(selectedSectorIds);

  // Sync localSelectedIds with selectedSectorIds whenever the prop changes or dialog opens
  useEffect(() => {
    setLocalSelectedIds(selectedSectorIds);
  }, [selectedSectorIds]);

  // Additional sync when dialog opens to ensure state is correct
  useEffect(() => {
    console.log("ChooseSectorsDialog", { isOpen, selectedSectorIds });
    if (isOpen) {
      setLocalSelectedIds(selectedSectorIds);
    }
  }, [isOpen, selectedSectorIds]);

  // Filter sectors based on search term
  const filteredSectors = sectors.filter(
    (sector) =>
      sector.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (sector.description &&
        sector.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleSectorToggle = (sectorId: string) => {
    setLocalSelectedIds((prev) =>
      prev.includes(sectorId)
        ? prev.filter((id) => id !== sectorId)
        : [...prev, sectorId]
    );
  };

  const handleSave = async () => {
    await onSectorsChange(localSelectedIds);
    onClose();
  };

  const handleCancel = () => {
    setLocalSelectedIds(selectedSectorIds);
    onClose();
  };

  // Calculate select all state for filtered sectors
  const filteredSectorIds = filteredSectors.map((sector) => sector.id);
  const allFilteredSelected =
    filteredSectorIds.length > 0 &&
    filteredSectorIds.every((id) => localSelectedIds.includes(id));
  const someFilteredSelected = filteredSectorIds.some((id) =>
    localSelectedIds.includes(id)
  );
  const isIndeterminate = someFilteredSelected && !allFilteredSelected;

  // Handle select all toggle for filtered sectors
  const handleSelectAllToggle = () => {
    if (allFilteredSelected) {
      // Unselect all filtered sectors
      setLocalSelectedIds((prev) =>
        prev.filter((id) => !filteredSectorIds.includes(id))
      );
    } else {
      // Select all filtered sectors
      setLocalSelectedIds((prev) => {
        const newIds = [...prev];
        filteredSectorIds.forEach((id) => {
          if (!newIds.includes(id)) {
            newIds.push(id);
          }
        });
        return newIds;
      });
    }
  };

  return (
    <Modal
      title="Escolher Setores"
      isOpen={isOpen}
      onClose={handleCancel}
      showCloseButton={true}
    >
      <div className="space-y-4">
        {/* Search Box */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Buscar setores..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>

        {/* Select All Header */}
        {filteredSectors.length > 0 && (
          <div className="flex items-center gap-3 px-3 py-2 bg-gray-50 rounded-lg border border-gray-200">
            <div className="relative">
              <input
                type="checkbox"
                checked={allFilteredSelected}
                ref={(input) => {
                  if (input) input.indeterminate = isIndeterminate;
                }}
                onChange={handleSelectAllToggle}
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              />
            </div>
            <span className="text-sm font-medium text-gray-700">
              {allFilteredSelected
                ? `Todos selecionados (${filteredSectorIds.filter((id) => localSelectedIds.includes(id)).length})`
                : someFilteredSelected
                  ? `${filteredSectorIds.filter((id) => localSelectedIds.includes(id)).length} de ${filteredSectors.length} selecionados`
                  : "Selecionar todos"}
            </span>
          </div>
        )}

        {/* Sectors List */}
        <div className="max-h-96 space-y-1">
          {filteredSectors.map((sector) => (
            <div
              key={sector.id}
              className="flex items-center gap-2 p-2 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
              onClick={() => handleSectorToggle(sector.id)}
            >
              <input
                type="checkbox"
                checked={localSelectedIds.includes(sector.id)}
                readOnly
                className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded pointer-events-none"
              />
              <div className="flex-1">
                <div className="font-medium text-gray-900">{sector.name}</div>
                {sector.description && (
                  <div className="text-sm text-gray-500">
                    {sector.description}
                  </div>
                )}
              </div>
            </div>
          ))}
          {filteredSectors.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              Nenhum setor encontrado
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button onClick={handleCancel} variant="secondary">
            Cancelar
          </Button>
          <Button onClick={handleSave} variant="primary">
            Salvar ({localSelectedIds.length} selecionados)
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default ChooseSectorsDialog;
