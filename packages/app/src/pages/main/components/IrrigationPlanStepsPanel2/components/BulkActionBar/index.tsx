import { Edit } from "lucide-react";
import { BulkActionBarProps } from "../../types";
import Button from "@/components/ui/Button";

/**
 * Component that handles bulk operation controls.
 * Shows when steps are selected and displays edit button with selection count.
 */
function BulkActionBar({
  selectedCount,
  readOnly,
  onBulkEdit,
}: BulkActionBarProps) {
  if (selectedCount === 0) {
    return null;
  }

  return (
    <div className="flex justify-center">
      <Button
        onClick={onBulkEdit}
        disabled={readOnly}
        icon={<Edit className="h-4 w-4" />}
        fullWidth
      >
        Editar Selecionados ({selectedCount})
      </Button>
    </div>
  );
}

export default BulkActionBar;
