import {
  WATER_PUMP_TYPE_LABELS,
  type WaterPumpType,
} from "@/api/model/water-pump";
import Modal from "@/components/Modal";
import {
  appShell,
  devicesAtom,
  waterPumpsAtom,
  propertyDevicesAtom,
} from "@/store";
import { dayjs } from "@/utils/date";
import {
  resolveDeviceDisplayInfo,
  formatDeviceDisplayText,
  formatLICDisplayText,
  type ExtendedDeviceDisplayInfo,
} from "@/utils/device-label-utils";
import { useAtomValue, useSetAtom } from "jotai";
import { Clock, Droplets, Play, Square, Timer } from "lucide-react";
import { useEffect, useMemo, useState } from "react";

interface ConsolidatedWaterPump {
  id: string;
  metadata: any;
  notes: string | null;
  label: string;
  identifier: string;
  pump_type: WaterPumpType;
  pump_model: string;
  water_pump_controller:
    | {
        id: string;
        metadata: any;
        notes: string | null;
        identifier: string;
        model: "LIC" | "WPC-PL10" | "WPC-PL50" | "VC" | "RM";
      }
    | undefined;
  water_pump_controller_display_info: ExtendedDeviceDisplayInfo | null;
  current_state: {
    status: "on" | "off";
    time_left_minutes: number | null;
    start_time: string | null;
  };
  last_activity: string;
}

// function genRandomDate(maxDaysAgo: number): string {
//   const now = new Date();
//   const randomDaysAgo = Math.floor(
//     Math.random() * maxDaysAgo * 24 * 60 * 60 * 1000
//   );
//   const randomDate = new Date(now.getTime() - randomDaysAgo);
//   return randomDate.toISOString();
// }

function genRandomPumpState(): {
  status: "on" | "off";
  time_left_minutes: number | null;
  start_time: string | null;
} {
  const status = "off";
  return {
    status,
    time_left_minutes: null,
    start_time: null,
  };
}

interface WaterPumpControlModalProps {
  isOpen: boolean;
  onClose: () => void;
  pump: ConsolidatedWaterPump;
  onAction: (action: "turn_on" | "turn_off", duration?: number) => void;
}

function WaterPumpControlModal({
  isOpen,
  onClose,
  pump,
  onAction,
}: WaterPumpControlModalProps) {
  const [duration, setDuration] = useState<string>("30");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isRunning = pump.current_state.status === "on";

  const handleTurnOn = async () => {
    const durationNum = parseInt(duration);
    if (isNaN(durationNum) || durationNum <= 0) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onAction("turn_on", durationNum);
      onClose();
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTurnOff = async () => {
    setIsSubmitting(true);
    try {
      await onAction("turn_off");
      onClose();
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatTimeLeft = (minutes: number | null) => {
    if (!minutes) return null;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Acionamento da Bomba"
      size="md"
    >
      <div className="space-y-6">
        {/* Pump Info */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-2">{pump.label}</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Status:</span>
              <span
                className={`ml-2 font-medium ${
                  isRunning ? "text-green-600" : "text-gray-600"
                }`}
              >
                {isRunning ? "Ligada" : "Desligada"}
              </span>
            </div>
            <div>
              <span className="text-gray-500">Tipo:</span>
              <span className="ml-2 text-gray-900">
                {WATER_PUMP_TYPE_LABELS[pump.pump_type] || pump.pump_type}
              </span>
            </div>
            {isRunning && pump.current_state.time_left_minutes && (
              <div className="col-span-2">
                <span className="text-gray-500">Tempo Restante:</span>
                <span className="ml-2 font-medium text-green-600">
                  {formatTimeLeft(pump.current_state.time_left_minutes)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Turn On Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <Play className="w-4 h-4 text-green-600" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-gray-900">Ligar Bomba</h4>
              <p className="text-sm text-gray-600">
                Defina o tempo de funcionamento em minutos
              </p>
            </div>
          </div>

          <div className="flex items-end gap-3">
            <div className="flex-1">
              <label
                htmlFor="duration"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Duração (minutos)
              </label>
              <div className="relative">
                <Timer className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                <input
                  id="duration"
                  type="number"
                  min="1"
                  max="480"
                  value={duration}
                  onChange={(e) => setDuration(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="30"
                />
              </div>
            </div>
            <button
              onClick={handleTurnOn}
              disabled={isSubmitting || !duration || parseInt(duration) <= 0}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? "Ligando..." : "Ligar"}
            </button>
          </div>

          <div className="text-xs text-gray-500">
            Tempo máximo: 8 horas (480 minutos)
          </div>
        </div>

        {/* Turn Off Section */}
        <div className="border-t pt-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
              <Square className="w-4 h-4 text-red-600" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-gray-900">Desligar Bomba</h4>
              <p className="text-sm text-gray-600">
                Interrompe o funcionamento imediatamente
              </p>
            </div>
            <button
              onClick={handleTurnOff}
              disabled={isSubmitting}
              className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? "Desligando..." : "Desligar"}
            </button>
          </div>
        </div>
      </div>
    </Modal>
  );
}

function WaterPumpPage() {
  const setBackButton = useSetAtom(appShell.backButtonAtom);
  const [selectedPump, setSelectedPump] =
    useState<ConsolidatedWaterPump | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    setBackButton(false);
  });

  const waterPumps = useAtomValue(waterPumpsAtom);
  const devices = useAtomValue(devicesAtom);
  const propertyDevices = useAtomValue(propertyDevicesAtom);

  const consolidatedData = useMemo(() => {
    return waterPumps.map((pump) => {
      const device = devices.find((d) => d.id === pump.water_pump_controller);
      const controllerDisplayInfo = resolveDeviceDisplayInfo(
        pump.water_pump_controller,
        propertyDevices
      );
      const randomPumpState = genRandomPumpState();
      return {
        ...pump,
        water_pump_controller: device,
        water_pump_controller_display_info: controllerDisplayInfo,
        current_state: randomPumpState,
        last_activity: "", //randomPumpState.start_time ?? genRandomDate(3), // Simulating last activity
      };
    });
  }, [waterPumps, devices, propertyDevices]);

  const handlePumpAction = async (
    action: "turn_on" | "turn_off",
    duration?: number
  ) => {
    if (!selectedPump) return;

    // Here you would make the actual API call to control the pump
    console.log(
      `${action} pump ${selectedPump.id}`,
      duration ? `for ${duration} minutes` : ""
    );

    // For now, just log the action - in a real app you'd call your API
    // Example: await pumpService.controlPump(selectedPump.id, action, duration);
  };

  const handleOpenModal = (pump: ConsolidatedWaterPump) => {
    setSelectedPump(pump);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPump(null);
  };

  return (
    <div className="p-4 pb-20 bg-gray-50 min-h-screen">
      <h1 className="text-2xl font-bold text-gray-900 mb-2">Bombas de Água</h1>

      {consolidatedData.length === 0 ? (
        <div className="bg-white rounded-xl border border-gray-200 p-8 text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Droplets className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhuma bomba cadastrada
          </h3>
          <p className="text-gray-600">
            Cadastre bombas de água para monitorar e controlar a irrigação.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {consolidatedData.map((pump) => (
            <WaterPumpCard
              key={pump.id}
              pump={pump}
              onClick={() => handleOpenModal(pump)}
            />
          ))}
        </div>
      )}

      {/* Control Modal */}
      {selectedPump && (
        <WaterPumpControlModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          pump={selectedPump}
          onAction={handlePumpAction}
        />
      )}
    </div>
  );
}

interface WaterPumpCardProps {
  pump: ConsolidatedWaterPump;
  onClick: () => void;
}

function WaterPumpCard({ pump, onClick }: WaterPumpCardProps) {
  const isRunning = pump.current_state.status === "on";
  const [isModalOpen, setIsModalOpen] = useState(false);

  const getStatusColor = (status: "on" | "off") => {
    return status === "on"
      ? "bg-green-100 text-green-800 border-green-200"
      : "bg-gray-100 text-gray-600 border-gray-200";
  };

  const getStatusIcon = (status: "on" | "off") => {
    return status === "on" ? (
      <Play className="w-3 h-3" />
    ) : (
      <Square className="w-3 h-3" />
    );
  };

  const formatTimeLeft = (minutes: number | null) => {
    if (!minutes) return null;

    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const formatLastActivity = (isoString: string) => {
    return dayjs(isoString).fromNow();
  };

  const getDeviceModelShort = (model: string) => {
    switch (model) {
      case "LIC":
        return "LIC";
      case "WPD":
        return "WPD";
      case "VD":
        return "VD";
      default:
        return model;
    }
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div
      className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer"
      onClick={onClick}
    >
      <div className="p-4">
        {/* Header Row */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3">
            <div>
              <h3 className="font-semibold text-gray-900">{pump.label}</h3>
              <p className="text-xs text-gray-500">{pump.identifier}</p>
            </div>
          </div>

          {/* Status Badge */}
          <div
            className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border ${getStatusColor(
              pump.current_state.status
            )}`}
          >
            {getStatusIcon(pump.current_state.status)}
            {isRunning ? "Ligada" : "Desligada"}
          </div>
        </div>

        {/* Info Grid */}
        <div className="grid grid-cols-3 lg:grid-cols-5 gap-3 text-xs">
          {/* Pump Type */}
          <div>
            <p className="text-gray-500 mb-1">Tipo</p>
            <p className="text-gray-900 font-medium">
              {WATER_PUMP_TYPE_LABELS[pump.pump_type] || pump.pump_type}
            </p>
          </div>

          {/* Device Controller */}
          <div>
            <p className="text-gray-500 mb-1">Controlador</p>
            <p className="text-gray-900 font-medium">
              {pump.water_pump_controller_display_info
                ? formatDeviceDisplayText(
                    pump.water_pump_controller_display_info
                  )
                : pump.water_pump_controller
                ? getDeviceModelShort(pump.water_pump_controller.model)
                : "N/A"}
            </p>
          </div>

          {/* LIC Association */}
          <div>
            <p className="text-gray-500 mb-1">LIC</p>
            <p className="text-gray-900 font-medium">
              {pump.water_pump_controller_display_info?.licInfo
                ? formatLICDisplayText(
                    pump.water_pump_controller_display_info.licInfo
                  )
                : "N/A"}
            </p>
          </div>

          {/* Last Activity */}
          <div>
            <p className="text-gray-500 mb-1">Última Atividade</p>
            <p className="text-gray-900 font-medium">
              {pump.last_activity && formatLastActivity(pump.last_activity)}
            </p>
          </div>

          {/* Time Left or Model */}
          <div>
            {isRunning && pump.current_state.time_left_minutes ? (
              <>
                <p className="text-green-600 mb-1">Tempo Restante</p>
                <p className="text-green-800 font-bold">
                  {formatTimeLeft(pump.current_state.time_left_minutes)}
                </p>
              </>
            ) : (
              <>
                <p className="text-gray-500 mb-1">Modelo</p>
                <p className="text-gray-900 font-medium truncate">
                  {pump.pump_model}
                </p>
              </>
            )}
          </div>
        </div>

        {/* Running State Banner */}
        {isRunning && (
          <div className="mt-3 bg-green-50 border border-green-200 rounded-md p-2">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs font-medium text-green-800">
                  Em Funcionamento
                </p>
                {pump.current_state.start_time && (
                  <p className="text-xs text-green-700">
                    Iniciada:{" "}
                    {dayjs(pump.current_state.start_time).format("HH:mm")}
                  </p>
                )}
              </div>
              <Clock className="w-3 h-3 text-green-600" />
            </div>
          </div>
        )}

        {/* Notes */}
        {pump.notes && (
          <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-xs text-blue-700 line-clamp-2">{pump.notes}</p>
          </div>
        )}
      </div>

      {/* Water Pump Control Modal */}
      <WaterPumpControlModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        pump={pump}
        onAction={async (action, duration) => {
          // Handle turn on/off action
          console.log(`Action: ${action}, Duration: ${duration}`);
          // Here you would typically call an API to perform the action
          // await api.turnOnPump(pump.id, duration);
          // or
          // await api.turnOffPump(pump.id);
        }}
      />
    </div>
  );
}

export default WaterPumpPage;
