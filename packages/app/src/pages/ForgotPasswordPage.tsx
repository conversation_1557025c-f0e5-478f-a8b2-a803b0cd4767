import LogoSymbol from "@/assets/logo-symbol-transparent-79x96.png";
import LogoWordmark from "@/assets/logo-wordmark-256x48.png";
import { useToast } from "@/components";
import Button from "@/components/ui/Button";
import { apiService } from "@/api/service";
import React, { useState } from "react";
import { useLocation } from "wouter";

function ForgotPasswordPage() {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { showSuccess, showError } = useToast();
  const [, setLocation] = useLocation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Request password reset
      await apiService.auth.requestPasswordReset(email);

      showSuccess({
        title: "Email Enviado",
        message:
          "Verifique sua caixa de entrada para instruções de redefinição de senha.",
      });

      // Redirect to check email page
      setLocation("/check-email");
    } catch (err) {
      if (err instanceof Error) {
        showError({
          title: "Erro ao Enviar Email",
          message:
            err.message ||
            "Não foi possível enviar o email de redefinição. Tente novamente.",
        });
      } else {
        showError({
          title: "Erro ao Enviar Email",
          message:
            "Não foi possível enviar o email de redefinição. Tente novamente.",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="h-full overflow-y-auto px-4 flex flex-col items-center justify-center"
      style={{
        background:
          "linear-gradient(0deg, #18181c 0%, #121b23 40%, #042438 100%)",
      }}
    >
      <div className="w-full max-w-md mt-4">
        {/* Logo and Header */}
        <div className="text-center mb-4">
          <div className="mx-auto flex flex-col items-center text-center">
            <img
              src={LogoWordmark}
              alt="Irriga+ Wordmark"
              className="h-8 mb-2"
            />
          </div>
          <p className="text-gray-50">Sistema de Controle de Irrigação</p>
        </div>

        {/* Forgot Password Form */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-8 relative">
          <img
            src={LogoSymbol}
            alt="Description"
            className="absolute top-4 right-4 h-16 object-cover"
          />
          <h2 className="text-2xl font-semibold text-gray-900 mb-4 text-center">
            Redefinir Senha
          </h2>
          <p className="text-gray-600 text-center mb-6">
            Digite seu email e enviaremos instruções para redefinir sua senha.
          </p>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label
                className="block text-sm font-medium text-gray-700 mb-2"
                htmlFor="email"
              >
                Email
              </label>
              <input
                id="email"
                type="email"
                className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500 transition-colors"
                placeholder="Digite seu email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                autoComplete="email"
              />
            </div>

            <Button
              type="submit"
              disabled={isLoading}
              variant="primary"
              className="w-full shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              loading={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Enviando...
                </div>
              ) : (
                "Enviar Instruções"
              )}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Lembrou sua senha?{" "}
              <Button
                type="button"
                variant="ghost"
                className="font-medium"
                onClick={() => setLocation("/login")}
              >
                Voltar ao login
              </Button>
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-xs text-gray-500">
          <p>Sistema de Gestão de Irrigação Automatizada</p>
          <p className="mt-1">
            Para propriedades rurais de pequeno e médio porte
          </p>
        </div>
      </div>
    </div>
  );
}

export default ForgotPasswordPage;
