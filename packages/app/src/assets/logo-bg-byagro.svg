<?xml version="1.0" encoding="UTF-8"?>
<svg id="Camada_2" data-name="Camada 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 156.12 146.85">
  <defs>
    <style>
      .cls-1 {
        fill: url(#Gradiente_sem_nome_4);
      }
    </style>
    <linearGradient id="Gradiente_sem_nome_4" data-name="Gradiente sem nome 4" x1="78.06" y1="146.85" x2="78.06" y2=".28" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#18181c"/>
      <stop offset=".4" stop-color="#121b23"/>
      <stop offset="1" stop-color="#042438"/>
    </linearGradient>
  </defs>
  <g id="Camada_1-2" data-name="Camada 1">
    <rect class="cls-1" width="156.12" height="146.85"/>
  </g>
</svg>