import { useState } from 'react';
import type { AUTDevice, AUTWaterPump } from '@/api/queries/account';
import type { DeviceWithMapping } from '@/utils/mesh-device-utils';

export type ModalMode = 'create' | 'edit';

export function useHardwareModals() {
  const [isDeviceModalOpen, setIsDeviceModalOpen] = useState(false);
  const [isPumpModalOpen, setIsPumpModalOpen] = useState(false);
  const [isManageNetworkModalOpen, setIsManageNetworkModalOpen] = useState(false);
  const [isAssignToLICModalOpen, setIsAssignToLICModalOpen] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<AUTDevice | null>(null);
  const [selectedPump, setSelectedPump] = useState<AUTWaterPump | null>(null);
  const [selectedEnhancedDevice, setSelectedEnhancedDevice] = useState<DeviceWithMapping | null>(null);
  const [modalMode, setModalMode] = useState<ModalMode>('create');

  const openDeviceModal = (device: AUTDevice | null, enhancedDevice: DeviceWithMapping | null, mode: ModalMode) => {
    setSelectedDevice(device);
    setSelectedEnhancedDevice(enhancedDevice);
    setModalMode(mode);
    setIsDeviceModalOpen(true);
  };

  const closeDeviceModal = () => {
    setIsDeviceModalOpen(false);
    setSelectedDevice(null);
    setSelectedEnhancedDevice(null);
  };

  const openPumpModal = (pump: AUTWaterPump | null, mode: ModalMode) => {
    setSelectedPump(pump);
    setModalMode(mode);
    setIsPumpModalOpen(true);
  };

  const closePumpModal = () => {
    setIsPumpModalOpen(false);
    setSelectedPump(null);
  };

  const openManageNetworkModal = (device: DeviceWithMapping) => {
    setSelectedEnhancedDevice(device);
    setIsManageNetworkModalOpen(true);
  };

  const closeManageNetworkModal = () => {
    setIsManageNetworkModalOpen(false);
    setSelectedEnhancedDevice(null);
  };

  const openAssignToLICModal = (device: DeviceWithMapping) => {
    setSelectedEnhancedDevice(device);
    setIsDeviceModalOpen(false);
    setIsAssignToLICModalOpen(true);
  };

  const closeAssignToLICModal = () => {
    setIsAssignToLICModalOpen(false);
    setSelectedEnhancedDevice(null);
  };

  return {
    // State
    isDeviceModalOpen,
    isPumpModalOpen,
    isManageNetworkModalOpen,
    isAssignToLICModalOpen,
    selectedDevice,
    selectedPump,
    selectedEnhancedDevice,
    modalMode,
    // Actions
    openDeviceModal,
    closeDeviceModal,
    openPumpModal,
    closePumpModal,
    openManageNetworkModal,
    closeManageNetworkModal,
    openAssignToLICModal,
    closeAssignToLICModal,
    setSelectedEnhancedDevice,
  };
}