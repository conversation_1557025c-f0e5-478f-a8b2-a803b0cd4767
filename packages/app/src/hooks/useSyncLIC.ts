/**
 * A react hook for syncing LIC (Local Integration Controller) configurations.
 * It will create device message requests to sync the configurations in intervals.
 * The device message requests created will be in this order: "config" | "devices" | "scheduling" | "dev_scheduling", with a configurable interval between each request creation defaulting to 5 seconds.
 * There will be a state holding the current status that can be:
 * - config (device_message_request of payload_type=config created)
 * - devices (device_message_request of payload_type=devices created)
 * - scheduling (device_message_request of payload_type=scheduling created)
 * - dev_scheduling (device_message_request of payload_type=dev_scheduling created)
 * - finished (no device_message_request creation, indicates the completion of the sync process)
 *
 * For creating a device message request, this atom will be used:
 * const createDeviceMessageRequest = useSetAtom(createDeviceMessageRequestAtom);
 */

import { useCallback, useEffect, useState } from "react";
import { useSetAtom } from "jotai";
import { createDeviceMessageRequestAtom } from "@/store/crud";
import type { DeviceMessageRequestPayloadType } from "@/api/model/device-message-request";

type SyncStatus =
  | "config"
  | "devices"
  | "scheduling"
  | "dev_scheduling"
  | "finished";

interface UseSyncLICOptions {
  deviceId?: string;
  interval?: number;
}

interface UseSyncLICReturn {
  status: SyncStatus | null;
  isRunning: boolean;
  currentIndex: number;
  start: () => void;
  stop: () => void;
  reset: () => void;
}

export const SYNC_SEQUENCE: DeviceMessageRequestPayloadType[] = [
  "config",
  "devices",
  "scheduling",
  "automation",
];

export const useSyncLIC = ({
  deviceId,
  interval = 5000,
}: UseSyncLICOptions): UseSyncLICReturn => {
  const [status, setStatus] = useState<SyncStatus | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);

  const createDeviceMessageRequest = useSetAtom(createDeviceMessageRequestAtom);

  const createRequest = useCallback(
    async (index: number) => {
      if (index >= SYNC_SEQUENCE.length) {
        setStatus("finished");
        setIsRunning(false);
        return;
      }

      const payloadType = SYNC_SEQUENCE[index];
      console.log("Creating request for payload type:", {
        device: deviceId,
        payload_type: payloadType,
        currentIndex: index,
      });
      setStatus(payloadType as SyncStatus);

      try {
        await createDeviceMessageRequest({
          device: deviceId,
          payload_type: payloadType,
        });

        const nextIndex = index + 1;
        console.log("Current index updated:", index, nextIndex);
        setCurrentIndex(nextIndex);
      } catch (error) {
        console.error("Failed to create device message request:", error);
        setIsRunning(false);
      }
    },
    [deviceId, createDeviceMessageRequest]
  );

  // Effect to handle the sequence progression
  useEffect(() => {
    if (!isRunning || !deviceId) return;

    if (currentIndex >= SYNC_SEQUENCE.length) {
      setStatus("finished");
      setIsRunning(false);
      return;
    }

    if (currentIndex === 0) {
      // Start immediately for the first request
      createRequest(currentIndex);
    } else {
      // Use interval for subsequent requests
      const id = setTimeout(() => {
        createRequest(currentIndex);
      }, interval);
      setTimeoutId(id);

      return () => {
        clearTimeout(id);
      };
    }
  }, [isRunning, currentIndex, deviceId, interval, createRequest]);

  const start = useCallback(() => {
    if (isRunning || !deviceId) return;

    setIsRunning(true);
    setCurrentIndex(0);
  }, [isRunning, deviceId]);

  const stop = useCallback(() => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    setIsRunning(false);
  }, [timeoutId]);

  const reset = useCallback(() => {
    stop();
    setStatus(null);
    setCurrentIndex(0);
  }, [stop]);

  useEffect(() => {
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [timeoutId]);

  return {
    status,
    isRunning,
    currentIndex,
    start,
    stop,
    reset,
  };
};
