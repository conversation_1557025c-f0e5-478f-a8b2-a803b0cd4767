import { useState, useEffect } from "react";

interface PWAInstallPrompt {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: "accepted" | "dismissed" }>;
}

interface UsePWAReturn {
  isInstallable: boolean;
  isInstalled: boolean;
  isOnline: boolean;
  install: () => Promise<boolean>;
  showInstallPrompt: boolean;
  dismissInstallPrompt: () => void;
}

declare global {
  interface WindowEventMap {
    beforeinstallprompt: Event & PWAInstallPrompt;
  }
}

export function usePWA(): UsePWAReturn {
  const [deferredPrompt, setDeferredPrompt] = useState<PWAInstallPrompt | null>(
    null
  );
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);

  useEffect(() => {
    // Check if already installed
    if (
      window.matchMedia("(display-mode: standalone)").matches ||
      (window.navigator as any).standalone === true
    ) {
      setIsInstalled(true);
    }

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e: Event & PWAInstallPrompt) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setIsInstallable(true);

      // Show install prompt after a delay (don't be too aggressive)
      setTimeout(() => {
        const hasSeenPrompt = localStorage.getItem("pwa-install-prompt-seen");
        if (!hasSeenPrompt) {
          setShowInstallPrompt(true);
        }
      }, 60000); // Show after 1 minute
    };

    // Listen for successful installation
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsInstallable(false);
      setShowInstallPrompt(false);
      setDeferredPrompt(null);
    };

    // Listen for online/offline status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
    window.addEventListener("appinstalled", handleAppInstalled);
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener(
        "beforeinstallprompt",
        handleBeforeInstallPrompt
      );
      window.removeEventListener("appinstalled", handleAppInstalled);
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  const install = async (): Promise<boolean> => {
    if (!deferredPrompt) return false;

    try {
      await deferredPrompt.prompt();
      const choiceResult = await deferredPrompt.userChoice;

      if (choiceResult.outcome === "accepted") {
        setIsInstalled(true);
        setIsInstallable(false);
        setShowInstallPrompt(false);
        setDeferredPrompt(null);
        return true;
      }
    } catch (error) {
      console.error("Error installing PWA:", error);
    }

    return false;
  };

  const dismissInstallPrompt = () => {
    setShowInstallPrompt(false);
    localStorage.setItem("pwa-install-prompt-seen", "true");
    // Allow showing again after 7 days
    setTimeout(() => {
      localStorage.removeItem("pwa-install-prompt-seen");
    }, 7 * 24 * 60 * 60 * 1000);
  };

  return {
    isInstallable,
    isInstalled,
    isOnline,
    install,
    showInstallPrompt,
    dismissInstallPrompt,
  };
}

// Service Worker registration utility
export const registerSW =
  async (): Promise<ServiceWorkerRegistration | null> => {
    if ("serviceWorker" in navigator) {
      try {
        const registration = await navigator.serviceWorker.register("/sw.js");
        console.log("SW registered:", registration);

        // Listen for updates
        registration.addEventListener("updatefound", () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener("statechange", () => {
              if (
                newWorker.state === "installed" &&
                navigator.serviceWorker.controller
              ) {
                // New service worker installed, show update notification
                if (
                  window.confirm("Nova versão disponível. Atualizar agora?")
                ) {
                  window.location.reload();
                }
              }
            });
          }
        });

        return registration;
      } catch (error) {
        console.error("SW registration failed:", error);
        return null;
      }
    }
    return null;
  };

// Background sync utility for offline actions
export const addToSyncQueue = async (
  action: string,
  data: any
): Promise<void> => {
  if (
    "serviceWorker" in navigator &&
    "sync" in window.ServiceWorkerRegistration.prototype
  ) {
    try {
      // Store data in IndexedDB for background sync
      await storeOfflineAction(action, data);

      // Register background sync
      const registration = await navigator.serviceWorker.ready;
      // Type assertion for sync API which may not be in all TypeScript definitions
      await (registration as any).sync?.register(action);
    } catch (error) {
      console.error("Background sync registration failed:", error);
    }
  }
};

// Simple IndexedDB wrapper for storing offline actions
const storeOfflineAction = async (action: string, data: any): Promise<void> => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open("IrrigaPlusOffline", 1);

    request.onerror = () => reject(request.error);

    request.onsuccess = () => {
      const db = request.result;
      const transaction = db.transaction(["actions"], "readwrite");
      const store = transaction.objectStore("actions");

      store.add({
        id: Date.now(),
        action,
        data,
        timestamp: new Date().toISOString(),
      });

      transaction.oncomplete = () => resolve();
      transaction.onerror = () => reject(transaction.error);
    };

    request.onupgradeneeded = () => {
      const db = request.result;
      const store = db.createObjectStore("actions", { keyPath: "id" });
      store.createIndex("action", "action", { unique: false });
      store.createIndex("timestamp", "timestamp", { unique: false });
    };
  });
};

// Push notification utilities
export const subscribeToPushNotifications =
  async (): Promise<PushSubscription | null> => {
    if ("serviceWorker" in navigator && "PushManager" in window) {
      try {
        const registration = await navigator.serviceWorker.ready;
        const permission = await Notification.requestPermission();

        if (permission === "granted") {
          const subscription = await registration.pushManager.subscribe({
            userVisibleOnly: true,
            applicationServerKey: process.env.VAPID_PUBLIC_KEY, // You'll need to add this
          });

          // Send subscription to your server
          await fetch("/api/push/subscribe", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(subscription),
          });

          return subscription;
        }
      } catch (error) {
        console.error("Push notification subscription failed:", error);
      }
    }
    return null;
  };
