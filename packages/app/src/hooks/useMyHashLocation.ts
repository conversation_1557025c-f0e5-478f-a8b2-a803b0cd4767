import { useSyncExternalStore, useCallback } from "react";
import { BaseSearchHook } from "wouter";

export function useMyHashLocation(): [
  string,
  (path: string, options?: { replace?: boolean }) => void
] {
  const subscribe = useCallback((callback: () => void) => {
    const handleHashChange = () => {
      callback();
    };

    window.addEventListener("hashchange", handleHashChange);
    return () => window.removeEventListener("hashchange", handleHashChange);
  }, []);

  const getSnapshot = useCallback(() => {
    const hash = window.location.hash.slice(1); // Remove the #

    if (!hash) return "/";

    // Extract only the path part (before '?')
    const pathOnly = hash.split("?")[0];

    // Ensure we have a clean path
    return pathOnly || "/";
  }, []);

  const getServerSnapshot = useCallback(() => {
    return "/";
  }, []);

  const currentPath = useSyncExternalStore(
    subscribe,
    getSnapshot,
    getServerSnapshot
  );

  const setPath = useCallback(
    (path: string, options?: { replace?: boolean }) => {
      // Remove any existing query params from the input path
      const cleanPath = path.split("?")[0];
      const newHash = `#${cleanPath}`;

      if (options?.replace) {
        // Preserve existing query params if any
        const currentSearch = window.location.search;
        window.history.replaceState(
          null,
          "",
          `${window.location.pathname}${currentSearch}${newHash}`
        );
        window.dispatchEvent(new HashChangeEvent("hashchange"));
      } else {
        window.location.hash = newHash;
      }
    },
    []
  );

  return [currentPath, setPath];
}

export const useMySearchHook: BaseSearchHook = () => {
  const search = location.search;
  const hashSearch = location.hash.split("?")[1] || "";
  const fullSearch = [search, hashSearch].filter(Boolean).join("&");
  return fullSearch ? `?${fullSearch}` : "";
};
