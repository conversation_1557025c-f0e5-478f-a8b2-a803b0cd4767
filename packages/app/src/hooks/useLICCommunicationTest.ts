import { useState, useEffect, useRef, useCallback } from 'react';
import { apiService } from '@/api/service';
import { loadCurrentLICPacketForDeviceAndType } from '@/api/queries/current-lic-packet';
import type { DeviceMessageRequest } from '@/api/model/device-message-request';
import type { CurrentLICPacket } from '@/api/model/current-lic-packet';

export type LICCommunicationTestStatus = 
  | 'idle'
  | 'created'
  | 'pending'
  | 'sent'
  | 'failed'
  | 'acknowledged';

export interface LICCommunicationTestError {
  phase: 'request_creation' | 'request_polling' | 'packet_polling';
  message: string;
  originalError?: any;
}

export interface LICCommunicationTestParams {
  deviceId: string;
  pollingInterval?: number;
  timeout?: number;
}

export interface LICCommunicationTestResult {
  status: LICCommunicationTestStatus;
  error: LICCommunicationTestError | null;
  deviceMessageRequest: DeviceMessageRequest | null;
  currentPacket: CurrentLICPacket | null;
  isPolling: boolean;
  startTest: () => void;
  stopTest: () => void;
  resetTest: () => void;
}

export function useLICCommunicationTest({
  deviceId,
  pollingInterval = 2000,
  timeout = 30000,
}: LICCommunicationTestParams): LICCommunicationTestResult {
  const [status, setStatus] = useState<LICCommunicationTestStatus>('idle');
  const [error, setError] = useState<LICCommunicationTestError | null>(null);
  const [deviceMessageRequest, setDeviceMessageRequest] = useState<DeviceMessageRequest | null>(null);
  const [currentPacket, setCurrentPacket] = useState<CurrentLICPacket | null>(null);
  const [isPolling, setIsPolling] = useState(false);

  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const pollingRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const abortRef = useRef(false);

  const cleanup = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
    if (pollingRef.current) {
      clearTimeout(pollingRef.current);
      pollingRef.current = undefined;
    }
    setIsPolling(false);
    abortRef.current = true;
  }, []);

  const resetTest = useCallback(() => {
    cleanup();
    setStatus('idle');
    setError(null);
    setDeviceMessageRequest(null);
    setCurrentPacket(null);
    abortRef.current = false;
  }, [cleanup]);

  const stopTest = useCallback(() => {
    cleanup();
    if (status !== 'idle') {
      setStatus('idle');
    }
  }, [cleanup, status]);

  const startTest = useCallback(async () => {
    if (status !== 'idle') {
      return;
    }

    resetTest();
    setIsPolling(true);
    setStatus('created');
    abortRef.current = false;

    // Set overall timeout
    timeoutRef.current = setTimeout(() => {
      if (!abortRef.current) {
        setError({
          phase: 'request_polling',
          message: 'Communication test timed out',
        });
        setStatus('failed');
        cleanup();
      }
    }, timeout);

    try {
      // Phase 1: Create device message request
      const messageRequest = await apiService.deviceMessageRequest.create({
        device: deviceId,
        payload_type: 'request_info',
        payload_data: {},
        scheduled_at: new Date().toISOString(),
        priority: 1,
        max_attempts: 1,
      });

      if (abortRef.current) return;

      setDeviceMessageRequest(messageRequest);
      
      // Start Phase 1 polling
      await pollDeviceMessageRequest(messageRequest.id);
      
    } catch (err) {
      if (!abortRef.current) {
        setError({
          phase: 'request_creation',
          message: 'Failed to create device message request',
          originalError: err,
        });
        setStatus('failed');
        cleanup();
      }
    }
  }, [deviceId, status, timeout, resetTest, cleanup]);

  const pollDeviceMessageRequest = useCallback(async (messageRequestId: string) => {
    const poll = async () => {
      if (abortRef.current) return;

      try {
        const updatedRequest = await apiService.deviceMessageRequest.getOne(messageRequestId);
        
        if (abortRef.current) return;

        setDeviceMessageRequest(updatedRequest);

        switch (updatedRequest.status) {
          case 'pending':
          case 'processing':
            setStatus('pending');
            // Continue polling
            pollingRef.current = setTimeout(poll, pollingInterval);
            break;

          case 'failed':
          case 'expired':
          case 'cancelled':
            setError({
              phase: 'request_polling',
              message: `Device message request ${updatedRequest.status}`,
            });
            setStatus('failed');
            cleanup();
            break;

          case 'acknowledged':
            setStatus('acknowledged');
            cleanup();
            break;

          case 'sent':
            setStatus('sent');
            // Move to Phase 2
            await pollCurrentLICPacket(updatedRequest);
            break;

          default:
            // Continue polling for unknown states
            pollingRef.current = setTimeout(poll, pollingInterval);
            break;
        }
      } catch (err) {
        if (!abortRef.current) {
          setError({
            phase: 'request_polling',
            message: 'Failed to poll device message request',
            originalError: err,
          });
          setStatus('failed');
          cleanup();
        }
      }
    };

    await poll();
  }, [pollingInterval, cleanup]);

  const pollCurrentLICPacket = useCallback(async (messageRequest: DeviceMessageRequest) => {
    const poll = async () => {
      if (abortRef.current) return;

      try {
        const packet = await loadCurrentLICPacketForDeviceAndType(
          apiService,
          deviceId,
          'info'
        );

        if (abortRef.current) return;

        if (packet && new Date(packet.packet_date) > new Date(messageRequest.date_created)) {
          setCurrentPacket(packet);
          setStatus('acknowledged');
          cleanup();
        } else {
          // Continue polling
          pollingRef.current = setTimeout(poll, pollingInterval);
        }
      } catch (err) {
        if (!abortRef.current) {
          setError({
            phase: 'packet_polling',
            message: 'Failed to poll current LIC packet',
            originalError: err,
          });
          setStatus('failed');
          cleanup();
        }
      }
    };

    await poll();
  }, [deviceId, pollingInterval, cleanup]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    status,
    error,
    deviceMessageRequest,
    currentPacket,
    isPolling,
    startTest,
    stopTest,
    resetTest,
  };
}