import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import {
  createMeshDeviceMapping<PERSON>tom,
  deleteMeshDeviceMappingAtom,
  updateMeshDeviceMappingAtom,
} from "@/store";
import type { DeviceWithMapping } from "@/utils/mesh-device-utils";
import {
  createMeshDeviceMappingBatchAtom,
  updateMeshDeviceMappingBatchAtom,
} from "@/store/crud";

export function useMeshDeviceMapping() {
  const createMeshDeviceMapping = useSetAtom(createMeshDeviceMappingAtom);
  const deleteMeshDeviceMapping = useSetAtom(deleteMeshDeviceMappingAtom);
  const updateMeshDeviceMapping = useSetAtom(updateMeshDeviceMappingAtom);
  const createMeshDeviceMappingBatch = useSetAtom(
    createMeshDeviceMappingBatchAtom
  );
  const updateMeshDeviceMappingBatch = useSetAtom(
    updateMeshDeviceMappingBatchAtom
  );

  const addDeviceToLIC = async (
    meshDeviceId: string,
    licDeviceId: string,
    startDate: string,
    enhancedDevices: DeviceWithMapping[]
  ) => {
    try {
      // Find the device to check if it has an existing mapping
      const deviceToAdd = enhancedDevices.find((d) => d.id === meshDeviceId);

      // If device is already mapped, end the current mapping first
      if (deviceToAdd?.current_mesh_device_mapping) {
        await updateMeshDeviceMapping({
          id: deviceToAdd.current_mesh_device_mapping.id,
          data: {
            end_date: startDate, // End current mapping on the start date of new mapping
          },
        });
      }

      // Create the new mesh device mapping
      await createMeshDeviceMapping({
        mesh_property_device: meshDeviceId,
        lic_property_device: licDeviceId,
        start_date: startDate,
      });
    } catch (error) {
      console.error("Error adding device to LIC:", error);
      throw error;
    }
  };

  const addDevicesToLIC = async (
    items: Array<{
      meshDeviceId: string;
      licDeviceId: string;
      startDate: string;
    }>,
    enhancedDevices: DeviceWithMapping[]
  ) => {
    // Collect mappings to end
    const mappingsToEnd: Array<{ id: string; end_date: string }> = [];
    for (const { meshDeviceId, startDate } of items) {
      const device = enhancedDevices.find((d) => d.id === meshDeviceId);
      if (device?.current_mesh_device_mapping) {
        mappingsToEnd.push({
          id: device.current_mesh_device_mapping.id,
          end_date: startDate,
        });
      }
    }

    // End existing mappings in batch
    if (mappingsToEnd.length > 0) {
      try {
        await updateMeshDeviceMappingBatch(mappingsToEnd);
      } catch (error) {
        console.error("Error ending existing device mappings:", error);
        throw error;
      }
    }

    // Create new mappings in batch
    try {
      await createMeshDeviceMappingBatch(
        items.map(({ meshDeviceId, licDeviceId, startDate }) => ({
          mesh_property_device: meshDeviceId,
          lic_property_device: licDeviceId,
          start_date: startDate,
        }))
      );
    } catch (error) {
      console.error("Error adding devices to LIC:", error);
      throw error;
    }
  };

  const removeDeviceFromLIC = async (
    deviceId: string,
    enhancedDevices: DeviceWithMapping[]
  ) => {
    try {
      // Find the mesh device mapping to delete
      const deviceToRemove = enhancedDevices.find((d) => d.id === deviceId);
      if (deviceToRemove?.current_mesh_device_mapping) {
        await deleteMeshDeviceMapping(
          deviceToRemove.current_mesh_device_mapping.id
        );
      }
    } catch (error) {
      console.error("Error removing device from LIC:", error);
      throw error;
    }
  };

  return {
    addDeviceToLIC,
    addDevicesToLIC,
    removeDeviceFromLIC,
  };
}
