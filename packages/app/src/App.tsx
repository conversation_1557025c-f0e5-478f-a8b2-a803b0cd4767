import { useEffect } from "react";
import "./index.css";

import Routes from "./Routes";
import {
  PWAInstallBanner,
  NetworkStatus,
  LoadingOverlay,
  ErrorToast,
  ToastContainer,
} from "./components";
import { registerSW } from "./hooks/usePWA";
import { StoreProvider } from "./store/Provider";
import { useAtomsDebugValue } from "jotai-devtools";

function DebugAtoms() {
  useAtomsDebugValue({ enabled: true });
  return null;
}

export function App() {
  useEffect(() => {
    // Register service worker
    registerSW();
  }, []);

  return (
    <StoreProvider>
      <NetworkStatus />
      <Routes />
      <PWAInstallBanner />
      <LoadingOverlay />
      <ErrorToast />
      <DebugAtoms />
      <ToastContainer />
    </StoreProvider>
  );
}

export default App;
