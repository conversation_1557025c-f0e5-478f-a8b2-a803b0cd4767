#!/usr/bin/env sh

if [ -z "$APP_CONFIG" ]
then
  APP_CONFIG='{}'
fi

if [ -z "$PROXY_CONFIG" ]
then
  PROXY_CONFIG=$(cat <<'END_HEREDOC'
server {
  listen 80;
  # location /api/ {
    # proxy_pass https://agrocontrol-directus.saas.byagro.com.br/;
    # proxy_set_header Host $host;
  # }
  location / {
    root   /usr/share/nginx/html;
    index  index.html index.htm;
    try_files $uri $uri/ /index.html;
  }
  error_page   500 502 503 504  /50x.html;
  location = /50x.html {
    root   /usr/share/nginx/html;
  }
}
END_HEREDOC
)
fi

echo "APP_CONFIG=$APP_CONFIG"
echo "PROXY_CONFIG=$PROXY_CONFIG"

[ -n "$PROXY_CONFIG" ] && echo "$PROXY_CONFIG" > /etc/nginx/conf.d/default.conf || true
[ -n "$APP_CONFIG" ] && echo "$APP_CONFIG" > /usr/share/nginx/html/config.json || true

nginx -g "daemon off;"
