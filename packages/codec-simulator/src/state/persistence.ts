import type {
  LICSimulatorState,
  SystemConfig,
  DeviceConfig,
  ScheduleConfig,
  DeviceScheduleConfig,
  AutomationConfig,
  DeviceState,
} from './types';
import path from 'path';
import fs from 'fs/promises';
import logger from '../utils/logger';

const STATE_DATA_DIR = 'state-data';

export class LICStatePersistence {
  private codecId: string;
  private stateDirPath: string;

  constructor(codecId: string) {
    this.codecId = codecId;
    this.stateDirPath = path.join(STATE_DATA_DIR, this.codecId);
  }

  private async ensureDirectoryExists(): Promise<void> {
    try {
      await fs.mkdir(this.stateDirPath, { recursive: true });
    } catch (error) {
      logger.error(`Failed to create state directory for ${this.codecId}`, error);
      throw error;
    }
  }

  private async readFile<T>(fileName: string, defaultValue: T): Promise<T> {
    const filePath = path.join(this.stateDirPath, fileName);
    try {
      const data = await fs.readFile(filePath, 'utf-8');
      return JSON.parse(data) as T;
    } catch (error) {
      if (error.code === 'ENOENT') {
        logger.warn(`State file ${fileName} not found for ${this.codecId}. Using default.`);
        return defaultValue;
      }
      logger.error(`Failed to read state file ${fileName} for ${this.codecId}`, error);
      throw error;
    }
  }

  async loadState(): Promise<LICSimulatorState> {
    await this.ensureDirectoryExists();

    const config = await this.readFile<SystemConfig>('config.json', this.getDefaultSystemConfig());
    const devices = await this.readFile<DeviceConfig[]>('devices.json', []);
    const schedules = await this.readFile<ScheduleConfig[]>('schedules.json', []);
    const deviceSchedules = await this.readFile<DeviceScheduleConfig[]>('deviceSchedules.json', []);
    const automation = await this.readFile<AutomationConfig[]>('automation.json', []);
    const deviceStates = await this.readFile<DeviceState[]>('deviceStates.json', []);
    
    let resets = await this.readFile<number>('resets.json', 0);
    resets++;
    await this.saveFile('resets.json', resets);


    const state: LICSimulatorState = {
      codecId: this.codecId,
      firmwareEsp: 1,
      firmwareMesh: 1,
      hardwareVersion: 1,
      resets,
      configId: await this.readFile<number>('configId.json', 0),
      devicesId: await this.readFile<number>('devicesId.json', 0),
      schedulingId: await this.readFile<number>('schedulingId.json', 0),
      devSchedulingId: await this.readFile<number>('devSchedulingId.json', 0),
      automationId: await this.readFile<number>('automationId.json', 0),
      config,
      devices,
      schedules,
      deviceSchedules,
      automation,
      deviceStates,
      schedulingRunning: 0,
      schedulingPaused: false,
    };

    return state;
  }

  async saveState(state: LICSimulatorState): Promise<void> {
    await this.ensureDirectoryExists();

    await this.saveFile('config.json', state.config);
    await this.saveFile('devices.json', state.devices);
    await this.saveFile('schedules.json', state.schedules);
    await this.saveFile('deviceSchedules.json', state.deviceSchedules);
    await this.saveFile('automation.json', state.automation);
    await this.saveFile('deviceStates.json', state.deviceStates);
    await this.saveFile('resets.json', state.resets);
    await this.saveFile('configId.json', state.configId);
    await this.saveFile('devicesId.json', state.devicesId);
    await this.saveFile('schedulingId.json', state.schedulingId);
    await this.saveFile('devSchedulingId.json', state.devSchedulingId);
    await this.saveFile('automationId.json', state.automationId);
  }

  private getDefaultSystemConfig(): SystemConfig {
    return {
      pipe_wash_time_seconds: 0,
      backwash_duration_seconds: 0,
      backwash_period_seconds: 0,
      rain_gauge_enabled: false,
      rain_gauge_resolution_mm: 0,
      precipitation_volume_limit_mm: 0,
      precipitation_suspended_duration_hours: 0,
    };
  }

  private async saveFile(fileName: string, data: any): Promise<void> {
    const filePath = path.join(this.stateDirPath, fileName);
    try {
      await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');
    } catch (error) {
      logger.error(`Failed to save state file ${fileName} for ${this.codecId}`, error);
      throw error;
    }
  }
}
