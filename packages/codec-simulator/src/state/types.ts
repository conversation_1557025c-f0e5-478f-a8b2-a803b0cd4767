
export interface LICSimulatorState {
  // Device identification & metadata
  codecId: string;
  firmwareEsp: number;
  firmwareMesh: number;
  hardwareVersion: number;
  resets: number;

  // Configuration timestamps for sync
  configId: number;
  devicesId: number;
  schedulingId: number;
  devSchedulingId: number;
  automationId: number;

  // Configuration data
  config: SystemConfig;
  devices: DeviceConfig[];
  schedules: ScheduleConfig[];
  deviceSchedules: DeviceScheduleConfig[];
  automation: AutomationConfig[];

  // Runtime state
  deviceStates: DeviceState[];
  schedulingRunning: number;
  schedulingPaused: boolean;
}

export interface SystemConfig {
  pipe_wash_time_seconds: number;
  backwash_duration_seconds: number;
  backwash_period_seconds: number;
  rain_gauge_enabled: boolean;
  rain_gauge_resolution_mm: number;
  precipitation_volume_limit_mm: number;
  precipitation_suspended_duration_hours: number;
}

export interface DeviceConfig {
  idx: number;
  mode: number; // 0 for CONTINUOUS, 1 for PULSE
  out1: number;
  out2: number;
  input: number;
  power: number;
  sector: number;
  mesh_id: number;
  device_id: number;
  equipment: number;
  group_idx: number;
  device_type: number; // 0: Valve, 1: IrrigationPump, 2: Ferti, 3: Backwash, 4: ServicePump, 5: Level
}

export interface ScheduleConfig {
  start_time: string; // HH:MM:SS
  days_of_week: ("MON" | "TUE" | "WED" | "THU" | "FRI" | "SAT" | "SUN")[];
  is_enabled: boolean;
  fertigation_enabled: boolean;
  backwash_enabled: boolean;
}

export interface DeviceScheduleConfig {
  order: number;
  duration_seconds: number;
  fertigation_start_delay_seconds: number;
  fertigation_duration_seconds: number;
  device_idx: number;
}

export interface AutomationConfig {
  level_device_idx: number;
  pump_device_idx: number;
  safety_time_minutes: number;
}

export interface DeviceState {
  device_idx: number;
  is_on: boolean;
  timeLeft: number; // in seconds
}
