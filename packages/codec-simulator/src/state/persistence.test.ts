import { test, expect, mock, spyOn } from 'bun:test';
import { LICStatePersistence } from './persistence';
import fs from 'fs/promises';
import type { LICSimulatorState } from './types';

const mockFs = {
  mkdir: async (path: string, options: any) => {},
  readFile: async (path: string, encoding: string) => {
    if (path.endsWith('resets.json')) {
      return '0';
    }
    return '{}';
  },
  writeFile: async (path: string, data: string, encoding: string) => {},
};

mock.module('fs/promises', () => ({
  default: mockFs,
}));



test('LICStatePersistence load and save', async () => {
  const codecId = 'test-codec';
  const persistence = new LICStatePersistence(codecId);

  const mkdirSpy = spyOn(mockFs, 'mkdir');
  const readFileSpy = spyOn(mockFs, 'readFile');
  const writeFileSpy = spyOn(mockFs, 'writeFile');

  // Test loadState
  const state = await persistence.loadState();
  expect(state).toBeDefined();
  expect(state.resets).toBe(1);
  expect(mkdirSpy).toHaveBeenCalled();
  expect(readFileSpy).toHaveBeenCalled();
  expect(writeFileSpy).toHaveBeenCalledWith('state-data/test-codec/resets.json', '1', 'utf-8');

  // Test saveState
  const newState: LICSimulatorState = {
    ...state,
    configId: 123,
  };
  await persistence.saveState(newState);
  expect(writeFileSpy).toHaveBeenCalled();
});
