import type { LICSimulatorState } from './state/types';
import { LICStatePersistence } from './state/persistence';
import { MqttClient } from './mqtt/client';
import { ProtocolHandler } from './protocol/handler';
import logger from './utils/logger';
import { codec } from 'proto';
import { produce } from 'immer';
import { DeviceLogic } from './logic/device';
import { SchedulingLogic } from './logic/scheduling';
import { AutomationLogic } from './logic/automation';

export class CODEKSimulator {
  private state: LICSimulatorState;
  private persistence: LICStatePersistence;
  private mqttClient: MqttClient;
  private protocolHandler: ProtocolHandler;
  private deviceLogic: DeviceLogic;
  private schedulingLogic: SchedulingLogic;
  private automationLogic: AutomationLogic;
  private mainLoopInterval: NodeJS.Timeout | null = null;

  constructor(private codecId: string, brokerUrl: string) {
    this.persistence = new LICStatePersistence(codecId);
    this.mqttClient = new MqttClient(codecId, brokerUrl);
    this.protocolHandler = new ProtocolHandler(this.mqttClient);
    this.state = {} as LICSimulatorState; // Will be loaded
    this.deviceLogic = new DeviceLogic(this.state);
    this.schedulingLogic = new SchedulingLogic(this.state, this);
    this.automationLogic = new AutomationLogic(this.state, this);
  }

  async start() {
    this.state = await this.persistence.loadState();
    this.deviceLogic.updateState(this.state);
    this.schedulingLogic.updateState(this.state);
    this.automationLogic.updateState(this.state);
    logger.info(`Simulator started for codec ${this.codecId}`, this.state);

    this.mqttClient.setMessageHandler((topic, payload) => {
      this.protocolHandler.handleIncomingMessage(payload);
    });

    this.protocolHandler.onMessage((message) => {
      this.handleIncomingPacket(message);
    });

    this.mainLoopInterval = setInterval(() => this.mainLoop(), 1000);
  }

  private mainLoop() {
    const newState = produce(this.state, draft => {
      for (const deviceState of draft.deviceStates) {
        if (deviceState.is_on && deviceState.timeLeft > 0) {
          deviceState.timeLeft--;
          if (deviceState.timeLeft === 0) {
            deviceState.is_on = false;
            logger.info(`Device ${deviceState.device_idx} turned off automatically`);
          }
        }
      }
    });
    this.updateState(newState, false); // Don't save state on every tick
    this.schedulingLogic.tick();
    this.automationLogic.tick();
  }

  private updateState(newState: LICSimulatorState, save = true) {
    this.state = newState;
    this.deviceLogic.updateState(newState);
    this.schedulingLogic.updateState(newState);
    this.automationLogic.updateState(newState);
    if (save) {
      this.persistence.saveState(this.state);
    }
  }

  turnDeviceOn(deviceIdx: number, time: number) {
    const newState = this.deviceLogic.turnDeviceOn(deviceIdx, time);
    this.updateState(newState);
  }

  turnDeviceOff(deviceIdx: number) {
    const newState = this.deviceLogic.turnDeviceOff(deviceIdx);
    this.updateState(newState);
  }

  getInputBitmask(): number {
    return this.deviceLogic.getInputBitmask();
  }

  private handleIncomingPacket(packet: codec.in_.IIncomingPacket) {
    logger.info(`Handling incoming packet for codec ${this.codecId}`, packet);

    const payloadType = packet.payload;
    if (!payloadType) {
      logger.warn('Incoming packet with no payload type');
      return;
    }

    switch (payloadType) {
      case 'config':
        this.handleConfigPackage(packet.config!);
        break;
      case 'devices':
        this.handleDevicesPackage(packet.devices!);
        break;
      case 'scheduling':
        this.handleSchedulingPackage(packet.scheduling!);
        break;
      case 'dev_scheduling':
        this.handleDeviceSchedulingPackage(packet.dev_scheduling!);
        break;
      case 'automation':
        this.handleAutomationPackage(packet.automation!);
        break;
      case 'control':
        this.handleControlPackage(packet.control!);
        break;
      case 'command':
        this.handleCommandPackage(packet.command!);
        break;
      case 'request_info':
        this.handleRequestInfoPackage(packet.request_info!);
        break;
      default:
        logger.warn(`Unhandled payload type: ${payloadType}`);
    }
  }

  private handleConfigPackage(configPackage: codec.in_.IConfigPackage) {
    const newState = produce(this.state, draft => {
      draft.config.pipe_wash_time_seconds = configPackage.pipeWashTime ?? 0;
      draft.config.backwash_duration_seconds = configPackage.backwashTime ?? 0;
      draft.config.backwash_period_seconds = configPackage.backwashWaitTime ?? 0;
      draft.config.rain_gauge_enabled = (configPackage.rainSensor ?? 0) > 0;
      draft.config.precipitation_volume_limit_mm = configPackage.rainStop ?? 0;
      draft.config.precipitation_suspended_duration_hours = configPackage.rainWaitTime ?? 0;
      draft.configId = configPackage.id;
    });
    this.updateState(newState);
    this.sendInfoPackage();
  }

  private handleDevicesPackage(devicesPackage: codec.in_.IDevicesPackage) {
    const newState = produce(this.state, draft => {
      draft.devices = devicesPackage.devices.map(d => ({
        idx: d.idx,
        mode: d.mode,
        out1: d.out1,
        out2: d.out2,
        input: d.input,
        power: d.power,
        sector: d.sector,
        mesh_id: d.meshId,
        device_id: d.deviceId,
        equipment: d.equipment,
        group_idx: d.groupIdx,
        device_type: d.deviceType,
      }));
      draft.devicesId = devicesPackage.id;
    });
    this.updateState(newState);
    this.sendInfoPackage();
  }

  private handleSchedulingPackage(schedulingPackage: codec.in_.ISchedulingPackage) {
    const newState = produce(this.state, draft => {
      draft.schedules = schedulingPackage.schedules.map(s => ({
        start_time: `${s.hour}:${s.minute}:${s.second}`,
        days_of_week: this.daysOfWeekFromBitmask(s.days),
        is_enabled: s.enabled,
        fertigation_enabled: s.ferti,
        backwash_enabled: s.backwash,
      }));
      draft.schedulingId = schedulingPackage.id;
    });
    this.updateState(newState);
    this.sendInfoPackage();
  }

  private handleDeviceSchedulingPackage(deviceSchedulingPackage: codec.in_.IDeviceSchedulingPackage) {
    const newState = produce(this.state, draft => {
      draft.deviceSchedules = deviceSchedulingPackage.deviceSchedules.map(ds => ({
        order: ds.order,
        duration_seconds: ds.time,
        fertigation_start_delay_seconds: ds.fertiDelay,
        fertigation_duration_seconds: ds.fertiTime,
        device_idx: ds.device,
      }));
      draft.devSchedulingId = deviceSchedulingPackage.id;
    });
    this.updateState(newState);
    this.sendInfoPackage();
  }

  private handleAutomationPackage(automationPackage: codec.in_.IAutomationPackage) {
    const newState = produce(this.state, draft => {
      draft.automation = automationPackage.automations.map(a => ({
        level_device_idx: a.level,
        pump_device_idx: a.pump,
        safety_time_minutes: a.time,
      }));
      draft.automationId = automationPackage.id;
    });
    this.updateState(newState);
    this.sendInfoPackage();
  }

  private handleControlPackage(controlPackage: codec.in_.IControlPackage) {
    logger.info('Handling ControlPackage', controlPackage);
    
    let newState = this.state;
    for (const device of controlPackage.devices) {
      if (device.on) {
        newState = this.deviceLogic.turnDeviceOn(device.idx, device.time);
      } else {
        newState = this.deviceLogic.turnDeviceOff(device.idx);
      }
    }
    this.updateState(newState);
    this.sendAckPackage(controlPackage.id);
  }

  private handleCommandPackage(commandPackage: codec.in_.ICommandPackage) {
    logger.info('Handling CommandPackage', commandPackage);
    if (commandPackage.command === codec.in_.Command.PAUSE) {
      const newState = produce(this.state, draft => {
        draft.schedulingPaused = true;
      });
      this.updateState(newState);
    } else if (commandPackage.command === codec.in_.Command.RESUME) {
      const newState = produce(this.state, draft => {
        draft.schedulingPaused = false;
      });
      this.updateState(newState);
    }
    this.sendAckPackage(commandPackage.id);
  }

  private handleRequestInfoPackage(requestInfoPackage: codec.in_.IRequestInfoPackage) {
    logger.info('Handling RequestInfoPackage', requestInfoPackage);
    this.sendInfoPackage();
  }

  private sendAckPackage(id: number) {
    const ackPackage: codec.out.IAckPackage = { id };
    const outgoingPacket: codec.out.IOutgoingPacket = { ack: ackPackage };
    this.protocolHandler.sendOutgoingMessage(outgoingPacket);
  }

  private sendInfoPackage() {
    const infoPackage: codec.out.IInfoPackage = {
      id: this.state.configId,
      config: this.state.configId,
      devices: this.state.devicesId,
      scheduling: this.state.schedulingId,
      devScheduling: this.state.devSchedulingId,
      automation: this.state.automationId,
      time: Math.floor(Date.now() / 1000),
      resets: this.state.resets,
      firmwareEsp: this.state.firmwareEsp,
      firmwareMesh: this.state.firmwareMesh,
      hwVersion: this.state.hardwareVersion,
    };

    const outgoingPacket: codec.out.IOutgoingPacket = {
      info: infoPackage,
    };

    this.protocolHandler.sendOutgoingMessage(outgoingPacket);
  }

  private daysOfWeekFromBitmask(mask: number): ("MON" | "TUE" | "WED" | "THU" | "FRI" | "SAT" | "SUN")[] {
    const days: ("MON" | "TUE" | "WED" | "THU" | "FRI" | "SAT" | "SUN")[] = [];
    if (mask & 1) days.push('SUN');
    if (mask & 2) days.push('MON');
    if (mask & 4) days.push('TUE');
    if (mask & 8) days.push('WED');
    if (mask & 16) days.push('THU');
    if (mask & 32) days.push('FRI');
    if (mask & 64) days.push('SAT');
    return days;
  }

  stop() {
    this.mqttClient.end();
    if (this.mainLoopInterval) {
      clearInterval(this.mainLoopInterval);
    }
    logger.info(`Simulator stopped for codec ${this.codecId}`);
  }
}
