
import { Command } from 'commander';
import { CODEKSimulator } from './simulator';

const program = new Command();

program
  .name('codec-simulator')
  .description('A simulator for the CODEK irrigation controller.')
  .version('1.0.0');

program
  .option('--codec-id <ids...>', 'Specify one or more CODEC IDs to simulate.')
  .option('--broker-url <url>', 'MQTT broker URL', 'mqtt://localhost:1883');

program.parse(process.argv);

const options = program.opts();

if (options.codecId && options.codecId.length > 0) {
  console.log('Simulating CODEC IDs:', options.codecId);
  
  for (const codecId of options.codecId) {
    const simulator = new CODEKSimulator(codecId, options.brokerUrl);
    simulator.start();
  }

} else {
  console.log('No CODEC IDs specified. Exiting.');
  process.exit(0);
}
