import mqtt from 'mqtt';
import logger from '../utils/logger';

type MessageHandler = (topic: string, payload: Buffer) => void;

export class MqttClient {
  private client: mqtt.MqttClient;
  private codecId: string;
  private reportTopic: string;
  private downlinkTopic: string;
  private messageHandler: MessageHandler | null = null;

  constructor(codecId: string, brokerUrl: string) {
    this.codecId = codecId;
    this.reportTopic = `/codec/${this.codecId}/report`;
    this.downlinkTopic = `/codec/${this.codecId}/downlink`;
    this.client = mqtt.connect(brokerUrl);

    this.client.on('connect', () => {
      logger.info(`MQTT client connected for codec ${this.codecId}`);
      this.client.subscribe(this.downlinkTopic, (err) => {
        if (err) {
          logger.error(`Failed to subscribe to ${this.downlinkTopic}: ${err.message}`);
        } else {
          logger.info(`Subscribed to ${this.downlinkTopic}`);
        }
      });
    });

    this.client.on('message', (topic, payload) => {
      if (this.messageHandler) {
        this.messageHandler(topic, payload);
      }
    });

    this.client.on('error', (err) => {
      logger.error(`MQTT client error for codec ${this.codecId}: ${err.message}`);
    });
  }

  setMessageHandler(handler: MessageHandler) {
    this.messageHandler = handler;
  }

  publish(payload: Buffer) {
    this.client.publish(this.reportTopic, payload, (err) => {
      if (err) {
        logger.error(`Failed to publish message to ${this.reportTopic}`, err);
      } else {
        logger.info(`Published message to ${this.reportTopic}`);
      }
    });
  }

  end() {
    this.client.end();
  }
}
