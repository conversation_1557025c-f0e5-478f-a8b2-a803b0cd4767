import type { LICSimulatorState, ScheduleConfig, DeviceScheduleConfig } from '../state/types';
import logger from '../utils/logger';
import { CODEKSimulator } from '../simulator';

export class SchedulingLogic {
  private state: LICSimulatorState;
  private simulator: CODEKSimulator;
  private runningSchedule: {
    schedule: ScheduleConfig;
    steps: DeviceScheduleConfig[];
    currentStepIndex: number;
    stepTimeLeft: number;
  } | null = null;

  constructor(state: LICSimulatorState, simulator: CODEKSimulator) {
    this.state = state;
    this.simulator = simulator;
  }

  updateState(newState: LICSimulatorState) {
    this.state = newState;
  }

  tick() {
    if (this.state.schedulingPaused) {
      return;
    }

    if (this.runningSchedule) {
      this.runningSchedule.stepTimeLeft--;
      if (this.runningSchedule.stepTimeLeft <= 0) {
        this.finishStep();
        this.startNextStep();
      }
    } else {
      this.checkForDueSchedules();
    }
  }

  private checkForDueSchedules() {
    const now = new Date();
    const currentDay = this.dayOfWeekAsString(now.getDay());
    const currentTime = `${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}`;

    for (const schedule of this.state.schedules) {
      if (schedule.is_enabled && schedule.days_of_week.includes(currentDay) && schedule.start_time === currentTime) {
        this.startSchedule(schedule);
        break; // Run only one schedule at a time
      }
    }
  }

  private startSchedule(schedule: ScheduleConfig) {
    logger.info(`Starting schedule: ${JSON.stringify(schedule)}`);
    const steps = this.state.deviceSchedules.sort((a, b) => a.order - b.order);
    this.runningSchedule = {
      schedule,
      steps,
      currentStepIndex: -1,
      stepTimeLeft: 0,
    };
    this.startNextStep();
  }

  private startNextStep() {
    if (!this.runningSchedule) return;

    this.runningSchedule.currentStepIndex++;
    if (this.runningSchedule.currentStepIndex >= this.runningSchedule.steps.length) {
      this.finishSchedule();
      return;
    }

    const currentStep = this.runningSchedule.steps[this.runningSchedule.currentStepIndex];
    if (currentStep) {
      this.runningSchedule.stepTimeLeft = currentStep.duration_seconds;
      this.simulator.turnDeviceOn(currentStep.device_idx, currentStep.duration_seconds);
    }
  }

  private finishStep() {
    if (!this.runningSchedule) return;
    const currentStep = this.runningSchedule.steps[this.runningSchedule.currentStepIndex];
    if (currentStep) {
      this.simulator.turnDeviceOff(currentStep.device_idx);
    }
  }

  private finishSchedule() {
    logger.info(`Finished schedule: ${JSON.stringify(this.runningSchedule?.schedule)}`);
    // TODO: Send SchedulingReportPackage
    this.runningSchedule = null;
  }

  private dayOfWeekAsString(dayIndex: number): "MON" | "TUE" | "WED" | "THU" | "FRI" | "SAT" | "SUN" {
    return ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"][dayIndex] as "MON" | "TUE" | "WED" | "THU" | "FRI" | "SAT" | "SUN";
  }
}
