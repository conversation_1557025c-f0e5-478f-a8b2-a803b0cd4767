import type { LICSimulatorState } from '../state/types';
import logger from '../utils/logger';
import { CODEKSimulator } from '../simulator';

export class AutomationLogic {
  private state: LICSimulatorState;
  private simulator: CODEKSimulator;

  constructor(state: LICSimulatorState, simulator: CODEKSimulator) {
    this.state = state;
    this.simulator = simulator;
  }

  updateState(newState: LICSimulatorState) {
    this.state = newState;
  }

  tick() {
    const inputBitmask = this.simulator.getInputBitmask();

    for (const automation of this.state.automation) {
      const levelDevice = this.state.devices.find(d => d.idx === automation.level_device_idx);
      if (!levelDevice) continue;

      const isLevelSensorOn = (inputBitmask & (1 << levelDevice.input)) !== 0;

      if (isLevelSensorOn) {
        // Level sensor is on, so we should turn off the pump
        this.simulator.turnDeviceOff(automation.pump_device_idx);
      } else {
        // Level sensor is off, so we should turn on the pump
        this.simulator.turnDeviceOn(automation.pump_device_idx, automation.safety_time_minutes * 60);
      }
    }
  }
}
