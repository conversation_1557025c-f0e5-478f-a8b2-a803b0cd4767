import { produce } from 'immer';
import type { LICSimulatorState, DeviceState } from '../state/types';
import logger from '../utils/logger';

export class DeviceLogic {
  private state: LICSimulatorState;

  constructor(state: LICSimulatorState) {
    this.state = state;
  }

  updateState(newState: LICSimulatorState) {
    this.state = newState;
  }

  turnDeviceOn(deviceIdx: number, time: number): LICSimulatorState {
    logger.info(`Turning device ${deviceIdx} on for ${time} seconds`);
    
    const newState = produce(this.state, draft => {
      let deviceState = draft.deviceStates.find(d => d.device_idx === deviceIdx);
      if (deviceState) {
        deviceState.is_on = true;
        deviceState.timeLeft = time;
      } else {
        draft.deviceStates.push({
          device_idx: deviceIdx,
          is_on: true,
          timeLeft: time,
        });
      }
    });

    return newState;
  }

  turnDeviceOff(deviceIdx: number): LICSimulatorState {
    logger.info(`Turning device ${deviceIdx} off`);

    const newState = produce(this.state, draft => {
      let deviceState = draft.deviceStates.find(d => d.device_idx === deviceIdx);
      if (deviceState) {
        deviceState.is_on = false;
        deviceState.timeLeft = 0;
      }
    });

    return newState;
  }

  getOnBitmask(): number {
    let bitmask = 0;
    for (const deviceState of this.state.deviceStates) {
      if (deviceState.is_on) {
        bitmask |= (1 << deviceState.device_idx);
      }
    }
    return bitmask;
  }

  getInputBitmask(): number {
    // TODO: Simulate input bitmask
    return 0;
  }
}
