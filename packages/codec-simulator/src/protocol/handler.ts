import { MqttClient } from '../mqtt/client';
import logger from '../utils/logger';
import { crc16 } from './crc16';
import { codec } from 'proto';
import { appendCRC16 } from './crc16';

export class ProtocolHandler {
  private mqttClient: MqttClient;
  private onMessageCallback: (message: codec.in_.IIncomingPacket) => void;

  constructor(mqttClient: MqttClient) {
    this.mqttClient = mqttClient;
    this.onMessageCallback = () => {};
  }

  onMessage(callback: (message: codec.in_.IIncomingPacket) => void) {
    this.onMessageCallback = callback;
  }

  handleIncomingMessage(payload: Buffer) {
    const messageWithoutCrc = payload.slice(0, -2);
    const crcFromMessage = payload.readUInt16BE(payload.length - 2);
    const calculatedCrc = crc16(messageWithoutCrc);

    if (crcFromMessage !== calculatedCrc) {
      logger.error('Incoming message with invalid CRC');
      return;
    }

    try {
      const decoded = codec.in_.IncomingPacket.decode(messageWithoutCrc);
      logger.info('Decoded incoming message:', decoded.toJSON());
      this.onMessageCallback(decoded);
    } catch (error) {
      logger.error('Failed to decode incoming message:', error);
    }
  }

  sendOutgoingMessage(packet: codec.out.IOutgoingPacket) {
    try {
      const encoded = codec.out.OutgoingPacket.encode(packet).finish();
      const payloadWithCrc = appendCRC16(encoded);
      this.mqttClient.publish(Buffer.from(payloadWithCrc));
    } catch (error) {
      logger.error('Failed to encode outgoing message:', error);
    }
  }
}
