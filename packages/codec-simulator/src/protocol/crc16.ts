import { crc16modbus } from 'crc';

export function crc16(data: string | Buffer | Uint8Array | number[]): number {
  const buffer = Buffer.isBuffer(data)
    ? data
    : typeof data === 'string'
    ? Buffer.from(data)
    : Buffer.from(data);
  return crc16modbus(buffer);
}

export function appendCRC16(payload: Buffer): Buffer {
  const crc = crc16(payload);
  const crcBuffer = Buffer.alloc(2);
  crcBuffer.writeUInt16BE(crc, 0);
  return Buffer.concat([payload, crcBuffer]);
}
