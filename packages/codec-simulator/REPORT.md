# CODEK Simulator Architecture Report

## 1. Introduction

This document outlines the software architecture for the CODEK Simulator, a virtual representation of a physical CODEK (LIC) device. The simulator is designed to facilitate the development, testing, and integration of the Irriga Mais backend systems without the need for physical hardware. It will accurately mimic the behavior, communication, and state management of a real CODEK device as documented in the project's technical specifications.

The simulator will be a Bun-based TypeScript application, running as a standalone process that connects to the same MQTT broker as a real device would.

## 2. Goals and Objectives

- **Behavioral Accuracy**: To behave identically to a real CODEK device from the perspective of the MQTT broker and backend services.
- **State Persistence**: To maintain its state across restarts, simulating the non-volatile memory of the physical hardware.
- **Scalability**: To support the simulation of multiple CODEK devices concurrently, each with a unique identifier and state.
- **Configurability**: To allow for easy configuration of device properties, network conditions, and error scenarios.
- **Introspectability**: To provide mechanisms for inspecting the internal state of the simulator for debugging and testing purposes.

## 3. High-Level Architecture

The simulator will be composed of several key modules, each responsible for a specific aspect of the CODEK's functionality.

```
+-------------------------------------------------+
|               CODEK Simulator                   |
+-------------------------------------------------+
| +-----------------+   +-----------------------+ |
| |  MQTT Client    |<->|   Protocol Handler    | |
| +-----------------+   +-----------------------+ |
|       ^                               ^         |
|       |                               |         |
|       v                               v         |
| +-----------------------------------------------+ |
| |                 State Engine                  | |
| +-----------------------------------------------+ |
| | +-----------------+   +---------------------+ | |
| | |  Device Logic   |   |  Scheduling Logic   | | |
| | +-----------------+   +---------------------+ | |
| | +-----------------+   +---------------------+ | |
| | | Automation Logic|   |   State Manager     | | |
| | +-----------------+   +---------------------+ | |
| +-----------------------------------------------+ |
|       ^                                           |
|       |                                           |
|       v                                           |
| +-----------------------------------------------+ |
| |              Persistence Layer                | |
| |           (JSON files on disk)                | |
| +-----------------------------------------------+ |
+-------------------------------------------------+
```

### Core Components:

-   **MQTT Client**: Manages the connection to the MQTT broker, subscribing to the `downlink` topic and publishing to the `report` topic.
-   **Protocol Handler**: Responsible for serializing and deserializing Protobuf messages, including CRC16 checksum validation. It acts as the bridge between the raw byte streams from MQTT and the structured data used by the State Engine.
-   **State Engine**: The core of the simulator. It orchestrates the logic for device operations, scheduling, and automation. It receives commands from the Protocol Handler and directs the appropriate modules to update the state.
-   **Device Logic**: Simulates the behavior of individual mesh devices (Valves, Pumps, etc.) controlled by the CODEK.
-   **Scheduling Logic**: Manages the execution of irrigation schedules, including timing, step execution, and reporting.
-   **Automation Logic**: Handles the execution of automation rules, such as reservoir level control.
-   **State Manager**: Holds the in-memory representation of the CODEK's state, mirroring the C structures of the ESP32 firmware. This includes device configurations, runtime states, schedules, and system settings.
-   **Persistence Layer**: Manages the reading and writing of the simulator's state to the file system, ensuring that the state is preserved across restarts.

## 4. Detailed Component Design

### 4.1. State Manager & Persistence Layer

This is the foundation of the simulator. Based on the analysis in `DEVICE_STATE_PERSISTENCE_DESIGN.md`, the state will be managed as follows:

-   **In-Memory State**: A single `LICSimulatorState` TypeScript interface will hold the entire state of a simulated device, closely mirroring the collection of `structs` in the firmware.

    ```typescript
    interface LICSimulatorState {
      // Device identification & metadata
      codecId: string;
      firmwareEsp: number;
      firmwareMesh: number;
      hardwareVersion: number;
      resets: number;

      // Configuration timestamps for sync
      configId: number;
      devicesId: number;
      schedulingId: number;
      devSchedulingId: number;
      automationId: number;

      // Configuration data
      config: SystemConfig;
      devices: DeviceConfig[];
      schedules: ScheduleConfig[];
      deviceSchedules: DeviceScheduleConfig[];
      automation: AutomationConfig[];

      // Runtime state
      deviceStates: DeviceState[];
      schedulingRunning: number;
      schedulingPaused: boolean;
      // ... and other runtime variables
    }
    ```

-   **File-Based Persistence**: The state will be persisted to a directory of JSON files, as designed in the analysis document. A `LICStatePersistence` class will handle all file I/O, including loading state on startup, saving on changes, and performing periodic saves. On startup, it will increment the `resets` counter, just like the real firmware.

### 4.2. MQTT Client

-   The client will use the `mqtt` library.
-   It will derive the `codecId` from a configurable value (e.g., a simulated MAC address) to construct the report and downlink topics: `/codec/{codecId}/report` and `/codec/{codecId}/downlink`.
-   Upon connection, it will subscribe to the `downlink` topic.
-   It will pass all incoming messages to the Protocol Handler.
-   It will expose a `publish` method for the Protocol Handler to send outgoing messages.

### 4.3. Protocol Handler

This component is critical for ensuring the simulator speaks the exact same language as the real device. It will leverage the dependencies defined in `package.json`:

-   **`mqtt`**: The official MQTT.js library for handling all communication with the broker.
-   **`proto`**: A local workspace package (`@packages/protobuf`) that provides the necessary Protobuf.js bindings for all message types.

The simulator's role is the inverse of the `mqtt-integration` package. While `mqtt-integration` encodes `IncomingPacket`s and decodes `OutgoingPacket`s, the simulator will **decode `IncomingPacket`s** and **encode `OutgoingPacket`s**.

-   **Message Deserialization (Downlink - `IncomingPacket`)**:
    1.  Receives a raw buffer from the MQTT Client.
    2.  Performs CRC16 validation on the payload. If it fails, the message is dropped and an error is logged.
    3.  Uses the `proto` package to deserialize the `IncomingPacket`. The logic will be similar to the `parseMessage` function in `@packages/mqtt-integration/src/irriganet/codec-manager.ts`, but adapted for `IncomingPacket`.
    4.  Identifies the `oneof payload` type.
    5.  Dispatches the typed message (e.g., `ConfigPackage`, `DevicesPackage`) to the State Engine for processing.

-   **Message Serialization (Uplink - `OutgoingPacket`)**:
    1.  Receives a structured `OutgoingPacket` object from the State Engine (e.g., `InfoPackage`, `SystemStatusPackage`).
    2.  Uses the `proto` package to serialize the object into a binary buffer. The implementation can be guided by the factory functions in `@packages/mqtt-integration/src/irriganet/proto.ts`, but will create `OutgoingPacket` messages instead of `IncomingPacket` messages.
    3.  Calculates the CRC16 checksum of the buffer.
    4.  Appends the 2-byte CRC to the buffer.
    5.  Passes the final buffer to the MQTT Client for publishing.

### 4.4. State Engine

The State Engine is the central coordinator. It will be implemented as a class that contains the logic for processing each type of `IncomingPacket`.

-   It will instantiate the `LICStatePersistence` and `ProtocolHandler`.
-   It will contain methods for each downlink message type, e.g., `handleConfigPackage`, `handleDevicesPackage`, `handleControlPackage`.
-   **Configuration Handling**: For messages like `ConfigPackage`, `DevicesPackage`, etc., it will:
    1.  Update the in-memory state via the State Manager.
    2.  Update the corresponding `*Id` timestamp (e.g., `devicesId`).
    3.  Trigger the `PersistenceLayer` to save the new configuration.
    4.  Construct and send an `InfoPackage` as an acknowledgment, containing the updated timestamps.
-   **Control Command Handling**: For `ControlPackage`, it will delegate the action to the Device Logic module and send an `AckPackage`.
-   **Information Requests**: For `RequestInfoPackage`, it will gather the requested information from the State Manager and send the corresponding response (e.g., `InfoPackage`, `SystemStatusPackage`).
-   **Periodic Tasks**: It will manage timers for periodic status updates (sending `SystemStatusPackage`) and for checking schedule execution.

### 4.5. Scheduling, Device, and Automation Logic

These modules contain the core operational logic of the CODEK.

-   **Scheduling Logic**:
    -   A timer will run every minute to check for due schedules based on `startTime` and `daysOfWeek`.
    -   When a schedule starts, it will manage the sequence of `DeviceScheduling` steps.
    -   It will interact with the **Device Logic** to turn sectors (valves) and pumps on/off for the specified durations (`sector_working_time`, `ferti_working_time`).
    -   After a schedule completes or fails, it will generate and send a `SchedulingReportPackage`.

-   **Device Logic**:
    -   Simulates the state of each device in the `deviceStates` array.
    -   Handles `ControlPackage` actions: turning devices on/off and managing `timeLeft` for timed operations.
    -   Simulates the physical behavior, e.g., a device in `PULSE` mode uses two outputs, while `CONTINUOUS` uses one.
    -   Updates the `on_bitmask` and `input_bitmask` based on the state of the simulated devices.

-   **Automation Logic**:
    -   Monitors the `input_bitmask` for changes that trigger automation rules (e.g., a reservoir level sensor changing state).
    -   When a rule is triggered, it interacts with the **Device Logic** to control the associated pump.
    -   Generates and sends an `AutomationReportPackage` upon completion.

## 5. Proposed Technology Stack

To support the architecture described above, the following libraries and tools are recommended for the implementation.

-   **Runtime & Language**:
    -   **Bun**: The project is already initialized as a Bun package, which will serve as the runtime, bundler, and package manager.
    -   **TypeScript**: For type safety and to clearly define the state and message structures, mirroring the Protobuf and C-struct definitions.

-   **Core Dependencies**:
    -   **`mqtt`**: The standard MQTT.js library will be used for all interactions with the MQTT broker.
    -   **`proto`**: The local workspace package providing the compiled Protobuf.js modules for message encoding and decoding.

-   **Command-Line Interface**:
    -   **`commander`**: A powerful and popular library for creating the command-line interface. It will be used to parse arguments like `--codec-id`, manage multiple simulator instances, and handle help commands.

-   **Logging**:
    -   **`pino`**: A high-performance, low-overhead logging library. It will be used to provide structured, leveled logging (e.g., `trace`, `debug`, `info`, `warn`, `error`). This is crucial for debugging the simulator's behavior and for introspecting the flow of messages without halting execution.

-   **State Management**:
    -   **`immer`**: To handle state updates in an immutable fashion. Given the complexity of the `LICSimulatorState` object, `immer` will simplify the process of creating modified state objects, reducing boilerplate and preventing hard-to-find mutation bugs.

-   **Testing**:
    -   **`bun:test`**: Bun's built-in test runner will be the primary tool for unit and integration testing. Its high speed and Jest-compatibility make it a natural choice.
    -   **`vi` (from Vitest/Bun)**: The mocking capabilities built into Bun's test runner will be used to mock dependencies like the MQTT client and the persistence layer during unit tests.

## 6. Directory Structure

The `packages/codec-simulator` directory will be organized as follows:

```
packages/codec-simulator/
├── src/
│   ├── index.ts                # Main entry point, CLI for starting simulators
│   ├── simulator.ts            # The main CODEKSimulator class (State Engine)
│   ├── mqtt/
│   │   └── client.ts           # MQTT client wrapper
│   ├── protocol/
│   │   ├── handler.ts          # Protocol Handler (serialization, CRC)
│   │   └── protobuf/           # Generated Protobuf-TS files
│   ├── state/
│   │   ├── persistence.ts      # Persistence Layer (file I/O)
│   │   └── types.ts            # All TypeScript state interfaces
│   └── logic/
│       ├── device.ts           # Device logic
│       ├── scheduling.ts       # Scheduling logic
│       └── automation.ts       # Automation logic
├── state-data/                 # Default directory for persisted state files
│   └── {codecId}/              # State for a specific simulator instance
│       ├── system.json
│       └── ...
├── package.json
├── tsconfig.json
└── REPORT.md                   # This file
```

## 6. Getting Started & Usage

The simulator will be launched from the command line, allowing multiple instances to run concurrently.

```bash
# Start a single simulator with a specific ID
bun run start --codec-id ECC9FF468E64

# Start multiple simulators
bun run start --codec-id ECC9FF468E64 --codec-id ABCDEF123456
```

The entry point (`src/index.ts`) will parse these command-line arguments and instantiate a `CODEKSimulator` for each ID provided, each with its own state directory under `state-data/`.

## 8. Conclusion

This architecture provides a robust and accurate foundation for the CODEK Simulator. By closely mirroring the state, communication, and logic of the real ESP32 firmware, it will serve as an invaluable tool for developing and testing the entire Irriga Mais ecosystem. The modular design allows for future extensions, such as simulating network latency or specific hardware failures.
