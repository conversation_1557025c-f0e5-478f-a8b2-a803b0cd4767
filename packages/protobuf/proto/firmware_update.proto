syntax = "proto3";

package codec.in.firmware_update;

message FirmwareUpdatePackage {
  MsgType type = 1;          // Tipo de atualização
  MsgProtocol protocol = 2;  // Tipo de protocolo
  int32 activation_code = 3; // Código indiviual para ativação do UpDate 
  int32 version = 4;         // Nova versão do firmware 
}

enum MsgType {
  MSG_ESP     = 0;
  MSG_STM     = 1;
}

enum MsgProtocol {
  MSG_HTTPS   = 0;
  MSG_HTTP    = 1;
}