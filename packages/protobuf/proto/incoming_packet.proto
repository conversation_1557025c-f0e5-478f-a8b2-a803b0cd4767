syntax = "proto3";

import "config.proto";
import "devices.proto";
import "scheduling.proto";
import "device_scheduling.proto";
import "automation.proto";
import "control.proto";
import "command.proto";
import "request_info.proto";
import "firmware_update.proto";

package codec.in;

message IncomingPacket {
  uint64 id = 1;
  oneof payload {
    codec.in.config.ConfigPackage config = 2;
    codec.in.devices.DevicesPackage devices = 3;
    codec.in.scheduling.SchedulingPackage scheduling = 4;
    codec.in.device_scheduling.DeviceSchedulingPackage dev_scheduling = 5;
    codec.in.automation.AutomationPackage automation = 6;
    codec.in.control.ControlPackage control = 7;
    codec.in.command.CommandPackage command = 8;
    codec.in.request_info.RequestInfoPackage request_info = 9;
    codec.in.firmware_update.FirmwareUpdatePackage firmware_update = 10;
  }
}
