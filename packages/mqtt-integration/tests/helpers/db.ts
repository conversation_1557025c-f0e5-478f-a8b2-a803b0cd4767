import { config, ConfigHolder } from "../../src/config";
import postgres, { type Sql, type TransactionSql } from "postgres";
ConfigHolder.init();

function createPostgresDB(): postgres.Sql {
  return postgres(
    config.test_postgres.url ||
      "postgres://postgres:@localhost:5432/irriga_mais?sslmode=prefer",
    {
      max: 20, // Maximum number of connections
      idle_timeout: 20, // Idle connection timeout in seconds
      connect_timeout: 10, // Connection timeout in seconds

      types: {
        // Add custom type parsers here if needed
        // bigint: postgres.BigInt,
      },
      transform: {
        undefined: null,
      },
    }
  );
}
export const db = createPostgresDB();
// export const db = new SQL(
//   config.test_postgres.url ||
//     "postgres://postgres:@localhost:5432/irriga_mais?sslmode=prefer",
//   {
//     max: 20,
//     idleTimeout: 30,
//     connectionTimeout: 30,
//   }
// );

type TransactionContextCallback<T> = (trx: TransactionSql) => Promise<T>;

/**
 * Begin a transaction for a test, executes the function, and rolls back  .
 */
export async function runInTransaction<T>(
  fn: TransactionContextCallback<T>,
  trxOrDb?: TransactionSql | Sql
) {
  const _db = trxOrDb ?? db;
  return _db.begin(async (trx) => {
    let result: { success: T } | { error: unknown } | null = null;
    try {
      const res = await fn(trx);
      result = { success: res };
    } catch (error) {
      result = { error };
    }
    if ("error" in result) {
      console.error("Transaction error:", result.error);
      throw result.error;
    }
    await trx`ROLLBACK;`;
    return result.success;
  });
}
