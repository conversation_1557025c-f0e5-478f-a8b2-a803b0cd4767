import { type TransactionSql as TransactionSQL } from "postgres";
import { randomUUID } from "crypto";

// --- Entity interfaces based on DDL ---
export interface Account {
  id: string;
  owner: string;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any | null;
  notes: string | null;
}

export interface Property {
  id: string;
  account: string;
  name: string;
  backwash_duration_minutes: number | null;
  backwash_period_minutes: number | null;
  backwash_delay_seconds: number | null;
  rain_gauge_enabled: boolean;
  rain_gauge_resolution_mm: number;
  precipitation_volume_limit_mm: number;
  precipitation_suspended_duration_hours: number;
  timezone: string;
  point: any | null;
  address_postal_code: string | null;
  address_street_name: string | null;
  address_street_number: string | null;
  address_complement: string | null;
  address_neighborhood: string | null;
  address_city: string | null;
  address_state: string | null;
  address_country: string;
  notes: string | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any | null;
}

export interface Device {
  id: string;
  identifier: string;
  model: "LIC" | "WPC-PL10" | "WPC-PL50" | "VC" | "RM";
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any | null;
  notes: string | null;
}

export interface PropertyDevice {
  id: string;
  device: string;
  property: string;
  start_date: string;
  end_date: string | null;
  current_mesh_device_mapping: string | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any | null;
  notes: string | null;
}

export interface MeshDeviceMapping {
  id: string;
  mesh_property_device: string;
  lic_property_device: string;
  start_date: string;
  end_date: string | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
}

export interface Project {
  id: string;
  property: string;
  localized_irrigation_controller: string;
  start_date: string;
  end_date: string | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any | null;
  notes: string | null;
}

export interface WaterPump {
  id: string;
  property: string;
  water_pump_controller: string | null;
  label: string;
  identifier: string;
  pump_type: "IRRIGATION" | "FERTIGATION" | "SERVICE";
  pump_model: string | null;
  has_frequency_inverter: boolean;
  monitor_operation: boolean;
  notes: string | null;
  metadata: any | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  flow_rate_lh: number | null;
}

export interface DirectusUser {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  status: string;
  provider: string;
  role: string | null;
}

// Additional fixture interfaces and functions
export interface IrrigationPlan {
  id: string;
  project: string;
  name: string;
  description: string | null;
  start_time: string;
  days_of_week: string[];
  is_enabled: boolean;
  fertigation_enabled: boolean;
  backwash_enabled: boolean;
  total_irrigation_duration: number;
  start_date: string | null;
  end_date: string | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any | null;
  notes: string | null;
}

export interface IrrigationPlanStep {
  id: string;
  irrigation_plan: string;
  sector: string;
  description: string | null;
  order: number;
  duration_seconds: number;
  fertigation_start_delay_seconds: number | null;
  fertigation_duration_seconds: number | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any | null;
  notes: string | null;
}

export async function insertUser(
  trx: TransactionSQL,
  overrides: Partial<DirectusUser> = {}
): Promise<DirectusUser> {
  const id = overrides.id ?? randomUUID();
  const first_name = overrides.first_name ?? "Test";
  const last_name = overrides.last_name ?? "User";
  const email =
    overrides.email ??
    `test_${Math.random().toString(36).slice(2)}@example.com`;
  const password = overrides.password ?? "hash";
  const status = overrides.status ?? "active";
  const provider = overrides.provider ?? "default";
  const role = overrides.role ?? null;

  const [user] = await trx<DirectusUser[]>`
    INSERT INTO directus_users (id, first_name, last_name, email, password, status, provider, role)
    VALUES (${id}, ${first_name}, ${last_name}, ${email}, ${password}, ${status}, ${provider}, ${role})
    RETURNING *
  `;
  if (!user) throw new Error("Failed to insert user");
  return user;
}

export async function insertAccount(
  trx: TransactionSQL,
  owner: string
): Promise<Account> {
  const [account] = await trx<Account[]>`
    INSERT INTO account (owner)
    VALUES (${owner})
    RETURNING *
  `;
  if (!account) throw new Error("Failed to insert account");
  return account;
}

export async function insertProperty(
  trx: TransactionSQL,
  accountId: string,
  name = `Prop ${Math.random().toString(36).slice(2)}`
): Promise<Property> {
  const timezone = "America/Sao_Paulo";
  const [property] = await trx<Property[]>`
    INSERT INTO property (account, name, timezone)
    VALUES (${accountId}, ${name}, ${timezone})
    RETURNING *
  `;
  if (!property) throw new Error("Failed to insert property");
  return property;
}

export async function insertDevice(
  trx: TransactionSQL,
  model: Device["model"],
  identifier?: string
): Promise<Device> {
  const ident =
    identifier ??
    Math.random().toString(16).slice(2) + Math.random().toString(16).slice(2);

  const [device] = await trx<Device[]>`
    INSERT INTO device (model, identifier)
    VALUES (${model}, ${ident})
    RETURNING *
  `;
  if (!device) throw new Error("Failed to insert device");
  return device;
}

export async function insertPropertyDevice(
  trx: TransactionSQL,
  deviceId: string,
  propertyId: string,
  start: Date,
  end?: Date | null
): Promise<PropertyDevice> {
  const [pd] = await trx<PropertyDevice[]>`
    INSERT INTO property_device (device, property, start_date, end_date)
    VALUES (${deviceId}, ${propertyId}, ${start}, ${end ?? null})
    RETURNING *
  `;
  if (!pd) throw new Error("Failed to insert property_device");
  return pd;
}

export async function insertMeshMapping(
  trx: TransactionSQL,
  meshPdId: string,
  licPdId: string,
  start: Date,
  end?: Date | null
): Promise<MeshDeviceMapping> {
  const [mapping] = await trx<MeshDeviceMapping[]>`
    INSERT INTO mesh_device_mapping (mesh_property_device, lic_property_device, start_date, end_date)
    VALUES (${meshPdId}, ${licPdId}, ${start}, ${end ?? null})
    RETURNING *
  `;
  if (!mapping) throw new Error("Failed to insert mesh_device_mapping");
  return mapping;
}

export async function insertWaterPump(
  trx: TransactionSQL,
  propertyId: string,
  pumpType: WaterPump["pump_type"] = "IRRIGATION",
  label: string = `Pump ${Math.random().toString(36).slice(2)}`,
  identifier: string = `PUMP_${Math.random()
    .toString(36)
    .slice(2)
    .toUpperCase()}`,
  waterPumpControllerId: string | null = null
): Promise<WaterPump> {
  const [waterPump] = await trx<WaterPump[]>`
    INSERT INTO water_pump (property, label, identifier, pump_type, water_pump_controller)
    VALUES (${propertyId}, ${label}, ${identifier}, ${pumpType}, ${
    waterPumpControllerId ?? null
  })
    RETURNING *
  `;
  if (!waterPump) throw new Error("Failed to insert water_pump");
  return waterPump;
}

export async function insertProject(
  trx: TransactionSQL,
  propertyId: string,
  licDeviceId: string,
  irrigationWaterPumpId: string,
  start: Date,
  end?: Date | null,
  name: string = `Project ${Math.random().toString(36).slice(2)}`,
  fertigationWaterPumpId?: string | null
): Promise<Project> {
  const [project] = await trx<Project[]>`
    INSERT INTO project (
      property,
      localized_irrigation_controller,
      irrigation_water_pump,
      fertigation_water_pump,
      start_date,
      end_date,
      name
    )
    VALUES (
      ${propertyId},
      ${licDeviceId},
      ${irrigationWaterPumpId},
      ${fertigationWaterPumpId ?? null},
      ${start}::timestamptz::date,
      ${end ?? null}::timestamptz::date,
      ${name}
    )
    RETURNING *
  `;
  if (!project) throw new Error("Failed to insert project");
  return project;
}

export async function getDeviceModel(trx: TransactionSQL, deviceId: string) {
  const rows =
    await trx`SELECT model FROM device WHERE id = ${deviceId} LIMIT 1`;
  return (rows[0]?.model as string) ?? undefined;
}

export async function getCurrentMeshMappingId(
  trx: TransactionSQL,
  meshPdId: string
): Promise<string | null> {
  const rows = await trx`
    SELECT current_mesh_device_mapping
    FROM property_device
    WHERE id = ${meshPdId}
    LIMIT 1
  `;
  return (rows[0]?.current_mesh_device_mapping as string) ?? null;
}

export function daysAgo(n: number): Date {
  const d = new Date();
  d.setUTCDate(d.getUTCDate() - n);
  return d;
}

export function plusSeconds(date: Date, seconds: number): Date {
  const d = new Date(date.getTime());
  d.setUTCSeconds(d.getUTCSeconds() + seconds);
  return d;
}

export interface Sector {
  id: string;
  project: string;
  name: string;
  valve_controller: string;
  valve_controller_output: number;
  description: string | null;
  power: number;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any;
  notes: string | null;
}

export async function insertSector(
  trx: TransactionSQL,
  projectId: string,
  valveControllerDeviceId: string,
  name: string = `Sector ${Math.random().toString(36).slice(2)}`,
  valveControllerOutput: number = 1,
  description?: string | null
): Promise<Sector> {
  const [sector] = await trx<Sector[]>`
    INSERT INTO sector (
      project,
      name,
      valve_controller,
      valve_controller_output,
      description
    )
    VALUES (
      ${projectId},
      ${name},
      ${valveControllerDeviceId},
      ${valveControllerOutput},
      ${description ?? null}
    )
    RETURNING *
  `;
  if (!sector) throw new Error("Failed to insert sector");
  return sector;
}

export interface Reservoir {
  id: string;
  property: string;
  name: string;
  reservoir_monitor: string | null;
  water_pump: string | null;
  description: string | null;
  capacity: number | null;
  location: any | null;
  notes: string | null;
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: any | null;
  enabled: boolean;
}

export async function insertReservoir(
  trx: TransactionSQL,
  propertyId: string,
  reservoirMonitorDeviceId: string | null,
  waterPumpId: string | null,
  name: string = `Reservoir ${Math.random().toString(36).slice(2)}`,
  capacity: number | null = null,
  location: any | null = null
): Promise<Reservoir> {
  const [reservoir] = await trx<Reservoir[]>`
    INSERT INTO reservoir (property, name, reservoir_monitor, water_pump, capacity, location)
    VALUES (${propertyId}, ${name}, ${reservoirMonitorDeviceId ?? null}, ${
    waterPumpId ?? null
  }, ${capacity ?? null}, ${location ?? null})
    RETURNING *
  `;
  if (!reservoir) throw new Error("Failed to insert reservoir");
  return reservoir;
}

export async function insertIrrigationPlan(
  trx: TransactionSQL,
  projectId: string,
  name: string = `Plan ${Math.random().toString(36).slice(2)}`,
  startTime: string = "06:00:00",
  daysOfWeek: string[] = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"], // All days
  isEnabled: boolean = true,
  fertigationEnabled: boolean = true,
  backwashEnabled: boolean = true
): Promise<IrrigationPlan> {
  const [plan] = (await trx`
    INSERT INTO irrigation_plan (
      project, name, start_time, days_of_week, is_enabled, 
      fertigation_enabled, backwash_enabled, total_irrigation_duration
    )
    VALUES (
      ${projectId}, ${name}, ${startTime}::time, ${daysOfWeek}::jsonb, 
      ${isEnabled}, ${fertigationEnabled}, ${backwashEnabled}, 3600
    )
    RETURNING *
  `) as IrrigationPlan[];
  if (!plan) throw new Error("Failed to insert irrigation_plan");
  return plan;
}

export async function insertIrrigationPlanStep(
  trx: TransactionSQL,
  irrigationPlanId: string,
  sectorId: string,
  order: number,
  durationSeconds: number = 900
): Promise<IrrigationPlanStep> {
  const [step] = (await trx`
    INSERT INTO irrigation_plan_step (
      irrigation_plan, sector, "order", duration_seconds
    )
    VALUES (${irrigationPlanId}, ${sectorId}, ${order}, ${durationSeconds})
    RETURNING *
  `) as IrrigationPlanStep[];
  if (!step) throw new Error("Failed to insert irrigation_plan_step");
  return step;
}

export interface CompletePropertySetup {
  user: DirectusUser;
  account: Account;
  property: Property;
  devices: {
    lic: Device;
    wpcPl10: Device;
    wpcPl50: Device;
    vc: Device;
    rm: Device;
  };
  propertyDevices: {
    lic: PropertyDevice;
    wpcPl10: PropertyDevice;
    wpcPl50: PropertyDevice;
    vc: PropertyDevice;
    rm: PropertyDevice;
  };
  waterPumps: {
    irrigation: WaterPump;
    fertigation: WaterPump;
    service: WaterPump;
  };
  reservoir: Reservoir;
  project: Project;
  sectors: {
    sector1: Sector;
    sector2: Sector;
    sector3: Sector;
    sector4: Sector;
  };
  irrigationPlan: IrrigationPlan;
  irrigationPlanSteps: {
    step1: IrrigationPlanStep;
    step2: IrrigationPlanStep;
    step3: IrrigationPlanStep;
    step4: IrrigationPlanStep;
  };
}

export async function createCompletePropertySetup(
  trx: TransactionSQL,
  overrides: {
    licIdentifier?: string;
    wpcPl10Identifier?: string;
    wpcPl50Identifier?: string;
    vcIdentifier?: string;
    rmIdentifier?: string;
    propertyName?: string;
    projectName?: string;
    irrigationPlanName?: string;
    wifiSSID?: string;
    wifiPassword?: string;
    startDate?: Date;
    endDate?: Date;
  } = {}
): Promise<CompletePropertySetup> {
  const {
    licIdentifier = "LIC001",
    wpcPl10Identifier = "WPC-PL10-001",
    wpcPl50Identifier = "WPC-PL50-001",
    vcIdentifier = "VC001",
    rmIdentifier = "RM001",
    propertyName,
    projectName = "Test Project",
    irrigationPlanName = "Test Irrigation Plan",
    wifiSSID = "TestNetwork",
    wifiPassword = "TestPassword123",
    startDate = new Date("2024-01-01"),
    endDate = new Date("2024-12-31"),
  } = overrides;

  // Create basic entities
  const user: DirectusUser = await insertUser(trx);
  const account: Account = await insertAccount(trx, user.id);
  const property: Property = await insertProperty(
    trx,
    account.id,
    propertyName
  );

  // Create devices
  const licDevice: Device = await insertDevice(trx, "LIC", licIdentifier);
  const wpcPl10Device: Device = await insertDevice(
    trx,
    "WPC-PL10",
    wpcPl10Identifier
  );
  const wpcPl50Device: Device = await insertDevice(
    trx,
    "WPC-PL50",
    wpcPl50Identifier
  );
  const vcDevice: Device = await insertDevice(trx, "VC", vcIdentifier);
  const rmDevice: Device = await insertDevice(trx, "RM", rmIdentifier);

  // Create property devices
  const licPropertyDevice: PropertyDevice = await insertPropertyDevice(
    trx,
    licDevice.id,
    property.id,
    startDate,
    endDate
  );

  const wifiCredentials = {
    wifiSSID,
    wifiPassword,
  };
  // Set wifiSSID and wifiPassword metadata for LIC property device
  await trx`UPDATE property_device SET metadata = ${
    wifiCredentials as any
  } WHERE id = ${licPropertyDevice.id}`;

  const wpcPl10PropertyDevice: PropertyDevice = await insertPropertyDevice(
    trx,
    wpcPl10Device.id,
    property.id,
    startDate,
    endDate
  );

  const wpcPl50PropertyDevice: PropertyDevice = await insertPropertyDevice(
    trx,
    wpcPl50Device.id,
    property.id,
    startDate,
    endDate
  );

  const vcPropertyDevice: PropertyDevice = await insertPropertyDevice(
    trx,
    vcDevice.id,
    property.id,
    startDate,
    endDate
  );

  const rmPropertyDevice: PropertyDevice = await insertPropertyDevice(
    trx,
    rmDevice.id,
    property.id,
    startDate,
    endDate
  );

  // Create mesh device mappings
  await insertMeshMapping(
    trx,
    wpcPl10PropertyDevice.id,
    licPropertyDevice.id,
    startDate,
    endDate
  );

  await insertMeshMapping(
    trx,
    vcPropertyDevice.id,
    licPropertyDevice.id,
    startDate,
    endDate
  );

  await insertMeshMapping(
    trx,
    rmPropertyDevice.id,
    licPropertyDevice.id,
    startDate,
    endDate
  );

  await insertMeshMapping(
    trx,
    wpcPl50PropertyDevice.id,
    licPropertyDevice.id,
    startDate,
    endDate
  );

  // Create water pumps
  const irrigationWaterPump: WaterPump = await insertWaterPump(
    trx,
    property.id,
    "IRRIGATION",
    "Irrigation Pump",
    "IRRIG_PUMP_001",
    wpcPl10Device.id
  );

  const fertigationWaterPump: WaterPump = await insertWaterPump(
    trx,
    property.id,
    "FERTIGATION",
    "Fertigation Pump",
    "FERTIG_PUMP_001",
    wpcPl10Device.id
  );

  const serviceWaterPump: WaterPump = await insertWaterPump(
    trx,
    property.id,
    "SERVICE",
    "Service Pump",
    "SERVICE_PUMP_001",
    wpcPl50Device.id
  );

  // Create reservoir
  const reservoir: Reservoir = await insertReservoir(
    trx,
    property.id,
    rmDevice.id,
    serviceWaterPump.id,
    "Main Reservoir",
    10000,
    null
  );

  // Create project
  const project: Project = await insertProject(
    trx,
    property.id,
    licDevice.id,
    irrigationWaterPump.id,
    startDate,
    endDate,
    projectName,
    fertigationWaterPump.id
  );

  // Create sectors
  const sector1: Sector = await insertSector(
    trx,
    project.id,
    vcDevice.id,
    "Sector 1",
    1
  );

  const sector2: Sector = await insertSector(
    trx,
    project.id,
    vcDevice.id,
    "Sector 2",
    2
  );

  const sector3: Sector = await insertSector(
    trx,
    project.id,
    vcDevice.id,
    "Sector 3",
    3
  );

  const sector4: Sector = await insertSector(
    trx,
    project.id,
    vcDevice.id,
    "Sector 4",
    4
  );

  // Create irrigation plan with backwash and fertigation enabled
  const irrigationPlan: IrrigationPlan = await insertIrrigationPlan(
    trx,
    project.id,
    irrigationPlanName,
    "06:00:00",
    ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"], // All days
    true, // enabled
    true, // fertigation enabled
    true // backwash enabled
  );

  // Create irrigation plan steps for each sector
  const step1 = await insertIrrigationPlanStep(
    trx,
    irrigationPlan.id,
    sector1.id,
    1,
    900
  );
  const step2 = await insertIrrigationPlanStep(
    trx,
    irrigationPlan.id,
    sector2.id,
    2,
    900
  );
  const step3 = await insertIrrigationPlanStep(
    trx,
    irrigationPlan.id,
    sector3.id,
    3,
    900
  );
  const step4 = await insertIrrigationPlanStep(
    trx,
    irrigationPlan.id,
    sector4.id,
    4,
    900
  );

  return {
    user,
    account,
    property,
    devices: {
      lic: licDevice,
      wpcPl10: wpcPl10Device,
      wpcPl50: wpcPl50Device,
      vc: vcDevice,
      rm: rmDevice,
    },
    propertyDevices: {
      lic: licPropertyDevice,
      wpcPl10: wpcPl10PropertyDevice,
      wpcPl50: wpcPl50PropertyDevice,
      vc: vcPropertyDevice,
      rm: rmPropertyDevice,
    },
    waterPumps: {
      irrigation: irrigationWaterPump,
      fertigation: fertigationWaterPump,
      service: serviceWaterPump,
    },
    reservoir,
    project,
    sectors: {
      sector1,
      sector2,
      sector3,
      sector4,
    },
    irrigationPlan,
    irrigationPlanSteps: {
      step1,
      step2,
      step3,
      step4,
    },
  };
}
