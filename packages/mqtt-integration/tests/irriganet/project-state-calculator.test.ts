import { describe, beforeAll, test, expect } from "bun:test";
import { calculateProjectsState } from "../../src/irriganet/project-state-calculator";

describe("calculateProjectsState", () => {
  const packetDate = new Date("2025-01-01T00:00:00.000Z");

  beforeAll(() => {
    // deterministically stub crypto.randomUUID used in the implementation
    (global as any).crypto = {
      randomUUID: () => "test-uuid",
    };
  });

  test("reports irrigation active when on_bitmask has device bit set", () => {
    const licTree = {
      projects: [
        {
          id: "proj-1",
          irrigation_water_pump: { id: "pump-1", water_pump_controller: true },
          fertigation_water_pump: undefined,
          backwash_pump_type: undefined,
          sectors: [],
        },
      ],
    } as any;

    const licState = {
      devices: [
        {
          identity: "pump-1-device",
          ord_idx: 2,
          elementId: "pump-1",
          elementType: "pump",
        },
      ],
    } as any;

    const systemStatusPackage = {
      // set bit 2
      on_bitmask: BigInt(1) << BigInt(2),
      failed_bitmask: BigInt(0),
    } as any;

    const result = calculateProjectsState(
      systemStatusPackage,
      licState,
      licTree,
      packetDate
    );

    expect(result).toHaveLength(1);
    const p = result[0];
    expect(p?.project).toBe("proj-1");
    expect(p?.irrigation_status).toBe("active");
    expect(p?.fertigation_status).toBe("inactive");
    expect(p?.backwash_status).toBe("inactive");
    expect(p?.sectors).toEqual([]);
  });

  test("reports error when failed_bitmask has device bit set and backwash uses irrigation pump variation", () => {
    const licTree = {
      projects: [
        {
          id: "proj-2",
          irrigation_water_pump: { id: "pump-A", water_pump_controller: true },
          fertigation_water_pump: undefined,
          backwash_pump_type: "IRRIGATION",
          sectors: [],
        },
      ],
    } as any;

    const licState = {
      devices: [
        // irrigation pump (normal) - no active bit
        {
          identity: "pump-A-main",
          ord_idx: 1,
          elementId: "pump-A",
          elementType: "pump",
        },
        // irrigation backwash variation - this one should be checked for backwash
        {
          identity: "pump-A-backwash",
          ord_idx: 3,
          elementId: "pump-A",
          elementType: "pump",
          elementVariation: "backwash",
        },
      ],
    } as any;

    const systemStatusPackage = {
      on_bitmask: BigInt(0),
      // set failed bit for ord_idx 3
      failed_bitmask: BigInt(1) << BigInt(3),
    } as any;

    const result = calculateProjectsState(
      systemStatusPackage,
      licState,
      licTree,
      packetDate
    );

    expect(result).toHaveLength(1);
    const p = result[0];
    expect(p?.project).toBe("proj-2");
    // irrigation pump has no on bit => inactive
    expect(p?.irrigation_status).toBe("inactive");
    // backwash should reflect the failed bit on the backwash variation device
    expect(p?.backwash_status).toBe("error");
  });

  test("calculates sector statuses: active when valve on bit set, inactive when missing", () => {
    const licTree = {
      projects: [
        {
          id: "proj-3",
          irrigation_water_pump: undefined,
          fertigation_water_pump: undefined,
          backwash_pump_type: undefined,
          sectors: [
            { id: "sec-1", name: "Sector 1", valve_controller_device: true },
            { id: "sec-2", name: "Sector 2", valve_controller_device: true },
            { id: "sec-3", name: "Sector 3", valve_controller_device: false }, // should be ignored
          ],
        },
      ],
    } as any;

    const licState = {
      devices: [
        {
          identity: "sec-1-device",
          ord_idx: 0,
          elementId: "sec-1",
          elementType: "valve",
        },
        // sec-2 has no device entry -> should be reported inactive
      ],
    } as any;

    const systemStatusPackage = {
      // set bit 0 => sec-1 active
      on_bitmask: BigInt(1) << BigInt(0),
      failed_bitmask: BigInt(0),
    } as any;

    const result = calculateProjectsState(
      systemStatusPackage,
      licState,
      licTree,
      packetDate
    );

    expect(result).toHaveLength(1);
    const p = result[0];
    expect(p?.sectors).toContainEqual({
      sector: "sec-1",
      sector_name: "Sector 1",
      status: "active",
    });
    expect(p?.sectors).toContainEqual({
      sector: "sec-2",
      sector_name: "Sector 2",
      status: "inactive",
    });
    // sec-3 had valve_controller_device: false so it should not appear
    expect(p?.sectors.find((s) => s.sector === "sec-3")).toBeUndefined();
  });
});
