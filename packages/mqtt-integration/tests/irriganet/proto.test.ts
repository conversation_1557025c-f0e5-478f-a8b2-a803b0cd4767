import { describe, expect, it } from "bun:test";

import { loadLICStateByIdentifier } from "../../src/irriganet/db-loader";
import {
  automationPackage,
  commandPacket,
  configPackage,
  controlPackage,
  deviceSchedulingPackage,
  devicesPackage,
  firmware_updatePackage,
  request_infoPackage,
  schedulingPackage,
} from "../../src/irriganet/proto";
import { runInTransaction } from "../helpers/db";
import { createCompletePropertySetup } from "../helpers/fixtures";

describe("proto.ts functions", () => {
  describe("devicesPackage", () => {
    it("should create a valid DevicesPackage from LIC state", async () => {
      await runInTransaction(async (trx) => {
        // Create complete property setup
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        // Load LIC state
        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Test devicesPackage function
        const devicesPackageResult = devicesPackage(licState);

        expect(devicesPackageResult).toBeDefined();
        expect(devicesPackageResult.data).toBeDefined();
        expect(Array.isArray(devicesPackageResult.data)).toBe(true);
        expect(devicesPackageResult.data.length).toBeGreaterThan(0);

        // Verify device data structure
        devicesPackageResult.data.forEach((deviceData) => {
          expect(deviceData).toBeDefined();
          expect(typeof deviceData.idx).toBe("number");
          expect(typeof deviceData.mesh_id).toBe("number");
          expect(typeof deviceData.device_id).toBe("number");
          expect(typeof deviceData.device_type).toBe("number");
        });
      });
    });

    it("should throw error when mesh device is not found for a device", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Corrupt the state by removing mesh devices
        const corruptedState = { ...licState, meshDevices: [] };

        // Should throw error when mesh device is not found
        expect(() => devicesPackage(corruptedState)).toThrow(
          /Mesh device not found for device/
        );
      });
    });
  });

  describe("schedulingPackage", () => {
    it("should create a valid SchedulingPackage from LIC state", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Test schedulingPackage function
        const schedulingPackageResult = schedulingPackage(licState);

        expect(schedulingPackageResult).toBeDefined();
        expect(schedulingPackageResult.scheduling_data).toBeDefined();
        expect(Array.isArray(schedulingPackageResult.scheduling_data)).toBe(
          true
        );

        // Verify scheduling data structure
        schedulingPackageResult.scheduling_data.forEach((scheduleData) => {
          expect(scheduleData).toBeDefined();
          expect(typeof scheduleData.idx).toBe("number");
          expect(typeof scheduleData.start_time).toBe("number");
          expect(typeof scheduleData.days_of_week).toBe("number");
          expect(typeof scheduleData.number_of_steps).toBe("number");
          expect(typeof scheduleData.allow_ferti).toBe("boolean");
          expect(typeof scheduleData.allow_backwash).toBe("boolean");
        });
      });
    });
  });

  describe("deviceSchedulingPackage", () => {
    it("should create a valid DeviceSchedulingPackage from LIC state", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Test deviceSchedulingPackage function
        const deviceSchedulingPackageResult = deviceSchedulingPackage(licState);

        expect(deviceSchedulingPackageResult).toBeDefined();
        expect(deviceSchedulingPackageResult.data).toBeDefined();
        expect(Array.isArray(deviceSchedulingPackageResult.data)).toBe(true);

        // Verify device scheduling data structure
        deviceSchedulingPackageResult.data.forEach((deviceScheduleData) => {
          expect(deviceScheduleData).toBeDefined();
          expect(typeof deviceScheduleData.idx).toBe("number");
          expect(typeof deviceScheduleData.scheduling_idx).toBe("number");
          expect(typeof deviceScheduleData.device_idx).toBe("number");
          expect(typeof deviceScheduleData.order).toBe("number");
          expect(typeof deviceScheduleData.sector_working_time).toBe("number");
        });
      });
    });

    it("should throw error when scheduling is not found for device schedule", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Corrupt the state by removing schedules
        const corruptedState = { ...licState, schedules: [] };

        // Should throw error when scheduling is not found
        expect(() => deviceSchedulingPackage(corruptedState)).toThrow(
          /Scheduling not found for device schedule/
        );
      });
    });

    it("should throw error when device is not found for device schedule", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Corrupt the state by removing devices
        const corruptedState = { ...licState, devices: [] };

        // Should throw error when device is not found
        expect(() => deviceSchedulingPackage(corruptedState)).toThrow(
          /Device not found for device schedule/
        );
      });
    });
  });

  describe("automationPackage", () => {
    it("should create a valid AutomationPackage from LIC state", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Test automationPackage function with default magic values
        const automationPackageResult = automationPackage(licState);

        expect(automationPackageResult).toBeDefined();
        expect(automationPackageResult.data).toBeDefined();
        expect(Array.isArray(automationPackageResult.data)).toBe(true);

        // Verify automation data structure
        automationPackageResult.data.forEach((automationData) => {
          expect(automationData).toBeDefined();
          expect(typeof automationData.level_idx).toBe("number");
          expect(typeof automationData.mask).toBe("number");
          expect(typeof automationData.value).toBe("number");
          expect(typeof automationData.working_time).toBe("number");
        });
      });
    });

    it("should accept custom magic values", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Test automationPackage function with custom magic values
        const customMagicValues = { mask: 10, value: 5 };
        const automationPackageResult = automationPackage(
          licState,
          customMagicValues
        );

        expect(automationPackageResult).toBeDefined();
        expect(automationPackageResult.data).toBeDefined();

        // Verify custom magic values are used
        automationPackageResult.data.forEach((automationData) => {
          expect(automationData.mask).toBe(10);
          expect(automationData.value).toBe(5);
        });
      });
    });
  });

  describe("configPackage", () => {
    it("should create a valid ConfigPackage from LIC state and include WiFi credentials", async () => {
      await runInTransaction(async (trx) => {
        const testWifiSSID = "TestNetwork";
        const testWifiPassword = "TestPassword123";

        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
          wifiSSID: testWifiSSID,
          wifiPassword: testWifiPassword,
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Test configPackage function
        const configPackageResult = configPackage(licState);

        expect(configPackageResult).toBeDefined();
        expect(configPackageResult.wifi).toBeDefined();

        // Verify WiFi configuration uses values from property_device metadata
        expect(configPackageResult.wifi?.ssid).toBe(testWifiSSID);
        expect(configPackageResult.wifi?.password).toBe(testWifiPassword);

        // Verify other config fields
        expect(typeof configPackageResult.debug).toBe("boolean");
        expect(configPackageResult.debug).toBe(false);
        expect(typeof configPackageResult.enable_ferti_resumption).toBe(
          "boolean"
        );
        expect(configPackageResult.enable_ferti_resumption).toBe(false);
        expect(typeof configPackageResult.enable_schedule_resumption).toBe(
          "boolean"
        );
        expect(configPackageResult.enable_schedule_resumption).toBe(false);
        expect(typeof configPackageResult.max_resumption_attempts).toBe(
          "number"
        );
        expect(configPackageResult.max_resumption_attempts).toBe(0);
        expect(typeof configPackageResult.publish_raw_data).toBe("boolean");
        expect(configPackageResult.publish_raw_data).toBe(false);
      });
    });

    it("should handle null WiFi values gracefully", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
          wifiSSID: "",
          wifiPassword: "",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Test configPackage function with null WiFi values
        const configPackageResult = configPackage(licState);

        expect(configPackageResult).toBeDefined();
        expect(configPackageResult.wifi).toBeDefined();
        expect(configPackageResult.wifi?.ssid).toBeUndefined();
        expect(configPackageResult.wifi?.password).toBeUndefined();
      });
    });
  });

  describe("request_infoPackage", () => {
    it("should create a valid RequestInfoPackage", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Test request_infoPackage function
        const requestInfoPackageResult = request_infoPackage(licState);

        expect(requestInfoPackageResult).toBeDefined();
        expect(requestInfoPackageResult.type).toBe(0);
      });
    });
  });

  describe("controlPackage", () => {
    it("should create a valid ControlPackage for turning on a valve", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Find a valve device to control
        const valveDevice = licState.devices.find(
          (d) => d.elementType === "valve"
        );
        if (!valveDevice || !valveDevice.elementId) {
          // Skip test if no valve device found
          return;
        }

        // Test controlPackage function for turning on a valve
        const controlPackageResult = controlPackage(licState, {
          elementType: "valve",
          elementId: valveDevice.elementId,
          turnOn: true,
          durationMinutes: 30,
        });

        expect(controlPackageResult).toBeDefined();
        expect(controlPackageResult.idx).toBe(valveDevice.ord_idx);
        expect(controlPackageResult.value).toBe(30);
      });
    });

    it("should create a valid ControlPackage for turning off a pump", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Find a pump device to control
        const pumpDevice = licState.devices.find(
          (d) => d.elementType === "pump"
        );
        if (!pumpDevice || !pumpDevice.elementId) {
          // Skip test if no pump device found
          return;
        }

        // Test controlPackage function for turning off a pump
        const controlPackageResult = controlPackage(licState, {
          elementType: "pump",
          elementId: pumpDevice.elementId,
          turnOn: false,
        });

        expect(controlPackageResult).toBeDefined();
        expect(controlPackageResult.idx).toBe(pumpDevice.ord_idx);
        expect(controlPackageResult.value).toBe(0);
      });
    });

    it("should throw error when device is not found", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Should throw error when device is not found
        expect(() =>
          controlPackage(licState, {
            elementType: "valve",
            elementId: "NONEXISTENT",
            turnOn: false,
          })
        ).toThrow("Device not found");
      });
    });
  });

  describe("commandPacket", () => {
    it("should create a valid CommandPackage for resume command", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Test commandPacket function for resume command
        const commandPackageResult = commandPacket(licState, {
          command: "resume",
        });

        expect(commandPackageResult).toBeDefined();
        expect(commandPackageResult.value).toBe(0);
      });
    });

    it("should create a valid CommandPackage for pause command", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Test commandPacket function for pause command
        const commandPackageResult = commandPacket(licState, {
          command: "pause",
          durationMinutes: 60,
        });

        expect(commandPackageResult).toBeDefined();
        expect(commandPackageResult.value).toBe(60);
      });
    });
  });

  describe("firmware_updatePackage", () => {
    it("should create a valid FirmwareUpdatePackage", async () => {
      await runInTransaction(async (trx) => {
        await createCompletePropertySetup(trx, {
          licIdentifier: "LIC001",
        });

        const referenceDate = new Date("2024-06-01");

        const licState = await loadLICStateByIdentifier(
          trx,
          "LIC001",
          referenceDate,
          1
        );

        expect(licState).not.toBeNull();
        if (!licState) return;

        // Test firmware_updatePackage function
        const firmwareParams = {
          type: 0, // MSG_ESP
          protocol: 0, // MSG_HTTPS
          activation_code: 12345,
          version: 123,
        };

        const firmwareUpdatePackageResult = firmware_updatePackage(
          licState,
          firmwareParams
        );

        expect(firmwareUpdatePackageResult).toBeDefined();
        expect(firmwareUpdatePackageResult.type).toBe(0);
        expect(firmwareUpdatePackageResult.protocol).toBe(0);
        expect(firmwareUpdatePackageResult.activation_code).toBe(12345);
        expect(firmwareUpdatePackageResult.version).toBe(123);
      });
    });
  });
});
