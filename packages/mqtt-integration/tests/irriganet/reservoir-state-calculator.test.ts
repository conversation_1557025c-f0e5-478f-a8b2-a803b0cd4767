import { describe, test, expect } from "bun:test";
import { calculateReservoirState } from "../../src/irriganet/reservoir-state-calculator";

describe("calculateReservoirState", () => {
  const packetDate = new Date("2025-09-04T12:00:00.000Z");
  const reservoirId = "00000000-0000-0000-0000-000000000001";

  test("converts timestamps and maps fields", () => {
    const report = {
      auto_idx: 1,
      start_time: "1730000000",
      restart_time: "1730001800",
      end_time: "1730003600",
      status: 1,
    } as any;

    const state = calculateReservoirState(report, reservoirId, packetDate);
    expect(state).not.toBeNull();
    expect(state!.reservoir).toBe(reservoirId);
    expect(state!.start_time).toEqual(new Date(1730000000 * 1000));
    expect(state!.restart_time).toEqual(new Date(1730001800 * 1000));
    expect(state!.end_time).toEqual(new Date(1730003600 * 1000));
  });

  test("fills end_time with packet_date when completed without end_time", () => {
    const report = {
      auto_idx: 1,
      start_time: 1730000000,
      restart_time: 0,
      end_time: 0,
      status: 1,
    } as any;

    const state = calculateReservoirState(report, reservoirId, packetDate)!;
    expect(state.end_time).toEqual(packetDate);
    expect(state.restart_time).toBeNull();
  });

  test("returns null when start_time is missing", () => {
    const report = { auto_idx: 1, start_time: undefined, status: 0 } as any;
    const state = calculateReservoirState(report, reservoirId, packetDate);
    expect(state).toBeNull();
  });
});
