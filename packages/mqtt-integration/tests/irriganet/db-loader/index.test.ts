import { describe, expect, it } from "bun:test";

import { loadLICStateByIdentifier } from "../../../src/irriganet/db-loader";
import { runInTransaction } from "../../helpers/db";
import {
  createCompletePropertySetup,
  insertDevice,
  type Device,
} from "../../helpers/fixtures";

describe("loadLICStateByIdentifier", () => {
  it("should load complete LIC state with all required entities", async () => {
    await runInTransaction(async (trx) => {
      // Create complete property setup using the reusable function
      const setup = await createCompletePropertySetup(trx, {
        licIdentifier: "LIC001",
        wifiSSID: "TestNetwork",
        wifiPassword: "TestPassword123",
      });

      const referenceDate = new Date("2024-06-01");

      // Test the loadLICStateByIdentifier function
      const result = await loadLICStateByIdentifier(
        trx,
        "LIC001",
        referenceDate,
        1
      );

      // Verify the result is not null
      expect(result).not.toBeNull();
      expect(result).toBeDefined();

      if (!result) return; // TypeScript guard

      // Verify LIC configuration
      expect(result.lic).toBeDefined();
      expect(result.lic.identity).toBe("LIC001");
      expect(result.lic.idx).toBe(1);

      // Verify groups are generated
      expect(result.groups).toBeDefined();
      expect(Array.isArray(result.groups)).toBe(true);

      // Verify devices are generated
      expect(result.devices).toBeDefined();
      expect(Array.isArray(result.devices)).toBe(true);

      // Verify mesh devices are generated
      expect(result.meshDevices).toBeDefined();
      expect(Array.isArray(result.meshDevices)).toBe(true);

      // Verify schedules are generated
      expect(result.schedules).toBeDefined();
      expect(Array.isArray(result.schedules)).toBe(true);

      // Verify sector schedules are generated
      expect(result.sectorSchedules).toBeDefined();
      expect(Array.isArray(result.sectorSchedules)).toBe(true);

      // Verify device schedules are generated
      expect(result.deviceSchedules).toBeDefined();
      expect(Array.isArray(result.deviceSchedules)).toBe(true);

      // Verify wifi configuration is properly set
      expect(result.lic.config.wifi).toBeDefined();
      expect(result.lic.config.wifi?.ssid).toBe("TestNetwork");
      expect(result.lic.config.wifi?.password).toBe("TestPassword123");
    });
  });

  it("should return null when LIC device is not found", async () => {
    await runInTransaction(async (trx) => {
      const result = await loadLICStateByIdentifier(
        trx,
        "NONEXISTENT_LIC",
        new Date(),
        1
      );

      expect(result).toBeNull();
    });
  });

  it("should return null when LIC device is not associated with any property at reference date", async () => {
    await runInTransaction(async (trx) => {
      // Create a LIC device but don't associate it with any property
      const _licDevice: Device = await insertDevice(trx, "LIC", "LIC002");

      const result = await loadLICStateByIdentifier(
        trx,
        "LIC002",
        new Date(),
        1
      );

      expect(result).toBeNull();
    });
  });

  it("should return null when LIC has property device association but referenceDate is outside it", async () => {
    await runInTransaction(async (trx) => {
      // Create complete property setup with specific date range
      const setup = await createCompletePropertySetup(trx, {
        licIdentifier: "LIC003",
        startDate: new Date("2024-01-01"),
        endDate: new Date("2024-12-31"),
      });

      // Update the LIC property device association to have a restricted date range
      await trx`
        UPDATE property_device 
        SET start_date = '2024-06-01'::date, end_date = '2024-08-31'::date
        WHERE id = ${setup.propertyDevices.lic.id}
      `;

      // Test with reference date outside the association period (before start_date)
      const resultBefore = await loadLICStateByIdentifier(
        trx,
        "LIC003",
        new Date("2024-05-15"), // Before start_date
        1
      );

      // Test with reference date outside the association period (after end_date)
      const resultAfter = await loadLICStateByIdentifier(
        trx,
        "LIC003",
        new Date("2024-09-15"), // After end_date
        1
      );

      expect(resultBefore).toBeNull();
      expect(resultAfter).toBeNull();
    });
  });
});
