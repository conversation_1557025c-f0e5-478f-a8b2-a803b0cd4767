import { describe, test, expect } from "bun:test";
import { calculateIrrigationPlanState } from "../../src/irriganet/irrigation-plan-state-calculator";
import { codec } from "proto";
import type { LICState } from "../../src/irriganet/db-loader/types";

// Load test data
import stateData from "../data/90150667FCF4-state.json";
import treeData from "../data/90150667FCF4-tree.json";

describe("calculateIrrigationPlanState", () => {
  const packetDate = new Date("2025-09-01T12:00:00.000Z");
  const licState = stateData as unknown as LICState;

  test("correctly identifies irrigation plan from scheduling_idx", () => {
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: 1, // Use scheduling_idx that matches sector schedules
        start_time: 1730000000, // Example timestamp
        end_time: 1730007200, // Example timestamp + 2 hours
        sector_bitmask1: 0b00000001, // First sector activated
        ferti_bitmask1: 0b00000001, // First sector with fertigation
        waterpump: true,
        had_waterpump: true,
        had_ferti: true,
        status: 1, // Completed
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).not.toBeNull();
    expect(result?.irrigation_plan).toBe(
      "c453c050-3360-4fd0-86cc-c89710e8b098"
    ); // irrigation plan ID for ord_idx: 1
  });

  test("correctly identifies activated steps from sector bitmask", () => {
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: 1,
        start_time: 1730000000,
        end_time: 1730007200,
        sector_bitmask1: 0b00000011, // First two sectors activated
        ferti_bitmask1: 0b00000001,
        waterpump: true,
        had_waterpump: true,
        had_ferti: true,
        status: 1,
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).not.toBeNull();
    expect(result?.activated_steps).toContain(
      "4b1bd37a-149a-40e1-9588-d2c5e3576ce7"
    ); // First step for scheduling_idx: 2 (n_order: 0)
    expect(result?.activated_steps).toContain(
      "df61ad29-a2d3-4bf9-afaa-fc5ba20eba8b"
    ); // Second step for scheduling_idx: 2 (n_order: 1)
    expect(result?.activated_steps).toHaveLength(2);
  });

  test("correctly identifies activated ferti steps from ferti bitmask", () => {
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: 1,
        start_time: 1730000000,
        end_time: 1730007200,
        sector_bitmask1: 0b00000011,
        ferti_bitmask1: 0b00000011, // First two sectors with fertigation
        waterpump: true,
        had_waterpump: true,
        had_ferti: true,
        status: 1,
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).not.toBeNull();
    expect(result?.activated_ferti_steps).toContain(
      "4b1bd37a-149a-40e1-9588-d2c5e3576ce7"
    ); // First step with ferti for scheduling_idx: 2 (n_order: 0)
    expect(result?.activated_ferti_steps).toContain(
      "df61ad29-a2d3-4bf9-afaa-fc5ba20eba8b"
    ); // Second step with ferti for scheduling_idx: 2 (n_order: 1)
    expect(result?.activated_ferti_steps).toHaveLength(2);
  });

  test("correctly converts start time from protobuf timestamp", () => {
    const testTimestamp = 1730000000; // 2024-10-27T06:13:20.000Z
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: 1,
        start_time: testTimestamp,
        end_time: 1730007200,
        sector_bitmask1: 0b00000001,
        ferti_bitmask1: 0b00000001,
        waterpump: true,
        had_waterpump: true,
        had_ferti: true,
        status: 1,
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).not.toBeNull();
    expect(result?.start_time).toEqual(new Date(testTimestamp * 1000));
  });

  test("correctly converts end time from protobuf timestamp", () => {
    const testEndTimestamp = 1730007200; // 2024-10-27T08:13:20.000Z
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: 1,
        start_time: 1730000000,
        end_time: testEndTimestamp,
        sector_bitmask1: 0b00000001,
        ferti_bitmask1: 0b00000001,
        waterpump: true,
        had_waterpump: true,
        had_ferti: true,
        status: 1,
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).not.toBeNull();
    expect(result?.end_time).toEqual(new Date(testEndTimestamp * 1000));
  });

  test("sets end_time to packet_date when status is completed but end_time is missing", () => {
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: 1,
        start_time: 1730000000,
        end_time: undefined, // Missing end_time
        sector_bitmask1: 0b00000001,
        ferti_bitmask1: 0b00000001,
        waterpump: true,
        had_waterpump: true,
        had_ferti: true,
        status: 1, // Completed status
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).not.toBeNull();
    expect(result?.end_time).toEqual(packetDate);
  });

  test("correctly converts backwash start time from protobuf timestamp", () => {
    const testBackwashTimestamp = 1730003600; // Middle of execution
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: 1,
        start_time: 1730000000,
        end_time: 1730007200,
        backwash_time: testBackwashTimestamp,
        sector_bitmask1: 0b00000001,
        ferti_bitmask1: 0b00000001,
        waterpump: true,
        had_waterpump: true,
        had_ferti: true,
        status: 1,
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).not.toBeNull();
    expect(result?.backwash_start_time).toEqual(
      new Date(testBackwashTimestamp * 1000)
    );
  });

  test("correctly identifies water pump working status", () => {
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: 1,
        start_time: 1730000000,
        end_time: 1730007200,
        sector_bitmask1: 0b00000001,
        ferti_bitmask1: 0b00000001,
        waterpump: true, // Water pump was working
        had_waterpump: true,
        had_ferti: true,
        status: 1,
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).not.toBeNull();
    expect(result?.waterpump_working).toBe(true);
    expect(result?.uses_waterpump).toBe(true);
    expect(result?.uses_ferti).toBe(true);
  });

  test("returns null when scheduling_idx is missing", () => {
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: undefined, // Missing scheduling_idx
        start_time: 1730000000,
        end_time: 1730007200,
        sector_bitmask1: 0b00000001,
        ferti_bitmask1: 0b00000001,
        waterpump: true,
        had_waterpump: true,
        had_ferti: true,
        status: 1,
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).toBeNull();
  });

  test("returns null when start_time is missing", () => {
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: 1,
        start_time: undefined, // Missing start_time
        end_time: 1730007200,
        sector_bitmask1: 0b00000001,
        ferti_bitmask1: 0b00000001,
        waterpump: true,
        had_waterpump: true,
        had_ferti: true,
        status: 1,
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).toBeNull();
  });

  test("returns null when schedule is not found for scheduling_idx", () => {
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: 999, // Non-existent scheduling index
        start_time: 1730000000,
        end_time: 1730007200,
        sector_bitmask1: 0b00000001,
        ferti_bitmask1: 0b00000001,
        waterpump: true,
        had_waterpump: true,
        had_ferti: true,
        status: 1,
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).toBeNull();
  });

  test("handles empty sector bitmasks correctly", () => {
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: 1,
        start_time: 1730000000,
        end_time: 1730007200,
        sector_bitmask1: 0, // No sectors activated
        ferti_bitmask1: 0, // No fertigation
        waterpump: true,
        had_waterpump: true,
        had_ferti: true,
        status: 1,
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).not.toBeNull();
    expect(result?.activated_steps).toHaveLength(0);
    expect(result?.activated_ferti_steps).toHaveLength(0);
  });

  test("handles second scheduling index correctly", () => {
    const schedulingReportData: codec.out.scheduling_report.ISchedulingReportData =
      {
        scheduling_idx: 2, // Use scheduling_idx that matches sector schedules
        start_time: 1730000000,
        end_time: 1730007200,
        sector_bitmask1: 0b00000001, // First sector activated
        ferti_bitmask1: 0b00000001, // First sector with fertigation
        waterpump: true,
        had_waterpump: true,
        had_ferti: true,
        status: 1,
      };

    const result = calculateIrrigationPlanState(
      schedulingReportData,
      licState,
      packetDate
    );

    expect(result).not.toBeNull();
    expect(result?.irrigation_plan).toBe(
      "9aa12eaa-3b14-47b0-964f-3060a5908e9f"
    ); // irrigation plan ID for ord_idx: 2
    expect(result?.activated_steps).toContain(
      "cc3a7439-2152-473c-873d-39fcca6b74ed"
    ); // First step for scheduling_idx: 3 (n_order: 0)
  });
});
