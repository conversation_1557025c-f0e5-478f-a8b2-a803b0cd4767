export interface IConfig {
  mqtt: {
    brokerUrl: string;
    username: string;
    password: string;
  };
  postgres: {
    url: string;
  };
  directus: {
    url: string;
    authToken: string;
  };
  deviceMessageQueue: {
    pollingIntervalMs: number;
    batchSize: number;
    retryBatchSize: number;
    enableRetryProcessing: boolean;
  };
  codecManager: {
    updateConfigInterval: number;
    outdatedConfigurationCheckEnabled: boolean;
  };
  debug: {
    codecState: {
      enabled: boolean;
      outputDir: string;
    };
  };
  test_postgres: {
    url: string;
  };
}

export class Config implements IConfig {
  // We do not handle log config because log module will be extracted into a new package and will no have access to this Config class
  //   log = {
  //     level: process.env.LOG_LEVEL || "info",
  //     childrenLevel: process.env.CHILDREN_LOG_LEVEL,
  //   };
  mqtt = {
    brokerUrl: process.env.MQTT_BROKER_URL || "mqtt://localhost:1883",
    username: process.env.MQTT_USERNAME || "user",
    password: process.env.MQTT_PASSWORD || "password",
  };
  postgres = {
    url: process.env.POSTGRES_URL || "postgres://localhost:5432/irriga_mais",
  };
  directus = {
    url: process.env.DIRECTUS_URL || "http://localhost:8055",
    authToken: process.env.DIRECTUS_AUTH_TOKEN || "",
  };
  deviceMessageQueue = {
    pollingIntervalMs: Number(
      process.env.DEVICE_MESSAGE_QUEUE_POLLING_INTERVAL_MS || 1000
    ),
    batchSize: Number(process.env.DEVICE_MESSAGE_QUEUE_BATCH_SIZE || 50),
    retryBatchSize: Number(
      process.env.DEVICE_MESSAGE_QUEUE_RETRY_BATCH_SIZE || 20
    ),
    enableRetryProcessing:
      process.env.DEVICE_MESSAGE_QUEUE_ENABLE_RETRY_PROCESSING?.toLowerCase() ===
      "true",
  };
  codecManager = {
    updateConfigInterval: Number(
      process.env.CODEC_MANAGER_UPDATE_CONFIG_INTERVAL || 3000
    ),
    outdatedConfigurationCheckEnabled:
      process.env.CODEC_MANAGER_OUTDATED_CONFIGURATION_CHECK_ENABLED?.toLowerCase() ===
      "true",
  };
  debug = {
    codecState: {
      enabled: process.env.DEBUG_CODEC_STATE_ENABLED?.toLowerCase() === "true",
      outputDir: process.env.DEBUG_CODEC_STATE_OUTPUT_DIR || "var/states",
    },
  };
  test_postgres = {
    url: process.env.TEST_POSTGRES_URL || "postgres://localhost:5432/test_db",
  };
}

export class ConfigHolder {
  private static _config: Config;

  private constructor() {}

  static init(
    replaceExisting = false,
    factory: () => Config = () => new Config()
  ): void {
    if (replaceExisting) {
      ConfigHolder._config = factory();
    } else {
      ConfigHolder._config ||= factory();
    }
  }

  static get config(): Config {
    if (!ConfigHolder._config) {
      throw new Error("ConfigHolder not initialized");
    }
    return ConfigHolder._config;
  }
}

export const config: IConfig = {
  get debug() {
    return ConfigHolder.config.debug;
  },
  get mqtt() {
    return ConfigHolder.config.mqtt;
  },
  get postgres() {
    return ConfigHolder.config.postgres;
  },
  get directus() {
    return ConfigHolder.config.directus;
  },
  get deviceMessageQueue() {
    return ConfigHolder.config.deviceMessageQueue;
  },
  get codecManager() {
    return ConfigHolder.config.codecManager;
  },
  get test_postgres() {
    return ConfigHolder.config.test_postgres;
  },
};
