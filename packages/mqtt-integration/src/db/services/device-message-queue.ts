import { EventEmitter } from "events";
import type { Sql as SQL } from "@/db/connection";
import { LoggerManager } from "../../log";
import type { DeviceMessageRequestWithDevice } from "../queries/types";
import {
  getPendingDeviceMessageRequests,
  getRetryableDeviceMessageRequests,
} from "../queries/device-message-request-queries";

/**
 * Configuration options for DeviceMessageQueueService
 */
export interface DeviceMessageQueueServiceConfig {
  /** Polling interval in milliseconds (default: 5000ms = 5 seconds) */
  pollingIntervalMs: number;

  /** Maximum number of messages to process per polling cycle (default: 50) */
  batchSize: number;

  /** Maximum number of retry messages to process per cycle (default: 20) */
  retryBatchSize: number;

  /** Whether to process retry messages (default: true) */
  enableRetryProcessing: boolean;

  /** Whether to start polling automatically on service creation (default: false) */
  autoStart: boolean;
}

/**
 * Default configuration for DeviceMessageQueueService
 */
export const DEFAULT_CONFIG: DeviceMessageQueueServiceConfig = {
  pollingIntervalMs: 5000, // 5 seconds
  batchSize: 50,
  retryBatchSize: 20,
  enableRetryProcessing: true,
  autoStart: false,
};

/**
 * Events emitted by DeviceMessageQueueService
 */
export interface DeviceMessageQueueServiceEvents {
  /** Emitted when a device message request is ready for processing */
  device_message_request: [message: DeviceMessageRequestWithDevice];

  /** Emitted when a device message request is ready for retry */
  device_message_retry: [message: DeviceMessageRequestWithDevice];

  /** Emitted when the service starts */
  service_started: [];

  /** Emitted when the service stops */
  service_stopped: [];

  /** Emitted when a polling cycle completes */
  polling_cycle_complete: [processedCount: number, retryCount: number];

  /** Emitted when an error occurs during polling */
  polling_error: [error: Error];
}

/**
 * DeviceMessageQueueService monitors the device_message_request table for pending messages
 * and processes them according to priority, schedule, and dependencies.
 *
 * The service has a single responsibility: process the queue. All other concerns
 * (e.g. message building, retries, etc.) are handled by other services.
 *
 * It works as an event emitter of device_message_request of interest. Other services
 * listen to the events and act accordingly.
 *
 * The service does not update the device_message_request table. It only queries it
 * and emits events.
 */
export class DeviceMessageQueueService extends EventEmitter<DeviceMessageQueueServiceEvents> {
  private static readonly log = LoggerManager.getLogger(
    "DeviceMessageQueueService"
  );

  private db: SQL;
  private config: DeviceMessageQueueServiceConfig;
  private pollingTimer: Timer | null = null;
  private isRunning: boolean = false;
  private isDestroyed: boolean = false;

  constructor(db: SQL, config: Partial<DeviceMessageQueueServiceConfig> = {}) {
    super();
    this.db = db;
    this.config = { ...DEFAULT_CONFIG, ...config };

    DeviceMessageQueueService.log.info(
      `DeviceMessageQueueService created with config:`,
      this.config
    );

    if (this.config.autoStart) {
      this.start();
    }
  }

  /**
   * Start the polling service
   */
  start(): void {
    if (this.isDestroyed) {
      throw new Error("Cannot start destroyed DeviceMessageQueueService");
    }

    if (this.isRunning) {
      DeviceMessageQueueService.log.warn(
        "DeviceMessageQueueService is already running"
      );
      return;
    }

    DeviceMessageQueueService.log.info("Starting DeviceMessageQueueService");
    this.isRunning = true;
    this.scheduleNextPoll();
    this.emit("service_started");
  }

  /**
   * Stop the polling service
   */
  stop(): void {
    if (!this.isRunning) {
      DeviceMessageQueueService.log.warn(
        "DeviceMessageQueueService is not running"
      );
      return;
    }

    DeviceMessageQueueService.log.info("Stopping DeviceMessageQueueService");
    this.isRunning = false;

    if (this.pollingTimer) {
      clearTimeout(this.pollingTimer);
      this.pollingTimer = null;
    }

    this.emit("service_stopped");
  }

  /**
   * Destroy the service and clean up resources
   */
  destroy(): void {
    DeviceMessageQueueService.log.info("Destroying DeviceMessageQueueService");
    this.stop();
    this.isDestroyed = true;
    this.removeAllListeners();
  }

  /**
   * Get the current service status
   */
  getStatus(): {
    isRunning: boolean;
    isDestroyed: boolean;
    config: DeviceMessageQueueServiceConfig;
  } {
    return {
      isRunning: this.isRunning,
      isDestroyed: this.isDestroyed,
      config: { ...this.config },
    };
  }

  /**
   * Update service configuration (only when stopped)
   */
  updateConfig(newConfig: Partial<DeviceMessageQueueServiceConfig>): void {
    if (this.isRunning) {
      throw new Error(
        "Cannot update config while service is running. Stop the service first."
      );
    }

    this.config = { ...this.config, ...newConfig };
    DeviceMessageQueueService.log.info(
      "DeviceMessageQueueService config updated:",
      this.config
    );
  }

  /**
   * Schedule the next polling cycle
   */
  private scheduleNextPoll(): void {
    if (!this.isRunning || this.isDestroyed) {
      return;
    }

    this.pollingTimer = setTimeout(() => {
      this.performPollingCycle();
    }, this.config.pollingIntervalMs);
  }

  /**
   * Perform a single polling cycle
   */
  private async performPollingCycle(): Promise<void> {
    if (!this.isRunning || this.isDestroyed) {
      return;
    }

    try {
      DeviceMessageQueueService.log.debug("Starting polling cycle");
      const referenceDate = new Date();

      let processedCount = 0;
      let retryCount = 0;

      // Process pending messages
      const pendingMessages = await getPendingDeviceMessageRequests(
        this.db,
        this.config.batchSize,
        referenceDate
      );

      for (const message of pendingMessages) {
        this.emit("device_message_request", message);
        processedCount++;
      }

      // Process retry messages if enabled
      if (this.config.enableRetryProcessing) {
        const retryMessages = await getRetryableDeviceMessageRequests(
          this.db,
          referenceDate,
          this.config.retryBatchSize
        );

        for (const message of retryMessages) {
          this.emit("device_message_retry", message);
          retryCount++;
        }
      }

      if (DeviceMessageQueueService.log.debugEnabled) {
        DeviceMessageQueueService.log.debug(
          `Polling cycle complete: processed=${processedCount}, retry=${retryCount}`
        );
      }

      this.emit("polling_cycle_complete", processedCount, retryCount);
    } catch (error) {
      DeviceMessageQueueService.log.error("Error during polling cycle:", error);
      this.emit("polling_error", error as Error);
    } finally {
      // Schedule next poll regardless of success/failure
      this.scheduleNextPoll();
    }
  }

  /**
   * Trigger an immediate polling cycle (useful for testing or manual triggering)
   */
  async triggerImmediatePoll(): Promise<void> {
    if (!this.isRunning) {
      throw new Error("Cannot trigger poll on stopped service");
    }

    DeviceMessageQueueService.log.info("Triggering immediate polling cycle");
    await this.performPollingCycle();
  }
}
