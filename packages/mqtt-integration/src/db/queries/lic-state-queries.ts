import type { Sql as SQL } from "@/db/connection";
import type { LICStateRecord, LICStateRecordWithDevice } from "./types";

/**
 * Get LIC state by device ID
 * @param db - Database connection
 * @param deviceId - Device UUID
 * @returns LIC state record or null if not found
 */
export async function getLICStateByDeviceId(
  db: SQL,
  deviceId: string
): Promise<LICStateRecord | null> {
  const [result] = await db<LICStateRecord[]>`
    SELECT * FROM lic_state WHERE device = ${deviceId}
  `;

  return result || null;
}

/**
 * Get LIC state with device information
 * @param db - Database connection
 * @param deviceId - Device UUID
 * @returns LIC state record with device info or null if not found
 */
export async function getLICStateWithDevice(
  db: SQL,
  deviceId: string
): Promise<LICStateRecordWithDevice | null> {
  const [result] = await db<LICStateRecordWithDevice[]>`
    SELECT 
      ls.*,
      d.id as "device.id",
      d.identifier as "device.identifier",
      d.model as "device.model",
      d.date_created as "device.date_created",
      d.user_created as "device.user_created",
      d.date_updated as "device.date_updated",
      d.user_updated as "device.user_updated",
      d.metadata as "device.metadata",
      d.notes as "device.notes"
    FROM lic_state ls
    INNER JOIN device d ON ls.device = d.id
    WHERE ls.device = ${deviceId}
  `;

  if (!result) return null;

  // Transform flat result into nested structure
  const { device: deviceData, ...licStateData } = result as any;
  
  return {
    ...licStateData,
    device: deviceData,
  };
}

/**
 * Get all LIC states for a property
 * @param db - Database connection
 * @param propertyId - Property UUID
 * @returns Array of LIC state records with device info
 */
export async function getLICStatesByProperty(
  db: SQL,
  propertyId: string
): Promise<LICStateRecordWithDevice[]> {
  const results = await db<any[]>`
    SELECT 
      ls.*,
      d.id as "device.id",
      d.identifier as "device.identifier",
      d.model as "device.model",
      d.date_created as "device.date_created",
      d.user_created as "device.user_created",
      d.date_updated as "device.date_updated",
      d.user_updated as "device.user_updated",
      d.metadata as "device.metadata",
      d.notes as "device.notes"
    FROM lic_state ls
    INNER JOIN device d ON ls.device = d.id
    INNER JOIN property_device pd ON d.id = pd.device
    WHERE pd.property = ${propertyId}
      AND d.model = 'LIC'
  `;

  // Transform flat results into nested structure
  return results.map((result) => {
    const { device: deviceData, ...licStateData } = result;
    
    return {
      ...licStateData,
      device: deviceData,
    };
  });
}

/**
 * Get all LIC states
 * @param db - Database connection
 * @returns Array of all LIC state records
 */
export async function getAllLICStates(
  db: SQL
): Promise<LICStateRecord[]> {
  return await db<LICStateRecord[]>`
    SELECT * FROM lic_state
    ORDER BY date_updated DESC
  `;
}

/**
 * Get devices that have out-of-sync configurations
 * @param db - Database connection
 * @returns Array of device IDs with out-of-sync configurations
 */
export async function getOutOfSyncDevices(
  db: SQL
): Promise<Array<{
  device: string;
  out_of_sync_types: string[];
}>> {
  const results = await db<LICStateRecord[]>`
    SELECT * FROM lic_state
    WHERE 
      (last_devices_request IS NOT NULL AND current_devices_timestamp IS NOT NULL AND current_devices_timestamp < last_devices_request)
      OR (last_scheduling_request IS NOT NULL AND current_scheduling_timestamp IS NOT NULL AND current_scheduling_timestamp < last_scheduling_request)
      OR (last_dev_scheduling_request IS NOT NULL AND current_dev_scheduling_timestamp IS NOT NULL AND current_dev_scheduling_timestamp < last_dev_scheduling_request)
      OR (last_automation_request IS NOT NULL AND current_automation_timestamp IS NOT NULL AND current_automation_timestamp < last_automation_request)
      OR (last_config_request IS NOT NULL AND current_config_timestamp IS NOT NULL AND current_config_timestamp < last_config_request)
  `;

  return results.map((state) => {
    const outOfSyncTypes: string[] = [];

    if (
      state.last_devices_request &&
      state.current_devices_timestamp &&
      state.current_devices_timestamp < state.last_devices_request
    ) {
      outOfSyncTypes.push("devices");
    }

    if (
      state.last_scheduling_request &&
      state.current_scheduling_timestamp &&
      state.current_scheduling_timestamp < state.last_scheduling_request
    ) {
      outOfSyncTypes.push("scheduling");
    }

    if (
      state.last_dev_scheduling_request &&
      state.current_dev_scheduling_timestamp &&
      state.current_dev_scheduling_timestamp < state.last_dev_scheduling_request
    ) {
      outOfSyncTypes.push("dev_scheduling");
    }

    if (
      state.last_automation_request &&
      state.current_automation_timestamp &&
      state.current_automation_timestamp < state.last_automation_request
    ) {
      outOfSyncTypes.push("automation");
    }

    if (
      state.last_config_request &&
      state.current_config_timestamp &&
      state.current_config_timestamp < state.last_config_request
    ) {
      outOfSyncTypes.push("config");
    }

    return {
      device: state.device,
      out_of_sync_types: outOfSyncTypes,
    };
  });
}

/**
 * Check if a device has any pending requests (requests newer than current timestamps)
 * @param db - Database connection
 * @param deviceId - Device UUID
 * @returns True if device has pending requests
 */
export async function hasLICStatePendingRequests(
  db: SQL,
  deviceId: string
): Promise<boolean> {
  const [result] = await db<{ has_pending: boolean }[]>`
    SELECT 
      CASE WHEN (
        (last_devices_request IS NOT NULL AND current_devices_timestamp IS NOT NULL AND current_devices_timestamp < last_devices_request)
        OR (last_scheduling_request IS NOT NULL AND current_scheduling_timestamp IS NOT NULL AND current_scheduling_timestamp < last_scheduling_request)
        OR (last_dev_scheduling_request IS NOT NULL AND current_dev_scheduling_timestamp IS NOT NULL AND current_dev_scheduling_timestamp < last_dev_scheduling_request)
        OR (last_automation_request IS NOT NULL AND current_automation_timestamp IS NOT NULL AND current_automation_timestamp < last_automation_request)
        OR (last_config_request IS NOT NULL AND current_config_timestamp IS NOT NULL AND current_config_timestamp < last_config_request)
      ) THEN true
      ELSE false
      END as has_pending
    FROM lic_state
    WHERE device = ${deviceId}
  `;

  return result?.has_pending || false;
}