import type { Sql as SQL } from "@/db/connection";
import type {
  <PERSON><PERSON><PERSON><PERSON>ageRequest,
  DeviceMessageRequestWithDevice,
  DeviceMessageRequestStatus,
  DeviceModel,
} from "./types";

/**
 * Query pending device message requests that are ready to be processed.
 * This is the main query used by DeviceMessageQueueService to fetch messages
 * that should be processed based on status, schedule, and dependencies.
 *
 * Query logic:
 * - Status must be 'pending' or 'processing' (for retry scenarios)
 * - scheduled_at must be <= current time (message is due)
 * - expires_at must be null or > current time (message not expired)
 * - If parent_message_id exists, parent must be in 'sent' or 'acknowledged' status
 * - Order by priority (1=highest), then scheduled_at (earliest first)
 *
 * @param db - Database connection
 * @param limit - Maximum number of messages to return (default: 50)
 * @param referenceDate - Current timestamp for scheduling comparison
 * @returns Array of pending device message requests with device info
 */
export async function getPendingDeviceMessageRequests(
  db: SQL,
  limit: number = 50,
  referenceDate: Date = new Date()
): Promise<DeviceMessageRequestWithDevice[]> {
  const messages = await db<
    (DeviceMessageRequest & {
      device_identifier: string;
      device_model: string;
      device_id: string;
      device_date_created: string;
      device_user_created: string | null;
      device_date_updated: string;
      device_user_updated: string | null;
      device_metadata: any;
      device_notes: string | null;
    })[]
  >`
    SELECT
      dmr.*,
      d.identifier as device_identifier,
      d.model as device_model,
      d.id as device_id,
      d.date_created as device_date_created,
      d.user_created as device_user_created,
      d.date_updated as device_date_updated,
      d.user_updated as device_user_updated,
      d.metadata as device_metadata,
      d.notes as device_notes
    FROM device_message_request dmr
    INNER JOIN device d ON dmr.device = d.id
    WHERE
      -- Message is ready for processing
      dmr.status IN ('pending', 'processing')

      -- Message is scheduled to run now or in the past
      AND dmr.scheduled_at <= ${referenceDate}

      -- Message has not expired
      AND (dmr.expires_at IS NULL OR dmr.expires_at > ${referenceDate})

      -- If message has parent, parent must be completed
      AND (
        dmr.parent_message_id IS NULL
        OR EXISTS (
          SELECT 1 FROM device_message_request parent
          WHERE parent.id = dmr.parent_message_id
          AND parent.status IN ('sent', 'acknowledged')
        )
      )

      -- Message has not exceeded max attempts
      AND dmr.attempts < dmr.max_attempts

    ORDER BY
      dmr.priority ASC,  -- 1 = highest priority
      dmr.scheduled_at ASC,  -- earliest scheduled first
      dmr.date_created ASC  -- oldest first as tiebreaker

    LIMIT ${limit}
  `;

  return messages.map((msg) => ({
    ...msg,
    device: {
      id: msg.device_id,
      identifier: msg.device_identifier,
      model: msg.device_model as DeviceModel,
      date_created: msg.device_date_created,
      user_created: msg.device_user_created,
      date_updated: msg.device_date_updated,
      user_updated: msg.device_user_updated,
      metadata: msg.device_metadata,
      notes: msg.device_notes,
    },
  }));
}

/**
 * Query device message requests by device identifier.
 * Used for device-specific message lookup and monitoring.
 *
 * @param db - Database connection
 * @param deviceIdentifier - Device identifier (e.g., "9C821FA2B9E8")
 * @param status - Optional status filter
 * @param limit - Maximum number of messages to return
 * @returns Array of device message requests for the specified device
 */
export async function getDeviceMessageRequestsByDevice(
  db: SQL,
  deviceIdentifier: string,
  status?: DeviceMessageRequestStatus,
  limit: number = 100
): Promise<DeviceMessageRequest[]> {
  const statusFilter = status ? db`AND dmr.status = ${status}` : db``;

  const messages = await db<DeviceMessageRequest[]>`
    SELECT dmr.*
    FROM device_message_request dmr
    INNER JOIN device d ON dmr.device = d.id
    WHERE 
      d.identifier = ${deviceIdentifier}
      ${statusFilter}
    ORDER BY 
      dmr.date_created DESC
    LIMIT ${limit}
  `;

  return messages;
}

/**
 * Query device message requests that need retry processing.
 * Finds messages in 'failed' status that are eligible for retry based on
 * retry_delay_seconds and max_attempts.
 *
 * @param db - Database connection
 * @param referenceDate - Current timestamp for retry delay calculation
 * @param limit - Maximum number of messages to return
 * @returns Array of device message requests ready for retry
 */
export async function getRetryableDeviceMessageRequests(
  db: SQL,
  referenceDate: Date = new Date(),
  limit: number = 20
): Promise<DeviceMessageRequestWithDevice[]> {
  const messages = await db<
    (DeviceMessageRequest & {
      device_identifier: string;
      device_model: string;
      device_id: string;
      device_date_created: string;
      device_user_created: string | null;
      device_date_updated: string;
      device_user_updated: string | null;
      device_metadata: any;
      device_notes: string | null;
    })[]
  >`
    SELECT
      dmr.*,
      d.identifier as device_identifier,
      d.model as device_model,
      d.id as device_id,
      d.date_created as device_date_created,
      d.user_created as device_user_created,
      d.date_updated as device_date_updated,
      d.user_updated as device_user_updated,
      d.metadata as device_metadata,
      d.notes as device_notes
    FROM device_message_request dmr
    INNER JOIN device d ON dmr.device = d.id
    WHERE
      dmr.status = 'failed'
      AND dmr.attempts < dmr.max_attempts
      AND (
        dmr.retry_delay_seconds IS NULL
        OR dmr.date_updated + INTERVAL '1 second' * dmr.retry_delay_seconds <= ${referenceDate}
      )
      AND (dmr.expires_at IS NULL OR dmr.expires_at > ${referenceDate})
    ORDER BY
      dmr.priority ASC,
      dmr.date_updated ASC
    LIMIT ${limit}
  `;

  return messages.map((msg) => ({
    ...msg,
    device: {
      id: msg.device_id,
      identifier: msg.device_identifier,
      model: msg.device_model as DeviceModel,
      date_created: msg.device_date_created,
      user_created: msg.device_user_created,
      date_updated: msg.device_date_updated,
      user_updated: msg.device_user_updated,
      metadata: msg.device_metadata,
      notes: msg.device_notes,
    },
  }));
}

/**
 * Query device message requests by correlation ID.
 * Used for tracking bulk operations and related message groups.
 *
 * @param db - Database connection
 * @param correlationId - Correlation UUID
 * @returns Array of device message requests with the same correlation ID
 */
export async function getDeviceMessageRequestsByCorrelation(
  db: SQL,
  correlationId: string
): Promise<DeviceMessageRequestWithDevice[]> {
  const messages = await db<
    (DeviceMessageRequest & {
      device_identifier: string;
      device_model: string;
      device_id: string;
      device_date_created: string;
      device_user_created: string | null;
      device_date_updated: string;
      device_user_updated: string | null;
      device_metadata: any;
      device_notes: string | null;
    })[]
  >`
    SELECT
      dmr.*,
      d.identifier as device_identifier,
      d.model as device_model,
      d.id as device_id,
      d.date_created as device_date_created,
      d.user_created as device_user_created,
      d.date_updated as device_date_updated,
      d.user_updated as device_user_updated,
      d.metadata as device_metadata,
      d.notes as device_notes
    FROM device_message_request dmr
    INNER JOIN device d ON dmr.device = d.id
    WHERE dmr.correlation_id = ${correlationId}
    ORDER BY
      dmr.priority ASC,
      dmr.scheduled_at ASC,
      dmr.date_created ASC
  `;

  return messages.map((msg) => ({
    ...msg,
    device: {
      id: msg.device_id,
      identifier: msg.device_identifier,
      model: msg.device_model as DeviceModel,
      date_created: msg.device_date_created,
      user_created: msg.device_user_created,
      date_updated: msg.device_date_updated,
      user_updated: msg.device_user_updated,
      metadata: msg.device_metadata,
      notes: msg.device_notes,
    },
  }));
}

/**
 * Get a single device message request by ID.
 *
 * @param db - Database connection
 * @param messageId - Message UUID
 * @returns Device message request or null if not found
 */
export async function getDeviceMessageRequestById(
  db: SQL,
  messageId: string
): Promise<DeviceMessageRequestWithDevice | null> {
  const [message] = await db<
    (DeviceMessageRequest & {
      device_identifier: string;
      device_model: string;
      device_id: string;
      device_date_created: string;
      device_user_created: string | null;
      device_date_updated: string;
      device_user_updated: string | null;
      device_metadata: any;
      device_notes: string | null;
    })[]
  >`
    SELECT
      dmr.*,
      d.identifier as device_identifier,
      d.model as device_model,
      d.id as device_id,
      d.date_created as device_date_created,
      d.user_created as device_user_created,
      d.date_updated as device_date_updated,
      d.user_updated as device_user_updated,
      d.metadata as device_metadata,
      d.notes as device_notes
    FROM device_message_request dmr
    INNER JOIN device d ON dmr.device = d.id
    WHERE dmr.id = ${messageId}
    LIMIT 1
  `;

  if (!message) return null;

  return {
    ...message,
    device: {
      id: message.device_id,
      identifier: message.device_identifier,
      model: message.device_model as DeviceModel,
      date_created: message.device_date_created,
      user_created: message.device_user_created,
      date_updated: message.device_date_updated,
      user_updated: message.device_user_updated,
      metadata: message.device_metadata,
      notes: message.device_notes,
    },
  };
}
