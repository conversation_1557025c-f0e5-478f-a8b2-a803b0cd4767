import type { Sql as SQL } from "@/db/connection";
import type { <PERSON><PERSON>ump<PERSON>ithController } from "./types";

/**
 * List all service water pumps where its water_pump_controller has an active property_device record at the reference date
 * that is mapped as mesh_property_device to a lic_property_device through mesh_device_mapping  record that is active at the reference date
 * and the lic_property_device has a property_device record that is active at the reference date
 * and the device of property_device of the lic_property_device has the identifier matching the licIdentifier.
 * @param db
 * @param licIdentifier
 * @param referenceDate
 * @returns
 */
export async function listServiceWaterPumpsByLICIdentifier(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date = new Date()
): Promise<WaterPumpWithController[]> {
  return await db<WaterPumpWithController[]>`
    select 
     service_pump.*,
     jsonb_build_object(
        'id', mesh_device.id,
        'identifier', mesh_device.identifier,
        'model', mesh_device.model,
        'date_created', mesh_device.date_created,
        'user_created', mesh_device.user_created,
        'date_updated', mesh_device.date_updated,
        'user_updated', mesh_device.user_updated,
        'metadata', mesh_device.metadata,
        'notes', mesh_device.notes
      ) as water_pump_controller
    from device as lic_device
    inner join property_device as lic_property_device
      on lic_device.id = lic_property_device.device
    inner join mesh_device_mapping as mdm
      on lic_property_device.id = mdm.lic_property_device
    inner join property_device as mesh_property_device
      on mdm.mesh_property_device = mesh_property_device.id
    inner join device as mesh_device
      on mesh_property_device.device = mesh_device.id
    inner join water_pump as service_pump
      on service_pump.water_pump_controller = mesh_device.id
    where 
      lic_device.identifier = ${licIdentifier}
      and lic_device.model = 'LIC'
      and ${referenceDate} >= COALESCE(lic_property_device.start_date, '-infinity'::timestamp)
      and ${referenceDate} <= COALESCE(lic_property_device.end_date, 'infinity'::timestamp)
      and ${referenceDate} >= COALESCE(mdm.start_date, '-infinity'::timestamp)
      and ${referenceDate} <= COALESCE(mdm.end_date, 'infinity'::timestamp)
      and ${referenceDate} >= COALESCE(mesh_property_device.start_date, '-infinity'::timestamp)
      and ${referenceDate} <= COALESCE(mesh_property_device.end_date, 'infinity'::timestamp)
      and service_pump.pump_type = 'SERVICE'
      and service_pump.property = lic_property_device.property
      and mesh_property_device.property = lic_property_device.property
    ORDER BY service_pump.date_created ASC
  `;
}
