import type { Sql as SQL } from "@/db/connection";
import type {
  CurrentIrrigationPlanStateInsert,
  CurrentIrrigationPlanStateUpdate,
} from "./types";
import type { CurrentIrrigationPlanState } from "../queries/types";

/**
 * Insert a new current irrigation plan state record
 * @param db - Database connection
 * @param record - Irrigation plan state data to insert
 * @returns The inserted record
 */
export async function insertCurrentIrrigationPlanState(
  db: SQL,
  record: CurrentIrrigationPlanStateInsert
): Promise<CurrentIrrigationPlanState | undefined> {
  const [r] = await db<
    CurrentIrrigationPlanState[]
  >`INSERT INTO current_irrigation_plan_state (
      irrigation_plan, 
      packet_date, 
      start_time, 
      end_time, 
      activated_steps, 
      activated_ferti_steps, 
      waterpump_working, 
      backwash_start_time, 
      uses_waterpump, 
      uses_ferti
    ) VALUES (
      ${record.irrigation_plan},
      ${record.packet_date},
      ${record.start_time},
      ${record.end_time},
      ${record.activated_steps as any},
      ${record.activated_ferti_steps as any},
      ${record.waterpump_working},
      ${record.backwash_start_time},
      ${record.uses_waterpump},
      ${record.uses_ferti}
    ) RETURNING *`;
  return r;
}

/**
 * Update an existing current irrigation plan state record
 * @param db - Database connection
 * @param irrigationPlanId - Irrigation plan ID to update
 * @param updates - Fields to update
 * @returns The updated record or null if not found
 */
export async function updateCurrentIrrigationPlanState(
  db: SQL,
  irrigationPlanId: string,
  updates: CurrentIrrigationPlanStateUpdate
): Promise<CurrentIrrigationPlanState | null> {
  // Check if there are any fields to update
  const hasUpdates = Object.keys(updates).some(
    (key) => updates[key as keyof CurrentIrrigationPlanStateUpdate] !== undefined
  );

  if (!hasUpdates) {
    throw new Error("No fields to update");
  }

  const [result] = await db<CurrentIrrigationPlanState[]>`
    UPDATE current_irrigation_plan_state 
    SET ${
      updates.packet_date !== undefined
        ? db`packet_date = ${updates.packet_date},`
        : db``
    }${
    updates.start_time !== undefined
      ? db`start_time = ${updates.start_time},`
      : db``
  }${
    updates.end_time !== undefined
      ? db`end_time = ${updates.end_time},`
      : db``
  }${
    updates.activated_steps !== undefined
      ? db`activated_steps = ${updates.activated_steps as any},`
      : db``
  }${
    updates.activated_ferti_steps !== undefined
      ? db`activated_ferti_steps = ${updates.activated_ferti_steps as any},`
      : db``
  }${
    updates.waterpump_working !== undefined
      ? db`waterpump_working = ${updates.waterpump_working},`
      : db``
  }${
    updates.backwash_start_time !== undefined
      ? db`backwash_start_time = ${updates.backwash_start_time},`
      : db``
  }${
    updates.uses_waterpump !== undefined
      ? db`uses_waterpump = ${updates.uses_waterpump},`
      : db``
  }${
    updates.uses_ferti !== undefined
      ? db`uses_ferti = ${updates.uses_ferti},`
      : db``
  }
      date_updated = NOW()
    WHERE irrigation_plan = ${irrigationPlanId}
    RETURNING *
  `;

  return result || null;
}

/**
 * Upsert (insert or update) a current irrigation plan state record
 * @param db - Database connection
 * @param record - Irrigation plan state data to upsert
 * @returns The upserted record
 */
export async function upsertCurrentIrrigationPlanState(
  db: SQL,
  record: CurrentIrrigationPlanStateInsert
): Promise<CurrentIrrigationPlanState | undefined> {
  const [r] = await db<
    CurrentIrrigationPlanState[]
  >`INSERT INTO current_irrigation_plan_state (
      irrigation_plan, 
      packet_date, 
      start_time, 
      end_time, 
      activated_steps, 
      activated_ferti_steps, 
      waterpump_working, 
      backwash_start_time, 
      uses_waterpump, 
      uses_ferti
    ) VALUES (
      ${record.irrigation_plan},
      ${record.packet_date},
      ${record.start_time},
      ${record.end_time},
      ${record.activated_steps as any},
      ${record.activated_ferti_steps as any},
      ${record.waterpump_working},
      ${record.backwash_start_time},
      ${record.uses_waterpump},
      ${record.uses_ferti}
    ) 
    ON CONFLICT (irrigation_plan) 
    DO UPDATE SET 
      packet_date = EXCLUDED.packet_date,
      start_time = EXCLUDED.start_time,
      end_time = CASE 
        WHEN current_irrigation_plan_state.start_time IS NOT DISTINCT FROM EXCLUDED.start_time 
          THEN COALESCE(current_irrigation_plan_state.end_time, EXCLUDED.end_time)
        ELSE EXCLUDED.end_time
      END,
      activated_steps = EXCLUDED.activated_steps,
      activated_ferti_steps = EXCLUDED.activated_ferti_steps,
      waterpump_working = EXCLUDED.waterpump_working,
      backwash_start_time = EXCLUDED.backwash_start_time,
      uses_waterpump = EXCLUDED.uses_waterpump,
      uses_ferti = EXCLUDED.uses_ferti,
      date_updated = NOW()
    RETURNING *`;
  return r;
}

/**
 * Batch upsert multiple current irrigation plan state records
 * @param db - Database connection
 * @param records - Array of irrigation plan state data to upsert
 * @returns Array of upserted records
 */
export async function batchUpsertCurrentIrrigationPlanStates(
  db: SQL,
  records: CurrentIrrigationPlanStateInsert[]
): Promise<CurrentIrrigationPlanState[]> {
  if (records.length === 0) {
    return [];
  }

  return await db.begin(async (tx) => {
    const results: CurrentIrrigationPlanState[] = [];

    for (const record of records) {
      try {
        const result = await upsertCurrentIrrigationPlanState(tx, record);
        if (result) {
          results.push(result);
        }
      } catch (error) {
        console.error(
          `Error upserting irrigation plan state for plan ${record.irrigation_plan}:`,
          error
        );
        // Continue processing other records instead of failing the entire batch
        // This matches the conflict resolution strategy described in the task
      }
    }

    return results;
  });
}

/**
 * Get current irrigation plan state by irrigation plan ID
 * @param db - Database connection
 * @param irrigationPlanId - Irrigation plan ID to lookup
 * @returns The current irrigation plan state or null if not found
 */
export async function getCurrentIrrigationPlanState(
  db: SQL,
  irrigationPlanId: string
): Promise<CurrentIrrigationPlanState | null> {
  const [r] = await db<
    CurrentIrrigationPlanState[]
  >`SELECT * FROM current_irrigation_plan_state WHERE irrigation_plan = ${irrigationPlanId}`;
  return r || null;
}

/**
 * Get all current irrigation plan states for irrigation plans associated with a specific LIC device
 * @param db - Database connection
 * @param licDeviceId - LIC device ID
 * @returns Array of current irrigation plan states
 */
export async function getCurrentIrrigationPlanStatesByLIC(
  db: SQL,
  licDeviceId: string
): Promise<CurrentIrrigationPlanState[]> {
  return await db<CurrentIrrigationPlanState[]>`SELECT cips.* 
    FROM current_irrigation_plan_state cips
    INNER JOIN irrigation_plan ip ON cips.irrigation_plan = ip.id
    INNER JOIN project p ON ip.project = p.id
    WHERE p.localized_irrigation_controller = ${licDeviceId}
    ORDER BY ip.name`;
}

/**
 * Get all current irrigation plan states for irrigation plans in a specific project
 * @param db - Database connection
 * @param projectId - Project ID
 * @returns Array of current irrigation plan states
 */
export async function getCurrentIrrigationPlanStatesByProject(
  db: SQL,
  projectId: string
): Promise<CurrentIrrigationPlanState[]> {
  return await db<CurrentIrrigationPlanState[]>`SELECT cips.* 
    FROM current_irrigation_plan_state cips
    INNER JOIN irrigation_plan ip ON cips.irrigation_plan = ip.id
    WHERE ip.project = ${projectId}
    ORDER BY ip.name`;
}

/**
 * Delete current irrigation plan state by irrigation plan ID
 * @param db - Database connection
 * @param irrigationPlanId - Irrigation plan ID to delete
 * @returns True if deleted, false if not found
 */
export async function deleteCurrentIrrigationPlanState(
  db: SQL,
  irrigationPlanId: string
): Promise<boolean> {
  const result =
    await db`DELETE FROM current_irrigation_plan_state WHERE irrigation_plan = ${irrigationPlanId}`;
  return result.count > 0;
}
