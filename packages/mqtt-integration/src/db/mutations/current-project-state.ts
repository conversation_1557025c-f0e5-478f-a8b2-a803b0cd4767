import type { Sql as SQL } from "@/db/connection";
import type {
  CurrentProjectStateInsert,
  CurrentProjectStateUpdate,
} from "./types";
import type { CurrentProjectState } from "../queries/types";

/**
 * Insert a new current project state record
 * @param db - Database connection
 * @param record - Project state data to insert
 * @returns The inserted record
 */
export async function insertCurrentProjectState(
  db: SQL,
  record: CurrentProjectStateInsert
): Promise<CurrentProjectState | undefined> {
  const [r] = await db<
    CurrentProjectState[]
  >`INSERT INTO current_project_state (
      project, 
      irrigation_status, 
      fertigation_status, 
      backwash_status, 
      sectors
    ) VALUES (
      ${record.project},
      ${record.irrigation_status},
      ${record.fertigation_status},
      ${record.backwash_status},
      ${record.sectors as any}
    ) RETURNING *`;
  return r;
}

/**
 * Update an existing current project state record
 * @param db - Database connection
 * @param projectId - Project ID to update
 * @param updates - Fields to update
 * @returns The updated record or null if not found
 */
export async function updateCurrentProjectState(
  db: SQL,
  projectId: string,
  updates: CurrentProjectStateUpdate
): Promise<CurrentProjectState | null> {
  // Check if there are any fields to update
  const hasUpdates = Object.keys(updates).some(
    (key) => updates[key as keyof CurrentProjectStateUpdate] !== undefined
  );

  if (!hasUpdates) {
    throw new Error("No fields to update");
  }

  const [result] = await db<CurrentProjectState[]>`
    UPDATE current_project_state 
    SET ${
      updates.irrigation_status !== undefined
        ? db`irrigation_status = ${updates.irrigation_status},`
        : db``
    }${
    updates.fertigation_status !== undefined
      ? db`fertigation_status = ${updates.fertigation_status},`
      : db``
  }${
    updates.backwash_status !== undefined
      ? db`backwash_status = ${updates.backwash_status},`
      : db``
  }${
    updates.sectors !== undefined
      ? db`sectors = ${updates.sectors as any},`
      : db``
  }
      date_updated = NOW()
    WHERE project = ${projectId}
    RETURNING *
  `;

  return result || null;
}

/**
 * Upsert (insert or update) a current project state record
 * @param db - Database connection
 * @param record - Project state data to upsert
 * @returns The upserted record
 */
export async function upsertCurrentProjectState(
  db: SQL,
  record: CurrentProjectStateInsert
): Promise<CurrentProjectState | undefined> {
  const [r] = await db<
    CurrentProjectState[]
  >`INSERT INTO current_project_state (
      project, 
      irrigation_status, 
      fertigation_status, 
      backwash_status, 
      sectors
    ) VALUES (
      ${record.project},
      ${record.irrigation_status},
      ${record.fertigation_status},
      ${record.backwash_status},
      ${record.sectors as any}
    ) 
    ON CONFLICT (project) 
    DO UPDATE SET 
      irrigation_status = EXCLUDED.irrigation_status,
      fertigation_status = EXCLUDED.fertigation_status,
      backwash_status = EXCLUDED.backwash_status,
      sectors = EXCLUDED.sectors,
      date_updated = NOW()
    RETURNING *`;
  return r;
}

/**
 * Batch upsert multiple current project state records
 * @param db - Database connection
 * @param records - Array of project state data to upsert
 * @returns Array of upserted records
 */
export async function batchUpsertCurrentProjectStates(
  db: SQL,
  records: CurrentProjectStateInsert[]
): Promise<CurrentProjectState[]> {
  if (records.length === 0) {
    return [];
  }

  return await db.begin(async (tx) => {
    const results: CurrentProjectState[] = [];

    for (const record of records) {
      const result = await upsertCurrentProjectState(tx, record);
      if (result) {
        results.push(result);
      }
    }

    return results;
  });
}

/**
 * Get current project state by project ID
 * @param db - Database connection
 * @param projectId - Project ID to lookup
 * @returns The current project state or null if not found
 */
export async function getCurrentProjectState(
  db: SQL,
  projectId: string
): Promise<CurrentProjectState | null> {
  const [r] = await db<
    CurrentProjectState[]
  >`SELECT * FROM current_project_state WHERE project = ${projectId}`;
  return r || null;
}

/**
 * Get all current project states for projects associated with a specific LIC device
 * @param db - Database connection
 * @param licDeviceId - LIC device ID
 * @returns Array of current project states
 */
export async function getCurrentProjectStatesByLIC(
  db: SQL,
  licDeviceId: string
): Promise<CurrentProjectState[]> {
  return await db<CurrentProjectState[]>`SELECT cps.* 
    FROM current_project_state cps
    INNER JOIN project p ON cps.project = p.id
    WHERE p.localized_irrigation_controller = ${licDeviceId}
    ORDER BY p.name`;
}

/**
 * Delete current project state by project ID
 * @param db - Database connection
 * @param projectId - Project ID to delete
 * @returns True if deleted, false if not found
 */
export async function deleteCurrentProjectState(
  db: SQL,
  projectId: string
): Promise<boolean> {
  const result =
    await db`DELETE FROM current_project_state WHERE project = ${projectId}`;
  return result.count > 0;
}
