import type { Sql as SQL } from "@/db/connection";
import type { LICStateRecord } from "../queries/types";
import type { LICStateInsert, LICStateUpdate } from "./types";
import type { LICState } from "../../irriganet/db-loader/types";

/**
 * Upsert LIC state record - creates if not exists, updates if exists
 * @param db - Database connection
 * @param deviceId - Device UUID
 * @param data - LIC state data to upsert
 * @returns Upserted LIC state record
 */
export async function upsertLICState(
  db: SQL,
  deviceId: string,
  data: Partial<LICStateInsert>
): Promise<LICStateRecord> {
  const [upserted] = await db<LICStateRecord[]>`
    INSERT INTO lic_state ${db({ device: deviceId, ...data })}
    ON CONFLICT (device) 
    DO UPDATE SET 
      ${db(data)}
    RETURNING *
  `;

  if (!upserted) {
    throw new Error("Failed to upsert lic_state record");
  }

  return upserted;
}

/**
 * Update LIC state with full LICState object
 * @param db - Database connection
 * @param deviceId - Device UUID
 * @param licState - Complete LIC state object
 * @param stateDate - State date
 * @returns Updated LIC state record
 */
export async function updateLICStateFromLICState(
  db: SQL,
  deviceId: string,
  licState: LICState,
  stateDate: Date | string
): Promise<LICStateRecord> {
  return upsertLICState(db, deviceId, {
    lic: licState.lic,
    groups: licState.groups,
    devices: licState.devices,
    mesh_devices: licState.meshDevices,
    schedules: licState.schedules,
    sector_schedules: licState.sectorSchedules,
    device_schedules: licState.deviceSchedules,
    state_date: stateDate,
  });
}

/**
 * Update request timestamp for a specific configuration type
 * @param db - Database connection
 * @param deviceId - Device UUID
 * @param configType - Configuration type
 * @param timestamp - Request timestamp
 * @param stateDate - State date
 * @returns Updated LIC state record
 */
export async function updateLICStateRequestTimestamp(
  db: SQL,
  deviceId: string,
  configType:
    | "devices"
    | "scheduling"
    | "dev_scheduling"
    | "automation"
    | "config",
  timestamp: number,
  stateDate: Date | string
): Promise<LICStateRecord> {
  const fieldMap = {
    devices: "last_devices_request",
    scheduling: "last_scheduling_request",
    dev_scheduling: "last_dev_scheduling_request",
    automation: "last_automation_request",
    config: "last_config_request",
  } as const;

  const field = fieldMap[configType];
  const timestampUpdate = {
    [field]: timestamp,
    state_date: stateDate,
  };
  if (configType === "devices") {
    timestampUpdate.last_dev_scheduling_request = timestamp;
  }
  return upsertLICState(db, deviceId, timestampUpdate);
}

/**
 * Update current timestamps from InfoPackage
 * @param db - Database connection
 * @param deviceId - Device UUID
 * @param infoPackage - InfoPackage data containing current timestamps
 * @returns Updated LIC state record
 */
export async function updateLICStateFromInfoPackage(
  db: SQL,
  deviceId: string,
  infoPackage: {
    devices_id?: number;
    scheduling_id?: number;
    dev_scheduling_id?: number;
    automation_id?: number;
    config_id?: number;
  },
  stateDate: Date | string
): Promise<LICStateRecord> {
  const updateData: LICStateUpdate = {
    state_date: stateDate,
  };

  if (infoPackage.devices_id !== undefined) {
    updateData.current_devices_timestamp = infoPackage.devices_id;
  }
  if (infoPackage.scheduling_id !== undefined) {
    updateData.current_scheduling_timestamp = infoPackage.scheduling_id;
  }
  if (infoPackage.dev_scheduling_id !== undefined) {
    updateData.current_dev_scheduling_timestamp = infoPackage.dev_scheduling_id;
  }
  if (infoPackage.automation_id !== undefined) {
    updateData.current_automation_timestamp = infoPackage.automation_id;
  }
  if (infoPackage.config_id !== undefined) {
    updateData.current_config_timestamp = infoPackage.config_id;
  }

  return upsertLICState(db, deviceId, updateData);
}

/**
 * Get LIC state record by device ID
 * @param db - Database connection
 * @param deviceId - Device UUID
 * @returns LIC state record or null if not found
 */
export async function getLICState(
  db: SQL,
  deviceId: string
): Promise<LICStateRecord | null> {
  const [record] = await db<LICStateRecord[]>`
    SELECT * FROM lic_state WHERE device = ${deviceId}
  `;

  return record || null;
}

/**
 * Calculate sync status for a specific configuration type
 * @param lastRequest - Last request timestamp
 * @param currentTimestamp - Current timestamp from InfoPackage
 * @returns Sync status: "BEHIND" | "IN_SYNC" | "AHEAD"
 */
export function calculateSyncStatus(
  lastRequest: number | null,
  currentTimestamp: number | null
): "BEHIND" | "IN_SYNC" | "AHEAD" | "UNKNOWN" {
  if (lastRequest === null || currentTimestamp === null) {
    return "UNKNOWN";
  }

  if (currentTimestamp < lastRequest) {
    return "BEHIND";
  } else if (currentTimestamp === lastRequest) {
    return "IN_SYNC";
  } else {
    return "AHEAD";
  }
}

/**
 * Get sync status for all configuration types of a device
 * @param db - Database connection
 * @param deviceId - Device UUID
 * @returns Object with sync status for each configuration type
 */
export async function getLICStateSyncStatus(
  db: SQL,
  deviceId: string
): Promise<{
  devices: "BEHIND" | "IN_SYNC" | "AHEAD" | "UNKNOWN";
  scheduling: "BEHIND" | "IN_SYNC" | "AHEAD" | "UNKNOWN";
  dev_scheduling: "BEHIND" | "IN_SYNC" | "AHEAD" | "UNKNOWN";
  automation: "BEHIND" | "IN_SYNC" | "AHEAD" | "UNKNOWN";
  config: "BEHIND" | "IN_SYNC" | "AHEAD" | "UNKNOWN";
}> {
  const licState = await getLICState(db, deviceId);

  if (!licState) {
    return {
      devices: "UNKNOWN",
      scheduling: "UNKNOWN",
      dev_scheduling: "UNKNOWN",
      automation: "UNKNOWN",
      config: "UNKNOWN",
    };
  }

  return {
    devices: calculateSyncStatus(
      licState.last_devices_request,
      licState.current_devices_timestamp
    ),
    scheduling: calculateSyncStatus(
      licState.last_scheduling_request,
      licState.current_scheduling_timestamp
    ),
    dev_scheduling: calculateSyncStatus(
      licState.last_dev_scheduling_request,
      licState.current_dev_scheduling_timestamp
    ),
    automation: calculateSyncStatus(
      licState.last_automation_request,
      licState.current_automation_timestamp
    ),
    config: calculateSyncStatus(
      licState.last_config_request,
      licState.current_config_timestamp
    ),
  };
}

/**
 * Delete LIC state record for a device
 * @param db - Database connection
 * @param deviceId - Device UUID
 * @returns True if record was deleted, false if not found
 */
export async function deleteLICState(
  db: SQL,
  deviceId: string
): Promise<boolean> {
  const result = await db`
    DELETE FROM lic_state WHERE device = ${deviceId}
  `;

  return (result.count || 0) > 0;
}
