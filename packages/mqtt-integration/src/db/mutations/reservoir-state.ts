import type { Sql as SQL } from "@/db/connection";
import type {
  CurrentReservoirStateInsert,
  CurrentReservoirStateUpdate,
} from "./types";
import type { CurrentReservoirState } from "../queries/types";

async function insertCurrentReservoirState(
  db: SQL,
  record: CurrentReservoirStateInsert
): Promise<CurrentReservoirState | undefined> {
  const [r] = await db<
    CurrentReservoirState[]
  >`INSERT INTO current_reservoir_state (
      reservoir,
      packet_date,
      start_time,
      restart_time,
      end_time
    ) VALUES (
      ${record.reservoir},
      ${record.packet_date},
      ${record.start_time},
      ${record.restart_time},
      ${record.end_time}
    ) RETURNING *`;
  return r;
}

async function updateCurrentReservoirState(
  db: SQL,
  reservoirId: string,
  updates: CurrentReservoirStateUpdate
): Promise<CurrentReservoirState | null> {
  const hasUpdates = Object.keys(updates).some(
    (k) => (updates as any)[k] !== undefined
  );
  if (!hasUpdates) throw new Error("No fields to update");

  const [result] = await db<CurrentReservoirState[]>`
    UPDATE current_reservoir_state
    SET ${
      updates.packet_date !== undefined
        ? db`packet_date = ${updates.packet_date},`
        : db``
    }${
    updates.start_time !== undefined
      ? db`start_time = ${updates.start_time},`
      : db``
  }${
    updates.restart_time !== undefined
      ? db`restart_time = ${updates.restart_time},`
      : db``
  }${
    updates.end_time !== undefined ? db`end_time = ${updates.end_time},` : db``
  }
      date_updated = NOW()
    WHERE reservoir = ${reservoirId}
    RETURNING *`;
  return result || null;
}

export async function upsertCurrentReservoirState(
  db: SQL,
  record: CurrentReservoirStateInsert
): Promise<CurrentReservoirState | undefined> {
  const [r] = await db<
    CurrentReservoirState[]
  >`INSERT INTO current_reservoir_state (
      reservoir,
      packet_date,
      start_time,
      restart_time,
      end_time
    ) VALUES (
      ${record.reservoir},
      ${record.packet_date},
      ${record.start_time},
      ${record.restart_time},
      ${record.end_time}
    )
    ON CONFLICT (reservoir)
    DO UPDATE SET
      packet_date = EXCLUDED.packet_date,
      start_time = EXCLUDED.start_time,
      restart_time = EXCLUDED.restart_time,
      end_time = CASE
        WHEN current_reservoir_state.start_time IS NOT DISTINCT FROM EXCLUDED.start_time
          THEN COALESCE(current_reservoir_state.end_time, EXCLUDED.end_time)
        ELSE EXCLUDED.end_time
      END,
      date_updated = NOW()
    RETURNING *`;
  return r;
}
