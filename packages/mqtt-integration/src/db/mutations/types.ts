import type {
  CurrentLICPacket,
  DeviceMessageRequest,
  LICStateRecord,
  CurrentProjectState,
  CurrentIrrigationPlanState,
  CurrentReservoirState,
} from "../queries/types";

export type CurrentLICPacketInsert = Omit<
  CurrentLICPacket,
  "id" | "date_created"
>;

export type DeviceMessageRequestInsert = Omit<
  DeviceMessageRequest,
  "id" | "date_created" | "date_updated" | "user_created" | "user_updated"
>;

export type DeviceMessageRequestUpdate = Partial<
  Pick<
    DeviceMessageRequest,
    | "status"
    | "attempts"
    | "payload_bytes"
    | "sent_at"
    | "acknowledged_at"
    | "last_error"
    | "metadata"
    | "notes"
  >
>;

export type LICStateInsert = Omit<
  LICStateRecord,
  "date_created" | "date_updated" | "user_created" | "user_updated"
>;

export type LICStateUpdate = Partial<
  Pick<
    LICStateRecord,
    | "lic"
    | "groups"
    | "devices"
    | "mesh_devices"
    | "schedules"
    | "sector_schedules"
    | "device_schedules"
    | "last_devices_request"
    | "last_scheduling_request"
    | "last_dev_scheduling_request"
    | "last_automation_request"
    | "last_config_request"
    | "current_devices_timestamp"
    | "current_scheduling_timestamp"
    | "current_dev_scheduling_timestamp"
    | "current_automation_timestamp"
    | "current_config_timestamp"
    | "state_date"
  >
>;

export type CurrentProjectStateInsert = Omit<
  CurrentProjectState,
  "id" | "date_created" | "date_updated"
>;

export type CurrentProjectStateUpdate = Partial<
  Pick<
    CurrentProjectState,
    "irrigation_status" | "fertigation_status" | "backwash_status" | "sectors"
  >
>;

export type CurrentIrrigationPlanStateInsert = Omit<
  CurrentIrrigationPlanState,
  "id" | "date_created" | "date_updated"
>;

export type CurrentIrrigationPlanStateUpdate = Partial<
  Pick<
    CurrentIrrigationPlanState,
    | "packet_date"
    | "start_time"
    | "end_time"
    | "activated_steps"
    | "activated_ferti_steps"
    | "waterpump_working"
    | "backwash_start_time"
    | "uses_waterpump"
    | "uses_ferti"
  >
>;

export type CurrentReservoirStateInsert = Omit<
  CurrentReservoirState,
  "id" | "date_created" | "date_updated"
>;

export type CurrentReservoirStateUpdate = Partial<
  Pick<
    CurrentReservoirState,
    "packet_date" | "start_time" | "restart_time" | "end_time"
  >
>;
