import { batchUpsertCurrentProjectStates } from "@/db/mutations/current-project-state";
import { isLICTree } from "@/irriganet/db-loader/validation";
import { calculateProjectsState } from "@/irriganet/project-state-calculator";
import { LoggerManager } from "@/log";
import type { DefaultPackageProcessorFunction } from "../types";
const log = LoggerManager.getLogger("StatusPackageProcessor");
export const processStatusPackage: DefaultPackageProcessorFunction<
  "status"
> = async (id, statusData, ctx) => {
  if (log.debugEnabled) {
    log.debug(
      `[${ctx.state.lic.identity}] Processing SystemStatusPackage for project state calculation:`,
      statusData
    );
  }

  // Get the LIC tree for project information
  const licTree = ctx.state.tree;

  if (!licTree || !isLICTree(licTree) || !licTree.propertyDevice) {
    log.warn(
      `[${ctx.state.lic.identity}] No LIC tree found or missing property device, skipping project state calculation`
    );
    return;
  }

  const packetDate = id ? new Date(Number(id) * 1000) : ctx.referenceDate;

  // Calculate project states using the SystemStatusPackage
  const projectStates = calculateProjectsState(
    statusData,
    ctx.state,
    licTree,
    packetDate
  );

  if (log.debugEnabled) {
    log.debug(
      `[${ctx.state.lic.identity}] Calculated ${projectStates.length} project states:`,
      projectStates
    );
  }

  // Store the calculated project states in the database
  if (projectStates.length > 0) {
    try {
      const insertRecords = projectStates.map((state) => ({
        project: state.project,
        packet_date: state.packet_date,
        irrigation_status: state.irrigation_status,
        fertigation_status: state.fertigation_status,
        backwash_status: state.backwash_status,
        sectors: state.sectors,
      }));

      const storedStates = await batchUpsertCurrentProjectStates(
        ctx.db,
        insertRecords
      );

      log.info(
        `[${ctx.state.lic.identity}] Successfully stored ${storedStates.length} project states`
      );

      for (const projectState of projectStates) {
        log.debug(
          `[${ctx.state.lic.identity}] Project ${projectState.project} state: irrigation=${projectState.irrigation_status}, fertigation=${projectState.fertigation_status}, backwash=${projectState.backwash_status}, sectors=${projectState.sectors.length}`
        );
      }
    } catch (dbError) {
      log.error(
        `[${ctx.state.lic.identity}] Failed to store project states in database:`,
        dbError
      );
    }
  } else {
    log.debug(`[${ctx.state.lic.identity}] No project states to store`);
  }
};
