import { upsertCurrentReservoirState } from "@/db/mutations/reservoir-state";
import { calculateReservoirState } from "../../reservoir-state-calculator";
import type { DefaultPackageProcessorFunction } from "../types";

export const processAutomationReportPackage: DefaultPackageProcessorFunction<
  "automation_report"
> = async (packetId, payload, ctx) => {
  const packetDate = ctx.referenceDate;
  const items = payload.data ?? [];

  for (const report of items) {
    try {
      const autoIdx = report.auto_idx;
      if (autoIdx == null) continue;

      // Resolve reservoir by LICState device mapping
      const device = ctx.state.devices.find(
        (d) =>
          d.elementType === "reservoir" &&
          d.elementVariation === String(autoIdx)
      );
      const reservoirId = device?.elementId;
      if (!reservoirId) {
        console.warn(
          `Automation report auto_idx=${autoIdx} has no mapped reservoir; skipping.`
        );
        continue;
      }

      const state = calculateReservoirState(report, reservoirId, packetDate);
      if (!state) continue;

      await upsertCurrentReservoirState(ctx.db, state);
    } catch (err) {
      console.error(
        `Failed to process automation_report item in packet ${packetId}:`,
        err
      );
    }
  }
};
