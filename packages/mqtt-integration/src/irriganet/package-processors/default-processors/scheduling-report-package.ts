import { batchUpsertCurrentIrrigationPlanStates } from "@/db/mutations/irrigation-plan-state";
import { calculateIrrigationPlanState } from "@/irriganet/irrigation-plan-state-calculator";
import { LoggerManager } from "@/log";
import type { DefaultPackageProcessorFunction } from "../types";

const log = LoggerManager.getLogger("SchedulingReportPackageProcessor");

export const processSchedulingReportPackage: DefaultPackageProcessorFunction<
  "scheduling_report"
> = async (id, schedulingReportPackageData, ctx) => {
  if (log.debugEnabled) {
    log.debug(
      `[${ctx.state.lic.identity}] Processing SchedulingReportPackage for irrigation plan state calculation:`,
      schedulingReportPackageData
    );
  }

  // Validate that we have scheduling report data
  if (!schedulingReportPackageData.data || schedulingReportPackageData.data.length === 0) {
    log.debug(
      `[${ctx.state.lic.identity}] No scheduling report data found, skipping irrigation plan state calculation`
    );
    return;
  }

  const packetDate = id ? new Date(Number(id) * 1000) : ctx.referenceDate;
  const irrigationPlanStates: NonNullable<ReturnType<typeof calculateIrrigationPlanState>>[] = [];

  // Process each SchedulingReportData in the package
  for (const schedulingReportData of schedulingReportPackageData.data) {
    try {
      if (log.debugEnabled) {
        log.debug(
          `[${ctx.state.lic.identity}] Processing scheduling report for scheduling_idx: ${schedulingReportData.scheduling_idx}`,
          schedulingReportData
        );
      }

      // Calculate irrigation plan state using the SchedulingReportData
      const irrigationPlanState = calculateIrrigationPlanState(
        schedulingReportData,
        ctx.state,
        packetDate
      );

      if (irrigationPlanState) {
        irrigationPlanStates.push(irrigationPlanState);
        
        if (log.debugEnabled) {
          log.debug(
            `[${ctx.state.lic.identity}] Calculated irrigation plan state for plan ${irrigationPlanState.irrigation_plan}:`,
            {
              irrigation_plan: irrigationPlanState.irrigation_plan,
              start_time: irrigationPlanState.start_time,
              end_time: irrigationPlanState.end_time,
              activated_steps: irrigationPlanState.activated_steps.length,
              activated_ferti_steps: irrigationPlanState.activated_ferti_steps.length,
              waterpump_working: irrigationPlanState.waterpump_working,
              uses_waterpump: irrigationPlanState.uses_waterpump,
              uses_ferti: irrigationPlanState.uses_ferti,
            }
          );
        }
      } else {
        log.debug(
          `[${ctx.state.lic.identity}] Could not calculate irrigation plan state for scheduling_idx: ${schedulingReportData.scheduling_idx}`
        );
      }
    } catch (calculationError) {
      log.error(
        `[${ctx.state.lic.identity}] Error calculating irrigation plan state for scheduling_idx ${schedulingReportData.scheduling_idx}:`,
        calculationError
      );
      // Continue processing other scheduling reports
    }
  }

  if (log.debugEnabled) {
    log.debug(
      `[${ctx.state.lic.identity}] Calculated ${irrigationPlanStates.length} irrigation plan states from ${schedulingReportPackageData.data.length} scheduling reports`
    );
  }

  // Store the calculated irrigation plan states in the database
  if (irrigationPlanStates.length > 0) {
    try {
      const insertRecords = irrigationPlanStates.map((state) => ({
        irrigation_plan: state.irrigation_plan,
        packet_date: state.packet_date,
        start_time: state.start_time,
        end_time: state.end_time,
        activated_steps: state.activated_steps,
        activated_ferti_steps: state.activated_ferti_steps,
        waterpump_working: state.waterpump_working,
        backwash_start_time: state.backwash_start_time,
        uses_waterpump: state.uses_waterpump,
        uses_ferti: state.uses_ferti,
      }));

      const storedStates = await batchUpsertCurrentIrrigationPlanStates(
        ctx.db,
        insertRecords
      );

      log.info(
        `[${ctx.state.lic.identity}] Successfully stored ${storedStates.length} irrigation plan states`
      );

      for (const irrigationPlanState of irrigationPlanStates) {
        log.debug(
          `[${ctx.state.lic.identity}] Irrigation plan ${irrigationPlanState.irrigation_plan} state: ` +
          `activated_steps=${irrigationPlanState.activated_steps.length}, ` +
          `activated_ferti_steps=${irrigationPlanState.activated_ferti_steps.length}, ` +
          `waterpump=${irrigationPlanState.waterpump_working}, ` +
          `uses_waterpump=${irrigationPlanState.uses_waterpump}, ` +
          `uses_ferti=${irrigationPlanState.uses_ferti}`
        );
      }
    } catch (dbError) {
      log.error(
        `[${ctx.state.lic.identity}] Failed to store irrigation plan states in database:`,
        dbError
      );
      throw dbError; // Re-throw to indicate processing failure
    }
  } else {
    log.debug(`[${ctx.state.lic.identity}] No irrigation plan states to store`);
  }
};