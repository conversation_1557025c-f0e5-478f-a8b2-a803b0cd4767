import type { Sql } from "@/db/connection";
import type { codec } from "proto";
import type { CodecManager } from "../codec-manager";
import type { LICState, LICTreeRaw } from "../db-loader/types";

export type OutgoingPacketProcessorContext = {
  db: Sql;
  codecManager: CodecManager;
  state: LICState & {
    tree?: LICTreeRaw;
  };
  referenceDate: Date;
};

export interface IOutgoingPacketProcessor {
  process(
    packet: codec.out.OutgoingPacket,
    ctx: OutgoingPacketProcessorContext
  ): Promise<void>;
}

export type DefaultPackageProcessorFunction<
  K extends NonNullable<codec.out.OutgoingPacket["payload"]>
> = (
  id: codec.out.OutgoingPacket["id"],
  payload: NonNullable<codec.out.OutgoingPacket[K]>,
  ctx: OutgoingPacketProcessorContext
) => Promise<void>;
