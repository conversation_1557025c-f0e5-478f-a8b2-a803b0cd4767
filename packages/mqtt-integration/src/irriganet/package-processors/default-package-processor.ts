import type { codec } from "proto";
import { processInfoPackage } from "./default-processors/info-package";
import { processStatusPackage } from "./default-processors/status-package";
import { processSchedulingReportPackage } from "./default-processors/scheduling-report-package";
import { processAutomationReportPackage } from "./default-processors/automation-report-package";
import type {
  IOutgoingPacketProcessor,
  OutgoingPacketProcessorContext,
} from "./types";

export class DefaultPackageProcessor implements IOutgoingPacketProcessor {
  async process(
    packet: codec.out.OutgoingPacket,
    ctx: OutgoingPacketProcessorContext
  ): Promise<void> {
    const payloadType = packet.payload;
    if (!payloadType) {
      return;
    }
    const payload = packet[payloadType];

    if (!payload) {
      return;
    }

    switch (payloadType) {
      case "info":
        await processInfoPackage(
          packet.id,
          payload as NonNullable<codec.out.OutgoingPacket["info"]>,
          ctx
        );
        break;
      case "status":
        await processStatusPackage(
          packet.id,
          payload as NonNullable<codec.out.OutgoingPacket["status"]>,
          ctx
        );
        break;
      case "scheduling_report":
        await processSchedulingReportPackage(
          packet.id,
          payload as NonNullable<codec.out.OutgoingPacket["scheduling_report"]>,
          ctx
        );
        break;
      case "automation_report":
        await processAutomationReportPackage(
          packet.id,
          payload as NonNullable<codec.out.OutgoingPacket["automation_report"]>,
          ctx
        );
        break;
      // Add more cases for different payload types as needed
    }
  }
}
