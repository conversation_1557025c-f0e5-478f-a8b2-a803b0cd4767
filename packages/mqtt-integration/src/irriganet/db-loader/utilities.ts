/**
 * Utility Functions for IrrigaNet Database Loaders
 *
 * This module contains reusable utility functions and classes that support
 * the database loading operations. Following the DRY principle, common
 * functionality is centralized here to avoid code duplication.
 */

import { deepEqual } from "../../utils/object";
import {
  DAYS_OF_WEEK,
  DEV_TYPES,
  type DaysOfWeek,
  type LICState,
} from "./types";

// ==============================================
// ID GENERATION UTILITIES
// ==============================================

/**
 * Simple ID generator for creating sequential IDs within a scope.
 * Used for generating mesh device and device IDs during the loading process.
 */
export class IdGenerator {
  private currentId = 1;

  /**
   * Get the current ID value without incrementing
   */
  getCurrentId(): number {
    return this.currentId;
  }

  /**
   * Get the next ID and increment the counter
   */
  next(): number {
    return this.currentId++;
  }

  /**
   * Reset the generator to start from 0
   */
  reset(): void {
    this.currentId = 0;
  }

  /**
   * Set the current ID to a specific value
   */
  setCurrentId(id: number): void {
    this.currentId = id;
  }
}

// ==============================================
// BITMASK UTILITIES
// ==============================================

/**
 * Convert device presence booleans to a bitmask using DEV_TYPES values.
 * This utility function creates a bitmask representation of which device
 * types are present in a mesh device configuration.
 *
 * @param irrigationPump - Whether irrigation pump is present
 * @param ferti - Whether fertigation device is present
 * @param backwash - Whether backwash device is present
 * @param individualPump - Whether individual/service pump is present
 * @returns Bitmask representing device presence
 */
export function deviceBooleansToBitmask(
  irrigationPump: boolean,
  ferti: boolean,
  backwash: boolean,
  individualPump: boolean
): number {
  let bitmask = 0;
  if (irrigationPump) bitmask |= 1 << DEV_TYPES.IrrigationPump;
  if (ferti) bitmask |= 1 << DEV_TYPES.Ferti;
  if (backwash) bitmask |= 1 << DEV_TYPES.Backwash;
  if (individualPump) bitmask |= 1 << DEV_TYPES.ServicePump;
  return bitmask;
}

/**
 * Extract device types from a bitmask
 *
 * @param bitmask - The bitmask to decode
 * @returns Object indicating which device types are present
 */
export function bitmaskToDeviceBooleans(bitmask: number): {
  irrigationPump: boolean;
  ferti: boolean;
  backwash: boolean;
  individualPump: boolean;
} {
  return {
    irrigationPump: (bitmask & (1 << DEV_TYPES.IrrigationPump)) !== 0,
    ferti: (bitmask & (1 << DEV_TYPES.Ferti)) !== 0,
    backwash: (bitmask & (1 << DEV_TYPES.Backwash)) !== 0,
    individualPump: (bitmask & (1 << DEV_TYPES.ServicePump)) !== 0,
  };
}

// ==============================================
// VALIDATION UTILITIES
// ==============================================

/**
 * Check if a value is defined and not null
 */
export function isDefined<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * Assert that a value is defined, throwing an error if not
 */
export function assertDefined<T>(
  value: T | null | undefined,
  message: string
): asserts value is T {
  if (!isDefined(value)) {
    throw new Error(message);
  }
}

/**
 * Check if a string is not empty
 */
export function isNonEmptyString(
  value: string | null | undefined
): value is string {
  return typeof value === "string" && value.length > 0;
}

// ==============================================
// ARRAY UTILITIES
// ==============================================

/**
 * Sort array by a date property, handling null/undefined dates
 */
export function sortByDate<T>(
  array: T[],
  dateExtractor: (item: T) => Date | string | null | undefined
): T[] {
  return array.toSorted((a, b) => {
    const dateA = dateExtractor(a);
    const dateB = dateExtractor(b);

    const timeA = dateA
      ? typeof dateA === "string"
        ? new Date(dateA).getTime()
        : dateA.getTime()
      : 0;
    const timeB = dateB
      ? typeof dateB === "string"
        ? new Date(dateB).getTime()
        : dateB.getTime()
      : 0;

    return timeA - timeB;
  });
}

/**
 * Group array items by a key function
 */
export function groupBy<T, K extends string | number | symbol>(
  array: T[],
  keyFn: (item: T) => K
): Record<K, T[]> {
  return array.reduce((groups, item) => {
    const key = keyFn(item);
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(item);
    return groups;
  }, {} as Record<K, T[]>);
}

// ==============================================
// CALCULATION UTILITIES
// ==============================================

/**
 * Calculate rain gauge factor from resolution
 */
export function calculateRainGaugeFactor(
  resolutionMm: number | null | undefined
): number {
  if (!resolutionMm || resolutionMm <= 0) {
    return 5; // Default factor for 0.2mm resolution
  }
  return Math.round(1.0 / resolutionMm);
}

/**
 * Convert hours to minutes
 */
export function hoursToMinutes(hours: number | null | undefined): number {
  return (hours || 0) * 60;
}

/**
 * Helper function to create days of week bitmask
 */
export function createDaysOfWeekBitmask(days: DaysOfWeek[]): number {
  let bitmask = 0;

  for (const day of days) {
    if (DAYS_OF_WEEK[day]) {
      bitmask |= DAYS_OF_WEEK[day];
    }
  }

  return bitmask;
}

/**
 * Helper function to check if a day is included in the bitmask
 */
export function isDayIncluded(bitmask: number, day: DaysOfWeek): boolean {
  return (bitmask & DAYS_OF_WEEK[day]) !== 0;
}

export function isDaysOfWeekArray(arr: string[]): arr is DaysOfWeek[] {
  return arr.every((day) => day in DAYS_OF_WEEK);
}

// ############################################
// # CRC
// ############################################

export function crc16(data: string): number;
export function crc16(bytes: Uint8Array | number[]): number;
export function crc16(input: string | Uint8Array | number[]): number {
  const bytes: Uint8Array =
    typeof input === "string"
      ? new TextEncoder().encode(input)
      : input instanceof Uint8Array
      ? input
      : Uint8Array.from(input);

  let crc = 0xffff;
  for (let i = 0; i < bytes.length; i++) {
    crc ^= bytes[i]! & 0xff;
    for (let j = 0; j < 8; j++) {
      if ((crc & 0x0001) !== 0) {
        crc = (crc >>> 1) ^ 0xa001;
      } else {
        crc = crc >>> 1;
      }
    }
  }
  return crc & 0xffff;
}

export function appendCRC16(payload: Uint8Array | Buffer): Uint8Array {
  const crc = crc16(payload);
  const crcBytes = Buffer.from([
    (crc >> 8) & 0xff, // High byte
    crc & 0xff, // Low byte
  ]);
  return new Uint8Array([...payload, ...crcBytes]);
}

// ##########################################
// # LIC Utilities
// ##########################################

export function licStateDiff(
  oldState: LICState,
  newState: LICState
): { [k in keyof LICState]: boolean } {
  const stateDifferences = (Object.keys(oldState) as (keyof LICState)[]).reduce(
    (
      acc: {
        [k in keyof LICState]: boolean;
      },
      key: keyof LICState
    ) => {
      acc[key] = !deepEqual(oldState[key], newState[key]);
      return acc;
    },
    {} as {
      [k in keyof LICState]: boolean;
    }
  );
  return {
    ...stateDifferences,
  };
}

export function licStateChanges(
  oldState: LICState,
  newState: LICState
): Array<keyof LICState> {
  return (Object.keys(oldState) as (keyof LICState)[]).filter(
    (key) => !deepEqual(oldState[key], newState[key])
  );
}
