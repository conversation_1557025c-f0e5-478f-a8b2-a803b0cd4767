import type {
  IrriganetSchedulingWithIrrigationPlanId,
  IrriganetSectorSchedulingWithStepId,
} from "./types";
import type { IrriganetDeviceScheduling } from "./types";
import { IdGenerator } from "./utilities";

export function generateDevicesScheduling(
  schedules: IrriganetSchedulingWithIrrigationPlanId[],
  sectorsScheduling: IrriganetSectorSchedulingWithStepId[]
) {
  const result: IrriganetDeviceScheduling[] = [];
  const deviceSchedulingIdGen = new IdGenerator();

  const groupedBySchedulingIdx = Object.groupBy(
    sectorsScheduling,
    (s) => s.scheduling_idx
  );
  Object.entries(groupedBySchedulingIdx).forEach(
    ([schedulingIdx, sectorsScheduling]) => {
      if (!sectorsScheduling || sectorsScheduling.length === 0) {
        throw new Error(
          `No sectors scheduling found for scheduling index ${schedulingIdx}`
        );
      }
      const schedule = findSchedule(schedules, Number(schedulingIdx));
      let time = schedule.start_time;
      sectorsScheduling.forEach((sectorScheduling, index) => {
        const deviceSchedulingIdentifier = deviceSchedulingIdGen.next();
        const deviceScheduling: IrriganetDeviceScheduling = {
          idx: deviceSchedulingIdentifier,
          device_idx: sectorScheduling.device_idx,
          ferti_delay: sectorScheduling.ferti_delay,
          ferti_working_time: sectorScheduling.ferti,
          n_order: sectorScheduling.n_order,
          ord_idx: deviceSchedulingIdentifier - 1,
          scheduling_idx: sectorScheduling.scheduling_idx,
          sector_working_time: sectorScheduling.working_time,
          status: 0,
          time,
          type: sectorScheduling.type,
        };
        time += sectorScheduling.working_time * 60;
        if (index < sectorsScheduling.length - 1) {
          time += 30;
        }
        result.push(deviceScheduling);
      });
    }
  );

  return result;
}

function findSchedule(
  schedules: IrriganetSchedulingWithIrrigationPlanId[],
  schedulingIdx: number
) {
  const schedule = schedules.find((s) => s.idx === schedulingIdx);
  if (!schedule) {
    throw new Error(
      `Irrigation plan not found for scheduling index ${schedulingIdx}`
    );
  }
  return schedule;
}
