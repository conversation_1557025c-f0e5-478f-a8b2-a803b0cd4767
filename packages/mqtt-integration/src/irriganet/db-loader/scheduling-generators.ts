import type {
  IrrigationPlanWithSteps,
  ProjectWithSchedulingData,
} from "../../db/queries/types";
import { extractHHMM } from "../../utils/date";
import {
  DEV_TYPES,
  type IrriganetGroupWithProjectId,
  type IrriganetMeshDeviceWithDeviceId,
  type IrriganetSchedulingWithIrrigationPlanId,
  type LICTree,
} from "./types";
import {
  createDaysOfWeekBitmask,
  isDaysOfWeekArray,
  type IrriganetDevice,
} from "./types";
import { IdGenerator } from "./utilities";

export function generateScheduling(
  tree: LICTree,
  groups: IrriganetGroupWithProjectId[],
  meshDevices: IrriganetMeshDeviceWithDeviceId[],
  devices: IrriganetDevice[],
  referenceDate: Date
) {
  const schedulingIdGen = new IdGenerator();
  const result = tree.projects.reduce((acc, project) => {
    const group = groups.find((g) => g.projectId === project.id);
    if (!group) {
      throw new Error(`Group not found for project ${project.id}`);
    }
    const { schedulings } = generateProjectScheduling(
      project,
      group,
      schedulingIdGen,
      meshDevices,
      devices,
      referenceDate
    );
    return acc.concat(schedulings);
  }, [] as IrriganetSchedulingWithIrrigationPlanId[]);

  return result;
}

function generateProjectScheduling(
  project: ProjectWithSchedulingData,
  group: IrriganetGroupWithProjectId,
  schedulingIdGen: IdGenerator,
  meshDevices: IrriganetMeshDeviceWithDeviceId[],
  devices: IrriganetDevice[],
  _referenceDate: Date
) {
  const result: {
    schedulings: IrriganetSchedulingWithIrrigationPlanId[];
  } = {
    schedulings: [],
  };

  project.irrigation_plans.forEach((plan) => {
    const scheduling = createProjectScheduling(
      group,
      project,
      plan,
      schedulingIdGen,
      meshDevices,
      devices
    );
    result.schedulings.push(scheduling);
  });

  return result;
}

function createProjectScheduling(
  group: IrriganetGroupWithProjectId,
  project: ProjectWithSchedulingData,
  plan: IrrigationPlanWithSteps,
  schedulingIdGen: IdGenerator,
  meshDevices: IrriganetMeshDeviceWithDeviceId[],
  devices: IrriganetDevice[]
): IrriganetSchedulingWithIrrigationPlanId {
  const schedulingId = schedulingIdGen.next();
  if (!isDaysOfWeekArray(plan.days_of_week)) {
    throw new Error("Invalid days_of_week array");
  }
  const { hours, minutes } = extractHHMM(plan.start_time);
  const totalDuration = plan.total_irrigation_duration;
  const startTime = hours * 3600 + minutes * 60;
  const groupMeshDevices = meshDevices.filter(
    (device) => device.group_idx === group.idx
  );
  const groupDevices = devices.filter((device) =>
    groupMeshDevices.some((md) => md.idx === device.mesh_idx)
  );
  const irrigationDevice = groupDevices.find(
    (d) => d.type === DEV_TYPES.IrrigationPump
  );
  const fertiDevice = groupDevices.find((d) => d.type === DEV_TYPES.Ferti);
  const backwashDevice = groupDevices.find(
    (d) => d.type === DEV_TYPES.Backwash
  );
  return {
    idx: schedulingId,
    irrigationPlanId: plan.id,
    allow_backwash: project.backwash_pump_type
      ? plan.backwash_enabled
        ? 1
        : 0
      : 0,
    allow_ferti: project.fertigation_water_pump?.water_pump_controller
      ? plan.fertigation_enabled
        ? 1
        : 0
      : 0,
    days_of_week: createDaysOfWeekBitmask(plan.days_of_week),
    enabled: plan.is_enabled ? 1 : 0,
    group_idx: group.idx,
    hour: hours,
    min: minutes,
    name: plan.name,
    number_of_steps: plan.steps.length,
    ord_idx: schedulingId - 1,
    start_time: startTime,
    end_time: startTime + totalDuration,
    backwash_idx: backwashDevice?.idx,
    backwash_ord_idx: backwashDevice?.ord_idx,
    ferti_idx: fertiDevice?.idx,
    ferti_ord_idx: fertiDevice?.ord_idx,
    waterpump_idx: irrigationDevice?.idx,
    waterpump_ord_idx: irrigationDevice?.ord_idx,
    waterpump_working_time: irrigationDevice
      ? Math.round(totalDuration / 60)
      : undefined,
    once: 0,
  };
}
