/**
 * Mesh Device Generators for IrrigaNet Database Loaders
 *
 * This module contains functions for creating mesh device configurations
 * from project data. Each function handles a specific type of mesh device
 * following the Single Responsibility principle.
 */

import type {
  ProjectWithSchedulingData,
  ReservoirWithMonitorAndWaterPumpWithController,
  SectorWithValveController,
  WaterPumpWithController,
} from "../../db/queries/types";
import { use } from "../../utils/object";
import type {
  CodecConfig,
  IrriganetGroupWithProjectId,
  IrriganetMeshDeviceWithDeviceId,
  IrriganetGroup,
} from "./types";
import { MESH_DEVICE_TYPES } from "./types";
import { IdGenerator, deviceBooleansToBitmask } from "./utilities";
import { validatePumpControllerModel } from "./validation";

// ==============================================
// IRRIGATION MESH DEVICE GENERATION
// ==============================================

/**
 * Create irrigation pump mesh device configuration
 *
 * @param meshDeviceIdGen - ID generator for mesh devices
 * @param codec - Codec configuration
 * @param group - Group configuration
 * @param project - Project data
 * @param fertigationAndIrrigationControllerShared - Whether controllers are shared
 * @returns Irrigation mesh device configuration
 */
export function createIrrigationMeshDevice(
  meshDeviceIdGen: IdGenerator,
  codec: CodecConfig,
  group: IrriganetGroupWithProjectId,
  project: ProjectWithSchedulingData,
  fertigationAndIrrigationControllerShared: boolean
): IrriganetMeshDeviceWithDeviceId {
  const irrigationPump = project.irrigation_water_pump;
  const pumpController = irrigationPump.water_pump_controller;

  // Validate pump controller model
  validatePumpControllerModel(pumpController.model, pumpController.identifier);

  return {
    idx: meshDeviceIdGen.next(),
    identity: pumpController.identifier,
    name: irrigationPump.label || pumpController.identifier,
    check_input: irrigationPump.monitor_operation ? 1 : 0,
    codec_idx: codec.idx,
    devices_bitmask: deviceBooleansToBitmask(
      true, // Always has irrigation pump
      fertigationAndIrrigationControllerShared,
      project.backwash_pump_type === "IRRIGATION",
      false // Individual pump not supported on irrigation controller
    ),
    equipament: getEquipmentType(pumpController.model),
    group_idx: group.idx,
    level_pump_enable: 0,
    level_pump_idx: undefined,
    level_pump_working_time: 0,
    type: MESH_DEVICE_TYPES.Pump,
    mode: irrigationPump.mode === "CONTINUOUS" ? 0 : 1,
    deviceId: pumpController.id,
  };
}

// ==============================================
// FERTIGATION MESH DEVICE GENERATION
// ==============================================

/**
 * Create fertigation pump mesh device configuration
 *
 * @param meshDeviceIdGen - ID generator for mesh devices
 * @param codec - Codec configuration
 * @param group - Group configuration
 * @param project - Project data
 * @returns Fertigation mesh device configuration
 */
export function createFertigationMeshDevice(
  meshDeviceIdGen: IdGenerator,
  codec: CodecConfig,
  group: IrriganetGroupWithProjectId,
  project: ProjectWithSchedulingData
): IrriganetMeshDeviceWithDeviceId {
  const fertigationPump = project.fertigation_water_pump!;
  const pumpController = fertigationPump.water_pump_controller;

  // Validate pump controller model
  validatePumpControllerModel(pumpController.model, pumpController.identifier);

  return {
    idx: meshDeviceIdGen.next(),
    identity: pumpController.identifier,
    name: fertigationPump.label || pumpController.identifier,
    check_input: fertigationPump.monitor_operation ? 1 : 0,
    codec_idx: codec.idx,
    devices_bitmask: deviceBooleansToBitmask(
      false, // No irrigation pump on dedicated fertigation controller
      true, // Always has fertigation
      project.backwash_pump_type === "FERTIGATION",
      false // Individual pump not supported on fertigation controller
    ),
    equipament: getEquipmentType(pumpController.model),
    group_idx: group.idx,
    level_pump_enable: 0,
    level_pump_idx: undefined,
    level_pump_working_time: 0,
    type: MESH_DEVICE_TYPES.Pump,
    mode: fertigationPump.mode === "CONTINUOUS" ? 0 : 1,
    deviceId: pumpController.id,
  };
}

// ==============================================
// SERVICE MESH DEVICE GENERATION
// ==============================================

/**
 * Create service mesh device configuration
 *
 * @param meshDeviceIdGen - ID generator for mesh devices
 * @param codec - Codec configuration
 * @param group - Group configuration
 * @returns Service mesh device configuration
 */
export function createServiceMeshDevice(
  meshDeviceIdGen: IdGenerator,
  codec: CodecConfig,
  group: IrriganetGroup,
  waterPump: WaterPumpWithController
): IrriganetMeshDeviceWithDeviceId {
  const controller = waterPump.water_pump_controller;
  if (!controller) {
    throw new Error("Water pump controller is required");
  }
  // Validate service controller model
  validatePumpControllerModel(controller.model, controller.identifier);

  return {
    idx: meshDeviceIdGen.next(),
    identity: controller.identifier,
    name: waterPump.label || controller.identifier,
    check_input: waterPump.monitor_operation ? 1 : 0,
    codec_idx: codec.idx,
    devices_bitmask: deviceBooleansToBitmask(false, false, false, true),
    equipament: getEquipmentType(controller.model),
    group_idx: group.idx,
    level_pump_enable: 0,
    level_pump_idx: undefined,
    level_pump_working_time: 0,
    type: MESH_DEVICE_TYPES.Pump,
    mode: waterPump.mode === "CONTINUOUS" ? 0 : 1,
    deviceId: controller.id,
  };
}

// ==============================================
// VALVE MESH DEVICE GENERATION
// ==============================================

/**
 * Create valve mesh device configuration
 *
 * @param meshDeviceIdGen - ID generator for mesh devices
 * @param codec - Codec configuration
 * @param group - Group configuration
 * @param firstSector - First sector in the valve controller group
 * @returns Valve mesh device configuration
 */
export function createValveMeshDevice(
  meshDeviceIdGen: IdGenerator,
  codec: CodecConfig,
  group: IrriganetGroupWithProjectId,
  firstSector: SectorWithValveController
): IrriganetMeshDeviceWithDeviceId {
  return {
    idx: meshDeviceIdGen.next(),
    identity: firstSector.valve_controller_device.identifier,
    name: firstSector.name || firstSector.valve_controller_device.identifier,
    check_input: 0, // Valve controllers don't monitor input
    codec_idx: codec.idx,
    devices_bitmask: 0, // Valve devices don't use bitmask
    equipament: 0, // Standard equipment type for valve controllers
    group_idx: group.idx,
    level_pump_enable: 0,
    level_pump_idx: undefined,
    level_pump_working_time: 0,
    type: MESH_DEVICE_TYPES.Valve,
    mode: 0, // Standard mode for valve controllers
    deviceId: firstSector.valve_controller_device.id,
  };
}

// ==============================================
// LEVEL MESH DEVICE GENERATION
// ==============================================

/**
 * Create level mesh device configuration
 *
 * @param meshDeviceIdGen - ID generator for mesh devices
 * @param codec - Codec configuration
 * @param group - Group configuration
 * @param identity - Device identity
 * @param name - Device name
 * @returns Level mesh device configuration
 */
export function createLevelMeshDevice(
  meshDeviceIdGen: IdGenerator,
  codec: CodecConfig,
  group: IrriganetGroup,
  reservoir: ReservoirWithMonitorAndWaterPumpWithController,
  meshDevices: IrriganetMeshDeviceWithDeviceId[]
): IrriganetMeshDeviceWithDeviceId {
  if (!reservoir.reservoir_monitor) {
    throw new Error("Reservoir monitor is required");
  }
  const levelPump = use(reservoir.water_pump, (w) => {
    if (!w) {
      return {
        level_pump_enable: false,
        level_pump_idx: undefined,
        level_pump_working_time: reservoir.safety_time_minutes,
      };
    }
    return {
      level_pump_enable: true,
      level_pump_idx: meshDevices.find(
        (device) => device.identity === w.water_pump_controller?.identifier
      )?.idx,
      level_pump_working_time: reservoir.safety_time_minutes,
    };
  });
  if (levelPump.level_pump_enable) {
    if (levelPump.level_pump_idx === undefined) {
      throw new Error(
        "Level pump index is not found: " +
          reservoir.water_pump?.water_pump_controller?.identifier
      );
    }
    if (levelPump.level_pump_working_time === undefined) {
      throw new Error("Level pump working time is required");
    }
  }
  return {
    idx: meshDeviceIdGen.next(),
    identity: reservoir.reservoir_monitor.identifier,
    name: reservoir.name || reservoir.reservoir_monitor.identifier,
    check_input: 0, // Level devices don't monitor input
    codec_idx: codec.idx,
    devices_bitmask: 0, // Level devices don't use bitmask
    equipament: 0, // Standard equipment type for level devices
    group_idx: group.idx,
    level_pump_enable: levelPump.level_pump_enable ? 1 : 0,
    level_pump_idx: levelPump.level_pump_idx,
    level_pump_working_time: levelPump.level_pump_working_time ?? undefined,
    type: MESH_DEVICE_TYPES.Level,
    mode: 0, // Standard mode for level devices
    deviceId: reservoir.reservoir_monitor.id,
  };
}

// ==============================================
// UTILITY FUNCTIONS
// ==============================================

/**
 * Get equipment type code from pump controller model
 *
 * @param model - Pump controller model
 * @returns Equipment type code (0 for WPC-PL10, 1 for WPC-PL50)
 */
function getEquipmentType(model: string): number {
  return model === "WPC-PL10" ? 0 : 1;
}

// ==============================================
// MESH DEVICE VALIDATION
// ==============================================

/**
 * Validate mesh device configuration
 *
 * @param meshDevice - Mesh device to validate
 * @throws Error if configuration is invalid
 */
export function validateMeshDevice(
  meshDevice: IrriganetMeshDeviceWithDeviceId
): void {
  if (!meshDevice.identity || meshDevice.identity.trim().length === 0) {
    throw new Error("Mesh device must have a valid identity");
  }

  if (!meshDevice.name || meshDevice.name.trim().length === 0) {
    throw new Error("Mesh device must have a valid name");
  }

  if (meshDevice.idx < 1) {
    throw new Error("Mesh device idx must be greater than 0");
  }

  if (meshDevice.codec_idx && meshDevice.codec_idx < 1) {
    throw new Error("Mesh device codec_idx must be greater than 0");
  }

  if (meshDevice.group_idx && meshDevice.group_idx < 1) {
    throw new Error("Mesh device group_idx must be greater than 0");
  }

  if (!meshDevice.deviceId || meshDevice.deviceId.trim().length === 0) {
    throw new Error("Mesh device must have a valid deviceId");
  }

  // Validate type
  const validTypes = Object.values(MESH_DEVICE_TYPES);
  if (!validTypes.includes(meshDevice.type! as (typeof validTypes)[number])) {
    throw new Error(`Invalid mesh device type: ${meshDevice.type}`);
  }
}

// ==============================================
// MESH DEVICE UTILITIES
// ==============================================

/**
 * Create a base mesh device configuration with common properties
 *
 * @param meshDeviceIdGen - ID generator
 * @param codec - Codec configuration
 * @param group - Group configuration
 * @param identity - Device identity
 * @param name - Device name
 * @param deviceId - Associated device ID
 * @returns Base mesh device configuration
 */
export function createBaseMeshDevice(
  meshDeviceIdGen: IdGenerator,
  codec: CodecConfig,
  group: IrriganetGroupWithProjectId,
  identity: string,
  name: string,
  deviceId: string
): Omit<
  IrriganetMeshDeviceWithDeviceId,
  "type" | "mode" | "equipament" | "check_input" | "devices_bitmask"
> {
  return {
    idx: meshDeviceIdGen.next(),
    identity,
    name,
    codec_idx: codec.idx,
    group_idx: group.idx,
    level_pump_enable: 0,
    level_pump_idx: undefined,
    level_pump_working_time: 0,
    deviceId,
  };
}

/**
 * Check if a mesh device is a pump type
 *
 * @param meshDevice - Mesh device to check
 * @returns True if device is a pump type
 */
export function isPumpMeshDevice(
  meshDevice: IrriganetMeshDeviceWithDeviceId
): boolean {
  return meshDevice.type === MESH_DEVICE_TYPES.Pump;
}

/**
 * Check if a mesh device is a valve type
 *
 * @param meshDevice - Mesh device to check
 * @returns True if device is a valve type
 */
export function isValveMeshDevice(
  meshDevice: IrriganetMeshDeviceWithDeviceId
): boolean {
  return meshDevice.type === MESH_DEVICE_TYPES.Valve;
}

/**
 * Check if a mesh device is a level type
 *
 * @param meshDevice - Mesh device to check
 * @returns True if device is a level type
 */
export function isLevelMeshDevice(
  meshDevice: IrriganetMeshDeviceWithDeviceId
): boolean {
  return meshDevice.type === MESH_DEVICE_TYPES.Level;
}

/**
 * Get mesh device type name
 *
 * @param type - Mesh device type code
 * @returns Human-readable type name
 */
export function getMeshDeviceTypeName(type: number): string {
  switch (type) {
    case MESH_DEVICE_TYPES.Valve:
      return "Valve";
    case MESH_DEVICE_TYPES.Pump:
      return "Pump";
    case MESH_DEVICE_TYPES.Level:
      return "Level";
    default:
      return "Unknown";
  }
}
