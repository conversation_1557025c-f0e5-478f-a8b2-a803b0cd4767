import type {
  IrrigationPlanWithSteps,
  ProjectWithSchedulingData,
} from "../../db/queries/types";
import type {
  IrriganetDeviceWithElementId,
  IrriganetMeshDeviceWithDeviceId,
  IrriganetSchedulingWithIrrigationPlanId,
  IrriganetSectorSchedulingWithStepId,
  LICTree,
} from "./types";
import { IdGenerator } from "./utilities";

export function generateSectorsScheduling(
  tree: LICTree,
  irriganetSchedulingList: IrriganetSchedulingWithIrrigationPlanId[],
  meshDevices: IrriganetMeshDeviceWithDeviceId[],
  devices: IrriganetDeviceWithElementId[],
  referenceDate: Date
) {
  const result: IrriganetSectorSchedulingWithStepId[] = [];
  const sectorSchedulingIdGen = new IdGenerator();
  tree.projects.forEach((project) => {
    const sectorsScheduling = generateProjectSectorsScheduling(
      project,
      sectorSchedulingIdGen,
      irriganetSchedulingList,
      meshDevices,
      devices,
      referenceDate
    );
    result.push(...sectorsScheduling);
  });
  return result;
}

function generateProjectSectorsScheduling(
  project: ProjectWithSchedulingData,
  sectorSchedulingIdGen: IdGenerator,
  irriganetSchedulingList: IrriganetSchedulingWithIrrigationPlanId[],
  meshDevices: IrriganetMeshDeviceWithDeviceId[],
  devices: IrriganetDeviceWithElementId[],
  referenceDate: Date
) {
  const result: IrriganetSectorSchedulingWithStepId[] = [];
  project.irrigation_plans.forEach((plan) => {
    const irriganetScheduling = irriganetSchedulingList.find(
      (s) => s.irrigationPlanId === plan.id
    );
    if (!irriganetScheduling) {
      throw new Error(`Irriganet scheduling not found for plan ${plan.id}`);
    }
    const sectorsScheduling = generateIrrigationPlanSectorsScheduling(
      project,
      plan,
      irriganetScheduling,
      sectorSchedulingIdGen,
      meshDevices,
      devices,
      referenceDate
    );
    result.push(...sectorsScheduling);
  });
  return result;
}

function generateIrrigationPlanSectorsScheduling(
  project: ProjectWithSchedulingData,
  plan: IrrigationPlanWithSteps,
  irriganetScheduling: IrriganetSchedulingWithIrrigationPlanId,
  sectorSchedulingIdGen: IdGenerator,
  meshDevices: IrriganetMeshDeviceWithDeviceId[],
  devices: IrriganetDeviceWithElementId[],
  _referenceDate: Date
) {
  const result: IrriganetSectorSchedulingWithStepId[] = [];
  plan.steps
    .toSorted((a, b) => a.order - b.order)
    .forEach((step, stepIndex) => {
      const sector = project.sectors.find((s) => s.id === step.sector);
      if (!sector) {
        throw new Error(`Sector not found for step ${step.id}`);
      }
      const meshDevice = meshDevices.find(
        (d) => d.deviceId === sector.valve_controller
      );
      if (!meshDevice) {
        throw new Error(`Mesh device not found for step ${step.id}`);
      }
      const device = devices.find(
        (d) => d.mesh_idx === meshDevice.idx && d.elementId === sector.id
      );
      if (!device) {
        throw new Error(`Device not found for step ${step.id}`);
      }

      const sectorSchedulingId = sectorSchedulingIdGen.next();
      const sectorScheduling: IrriganetSectorSchedulingWithStepId = {
        idx: sectorSchedulingId,
        irrigationPlanStepId: step.id,
        device_idx: device.idx,
        enabled: 1,
        ferti:
          plan.fertigation_enabled &&
          step.fertigation_duration_seconds &&
          step.fertigation_duration_seconds > 0
            ? Math.round(step.fertigation_duration_seconds / 60)
            : 0,
        ferti_delay: step.fertigation_start_delay_seconds
          ? Math.round(step.fertigation_start_delay_seconds / 60)
          : 0,
        working_time: step.duration_seconds
          ? Math.round(step.duration_seconds / 60)
          : 0,
        scheduling_idx: irriganetScheduling.idx,
        type: 0,
        n_order: stepIndex,
      };
      result.push(sectorScheduling);
    });
  return result;
}
