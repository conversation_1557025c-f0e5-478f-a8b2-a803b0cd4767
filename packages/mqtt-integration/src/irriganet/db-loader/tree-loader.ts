/**
 * Tree Loading Module for IrrigaNet Database Loaders
 *
 * This module handles loading and validation of LIC tree data from the database.
 * Following the Single Responsibility principle, all tree loading logic is
 * centralized here.
 */

import type { Sql as SQL } from "@/db/connection";
import { getLICTree } from "../../db/queries/lic-queries";
import { getPropertyById } from "../../db/queries/property-queries";
import { listPropertyLICS } from "../../db/queries/property-device-queries";
import type {
  Property,
  PropertyDeviceWithDevice,
} from "../../db/queries/types";
import type { LICTree, LICTreeRaw, IrriganetGroupWithProjectId } from "./types";
import { validateLICTree, validateProperty } from "./validation";

// ==============================================
// TREE LOADING FUNCTIONS
// ==============================================

/**
 * Load and validate LIC tree from database
 *
 * @param db - Database connection
 * @param licIdentifier - LIC device identifier
 * @param referenceDate - Reference date for active records
 * @returns Validated LIC tree
 * @throws Error if tree is not found or invalid
 */
export async function loadTree(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date
) {
  const tree: LICTreeRaw | null = await getLICTree(
    db,
    licIdentifier,
    referenceDate
  );

  // validateLICTree(tree, licIdentifier);
  return tree;
}

/**
 * Load property data by ID with validation
 *
 * @param db - Database connection
 * @param propertyId - Property identifier
 * @returns Property data
 * @throws Error if property is not found
 */
export async function loadProperty(
  db: SQL,
  propertyId: string
): Promise<Property> {
  const property = await getPropertyById(db, propertyId);
  validateProperty(property, propertyId);
  return property!;
}

/**
 * Load all LIC devices for a property
 *
 * @param db - Database connection
 * @param propertyId - Property identifier
 * @param referenceDate - Reference date for active records
 * @returns Array of LIC devices with property device information
 */
export async function loadPropertyLICs(
  db: SQL,
  propertyId: string,
  referenceDate: Date
): Promise<PropertyDeviceWithDevice[]> {
  return await listPropertyLICS(db, propertyId, referenceDate);
}

// ==============================================
// GROUP GENERATION
// ==============================================

/**
 * Generate groups from LIC tree projects
 *
 * @param tree - Validated LIC tree
 * @returns Array of groups with project IDs
 */
export function generateProjectGroups(
  tree: LICTree
): Array<IrriganetGroupWithProjectId> {
  return tree.projects.map((project, index) => ({
    idx: index + 1,
    name: project.name,
    projectId: project.id,
  }));
}

// ==============================================
// TREE ANALYSIS UTILITIES
// ==============================================

/**
 * Get all unique device identifiers from a tree
 *
 * @param tree - LIC tree to analyze
 * @returns Set of unique device identifiers
 */
export function getTreeDeviceIdentifiers(tree: LICTree): Set<string> {
  const identifiers = new Set<string>();

  // Add LIC identifier
  identifiers.add(tree.identifier);

  // Add project device identifiers
  tree.projects.forEach((project) => {
    // Irrigation pump controller
    if (project.irrigation_water_pump?.water_pump_controller) {
      identifiers.add(
        project.irrigation_water_pump.water_pump_controller.identifier
      );
    }

    // Fertigation pump controller
    if (project.fertigation_water_pump?.water_pump_controller) {
      identifiers.add(
        project.fertigation_water_pump.water_pump_controller.identifier
      );
    }

    // Valve controllers
    project.sectors.forEach((sector) => {
      if (sector.valve_controller_device) {
        identifiers.add(sector.valve_controller_device.identifier);
      }
    });
  });

  // Add service pump identifiers
  tree.servicePumps?.forEach((pump) => {
    if (pump.water_pump_controller) {
      identifiers.add(pump.water_pump_controller.identifier);
    }
  });

  // Add reservoir monitor identifiers
  tree.reservoirs?.forEach((reservoir) => {
    if (reservoir.reservoir_monitor) {
      identifiers.add(reservoir.reservoir_monitor.identifier);
    }
  });

  return identifiers;
}

/**
 * Count total devices in a tree
 *
 * @param tree - LIC tree to analyze
 * @returns Object with device counts by type
 */
export function countTreeDevices(tree: LICTree): {
  total: number;
  lic: number;
  pumpControllers: number;
  valveControllers: number;
  reservoirMonitors: number;
} {
  let pumpControllers = 0;
  let valveControllers = 0;
  let reservoirMonitors = 0;

  // Count project devices
  tree.projects.forEach((project) => {
    if (project.irrigation_water_pump?.water_pump_controller) {
      pumpControllers++;
    }
    if (project.fertigation_water_pump?.water_pump_controller) {
      pumpControllers++;
    }

    // Count unique valve controllers
    const uniqueValveControllers = new Set(
      project.sectors
        .filter((sector) => sector.valve_controller_device)
        .map((sector) => sector.valve_controller_device.id)
    );
    valveControllers += uniqueValveControllers.size;
  });

  // Count service pumps
  if (tree.servicePumps) {
    pumpControllers += tree.servicePumps.length;
  }

  // Count reservoir monitors
  if (tree.reservoirs) {
    tree.reservoirs.forEach((reservoir) => {
      if (reservoir.reservoir_monitor) {
        reservoirMonitors += 1;
      }
    });
  }

  const lic = 1; // Always one LIC
  const total = lic + pumpControllers + valveControllers + reservoirMonitors;

  return {
    total,
    lic,
    pumpControllers,
    valveControllers,
    reservoirMonitors,
  };
}

/**
 * Validate tree structure and content
 *
 * @param tree - LIC tree to validate
 * @throws Error if tree structure is invalid
 */
export function validateTreeStructure(tree: LICTree): void {
  // Validate basic structure
  if (!tree.projects || !Array.isArray(tree.projects)) {
    throw new Error("Tree must have a projects array");
  }

  // Validate each project has required fields
  tree.projects.forEach((project, index) => {
    if (!project.id) {
      throw new Error(`Project at index ${index} must have an id`);
    }
    if (!project.name) {
      throw new Error(`Project ${project.id} must have a name`);
    }
  });

  // Validate property device exists
  if (!tree.propertyDevice) {
    throw new Error("Tree must have a propertyDevice");
  }
}

// ==============================================
// TREE TRANSFORMATION UTILITIES
// ==============================================

/**
 * Extract project IDs from tree
 *
 * @param tree - LIC tree
 * @returns Array of project IDs
 */
export function extractProjectIds(tree: LICTree): string[] {
  return tree.projects.map((project) => project.id);
}

/**
 * Find project by ID in tree
 *
 * @param tree - LIC tree
 * @param projectId - Project ID to find
 * @returns Project if found, undefined otherwise
 */
export function findProjectById(tree: LICTree, projectId: string) {
  return tree.projects.find((project) => project.id === projectId);
}

/**
 * Get tree summary information
 *
 * @param tree - LIC tree
 * @returns Summary object with key information
 */
export function getTreeSummary(tree: LICTree) {
  const deviceCounts = countTreeDevices(tree);
  const projectIds = extractProjectIds(tree);

  return {
    licIdentifier: tree.identifier,
    licModel: tree.model,
    propertyDeviceId: tree.propertyDevice.id,
    projectCount: tree.projects.length,
    projectIds,
    deviceCounts,
    hasServicePumps: Boolean(tree.servicePumps?.length),
    hasReservoirs: Boolean(tree.reservoirs?.length),
  };
}
