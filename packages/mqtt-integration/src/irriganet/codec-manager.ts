import type { Sql } from "@/db/connection";
import { getLICStateByDeviceId } from "@/db/queries/lic-state-queries";
import fastq from "fastq";
import path from "path";
import { codec } from "proto";
import { config } from "../config";
import { insertCurrentLICPacket } from "../db/mutations/current-lic-packet";
import {
  createDeviceMessageRequest,
  markDeviceMessageRequestAsFailed,
  markDeviceMessageRequestAsProcessing,
  markDeviceMessageRequestAsSent,
} from "../db/mutations/device-message-request";
import { insertLIC } from "../db/mutations/lic";
import {
  updateLICStateFromLICState,
  updateLICStateRequestTimestamp,
} from "../db/mutations/lic-state";
import type { CurrentLICPacketInsert } from "../db/mutations/types";
import { getLIC } from "../db/queries/lic-queries";
import type {
  DeviceMessageRequest,
  DeviceMessageRequestWithDevice,
} from "../db/queries/types";
import { logger, LoggerManager } from "../log";
import type {
  CodecTransportStopCallback,
  ICodecTransport,
  ICodecTransportFactory,
} from "../transport/types";
import { TimeoutTimer, type ITimer } from "../utils/timer";
import { loadLICStateByIdentifier } from "./db-loader";
import type { LICState, LICTreeRaw } from "./db-loader/types";
import { appendCRC16, licStateChanges } from "./db-loader/utilities";
import type { IOutgoingPacketProcessor } from "./package-processors/types";
import {
  automationPackage,
  commandPacket,
  configPackage,
  controlPackage,
  deviceSchedulingPackage,
  devicesPackage,
  firmware_updatePackage,
  request_infoPackage,
  schedulingPackage,
} from "./proto";

const insertPacketQueue = fastq<
  unknown,
  { db: Sql; record: CurrentLICPacketInsert },
  number
>(async (task, cb) => {
  await insertCurrentLICPacket(task.db, task.record)
    .then((r) => {
      const err = r?.id
        ? null
        : new Error("Failed to insert current LIC packet");
      cb(err, r?.id);
    })
    .catch((err) => {
      cb(err);
    });
}, 1); // Limit concurrency to 1

function isJSONPayload(payload: Buffer): boolean {
  return payload.at(0) === 123 && payload.at(-1) === 125; // Check if payload starts with '{' and ends with '}'
}

const parseMessageLogger = LoggerManager.getLogger("CodecManager.parseMessage");
function parseMessage(topic: string, message: Buffer, referenceDate: Date) {
  if (isJSONPayload(message)) {
    try {
      const json_payload = JSON.parse(message.toString());
      return { json_payload: json_payload };
    } catch (error) {
      parseMessageLogger.error("Failed to decode JSON report message:", error);
    }
    return null;
  }
  try {
    if (topic === "report") {
      const decoded = codec.out.OutgoingPacket.decode(message);
      if (parseMessageLogger.traceEnabled) {
        parseMessageLogger.debug(
          "Decoded report message:",
          `payload=${decoded.payload}`,
          JSON.stringify(decoded, null, 2)
        );
      }
      return decoded;
    }
  } catch (error) {
    parseMessageLogger.error("Failed to decode report message:", error);
  }
  return null;
}
// "config" | "devices" | "scheduling" | "dev_scheduling" | "automation" | "control" | "command" | "request_info" | "firmware_update"
const packetBuilders = {
  config: configPackage,
  devices: devicesPackage,
  scheduling: schedulingPackage,
  dev_scheduling: deviceSchedulingPackage,
  automation: automationPackage,
  request_info: request_infoPackage,
  firmware_update: firmware_updatePackage,
  control: controlPackage,
  command: commandPacket,
} as const;

type PacketBuilders = typeof packetBuilders;
type Tail<T extends any[]> = T extends [any, ...infer R] ? R : never;
type TailArgs<F extends (...a: any[]) => any> = Tail<Parameters<F>>;
type FirstArg<F extends (...a: any[]) => any> = Parameters<F>[0];
type FullArgs<K extends keyof PacketBuilders> = Parameters<PacketBuilders[K]>;
type IncomingPacketPayloadType = NonNullable<
  codec.in_.IncomingPacket["payload"]
>;

type UpdateStatus = "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED";
type UpdateFlags = {
  config: UpdateStatus;
  devices: UpdateStatus;
  scheduling: UpdateStatus;
  automation: UpdateStatus;
  request_info: UpdateStatus;
};

const UPDATE_FLAG_PRIORITY_ORDER: Array<keyof UpdateFlags> = [
  "config",
  "devices",
  "scheduling",
  "automation",
  "request_info",
];

const log = LoggerManager.getLogger("CodecManager");
export class CodecManager {
  private readonly stopCallback: CodecTransportStopCallback;
  private state!: LICState & { tree?: LICTreeRaw };

  private updateFlags: UpdateFlags = {
    config: "COMPLETED",
    devices: "COMPLETED",
    scheduling: "COMPLETED",
    automation: "COMPLETED",
    request_info: "PENDING",
  };

  private updateTimer: ITimer = new TimeoutTimer();

  constructor(
    private licIdentifier: string,
    private referenceDate: Date,
    state: LICState & { tree?: LICTreeRaw },
    private db: Sql,
    private transport: ICodecTransport,
    private packetProcessor: IOutgoingPacketProcessor
  ) {
    this.setState(state, referenceDate);
    this.stopCallback = this.transport.onMessage(
      async (topic, payload, referenceDate) => {
        await this.handleMessage(topic, payload, referenceDate);
      }
    );
    this.updateTimer.start(
      config.codecManager.updateConfigInterval,
      async () => {
        await this.sendNextUpdate();
      }
    );
  }

  getState(): LICState {
    return this.state;
  }

  protected async setState(
    newState: LICState & { tree?: LICTreeRaw },
    referenceDate: Date
  ) {
    if (log.debugEnabled) {
      log.debug(`[${this.licIdentifier}] Setting state`);
    }
    this.state = newState;

    // Save LIC state to database
    await updateLICStateFromLICState(
      this.db,
      this.state.lic.irrigaMaisDeviceId,
      newState,
      referenceDate
    )
      .then(() => {
        if (log.debugEnabled) {
          log.debug(`[${this.licIdentifier}] Updated lic_state in database`);
        }
      })
      .catch((error) => {
        log.error(
          `[${this.licIdentifier}] Failed to update lic_state in database:`,
          error
        );
      });

    if (config.debug.codecState.enabled && config.debug.codecState.outputDir) {
      Bun.file(
        path.join(
          config.debug.codecState.outputDir,
          `${this.licIdentifier}-state.json`
        )
      ).write(JSON.stringify(newState));
    }
  }

  getLicIdentifier() {
    return this.licIdentifier;
  }

  getReferenceDate() {
    return this.referenceDate;
  }

  async handleMessage(topic: string, message: Buffer, referenceDate: Date) {
    // Handle the incoming message

    log.debug(
      `[${this.licIdentifier}] Received report message on topic ${topic}:`,
      message
    );
    const parsed = parseMessage(topic, message, referenceDate);
    if (parsed) {
      const payloadType =
        "json_payload" in parsed ? "json_payload" : parsed.payload;
      const payloadData =
        "json_payload" in parsed
          ? parsed.json_payload
          : codec.out.OutgoingPacket.toObject(parsed, {
              longs: String,
              enums: String,
              bytes: String,
              json: true,
              defaults: true,
            });
      if (log.traceEnabled) {
        log.debug(
          `[${this.licIdentifier}] Parsed message ${payloadType} on topic ${topic}:`,
          payloadData
        );
      }

      const packetDate =
        "id" in parsed ? new Date(Number(parsed.id) * 1000) : referenceDate;

      insertPacketQueue.push(
        {
          db: this.db,
          record: {
            device: this.state.lic.irrigaMaisDeviceId,
            packet_date: packetDate,
            payload_type: payloadType ?? "unknown",
            payload_data: payloadData,
          },
        },
        (err, r) => {
          if (err) {
            log.error(
              `[${this.licIdentifier}] Failed to insert LIC packet for device ${this.state.lic.irrigaMaisDeviceId}:`,
              err
            );
          } else {
            log.debug(
              `[${this.licIdentifier}] Inserted LIC packet for device ${this.state.lic.irrigaMaisDeviceId} with id=${r}`
            );
          }
        }
      );

      if ("payload" in parsed) {
        await this.packetProcessor
          .process(parsed, {
            db: this.db,
            codecManager: this,
            state: this.state,
            referenceDate,
          })
          .catch((error) => {
            log.error(
              `[${this.licIdentifier}] Failed to process packet ${parsed.payload}:`,
              error
            );
          });
      }
    } else {
      log.error(
        `[${this.licIdentifier}] Failed to parse message on topic ${topic}:`,
        message
      );
    }
  }

  /**
   * Send a message based on device_message_request data with database integration.
   * This method handles the complete lifecycle of sending a message:
   * 1. Mark message as processing and increment attempts
   * 2. Build and send the protobuf message
   * 3. Update status based on send result
   *
   * @param messageRequest - Device message request with device info
   * @returns Promise that resolves to success/failure status
   */
  async sendDeviceMessageRequest(
    messageRequest: DeviceMessageRequestWithDevice
  ): Promise<{ success: boolean; error?: string }> {
    const messageId = messageRequest.id;
    const messageType: IncomingPacketPayloadType = messageRequest.payload_type;
    const packetId = Number(messageRequest.packet_id);
    const payloadData = messageRequest.payload_data;
    log.info(
      `[${
        this.licIdentifier
      }] Handling device message request ${messageId} (${messageType}, packet_id: ${packetId}), payload_data: ${JSON.stringify(
        payloadData
      )}`
    );
    try {
      // Step 1: Create the protobuf packet
      const packet = this.createIncomingPacket(
        messageType,
        packetId,
        payloadData
      );
      const payloadBytes = Buffer.from(
        appendCRC16(codec.in_.IncomingPacket.encode(packet).finish())
      );

      // Step 2: Mark message as processing and store payload_bytes
      log.debug(
        `[${this.licIdentifier}] Marking packet_id=${messageId} as processing`
      );
      const processingUpdate = await markDeviceMessageRequestAsProcessing(
        this.db,
        messageId,
        payloadBytes,
        {
          ...messageRequest.metadata,
          payload: packet,
        }
      );

      if (!processingUpdate) {
        const error =
          "Failed to mark message as processing - message may have been processed already";
        log.warn(`[${this.licIdentifier}] ${error} for message ${messageId}`);
        return { success: false, error };
      }

      log.debug(
        `[${this.licIdentifier}] Marked message ${messageId} as processing (attempt ${processingUpdate.attempts})`
      );

      // Step 3: Send the message via transport
      try {
        log.info(
          `[${this.licIdentifier}] Sending message ${messageId} (${messageType}, packet_id: ${packetId})`
        );
        await this.transport.sendMessage(payloadBytes);

        // Step 4: Mark as sent on successful transmission
        log.debug(
          `[${this.licIdentifier}] Marking packet_id=${messageId} as sent`
        );
        const sentUpdate = await markDeviceMessageRequestAsSent(
          this.db,
          messageId
        );

        if (sentUpdate) {
          log.info(
            `[${this.licIdentifier}] Successfully sent message ${messageId} (${messageType}, packet_id: ${packetId})`
          );
          await this.updateRequestTimestamp(messageRequest);
          return { success: true };
        } else {
          const error = "Failed to mark message as sent in database";
          log.error(
            `[${this.licIdentifier}] ${error} for message ${messageId}`
          );
          return { success: false, error };
        }
      } catch (sendError) {
        // Step 4b: Mark as failed on transmission error
        log.debug(
          `[${this.licIdentifier}] Marking packet_id=${messageId} as failed`
        );
        const errorMessage =
          sendError instanceof Error ? sendError.message : String(sendError);

        await markDeviceMessageRequestAsFailed(
          this.db,
          messageId,
          errorMessage
        );

        log.error(
          `[${this.licIdentifier}] Failed to send message ${messageId}: ${errorMessage}`
        );

        return { success: false, error: errorMessage };
      }
    } catch (error) {
      // Handle any other errors (packet creation, database errors, etc.)
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      try {
        await markDeviceMessageRequestAsFailed(
          this.db,
          messageId,
          errorMessage
        );
      } catch (dbError) {
        log.error(
          `[${this.licIdentifier}] Failed to mark message ${messageId} as failed: ${dbError}`
        );
      }

      log.error(
        `[${this.licIdentifier}] Error processing message ${messageId}: ${errorMessage}`,
        error
      );

      return { success: false, error: errorMessage };
    }
  }

  /*
- config can be updated any time
- devices can be updated any time and causes update of: scheduling, automation
- scheduling can be updated only if devices is updated
- automation can be updated only if devices is updated
*/
  private canBeUpdated(key: keyof UpdateFlags): boolean {
    switch (key) {
      case "config":
        return true;
      case "devices":
        return true;
      case "scheduling":
        return this.updateFlags.devices === "COMPLETED";
      case "automation":
        return this.updateFlags.devices === "COMPLETED";
      case "request_info":
        return (
          this.updateFlags.devices !== "PENDING" &&
          this.updateFlags.config !== "PENDING" &&
          this.updateFlags.scheduling !== "PENDING" &&
          this.updateFlags.automation !== "PENDING"
        );
      default:
        return false;
    }
  }

  /**
   * Check if the update can be processed.
   * SKIPPED if the update cannot be processed due to unmet dependencies.
   * UP_TO_DATE if the update is not needed.
   * FAILED if the update processing failed (e.g. database error when creating the message request).
   * UPDATED if the update was successful. (e.g. device_message_request created successfully)
   *
   * @param key The update key to check
   * @returns "SKIPPED" | "UPDATED" | "UP_TO_DATE" | "FAILED"
   */
  private async checkUpdate(
    key: keyof UpdateFlags
  ): Promise<"SKIPPED" | "UPDATED" | "UP_TO_DATE" | "FAILED"> {
    if (this.updateFlags[key] === "PENDING") {
      if (!this.canBeUpdated(key)) {
        log.info(
          `[${this.licIdentifier}] checkUpdate - Skipping update: ${key}`
        );
        return "SKIPPED";
      }
      this.updateFlags[key] = "PROCESSING";
      log.info(
        `[${this.licIdentifier}] checkUpdate - Processing update: ${key}`
      );
      return await createDeviceMessageRequest(this.db, {
        device: this.state.lic.irrigaMaisDeviceId,
        payload_type: key,
      })
        .then(async (res) => {
          log.debug(
            `[${
              this.licIdentifier
            }] checkUpdate - Created device message request: ${JSON.stringify(
              res
            )}`
          );

          if (this.updateFlags[key] === "PROCESSING") {
            this.updateFlags[key] = "COMPLETED";
          } else {
            log.warn(
              `[${this.licIdentifier}] checkUpdate - Update ${key} is no longer processing`
            );
          }
          return "UPDATED" as const;
        })
        .catch((error) => {
          if (this.updateFlags[key] === "PROCESSING") {
            this.updateFlags[key] = "FAILED";
          }
          log.error(
            `[${this.licIdentifier}] checkUpdate - Failed to create device message request`,
            error
          );
          return "FAILED" as const;
        });
    }
    return "UP_TO_DATE";
  }

  private markRequestInfoPendingTimeout: NodeJS.Timeout | null = null;
  private markRequestInfoPending() {
    if (this.markRequestInfoPendingTimeout) {
      clearTimeout(this.markRequestInfoPendingTimeout);
    }
    this.markRequestInfoPendingTimeout = setTimeout(() => {
      this.updateFlags.request_info = "PENDING";
    }, 30000);
  }

  private async updateRequestTimestamp(
    res: DeviceMessageRequest | DeviceMessageRequestWithDevice
  ) {
    const key = res.payload_type;
    if (key !== "request_info" && res.packet_id) {
      const configTypeMap = {
        config: "config",
        devices: "devices",
        scheduling: "scheduling",
        automation: "automation",
      } as const;

      const configType = configTypeMap[key as keyof typeof configTypeMap];
      if (configType) {
        this.markRequestInfoPending();
        try {
          await updateLICStateRequestTimestamp(
            this.db,
            this.state.lic.irrigaMaisDeviceId,
            configType,
            Number(res.packet_id),
            new Date(Number(res.packet_id) * 1000)
          );
          if (log.debugEnabled) {
            log.debug(
              `[${this.licIdentifier}] Updated ${configType} request timestamp: ${res.packet_id}`
            );
          }
        } catch (error) {
          log.error(
            `[${this.licIdentifier}] Failed to update ${configType} request timestamp:`,
            error
          );
        }
      }
    }
  }

  private async sendNextUpdate() {
    for (const key of UPDATE_FLAG_PRIORITY_ORDER) {
      const updateStatus = await this.checkUpdate(key);
      if (updateStatus === "UPDATED") {
        return key;
      }
    }
    return null;
  }

  private async applyStateChanges(
    newState: LICState & { tree?: LICTreeRaw },
    referenceDate: Date
  ) {
    const updates = licStateChanges(this.state, newState).reduce(
      (reqs, key) => {
        if (key === "meshDevices" || key === "devices" || key === "groups") {
          reqs.add("devices");
          reqs.add("scheduling");
          reqs.add("automation");
        } else if (
          key === "schedules" ||
          key === "deviceSchedules" ||
          key == "sectorSchedules"
        ) {
          reqs.add("scheduling");
        } else if (key === "lic") {
          reqs.add("config");
        }
        return reqs;
      },
      new Set<keyof UpdateFlags>()
    );
    if (updates.size > 0) {
      await this.setState(newState, referenceDate);
      this.referenceDate = referenceDate;
      updates.forEach((key) => {
        this.updateFlags[key] = "PENDING";
      });
      log.info(
        `[${
          this.licIdentifier
        }] applyStateChanges - State changes detected: ${Array.from(
          updates
        ).join(", ")}`
      );
      return true;
    }
    return false;
  }

  async reloadFromDB(db: Sql, referenceDate: Date) {
    this.referenceDate = referenceDate;
    await loadLICStateByIdentifier(db, this.licIdentifier, referenceDate)
      .then(async (state) => {
        if (!state) {
          if (log.traceEnabled) {
            log.warn(
              `[${this.licIdentifier}] LIC not associated to a property`
            );
          }
          return null;
        }
        await this.applyStateChanges(state, referenceDate);
      })
      .catch((error) => {
        log.error(
          `[${this.licIdentifier}] Error reloading LIC state: ${error}`
        );
      });
  }

  private createIncomingPacket<K extends keyof PacketBuilders>(
    messageType: K,
    id: number,
    ...params: TailArgs<PacketBuilders[K]>
  ): codec.in_.IIncomingPacket {
    const fn = packetBuilders[messageType] as unknown as (
      ...a: FullArgs<K>
    ) => ReturnType<PacketBuilders[K]>;

    const args = [this.state, ...params] as unknown as FullArgs<K>;

    const innerData = fn(...args);

    return {
      id,
      [messageType]: innerData,
    } as codec.in_.IIncomingPacket;
  }

  public async checkOutdatedConfiguration() {
    const outdatedConfig = await this.findOutdatedConfiguration();
    if (outdatedConfig.length > 0) {
      log.warn(
        `[${
          this.licIdentifier
        }] Outdated configuration found: ${outdatedConfig.join(", ")}`
      );
      if (config.codecManager.outdatedConfigurationCheckEnabled) {
        outdatedConfig.forEach((key) => {
          this.updateFlags[key] = "PENDING";
        });
      }
    }
  }

  private async findOutdatedConfiguration(): Promise<
    Array<"config" | "devices" | "scheduling" | "automation">
  > {
    const result: Array<"config" | "devices" | "scheduling" | "automation"> =
      [];
    const dbLicState = await getLICStateByDeviceId(
      this.db,
      this.state.lic.irrigaMaisDeviceId
    );
    if (!dbLicState) {
      log.warn(
        `[${this.licIdentifier}] checkOutdatedConfiguration - LIC state not found in database`
      );
      return result;
    }
    if (
      dbLicState.last_devices_request == null ||
      dbLicState.current_devices_timestamp == null ||
      dbLicState.current_devices_timestamp < dbLicState.last_devices_request
    ) {
      result.push("devices");
    }
    if (
      dbLicState.last_scheduling_request == null ||
      dbLicState.current_scheduling_timestamp == null ||
      dbLicState.current_scheduling_timestamp <
        dbLicState.last_scheduling_request
    ) {
      result.push("scheduling");
    }
    if (
      dbLicState.last_automation_request == null ||
      dbLicState.current_automation_timestamp == null ||
      dbLicState.current_automation_timestamp <
        dbLicState.last_automation_request
    ) {
      result.push("automation");
    }

    if (
      dbLicState.last_config_request == null ||
      dbLicState.current_config_timestamp == null ||
      dbLicState.current_config_timestamp < dbLicState.last_config_request
    ) {
      result.push("config");
    }
    return result;
  }

  static async loadFromDB(
    db: Sql,
    licIdentifier: string,
    transportFactory: ICodecTransportFactory,
    packetProcessor: IOutgoingPacketProcessor,
    referenceDate: Date
  ) {
    let state:
      | (LICState & {
          tree?: LICTreeRaw;
        })
      | null = await loadLICStateByIdentifier(db, licIdentifier, referenceDate);
    if (!state) {
      let lic = await getLIC(db, licIdentifier);
      if (!lic) {
        lic = await insertLIC(db, licIdentifier);
        if (!lic) {
          logger.error(`[${licIdentifier}] Failed to create LIC`);
          return null;
        }
      }
      state = {
        devices: [],
        deviceSchedules: [],
        groups: [],
        meshDevices: [],
        schedules: [],
        sectorSchedules: [],
        lic: {
          irrigaMaisDeviceId: lic.id,
          enabled: 1,
          identity: lic.identifier,
          idx: 1,
          name: lic.identifier,
          propertyDeviceId: "", // TODO: make propertyDeviceId optional
          config: {},
        },
        tree: undefined,
      };
    }
    return new CodecManager(
      licIdentifier,
      referenceDate,
      state,
      db,
      transportFactory.createTransport(licIdentifier),
      packetProcessor
    );
  }
}
