/**
 * Project State Calculator
 *
 * This module calculates the state of irrigation projects based on SystemStatusPackage
 * bitmask data and LIC device configuration.
 */

import { codec } from "proto";
import type { LICState, LICTree } from "./db-loader/types";

// Project state types
export type ProjectState = {
  id: string;
  project: string;
  packet_date: Date;
  irrigation_status: "active" | "inactive" | "error";
  fertigation_status: "active" | "inactive" | "error";
  backwash_status: "active" | "inactive" | "error";
  sectors: Array<{
    sector: string;
    sector_name: string;
    status: "active" | "inactive" | "error";
  }>;
};

/**
 * Calculate the state of all projects associated with a LIC based on SystemStatusPackage
 *
 * @param systemStatusPackage - The SystemStatusPackage from the LIC device
 * @param licState - The current LIC state containing device mappings
 * @param licTree - The LIC tree containing project and device relationships
 * @param packetDate - The date when the device status packet was recorded
 * @returns Array of ProjectState objects for each project
 */
export function calculateProjectsState(
  systemStatusPackage: codec.out.status.ISystemStatusPackage,
  licState: LICState,
  licTree: LICTree,
  packetDate: Date
): ProjectState[] {
  const projectStates: ProjectState[] = [];

  try {
    // Iterate through each project in the LIC tree
    for (const project of licTree.projects) {
      const projectState: ProjectState = {
        id: crypto.randomUUID(),
        project: project.id,
        packet_date: packetDate,
        irrigation_status: "inactive",
        fertigation_status: "inactive",
        backwash_status: "inactive",
        sectors: [],
      };

      // Calculate irrigation status
      if (project.irrigation_water_pump?.water_pump_controller) {
        const irrigationDevice = findDeviceByElementId(
          licState.devices,
          project.irrigation_water_pump.id,
          "pump"
        );
        if (irrigationDevice) {
          projectState.irrigation_status = calculateDeviceStatus(
            irrigationDevice,
            systemStatusPackage
          );
        }
      }

      // Calculate fertigation status
      if (project.fertigation_water_pump?.water_pump_controller) {
        const fertigationDevice = findDeviceByElementId(
          licState.devices,
          project.fertigation_water_pump.id,
          "pump"
        );
        if (fertigationDevice) {
          projectState.fertigation_status = calculateDeviceStatus(
            fertigationDevice,
            systemStatusPackage
          );
        }
      }

      // Calculate backwash status based on backwash_pump_type
      if (project.backwash_pump_type) {
        let backwashDevice;

        if (
          project.backwash_pump_type === "IRRIGATION" &&
          project.irrigation_water_pump
        ) {
          // Backwash uses irrigation pump
          backwashDevice = findDeviceByElementId(
            licState.devices,
            project.irrigation_water_pump.id,
            "pump",
            "backwash"
          );
        } else if (
          project.backwash_pump_type === "FERTIGATION" &&
          project.fertigation_water_pump
        ) {
          // Backwash uses fertigation pump
          backwashDevice = findDeviceByElementId(
            licState.devices,
            project.fertigation_water_pump.id,
            "pump",
            "backwash"
          );
        }

        if (backwashDevice) {
          projectState.backwash_status = calculateDeviceStatus(
            backwashDevice,
            systemStatusPackage
          );
        }
      }

      // Calculate sector states
      for (const sector of project.sectors) {
        if (sector.valve_controller_device) {
          const sectorDevice = findDeviceByElementId(
            licState.devices,
            sector.id,
            "valve"
          );

          const sectorStatus = sectorDevice
            ? calculateDeviceStatus(sectorDevice, systemStatusPackage)
            : "inactive";

          projectState.sectors.push({
            sector: sector.id,
            sector_name: sector.name,
            status: sectorStatus,
          });
        }
      }

      projectStates.push(projectState);
    }

    return projectStates;
  } catch (error) {
    console.error("Error calculating project states:", error);
    throw new Error(
      `Failed to calculate project states: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

/**
 * Find a device in the LIC state devices array by element ID and type
 *
 * @param devices - Array of devices from LIC state
 * @param elementId - The ID of the element (pump, sector, etc.)
 * @param elementType - The type of element ("valve", "pump", "reservoir")
 * @param elementVariation - Optional variation (e.g., "irrigation", "fertigation", "backwash")
 * @returns The matching device or undefined
 */
function findDeviceByElementId(
  devices: LICState["devices"],
  elementId: string,
  elementType: "valve" | "pump" | "reservoir",
  elementVariation?: string
): LICState["devices"][0] | undefined {
  return devices.find(
    (device) =>
      device.elementId === elementId &&
      device.elementType === elementType &&
      (elementVariation === undefined ||
        device.elementVariation === elementVariation)
  );
}

/**
 * Calculate the status of a device based on SystemStatusPackage bitmasks
 *
 * @param device - The device from LIC state
 * @param systemStatusPackage - The SystemStatusPackage containing bitmasks
 * @returns Device status: "active", "inactive", or "error"
 */
function calculateDeviceStatus(
  device: LICState["devices"][0],
  systemStatusPackage: codec.out.status.ISystemStatusPackage
): "active" | "inactive" | "error" {
  const ordIdx = device.ord_idx;

  if (ordIdx === undefined || ordIdx === null) {
    console.warn(
      `Device ${device.identity} has no ord_idx, defaulting to inactive`
    );
    return "inactive";
  }

  try {
    // Check if device has failed
    const hasFailed = checkBitmask(systemStatusPackage.failed_bitmask, ordIdx);
    if (hasFailed) {
      return "error";
    }

    // Check if device is active/on
    const isActive = checkBitmask(
      systemStatusPackage.on_bitmask
        ? BigInt(systemStatusPackage.on_bitmask.toString())
        : undefined,
      ordIdx
    );
    if (isActive) {
      return "active";
    }

    // Default to inactive
    return "inactive";
  } catch (error) {
    console.error(
      `Error calculating status for device ${device.identity}:`,
      error
    );
    return "inactive";
  }
}

/**
 * Check if a bit is set in a bitmask at the specified index
 *
 * @param bitmask - The bitmask to check (can be number, bigint, or string)
 * @param bitIndex - The index of the bit to check
 * @returns True if the bit is set, false otherwise
 */
function checkBitmask(
  bitmask: number | bigint | string | undefined | null,
  bitIndex: number
): boolean {
  if (bitmask === undefined || bitmask === null) {
    return false;
  }

  try {
    // Convert to BigInt to handle large bitmasks safely
    const mask = BigInt(bitmask);
    const bit = BigInt(1) << BigInt(bitIndex);
    return (mask & bit) !== BigInt(0);
  } catch (error) {
    console.error(
      `Error checking bitmask ${bitmask} at index ${bitIndex}:`,
      error
    );
    return false;
  }
}
