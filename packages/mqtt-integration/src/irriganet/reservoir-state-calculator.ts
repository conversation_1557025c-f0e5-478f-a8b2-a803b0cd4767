import { codec } from "proto";

export type ReservoirState = {
  reservoir: string;
  packet_date: Date;
  start_time: Date;
  restart_time: Date | null;
  end_time: Date | null;
};

export function calculateReservoirState(
  report: codec.out.automation_report.IAutomationReportData,
  reservoirId: string,
  packetDate: Date
): ReservoirState | null {
  if (report.start_time == null) return null;

  const start = convertTimestamp(report.start_time);
  if (!start) return null;

  const restart = report.restart_time
    ? convertTimestamp(report.restart_time)
    : null;
  const end = report.end_time ? convertTimestamp(report.end_time) : null;

  const finalEnd = !end && report.status === 1 ? packetDate : end;

  return {
    reservoir: reservoirId,
    packet_date: packetDate,
    start_time: start,
    restart_time: restart,
    end_time: finalEnd ?? null,
  };
}

function convertTimestamp(ts: number | Long | string): Date | null {
  try {
    if (typeof ts === "string") {
      ts = parseInt(ts, 10);
    }
    const seconds = typeof ts === "number" ? ts : ts.toNumber();
    return new Date(seconds * 1000);
  } catch {
    return null;
  }
}
