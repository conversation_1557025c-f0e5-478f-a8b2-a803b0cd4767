import type { Sql, Sql as SQL } from "@/db/connection";
import { LoggerManager } from "../log";
import { CodecManager } from "./codec-manager";
import type { ICodecTransportFactory } from "../transport/types";
import EventEmitter from "events";
import { listLICS } from "../db/queries/lic-queries";
import type { DeviceMessageRequestWithDevice } from "../db/queries/types";
import fs from "fs";
import { config } from "../config";
import type { IOutgoingPacketProcessor } from "./package-processors/types";
import { date } from "@/utils/date";
export class CodecManagerRegistry {
  private static readonly log = LoggerManager.getLogger("CodecManagerRegistry");
  private managers: Map<string, Promise<CodecManager>> = new Map();
  private emitter = new EventEmitter<{
    codecManagerLoadSuccess: [codecManager: CodecManager];
    codecManagerLoadError: [
      error: Error,
      licIdentifier: string,
      referenceDate: Date
    ];
  }>();
  constructor(
    private db: Sql,
    private transportFactory: ICodecTransportFactory,
    private packetProcessor: IOutgoingPacketProcessor
  ) {}
  async init(referenceDate: Date) {
    if (config.debug.codecState.enabled && config.debug.codecState.outputDir) {
      CodecManagerRegistry.log.info(
        `Creating directory: ${config.debug.codecState.outputDir}`
      );
      fs.mkdirSync(config.debug.codecState.outputDir, { recursive: true });
    }
    CodecManagerRegistry.log.info("CodecManagerRegistry initialized");
    await this.fetchLICS(referenceDate).then((lics) => {
      CodecManagerRegistry.log.info(`Found ${lics.length} LICs`);
    });
  }

  async fetchLICS(referenceDate: Date) {
    if (CodecManagerRegistry.log.traceEnabled) {
      CodecManagerRegistry.log.debug("Fetching LICs...");
    }
    const lics = await listLICS(this.db);
    if (CodecManagerRegistry.log.traceEnabled) {
      CodecManagerRegistry.log.debug("Fetched LICs:", lics.length);
    }

    for (const lic of lics) {
      if (this.managers.has(lic.identifier)) {
        await this.managers.get(lic.identifier)?.then((manager) => {
          return manager.reloadFromDB(this.db, new Date());
        });
      } else {
        await this.get(this.db, lic.identifier, referenceDate).catch((err) => {
          CodecManagerRegistry.log.error(
            `Failed to load CodecManager for ${lic.identifier}:`,
            err
          );
        });
      }
    }
    return lics;
  }

  async get(
    db: SQL,
    licIdentifier: string,
    referenceDate: Date
  ): Promise<CodecManager> {
    const key = licIdentifier;
    if (!this.managers.has(key)) {
      CodecManagerRegistry.log.info(`Creating new CodecManager for ${key}`);
      const promise = CodecManager.loadFromDB(
        db,
        licIdentifier,
        this.transportFactory,
        this.packetProcessor,
        referenceDate
      )
        .then((r) => {
          if (!r) {
            this.managers.delete(key);
            throw new Error("Failed to load CodecManager: " + key);
          }
          CodecManagerRegistry.log.info(`Loaded CodecManager for ${key}`);
          this.emitter.emit("codecManagerLoadSuccess", r);
          return r;
        })
        .catch((err) => {
          this.managers.delete(key);
          this.emitter.emit("codecManagerLoadError", err, key, referenceDate);
          throw err;
        });
      this.managers.set(key, promise);
    }
    return await this.managers.get(key)!;
  }

  /**
   * Handle device_message_request event from DeviceMessageQueueService.
   * Routes the message to the appropriate CodecManager for sending.
   *
   * @param messageRequest - Device message request with device info
   */
  async handleDeviceMessageRequest(
    messageRequest: DeviceMessageRequestWithDevice
  ): Promise<void> {
    const deviceIdentifier = messageRequest.device.identifier;

    try {
      CodecManagerRegistry.log.debug(
        `Handling device message request ${messageRequest.id} for device ${deviceIdentifier}`
      );

      // Get or create CodecManager for the device
      const codecManager = await this.get(
        this.db,
        deviceIdentifier,
        date(messageRequest.scheduled_at)
      );

      // Send the message using the CodecManager
      const result = await codecManager.sendDeviceMessageRequest(
        messageRequest
      );

      if (result.success) {
        CodecManagerRegistry.log.debug(
          `Successfully processed message request ${messageRequest.id} for device ${deviceIdentifier}`
        );
      } else {
        CodecManagerRegistry.log.warn(
          `Failed to process message request ${messageRequest.id} for device ${deviceIdentifier}: ${result.error}`
        );
      }
    } catch (error) {
      CodecManagerRegistry.log.error(
        `Error handling device message request ${messageRequest.id} for device ${deviceIdentifier}:`,
        error
      );
    }
  }

  /**
   * Handle device_message_retry event from DeviceMessageQueueService.
   * This is similar to handleDeviceMessageRequest but for retry scenarios.
   *
   * @param messageRequest - Device message request with device info
   */
  async handleDeviceMessageRetry(
    messageRequest: DeviceMessageRequestWithDevice
  ): Promise<void> {
    CodecManagerRegistry.log.info(
      `Retrying message request ${messageRequest.id} for device ${
        messageRequest.device.identifier
      } (attempt ${messageRequest.attempts + 1}/${messageRequest.max_attempts})`
    );

    // Use the same handler as regular requests
    await this.handleDeviceMessageRequest(messageRequest);
  }

  /**
   * Get the CodecManager for a specific device identifier.
   * This is a convenience method that uses the stored initialization parameters.
   *
   * @param deviceIdentifier - Device identifier (e.g., "9C821FA2B9E8")
   * @returns Promise that resolves to CodecManager or throws if not found
   */
  async getCodecManagerByDeviceIdentifier(
    deviceIdentifier: string,
    referenceDate: Date
  ): Promise<CodecManager> {
    return await this.get(this.db, deviceIdentifier, referenceDate);
  }

  /**
   * Get all currently loaded CodecManager instances.
   *
   * @returns Array of loaded CodecManager instances
   */
  async getAllLoadedCodecManagers(): Promise<CodecManager[]> {
    const managers: CodecManager[] = [];

    for (const [identifier, managerPromise] of this.managers.entries()) {
      try {
        const manager = await managerPromise;
        managers.push(manager);
      } catch (error) {
        CodecManagerRegistry.log.warn(
          `Failed to get CodecManager for ${identifier}:`,
          error
        );
      }
    }

    return managers;
  }
}
