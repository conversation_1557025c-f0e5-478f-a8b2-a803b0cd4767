/**
 * Irrigation Plan State Calculator
 *
 * This module calculates the state of irrigation plan executions based on SchedulingReportPackage
 * MQTT messages and LIC device configuration. It processes scheduling report data to determine
 * which irrigation plan steps have been activated and maps sector bitmasks to plan step IDs.
 */

import { codec } from "proto";
import type { LICState } from "./db-loader/types";

// Irrigation Plan State types
export type IrrigationPlanState = {
  irrigation_plan: string;
  packet_date: Date;
  start_time: Date;
  end_time: Date | null;
  activated_steps: string[];
  activated_ferti_steps: string[];
  waterpump_working: boolean;
  backwash_start_time: Date | null;
  uses_waterpump: boolean;
  uses_ferti: boolean;
};

/**
 * Calculate the state of irrigation plan execution based on SchedulingReportData
 *
 * This function processes a SchedulingReportData message to determine:
 * - Which irrigation plan is being executed (via scheduling_idx mapping)
 * - Which sectors/steps have been activated (via sector bitmasks)
 * - Which sectors received fertigation (via ferti bitmasks)
 * - Timing information and pump usage status
 *
 * @param schedulingReportData - The SchedulingReportData from MQTT message
 * @param licState - The current LIC state containing schedules and sector mappings
 * @param packetDate - The date when the device status packet was recorded
 * @returns IrrigationPlanState object or null if irrigation plan cannot be determined
 */
export function calculateIrrigationPlanState(
  schedulingReportData: codec.out.scheduling_report.ISchedulingReportData,
  licState: LICState,
  packetDate: Date
): IrrigationPlanState | null {
  try {
    // First its is imperative to understand how the ids are related between the data structures:
    // When the server configures the LIC with the schedules, the idx of the sent schedules are actually the ord_idx field of the LICState schedules.
    // Thus, when the LIC reports its schedulingReportData, the scheduling_idx field is actually the  the LICState schedule of ord_idx equivalent.
    // So, to find from what LICState schedule this report data is coming from, we need to map the scheduling_idx back to the ord_idx.
    // This is the only place where ord_idx is used directly.
    // To find out what LICState sector schedules belong to the found schedule, the mapping is made between schedule.idx -> sectorSchedule.scheduling_idx
    // So, in summary;
    // - we find the LICState schedule by matching ord_idx to the schedulingReportData.scheduling_idx
    // - we find the LICState sectorSchedules by matching the LICState schedule.idx to sectorSchedule.scheduling_idx

    const ordIdx = schedulingReportData.scheduling_idx;
    if (ordIdx === undefined || ordIdx === null) {
      console.warn("SchedulingReportData missing scheduling_idx");
      return null;
    }
    if (schedulingReportData.start_time == null) {
      console.warn("SchedulingReportData missing start_time");
      return null;
    }

    // Find the irrigation plan using scheduling_idx
    const schedule = licState.schedules.find((s) => s.ord_idx === ordIdx);

    if (!schedule) {
      console.warn(`No schedule found for scheduling_idx: ${ordIdx}`);
      return null;
    }

    if (!schedule.irrigationPlanId) {
      console.warn(`Schedule with ord_idx ${ordIdx} has no irrigation plan ID`);
      return null;
    }

    const schedulingIdx = schedule.idx;

    // Convert timestamps from protobuf (uint64) to Date objects
    const startTime = convertTimestampToDate(schedulingReportData.start_time);
    if (!startTime) {
      console.warn("SchedulingReportData missing start_time");
      return null;
    }
    const endTime = schedulingReportData.end_time
      ? convertTimestampToDate(schedulingReportData.end_time)
      : null;

    // If end_time is not provided but status is 1 (completed), set end_time to packet_date
    const finalEndTime =
      !endTime && schedulingReportData.status === 1 ? packetDate : endTime;

    const backwashStartTime = schedulingReportData.backwash_time
      ? convertTimestampToDate(schedulingReportData.backwash_time)
      : null;

    // Process sector bitmasks to get activated steps
    const activatedSteps = processSectorBitmask(
      schedulingReportData.sector_bitmask1,
      schedulingReportData.sector_bitmask2,
      schedulingIdx,
      licState.sectorSchedules,
      false // not ferti
    );

    // Process fertigation bitmasks to get activated ferti steps
    const activatedFertiSteps = processSectorBitmask(
      schedulingReportData.ferti_bitmask1,
      schedulingReportData.ferti_bitmask2,
      schedulingIdx,
      licState.sectorSchedules,
      true // ferti
    );

    const irrigationPlanState: IrrigationPlanState = {
      irrigation_plan: schedule.irrigationPlanId,
      packet_date: packetDate,
      start_time: startTime,
      end_time: finalEndTime,
      activated_steps: activatedSteps,
      activated_ferti_steps: activatedFertiSteps,
      waterpump_working: schedulingReportData.waterpump || false,
      backwash_start_time: backwashStartTime,
      uses_waterpump: schedulingReportData.had_waterpump || false,
      uses_ferti: schedulingReportData.had_ferti || false,
    };

    return irrigationPlanState;
  } catch (error) {
    console.error("Error calculating irrigation plan state:", error);
    throw new Error(
      `Failed to calculate irrigation plan state: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

/**
 * Process sector bitmasks to determine which irrigation plan steps were activated
 *
 * This function examines the sector bitmask bits to identify which sectors (in execution order)
 * were activated during the scheduling execution. It then maps these to the corresponding
 * irrigation plan step IDs using the sectorSchedules array.
 *
 * @param bitmask1 - First 64 bits of the sector bitmask
 * @param bitmask2 - Next 64 bits of the sector bitmask
 * @param schedulingIdx - The scheduling index to filter sector schedules
 * @param sectorSchedules - Array of sector scheduling configurations
 * @param isFerti - Whether this is for fertigation (true) or regular irrigation (false)
 * @returns Array of irrigation plan step IDs that were activated
 */
function processSectorBitmask(
  bitmask1: number | Long | null | undefined,
  bitmask2: number | Long | null | undefined,
  schedulingIdx: number,
  sectorSchedules: LICState["sectorSchedules"],
  isFerti: boolean
): string[] {
  const activatedSteps: string[] = [];

  try {
    // Convert bitmasks to BigInt for safe bit manipulation
    const mask1 = bitmask1 ? BigInt(bitmask1.toString()) : BigInt(0);
    const mask2 = bitmask2 ? BigInt(bitmask2.toString()) : BigInt(0);

    // Filter sector schedules for this scheduling
    const relevantSectorSchedules = sectorSchedules.filter(
      (sectorSchedule) => sectorSchedule.scheduling_idx === schedulingIdx
    );

    // Check each bit position to see if it's activated
    for (let bitIndex = 0; bitIndex < 128; bitIndex++) {
      // Use first 64 bits for indices 0-63, second 64 bits for indices 64-127
      const isActivated =
        bitIndex < 64
          ? checkBitmask(mask1, bitIndex)
          : checkBitmask(mask2, bitIndex - 64);

      if (isActivated) {
        // Find the sector schedule that matches this execution order (n_order)
        // The bit index corresponds to the execution order of the sector
        const sectorSchedule = relevantSectorSchedules.find(
          (ss) => ss.n_order === bitIndex
        );

        if (sectorSchedule?.irrigationPlanStepId) {
          // For fertigation, only include if the sector schedule has ferti enabled
          if (!isFerti || sectorSchedule.ferti > 0) {
            if (!activatedSteps.includes(sectorSchedule.irrigationPlanStepId)) {
              activatedSteps.push(sectorSchedule.irrigationPlanStepId);
            }
          }
        } else {
          console.debug(
            `No sector schedule found for scheduling_idx ${schedulingIdx} and n_order ${
              bitIndex + 1
            }`
          );
        }
      }
    }

    return activatedSteps;
  } catch (error) {
    console.error("Error processing sector bitmask:", error);
    return [];
  }
}

/**
 * Check if a bit is set in a bitmask at the specified index
 *
 * @param bitmask - The bitmask to check (BigInt)
 * @param bitIndex - The index of the bit to check (0-indexed)
 * @returns True if the bit is set, false otherwise
 */
function checkBitmask(bitmask: bigint, bitIndex: number): boolean {
  try {
    const bit = BigInt(1) << BigInt(bitIndex);
    return (bitmask & bit) !== BigInt(0);
  } catch (error) {
    console.error(
      `Error checking bitmask ${bitmask} at index ${bitIndex}:`,
      error
    );
    return false;
  }
}

/**
 * Convert protobuf timestamp (uint64) to JavaScript Date object
 *
 * The protobuf timestamp is expected to be in seconds since Unix epoch.
 * This function converts it to a JavaScript Date object.
 *
 * @param timestamp - Timestamp from protobuf (number or Long)
 * @returns Date object or null if conversion fails
 */
function convertTimestampToDate(
  timestamp: number | Long | string
): Date | null {
  try {
    // Convert to number (seconds since epoch)
    const timestampSeconds =
      typeof timestamp === "string"
        ? parseInt(timestamp, 10)
        : typeof timestamp === "number"
        ? timestamp
        : timestamp.toNumber();

    // Convert seconds to milliseconds and create Date
    return new Date(timestampSeconds * 1000);
  } catch (error) {
    console.error(`Error converting timestamp ${timestamp} to Date:`, error);
    return null;
  }
}
