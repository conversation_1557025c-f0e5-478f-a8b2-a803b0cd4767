import { ConfigHolder } from "./config";
import { createDB } from "./db/connection";
import { DirectusClient } from "./directus";
import { CodecManagerRegistry } from "./irriganet/codec-manager-registry";
import { DefaultPackageProcessor } from "./irriganet/package-processors/default-package-processor";
import { logger } from "./log";
import { createMQTTClient } from "./transport/mqtt/mqtt-client";
import { MQTTTransportFactory } from "./transport/mqtt/mqtt-transport-factory";
import { throttleWithFinal } from "./utils/fn";
import { isMutationQuery } from "./utils/sql";

ConfigHolder.init();
export const directusClient = new DirectusClient();
const clearCache = throttleWithFinal(() => directusClient.clearCache(), 1000);
export const db = createDB((connection, query, parameters, paramTypes) => {
  if (isMutationQuery(query)) {
    clearCache();
  }
});

export const mqttClient = createMQTTClient();

export const transportFactory = new MQTTTransportFactory(
  mqttClient,
  (licIdentifier, topic, message, referenceDate) => {
    logger.log(`Unhandled message for ${licIdentifier} on ${topic}:`, message);
    codecStateRegistry.get(db, licIdentifier, referenceDate).then((manager) => {
      // Handle the codec state manager
      manager.handleMessage(topic, message, referenceDate);
    });
  }
);

export const packetProcessor = new DefaultPackageProcessor();

export const codecStateRegistry = new CodecManagerRegistry(
  db,
  transportFactory,
  packetProcessor
);
