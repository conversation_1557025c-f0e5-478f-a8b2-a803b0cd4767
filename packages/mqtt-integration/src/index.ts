import { codecStateRegistry, db, mqttClient } from "./container";
import { DeviceMessageQueueService } from "./db/services/device-message-queue";
import { logger } from "./log";

// Initialize CodecManagerRegistry
codecStateRegistry.init(new Date());

// Create and configure DeviceMessageQueueService
const deviceMessageQueueService = new DeviceMessageQueueService(db, {
  pollingIntervalMs: 500, // 5 seconds
  batchSize: 50,
  retryBatchSize: 20,
  enableRetryProcessing: true,
  autoStart: false, // We'll start it manually after wiring events
});

// Wire event listeners between DeviceMessageQueueService and CodecManagerRegistry
deviceMessageQueueService.on(
  "device_message_request",
  async (messageRequest) => {
    await codecStateRegistry.handleDeviceMessageRequest(messageRequest);
  }
);

deviceMessageQueueService.on("device_message_retry", async (messageRequest) => {
  await codecStateRegistry.handleDeviceMessageRetry(messageRequest);
});

// Optional: Add logging for service events
deviceMessageQueueService.on("service_started", () => {
  logger.log("DeviceMessageQueueService started");
});

deviceMessageQueueService.on("service_stopped", () => {
  logger.log("DeviceMessageQueueService stopped");
});

deviceMessageQueueService.on(
  "polling_cycle_complete",
  (processedCount, retryCount) => {
    if (processedCount > 0 || retryCount > 0) {
      logger.log(
        `DeviceMessageQueueService cycle: processed=${processedCount}, retry=${retryCount}`
      );
    }
  }
);

deviceMessageQueueService.on("polling_error", (error) => {
  logger.error("DeviceMessageQueueService polling error:", error);
});

// Start the DeviceMessageQueueService
deviceMessageQueueService.start();

// Periodically fetch LICs
let licsRequestTimeout: NodeJS.Timeout;
const fetchLICIntervalMs = 5000; // 30 seconds
function fetchLICSPeriodically() {
  codecStateRegistry.fetchLICS(new Date()).finally(() => {
    licsRequestTimeout = setTimeout(fetchLICSPeriodically, fetchLICIntervalMs);
  });
}
logger.info("Started periodic fetching of LICs");
licsRequestTimeout = setTimeout(fetchLICSPeriodically, fetchLICIntervalMs);

// Handle graceful shutdown
const shutdown = () => {
  logger.log("Shutting down services...");

  licsRequestTimeout && clearInterval(licsRequestTimeout);

  // Stop DeviceMessageQueueService
  deviceMessageQueueService.destroy();

  // Disconnect from MQTT broker
  mqttClient.end(() => {
    logger.log("Disconnected from MQTT broker");
    process.exit(0);
  });
};

process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);
process.on("exit", shutdown);
