import { connect, type IClientOptions } from "mqtt";
import { LoggerManager } from "../../log";
import { config } from "../../config";
const log = LoggerManager.getLogger("MQTTClient");
export function createMQTTClient(
  brokerUrl = config.mqtt.brokerUrl,
  opts: Partial<IClientOptions> = {
    username: config.mqtt.username,
    password: config.mqtt.password,
    protocolVersion: 5,
    clean: true, // Clean session
    reconnectPeriod: 1000, // Reconnect every second if disconnected
    connectTimeout: 30000, // 30 seconds timeout for connection
    reschedulePings: true, // Reschedule pings automatically
    will: {
      topic: "client/disconnected",
      payload: JSON.stringify({
        message: "Client disconnected unexpectedly",
        timestamp: new Date().toISOString(),
      }),
      qos: 1,
      retain: false,
    },
    properties: {
      sessionExpiryInterval: 3600, // Session expiry interval in seconds
    },

    keepalive: 60, // Keepalive interval in seconds
  }
) {
  if (!brokerUrl) {
    throw new Error("MQTT configuration is missing in environment variables.");
  }
  log.info(`Connecting to MQTT broker at ${brokerUrl}...`, {
    ...opts,
    password: opts.password ? "****" : undefined,
  });
  const client = connect(brokerUrl, opts);

  client.once("connect", () => {
    // Handle graceful shutdown
    const shutdown = () => {
      log.info("Disconnecting from MQTT broker...");
      client.end(() => {
        log.info("Disconnected from MQTT broker");
        process.exit(0);
      });
    };

    process.on("SIGINT", shutdown);
    process.on("SIGTERM", shutdown);
    process.on("exit", shutdown);
  });

  client.on("connect", () => {
    log.info("Connected to MQTT broker");
  });

  client.on("error", (err) => {
    log.error("MQTT connection error:", err);
  });

  return client;
}
