const LOG_LEVELS = [
  "trace",
  "debug",
  "info",
  "warn",
  "error",
  "silent",
] as const;
export type LogLevel = (typeof LOG_LEVELS)[number];

export function isLogLevel(value: any): value is LogLevel {
  return LOG_LEVELS.includes(value);
}

const rootLogLevel: LogLevel = isLogLevel(process.env.LOG_LEVEL)
  ? (process.env.LOG_LEVEL as LogLevel)
  : "info";

const childrenLogLevel = process.env.CHILDREN_LOG_LEVEL?.split(",")
  .map(
    (part) =>
      part
        .trim()
        .split(":")
        .map((p) => p.trim()) as [string, LogLevel]
  )
  .reduce((acc, [name, level]) => {
    if (isLogLevel(level)) {
      acc[name] = level;
    }
    return acc;
  }, {} as Record<string, LogLevel>);

export class Logger {
  // Instance-only logger. Static/registry responsibilities were moved to LoggerManager.

  constructor(
    public readonly name: string,
    public readonly parent: Logger | undefined,
    protected level: LogLevel | undefined
  ) {}

  protected getEffectiveLogLevel(): LogLevel {
    if (this.level) {
      return this.level;
    }
    return this.parent ? this.parent.getEffectiveLogLevel() : rootLogLevel;
  }

  getLevel(): LogLevel {
    return this.getEffectiveLogLevel();
  }

  setLevel(level: LogLevel | undefined) {
    this.level = level;
  }

  isLevelEnabled(level: LogLevel): boolean {
    return (
      LOG_LEVELS.indexOf(level) >=
      LOG_LEVELS.indexOf(this.getEffectiveLogLevel())
    );
  }
  get traceEnabled(): boolean {
    return this.isLevelEnabled("trace");
  }

  get debugEnabled(): boolean {
    return this.isLevelEnabled("debug");
  }

  get infoEnabled(): boolean {
    return this.isLevelEnabled("info");
  }

  get warnEnabled(): boolean {
    return this.isLevelEnabled("warn");
  }

  get errorEnabled(): boolean {
    return this.isLevelEnabled("error");
  }

  get isSilent(): boolean {
    return this.getEffectiveLogLevel() === "silent";
  }

  protected prepareLogEntry(
    message: any,
    level: LogLevel | "assert" | "log"
  ): { formattedMessage: string } {
    if (typeof message !== "string") {
      message = JSON.stringify(message);
    }
    const timestamp = new Date();
    const timestampStr = this.formatTimestamp(timestamp);
    const prefix = this.name ? `[${this.name}] ` : "";
    const formattedMessage = `${prefix}${timestampStr} - ${level} - ${message}`;
    return { formattedMessage };
  }

  protected formatTimestamp(timestamp: Date) {
    return timestamp.toISOString().replace(/\..+Z/, "Z");
  }

  log(message?: any, ...optionalParams: any[]) {
    this._log("log", { message, optionalParams });
  }

  error(message?: any, ...optionalParams: any[]) {
    this._log("error", { message, optionalParams });
  }

  warn(message?: any, ...optionalParams: any[]) {
    this._log("warn", { message, optionalParams });
  }

  debug(message?: any, ...optionalParams: any[]) {
    this._log("debug", { message, optionalParams });
  }

  info(message?: any, ...optionalParams: any[]) {
    this._log("info", { message, optionalParams });
  }

  trace(message?: any, ...optionalParams: any[]) {
    this._log("trace", { message, optionalParams });
  }

  assert(condition: any, message?: any, ...optionalParams: any[]) {
    // if (!condition) {
    //   const { formattedMessage } = this.prepareLogEntry(message);
    //   console.assert(condition, formattedMessage, ...optionalParams);
    // }
    this._log("assert", { message, optionalParams, condition });
  }

  protected _log(
    level: LogLevel | "assert" | "log",
    {
      message,
      optionalParams,
      condition,
    }: { message?: any; optionalParams: any[]; condition?: any }
  ) {
    if (
      level !== "silent" &&
      (level === "assert" || level === "log" || this.isLevelEnabled(level))
    ) {
      const { formattedMessage } = this.prepareLogEntry(message, level);
      if (level === "assert") {
        console.assert(condition, formattedMessage, ...optionalParams);
      } else {
        console[level](formattedMessage, ...optionalParams);
      }
    }
  }

  getChildLogger(childName: string): Logger {
    const key = `${this.name ? `${this.name}.` : ""}${childName}`;
    return LoggerManager.getLogger(key);
  }

  toJSON() {
    return {
      name: this.name,
      level: this.level,
      effectiveLevel: this.getEffectiveLogLevel(),
      parentName: this.parent?.name,
    };
  }

  // Static/registry responsibilities are implemented in LoggerManager.
}

export interface ILoggerFactory {
  createLogger(
    name: string,
    parent: Logger | undefined,
    level: LogLevel | undefined
  ): Logger;
}

export class DefaultLoggerFactory implements ILoggerFactory {
  createLogger(
    name: string,
    parent: Logger | undefined,
    level: LogLevel | undefined
  ): Logger {
    return new Logger(name, parent, level);
  }
}

/**
 * LoggerManager holds all static registry and factory logic that used to belong to Logger.
 */
export class LoggerManager {
  private static factory: ILoggerFactory = new DefaultLoggerFactory();
  private static readonly loggers: Map<string, Logger> = new Map();
  private static readonly ROOT_LOGGER: Logger = new Logger(
    "",
    undefined,
    rootLogLevel
  );

  static {
    // register root logger
    LoggerManager.loggers.set(
      LoggerManager.ROOT_LOGGER.name,
      LoggerManager.ROOT_LOGGER
    );

    // apply children level overrides from env
    if (childrenLogLevel) {
      for (const [name, level] of Object.entries(childrenLogLevel)) {
        LoggerManager.setLoggerLevel(name, level);
      }
    }
  }

  static findParentLogger(childName: string): Logger | undefined {
    const idx = childName.lastIndexOf(".");
    if (idx === -1) return undefined;
    const parentName = childName.substring(0, idx);
    const parent = LoggerManager.loggers.get(parentName);
    return parent ?? LoggerManager.findParentLogger(parentName);
  }

  static getLogger(fullName: string): Logger {
    const key = fullName;
    if (!LoggerManager.loggers.has(key)) {
      const parent = LoggerManager.findParentLogger(key);
      LoggerManager.loggers.set(
        key,
        LoggerManager.factory.createLogger(fullName, parent, undefined)
      );
    }
    return LoggerManager.loggers.get(key)!;
  }

  static setLoggerLevel(fullName: string, level: LogLevel | undefined) {
    const logger = LoggerManager.getLogger(fullName);
    logger.setLevel(level);
  }

  static get root(): Logger {
    return LoggerManager.ROOT_LOGGER;
  }
}

export const logger = LoggerManager.root;
