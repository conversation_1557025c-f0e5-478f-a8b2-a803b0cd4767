#!/bin/bash

DB_NAME=$1
DROP_DB="false"
FAIL_IF_EXISTS="false"

for arg in "$@"; do
    if [ "$arg" = "--recreate" ]; then
        DROP_DB="true"
    fi
    if [ "$arg" = "--fail-if-exists" ]; then
        FAIL_IF_EXISTS="true"
    fi
done

if [ "$DROP_DB" = "true" ]; then
  docker compose exec -u postgres database dropdb --if-exists -f $DB_NAME; 
fi

if [ "$FAIL_IF_EXISTS" = "true" ]; then
    exists=$(docker compose exec -T -u postgres database psql -tA -c "SELECT 1 FROM pg_database WHERE datname='${DB_NAME}';")
    if [ "$exists" = "1" ]; then
        echo "Database '$DB_NAME' already exists" >&2
        exit 1
    fi
fi

docker compose exec -u postgres database createdb $DB_NAME; 
docker compose exec -u postgres database psql -d $DB_NAME -c "CREATE EXTENSION IF NOT EXISTS postgis;"; 
docker compose exec -u postgres database psql -d $DB_NAME -c "CREATE EXTENSION IF NOT EXISTS timescaledb;"; 
docker compose exec -e DB_DATABASE=$DB_NAME directus ./cli.js bootstrap