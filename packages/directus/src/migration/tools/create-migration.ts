import { readdir } from "fs/promises";

async function getTodayIdentifier() {
  const now = new Date();
  const yyyy = now.getFullYear();
  const mm = String(now.getMonth() + 1).padStart(2, "0");
  const dd = String(now.getDate()).padStart(2, "0");
  return `${yyyy}${mm}${dd}`;
}

async function getNextLetter(migrationsDir: string, todayId: string) {
  const files = await readdir(migrationsDir);
  const regex = new RegExp(`^${todayId}([A-Z])-`);
  const usedLetters = files
    .map((f) => {
      const m = f.match(regex);
      return m ? m[1] : null;
    })
    .filter(Boolean) as string[];
  if (usedLetters.length === 0) return "A";
  const last = usedLetters.sort().pop()!;
  return String.fromCharCode(last.charCodeAt(0) + 1);
}

function printHelp() {
  console.log("Usage: bun scripts/create-migration.ts <migration-name>");
  console.log("Example: bun scripts/create-migration.ts add-users-table");
}

async function main() {
  const args = Bun.argv.slice(2);
  if (args.length === 0 || args.includes("--help") || args.includes("-h")) {
    printHelp();
    process.exit(0);
  }
  const migrationName = args[0]?.replace(/\s+/g, "-").toLowerCase();
  if (!migrationName || !migrationName.match(/^[a-z0-9\-_]+$/)) {
    console.error(
      "Invalid migration name. Use only letters, numbers, dashes, and underscores."
    );
    process.exit(1);
  }
  const migrationsDir = "migrations";
  const todayId = await getTodayIdentifier();
  const letter = await getNextLetter(migrationsDir, todayId);
  const filename = `${todayId}${letter}-${migrationName}.js`;
  const filepath = `${migrationsDir}/${filename}`;
  const content = `/**\n * This migration file is a placeholder for the up migration.\n * @param {import('knex').Knex} knex\n */\nexport async function up(knex) {\n  await knex.transaction(async (tx) => {\n    // TODO: Add your migration logic here.\n  });\n}\n\n/**\n * This migration file is a placeholder for the down migration.\n * @param {import('knex').Knex} knex\n */\nexport async function down(knex) {\n  await knex.transaction(async (tx) => {\n    // TODO: Add your migration logic here.\n  });\n}\n`;
  await Bun.write(filepath, content);
  console.log(`Created migration: ${filepath}`);
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});
