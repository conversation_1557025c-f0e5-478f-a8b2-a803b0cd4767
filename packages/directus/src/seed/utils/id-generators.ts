/**
 * Utility functions for generating various types of identifiers
 */

/**
 * Generate a random hexadecimal string of given length
 */
export function generateHexId(length: number): string {
  let result = "";
  const hexChars = "0123456789ABCDEF";
  for (let i = 0; i < length; i++) {
    result += hexChars[Math.floor(Math.random() * 16)];
  }
  return result;
}

/**
 * Generate device identifier based on model
 * LIC devices get 12-character identifiers, others get 6-character identifiers
 */
export function generateDeviceIdentifier(model: "LIC" | "WPC-PL10" | "WPC-PL50" | "VC" | "RM"): string {
  if (model === "LIC") {
    return generateHexId(12);
  } else {
    return generateHexId(6);
  }
}
