/**
 * Business logic for creating reservoirs
 */

import type { Knex } from "knex";
import { createReservoir } from "../operations";

/**
 * Create reservoirs for a property
 */
export async function createReservoirsForProperty(
  trx: Knex,
  data: {
    property: string;
    propertyName: string;
    rmDeviceIds: string[];
    servicePumpIds: string[];
  }
): Promise<string[]> {
  const reservoirIds = [];
  const availableServicePumps = [...data.servicePumpIds]; // Copy array to track available pumps
  const availableRmDevices = [...data.rmDeviceIds]; // Copy array to track available RM devices

  // Create 1-3 reservoirs per property
  const reservoirCount = Math.floor(Math.random() * 3) + 1;

  for (let i = 0; i < reservoirCount; i++) {
    const reservoirName = `Reservatório ${i + 1} - ${data.propertyName}`;

    // Randomly assign RM device (some reservoirs may not have monitors)
    let rmDevice: string | undefined;
    if (Math.random() > 0.3 && availableRmDevices.length > 0) {
      const rmIndex = Math.floor(Math.random() * availableRmDevices.length);
      rmDevice = availableRmDevices[rmIndex];
      availableRmDevices.splice(rmIndex, 1); // Remove from available RM devices
    } else {
      rmDevice = undefined;
    }

    // Assign service pump ensuring each pump is only used once (unique constraint)
    let servicePump = undefined;
    if (Math.random() > 0.4 && availableServicePumps.length > 0) {
      const pumpIndex = Math.floor(
        Math.random() * availableServicePumps.length
      );
      servicePump = availableServicePumps[pumpIndex];
      availableServicePumps.splice(pumpIndex, 1); // Remove from available pumps
    }

    // Generate realistic capacity (1000L to 50000L)
    const capacity = Math.floor(Math.random() * 49000) + 1000;

    // Generate realistic safety time (30 to 120 minutes)
    const safetyTimeMinutes = Math.floor(Math.random() * 91) + 30;

    const reservoirId = await createReservoir(trx, {
      property: data.property,
      name: reservoirName,
      reservoir_monitor: rmDevice,
      water_pump: servicePump,
      description: `Reservatório de água para armazenamento na propriedade ${data.propertyName}`,
      capacity,
      safety_time_minutes: safetyTimeMinutes,
      enabled: Math.random() > 0.1, // 90% chance of being enabled
      notes:
        Math.random() > 0.5 ? `Observações para ${reservoirName}` : undefined,
    });

    reservoirIds.push(reservoirId);
  }

  return reservoirIds;
}
