/**
 * Business logic for water pump creation with controller-specific rules
 */

import type { WaterPumpData } from "../types";

export type WaterPumpController = "WPC-PL10" | "WPC-PL50";
export type PumpType = "IRRIGATION" | "FERTIGATION" | "SERVICE";

/**
 * Business rules for water pump controllers:
 * 
 * PL10 Controller:
 * - Does not allow PULSE mode (automatically sets to CONTINUOUS and disables option)
 * - Allows monitoring during irrigation
 * - Allows fertigation
 * - Allows backwash
 * 
 * PL50 Controller:
 * - Allows both PULSE and CONTINUOUS modes
 * - PULSE mode does not allow fertigation
 * - CONTINUOUS mode allows fertigation
 * - Never allows backwash
 * 
 * Additional System Rules:
 * - Fertigation pumps cannot operate in PULSE mode (system limitation)
 * - Only CONTINUOUS mode is supported for fertigation pumps regardless of controller
 */

export interface WaterPumpCreationOptions {
  controller: WaterPumpController;
  pumpType: PumpType;
  property: string;
  water_pump_controller: string;
  label: string;
  identifier: string;
  pump_model: string;
  allowRandomization?: boolean;
}

/**
 * Create water pump data following business rules for the specified controller
 */
export function createWaterPumpData(options: WaterPumpCreationOptions): WaterPumpData {
  const {
    controller,
    pumpType,
    property,
    water_pump_controller,
    label,
    identifier,
    pump_model,
    allowRandomization = true,
  } = options;

  // Base configuration
  const baseData: WaterPumpData = {
    property,
    water_pump_controller,
    label,
    identifier,
    pump_type: pumpType,
    pump_model,
    has_frequency_inverter: allowRandomization ? Math.random() > 0.5 : false,
    monitor_operation: false,
    mode: "CONTINUOUS",
  };

  // Apply business rules based on controller type
  if (controller === "WPC-PL10") {
    // PL10 rules:
    // - Only CONTINUOUS mode allowed
    // - Monitoring allowed for irrigation
    // - Fertigation allowed
    // - Backwash allowed (handled at project level)
    return {
      ...baseData,
      mode: "CONTINUOUS", // Force CONTINUOUS mode
      monitor_operation: pumpType === "IRRIGATION" 
        ? (allowRandomization ? Math.random() > 0.3 : true)  // Higher chance for irrigation pumps
        : (allowRandomization ? Math.random() > 0.4 : false), // Lower chance for other types
    };
  } else if (controller === "WPC-PL50") {
    // PL50 rules:
    // - Both PULSE and CONTINUOUS modes allowed for non-fertigation pumps
    // - Fertigation pumps MUST use CONTINUOUS mode (system limitation)
    // - PULSE mode doesn't allow fertigation
    // - CONTINUOUS mode allows fertigation
    // - No backwash (handled at project level)
    
    // Force CONTINUOUS mode for fertigation pumps (system limitation)
    const mode = pumpType === "FERTIGATION" 
      ? "CONTINUOUS" 
      : (allowRandomization && Math.random() > 0.5 ? "PULSE" : "CONTINUOUS");
    
    return {
      ...baseData,
      mode,
      monitor_operation: allowRandomization ? Math.random() > 0.4 : false,
    };
  }

  // Default fallback (should not reach here)
  return baseData;
}

/**
 * Check if controller supports backwash functionality
 */
export function controllerSupportsBackwash(controller: WaterPumpController): boolean {
  return controller === "WPC-PL10";
}

/**
 * Check if controller and mode combination supports fertigation
 * System rule: Fertigation is never supported in PULSE mode regardless of controller
 */
export function supportsFerti(controller: WaterPumpController, mode: "PULSE" | "CONTINUOUS"): boolean {
  // System rule: No fertigation in PULSE mode
  if (mode === "PULSE") {
    return false;
  }
  
  if (controller === "WPC-PL10") {
    return true; // PL10 always supports fertigation (only has CONTINUOUS mode)
  } else if (controller === "WPC-PL50") {
    return mode === "CONTINUOUS"; // PL50 only supports fertigation in CONTINUOUS mode
  }
  return false;
}

/**
 * Get available modes for a controller
 */
export function getAvailableModes(controller: WaterPumpController): ("PULSE" | "CONTINUOUS")[] {
  if (controller === "WPC-PL10") {
    return ["CONTINUOUS"]; // PL10 only supports CONTINUOUS
  } else if (controller === "WPC-PL50") {
    return ["PULSE", "CONTINUOUS"]; // PL50 supports both
  }
  return ["CONTINUOUS"];
}

/**
 * Get available modes for a controller and pump type combination
 * Takes into account system limitations (e.g., fertigation pumps cannot use PULSE mode)
 */
export function getAvailableModesForPumpType(
  controller: WaterPumpController, 
  pumpType: PumpType
): ("PULSE" | "CONTINUOUS")[] {
  const controllerModes = getAvailableModes(controller);
  
  // System rule: Fertigation pumps cannot operate in PULSE mode
  if (pumpType === "FERTIGATION") {
    return ["CONTINUOUS"];
  }
  
  return controllerModes;
}