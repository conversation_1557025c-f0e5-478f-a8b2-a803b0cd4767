/**
 * Business logic for creating irrigation plans and steps
 */

import type { Knex } from "knex";
import type { IrrigationPlanData } from "../types";
import { controllerSupportsBackwash, supportsFert<PERSON>, type WaterPumpController } from "./water-pump-creation";

/**
 * Create irrigation plans for a project
 */
export async function createIrrigationPlansForProject(
  trx: Knex,
  data: IrrigationPlanData & {
    irrigationController?: WaterPumpController;
    irrigationPumpMode?: "PULSE" | "CONTINUOUS";
    fertigationController?: WaterPumpController;
    fertigationPumpMode?: "PULSE" | "CONTINUOUS";
  }
): Promise<void> {
  // Determine if backwash is supported
  const backwashSupported = data.irrigationController 
    ? controllerSupportsBackwash(data.irrigationController)
    : false;

  // Determine if fertigation is supported
  const fertigationSupported = data.fertigationController && data.fertigationPumpMode
    ? supportsFerti(data.fertigationController, data.fertigationPumpMode)
    : false;

  for (const planData of data.plansData) {
    const [irrigationPlan] = await trx("irrigation_plan")
      .insert({
        project: data.project,
        name: planData.name,
        description: planData.description,
        start_time: planData.start_time,
        days_of_week: JSON.stringify(planData.days_of_week),
        is_enabled: true,
        fertigation_enabled: fertigationSupported && Math.random() > 0.5, // Only if controller supports it
        backwash_enabled: backwashSupported && Math.random() > 0.5, // Only if controller supports it
        start_date: "2024-01-01",
        end_date: null,
      })
      .returning("id");
    const irrigationPlanId = irrigationPlan.id || irrigationPlan;

    // Create irrigation plan steps
    for (const stepData of planData.steps) {
      await trx("irrigation_plan_step").insert({
        irrigation_plan: irrigationPlanId,
        sector: data.sectorIds[stepData.order - 1], // Use order to get correct sector
        description: stepData.description,
        order: stepData.order,
        duration_seconds: stepData.duration_seconds,
        fertigation_start_delay_seconds: fertigationSupported
          ? (stepData.fertigation_start_delay_seconds || null)
          : null, // Only set if fertigation is supported
        fertigation_duration_seconds: fertigationSupported
          ? (stepData.fertigation_duration_seconds || null)
          : null, // Only set if fertigation is supported
      });
    }
  }
}
