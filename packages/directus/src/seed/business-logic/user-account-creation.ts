/**
 * Business logic for creating users, accounts and their relationships
 */

import type { Knex } from "knex";
import type { UsersAndAccountsData, PropertiesData, TestUserData, AccountUserData } from "../types";
import { createUser, createAccount, createAccountUser } from "../operations";
import { createProperty } from "../operations";

/**
 * Create users and their accounts
 */
export async function createUsersAndAccounts(trx: Knex, data: UsersAndAccountsData) {
  const userIds = [];

  // Create users
  for (const userData of data.usersData) {
    const userId = await createUser(trx, userData);
    userIds.push(userId);
  }

  // Create accounts
  const accountIds = [];
  for (const userId of userIds) {
    const accountId = await createAccount(trx, { ownerId: userId });
    accountIds.push(accountId);
  }

  return {
    users: { joaquimUserId: userIds[0], mariaUserId: userIds[1] },
    accounts: {
      joaquimAccountId: accountIds[0],
      mariaAccountId: accountIds[1],
    },
  };
}

/**
 * Create properties for accounts
 */
export async function createProperties(trx: Knex, data: PropertiesData) {
  const propertyIds = [];

  for (const propertyData of data.propertiesData) {
    const propertyId = await createProperty(trx, propertyData);
    propertyIds.push(propertyId);
  }

  return propertyIds.map((id, index) => ({
    id,
    name: data.propertiesData[index]!.name,
  }));
}

/**
 * Create account-user relationships
 */
export async function createAccountUserRelationships(
  trx: Knex,
  data: {
    relationshipsData: AccountUserData[];
  }
) {
  for (const relationshipData of data.relationshipsData) {
    await createAccountUser(trx, relationshipData);
  }
}

/**
 * Create test user and associate with accounts
 */
export async function createTestUser(trx: Knex, data: TestUserData) {
  const testUserId = await createUser(trx, data.userData);

  await createAccountUser(trx, {
    account: data.joaquimAccountId,
    user: testUserId,
  });

  await createAccountUser(trx, {
    account: data.mariaAccountId,
    user: testUserId,
  });
}
