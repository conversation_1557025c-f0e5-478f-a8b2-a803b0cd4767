/**
 * Database operations for water pump-related entities
 */

import type { <PERSON><PERSON> } from "knex";
import type { WaterPumpData } from "../types";

/**
 * Create a water pump in the database
 */
export async function createWaterPump(
  trx: Knex,
  data: WaterPumpData
): Promise<string> {
  const [waterPump] = await trx("water_pump")
    .insert({
      property: data.property,
      water_pump_controller: data.water_pump_controller,
      label: data.label,
      identifier: data.identifier,
      pump_type: data.pump_type,
      pump_model: data.pump_model,
      has_frequency_inverter: data.has_frequency_inverter,
      monitor_operation: data.monitor_operation,
      mode: data.mode,
    })
    .returning("id");
  return waterPump.id || waterPump;
}
