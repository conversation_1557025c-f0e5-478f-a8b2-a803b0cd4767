/**
 * Database operations for property-related entities
 */

import type { Knex } from "knex";
import type { PropertyData } from "../types";

/**
 * Create a property in the database
 */
export async function createProperty(trx: Knex, data: PropertyData): Promise<string> {
  const [property] = await trx("property")
    .insert({
      account: data.account,
      name: data.name,
      timezone: data.timezone || "America/Sao_Paulo",
      address_postal_code: data.address_postal_code,
      address_street_name: data.address_street_name,
      address_street_number: data.address_street_number,
      address_city: data.address_city,
      address_state: data.address_state,
      address_country: data.address_country || "Brasil",
      backwash_duration_minutes: data.backwash_duration_minutes,
      backwash_period_minutes: data.backwash_period_minutes,
      backwash_delay_seconds: data.backwash_delay_seconds,
      rain_gauge_enabled: data.rain_gauge_enabled ?? false,
      rain_gauge_resolution_mm: data.rain_gauge_resolution_mm,
      precipitation_volume_limit_mm: data.precipitation_volume_limit_mm,
      precipitation_suspended_duration_hours:
        data.precipitation_suspended_duration_hours,
    })
    .returning("id");
  return property.id || property;
}
