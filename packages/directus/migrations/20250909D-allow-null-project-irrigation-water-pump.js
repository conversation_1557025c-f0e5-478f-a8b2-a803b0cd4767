/**
 * Migration to allow NULL values for irrigation_water_pump in project table
 * This is needed because when localized_irrigation_controller is null,
 * the irrigation_water_pump (which must be in the same mesh network) should also be null
 */
export async function up(knex) {
  // Allow NULL values for irrigation_water_pump column
  await knex.raw(`
    ALTER TABLE public.project 
    ALTER COLUMN irrigation_water_pump DROP NOT NULL;
  `);

  // Update the comment to reflect the change
  await knex.raw(`
    COMMENT ON COLUMN public.project.irrigation_water_pump IS 'Foreign key to water_pump table for irrigation - nullable when no LIC controller is assigned';
  `);
}

export async function down(knex) {
  // For rollback safety, we need to set NULL values to a placeholder or handle appropriately
  // This is a simplified approach - in practice, you'd want more sophisticated logic
  await knex.raw(`
    UPDATE public.project 
    SET irrigation_water_pump = (
      SELECT wp.id 
      FROM water_pump wp 
      INNER JOIN property_device pd ON wp.water_pump_controller = pd.device
      WHERE pd.property = project.property 
        AND wp.pump_type = 'IRRIGATION'
        AND pd.end_date IS NULL
      LIMIT 1
    )
    WHERE irrigation_water_pump IS NULL;
  `);

  // Then restore NOT NULL constraint
  await knex.raw(`
    ALTER TABLE public.project 
    ALTER COLUMN irrigation_water_pump SET NOT NULL;
  `);

  // Restore original comment
  await knex.raw(`
    COMMENT ON COLUMN public.project.irrigation_water_pump IS 'Foreign key to water_pump table for irrigation';
  `);
}