/**
 * Migration to add backwash_pump_type field to project table
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Add backwash_pump_type column to project table
    await tx.schema.alterTable("project", (table) => {
      table.enum("backwash_pump_type", ["IRRIGATION", "FERTIGATION"])
        .nullable()
        .defaultTo(null)
        .comment("Type of pump to use for backwashing operations: IRRIGATION, FERTIGATION, or NULL for no backwashing");
    });
  });
}

/**
 * Migration to remove backwash_pump_type field from project table
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Remove backwash_pump_type column from project table
    await tx.schema.alterTable("project", (table) => {
      table.dropColumn("backwash_pump_type");
    });
  });
}