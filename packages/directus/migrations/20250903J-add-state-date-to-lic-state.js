/**
 * Migration to add state_date column to lic_state table
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Add state_date column to lic_state table with temporary nullable constraint
    await tx.schema.alterTable("lic_state", (table) => {
      table
        .timestamp("state_date", { useTz: true })
        .nullable()
        .comment("Timestamp when the LIC state was recorded");
    });

    // Update existing records to set state_date = date_updated
    await tx.raw(`
      UPDATE lic_state 
      SET state_date = date_updated 
      WHERE state_date IS NULL
    `);

    // Now make the column not nullable with clock_timestamp() default
    await tx.schema.alterTable("lic_state", (table) => {
      table
        .timestamp("state_date", { useTz: true })
        .notNullable()
        .defaultTo(tx.raw("clock_timestamp()"))
        .alter();
    });
  });
}

/**
 * Migration to remove state_date column from lic_state table
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.alterTable("lic_state", (table) => {
      table.dropColumn("state_date");
    });
  });
}
