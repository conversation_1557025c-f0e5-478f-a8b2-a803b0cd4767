/**
 * Migration to create lic_state_history table for storing historical LIC state data
 * This table mirrors lic_state structure but with state_date instead of audit fields
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create lic_state_history table
    await tx.schema.createTable("lic_state_history", (table) => {
      // Primary Key - Device and state_date combination
      table
        .uuid("device")
        .notNullable()
        .comment("LIC device UUID - foreign key to device table")
        .references("id")
        .inTable("device")
        .onUpdate("RESTRICT")
        .onDelete("CASCADE");

      table
        .timestamp("state_date", { useTz: true })
        .notNullable()
        .comment(
          "Timestamp when the state was recorded (from date_created on insert or date_updated on update)"
        );

      // LICState JSONB columns - storing each property of LICState type
      table
        .jsonb("lic")
        .nullable()
        .comment("Codec configuration and metadata from LICState.lic property");

      table
        .jsonb("groups")
        .nullable()
        .comment("Groups array from LICState.groups property");

      table
        .jsonb("devices")
        .nullable()
        .comment("Devices array from LICState.devices property");

      table
        .jsonb("mesh_devices")
        .nullable()
        .comment("Mesh devices array from LICState.meshDevices property");

      table
        .jsonb("schedules")
        .nullable()
        .comment("Scheduling array from LICState.schedules property");

      table
        .jsonb("sector_schedules")
        .nullable()
        .comment(
          "Sector scheduling array from LICState.sectorSchedules property"
        );

      table
        .jsonb("device_schedules")
        .nullable()
        .comment(
          "Device scheduling array from LICState.deviceSchedules property"
        );

      // Configuration timestamp tracking columns
      table
        .bigInteger("last_devices_request")
        .nullable()
        .comment("Timestamp of last devices configuration request sent to LIC");

      table
        .bigInteger("last_scheduling_request")
        .nullable()
        .comment(
          "Timestamp of last scheduling configuration request sent to LIC"
        );

      table
        .bigInteger("last_dev_scheduling_request")
        .nullable()
        .comment(
          "Timestamp of last device scheduling configuration request sent to LIC"
        );

      table
        .bigInteger("last_automation_request")
        .nullable()
        .comment(
          "Timestamp of last automation configuration request sent to LIC"
        );

      table
        .bigInteger("last_config_request")
        .nullable()
        .comment("Timestamp of last config configuration request sent to LIC");

      // Current timestamps from last InfoPackage received from LIC
      table
        .bigInteger("current_devices_timestamp")
        .nullable()
        .comment(
          "Current devices timestamp from last InfoPackage received from LIC"
        );

      table
        .bigInteger("current_scheduling_timestamp")
        .nullable()
        .comment(
          "Current scheduling timestamp from last InfoPackage received from LIC"
        );

      table
        .bigInteger("current_dev_scheduling_timestamp")
        .nullable()
        .comment(
          "Current device scheduling timestamp from last InfoPackage received from LIC"
        );

      table
        .bigInteger("current_automation_timestamp")
        .nullable()
        .comment(
          "Current automation timestamp from last InfoPackage received from LIC"
        );

      table
        .bigInteger("current_config_timestamp")
        .nullable()
        .comment(
          "Current config timestamp from last InfoPackage received from LIC"
        );

      // Primary key constraint for (state_date, device)
      table.primary(["state_date", "device"]);
    });

    // Add table comment
    await tx.raw(`
      COMMENT ON TABLE public.lic_state_history IS 
        'Historical tracking of LIC state changes - TimescaleDB hypertable for time-series data';
    `);
  });
}

/**
 * Migration to drop lic_state_history table
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.dropTableIfExists("lic_state_history");
  });
}
