/**
 * Migration to create performance indexes for device_message_request table
 * 
 * This migration creates strategic indexes optimized for the device message queue
 * processing system. Each index serves specific query patterns and performance
 * requirements of the MQTT message lifecycle management.
 * 
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // ========================================================================
    // Index 1: Primary Queue Processing (MOST CRITICAL)
    // ========================================================================
    // Purpose: Optimize the main queue processor that selects pending/processing
    //          messages ordered by schedule time and priority
    // Query Pattern: WHERE status IN ('pending', 'processing') ORDER BY scheduled_at, priority
    // Performance Impact: Essential for real-time message processing throughput
    await tx.raw(`
      CREATE INDEX IF NOT EXISTS device_message_request_queue_processing_idx
        ON public.device_message_request USING btree
        (status, scheduled_at, priority)
        WHERE status IN ('pending', 'processing');
    `);

    // ========================================================================
    // Index 2: Device-Specific Message Lookup
    // ========================================================================
    // Purpose: Fast retrieval of messages for a specific device, filtered by status
    //          and ordered by creation time (newest first)
    // Query Pattern: WHERE device = ? AND status = ? ORDER BY date_created DESC
    // Use Case: Device status monitoring, message history, troubleshooting
    await tx.raw(`
      CREATE INDEX IF NOT EXISTS device_message_request_device_status_idx
        ON public.device_message_request USING btree
        (device, status, date_created DESC);
    `);

    // ========================================================================
    // Index 3: Property Device Association (Mesh Network Routing)
    // ========================================================================
    // Purpose: Optimize queries for messages routed through specific property devices
    //          in mesh network configurations
    // Query Pattern: WHERE property_device = ?
    // Use Case: Mesh network message routing, load balancing across LIC devices
    // Note: Sparse index - only indexes non-null property_device values
    await tx.raw(`
      CREATE INDEX IF NOT EXISTS device_message_request_property_device_idx
        ON public.device_message_request USING btree
        (property_device)
        WHERE property_device IS NOT NULL;
    `);

    // ========================================================================
    // Index 4: Scheduled Messages Processing
    // ========================================================================
    // Purpose: Optimize scheduled message processing by finding pending messages
    //          that are ready to be processed based on scheduled_at timestamp
    // Query Pattern: WHERE status = 'pending' AND scheduled_at <= NOW()
    // Use Case: Scheduled irrigation commands, delayed message delivery
    await tx.raw(`
      CREATE INDEX IF NOT EXISTS device_message_request_scheduled_idx
        ON public.device_message_request USING btree
        (scheduled_at)
        WHERE status = 'pending';
    `);

    // ========================================================================
    // Index 5: Message Deduplication (Unique Constraint)
    // ========================================================================
    // Purpose: Prevent duplicate messages by enforcing uniqueness on message hash
    //          when provided, while allowing NULL values (sparse unique index)
    // Query Pattern: SELECT WHERE message_hash = ?
    // Use Case: Preventing duplicate command execution, idempotent operations
    // Note: Unique constraint with sparse indexing for optional hash values
    await tx.raw(`
      CREATE UNIQUE INDEX IF NOT EXISTS device_message_request_dedup_idx
        ON public.device_message_request USING btree
        (message_hash)
        WHERE message_hash IS NOT NULL;
    `);

    // ========================================================================
    // Index 6: Message Correlation (Bulk Operations)
    // ========================================================================
    // Purpose: Group related messages across devices/operations using correlation_id
    // Query Pattern: WHERE correlation_id = ?
    // Use Case: Bulk operations (e.g., update all devices in property),
    //           cross-device coordination, operation tracking
    // Note: Sparse index - only when correlation_id is provided
    await tx.raw(`
      CREATE INDEX IF NOT EXISTS device_message_request_correlation_idx
        ON public.device_message_request USING btree
        (correlation_id)
        WHERE correlation_id IS NOT NULL;
    `);

    // ========================================================================
    // Index 7: Parent-Child Message Chains
    // ========================================================================
    // Purpose: Support sequential message operations and dependency chains
    // Query Pattern: WHERE parent_message_id = ?
    // Use Case: Sequential operations (e.g., config → devices → scheduling),
    //           dependent message execution, operation rollback
    // Note: Sparse index - only when parent relationship exists
    await tx.raw(`
      CREATE INDEX IF NOT EXISTS device_message_request_parent_idx
        ON public.device_message_request USING btree
        (parent_message_id)
        WHERE parent_message_id IS NOT NULL;
    `);

    // ========================================================================
    // Index 8: Failed Message Retry Processing
    // ========================================================================
    // Purpose: Optimize retry mechanism by finding failed messages that haven't
    //          exceeded max_attempts and are ready for retry
    // Query Pattern: WHERE status = 'failed' AND attempts < max_attempts 
    //                AND scheduled_at <= NOW()
    // Use Case: Automatic retry processing, exponential backoff scheduling
    await tx.raw(`
      CREATE INDEX IF NOT EXISTS device_message_request_retry_idx
        ON public.device_message_request USING btree
        (status, attempts, scheduled_at)
        WHERE status = 'failed' AND attempts < max_attempts;
    `);

    // ========================================================================
    // Index 9: Cleanup and Archival Operations
    // ========================================================================
    // Purpose: Optimize cleanup operations for completed/terminal state messages
    //          based on age and status
    // Query Pattern: WHERE status IN ('sent', 'acknowledged', 'failed', 'expired', 'cancelled')
    //                AND date_created < (NOW() - INTERVAL '30 days')
    // Use Case: Message archival, database maintenance, storage management
    await tx.raw(`
      CREATE INDEX IF NOT EXISTS device_message_request_cleanup_idx
        ON public.device_message_request USING btree
        (status, date_created)
        WHERE status IN ('sent', 'acknowledged', 'failed', 'expired', 'cancelled');
    `);

    // Add comprehensive index comments for documentation
    await tx.raw(`
      -- ============================================================================
      -- Index Documentation Comments
      -- ============================================================================
      
      COMMENT ON INDEX public.device_message_request_queue_processing_idx IS 
        'Critical index for queue processor: optimizes pending/processing message selection by schedule and priority';
      
      COMMENT ON INDEX public.device_message_request_device_status_idx IS 
        'Device-specific message lookup: enables fast filtering by device and status with chronological ordering';
      
      COMMENT ON INDEX public.device_message_request_property_device_idx IS 
        'Mesh network routing: optimizes queries for messages routed through specific property devices (sparse)';
      
      COMMENT ON INDEX public.device_message_request_scheduled_idx IS 
        'Scheduled message processing: finds pending messages ready for execution based on scheduled_at timestamp';
      
      COMMENT ON INDEX public.device_message_request_dedup_idx IS 
        'Message deduplication: enforces uniqueness on message hash to prevent duplicate operations (sparse unique)';
      
      COMMENT ON INDEX public.device_message_request_correlation_idx IS 
        'Bulk operation correlation: groups related messages across devices using correlation_id (sparse)';
      
      COMMENT ON INDEX public.device_message_request_parent_idx IS 
        'Message dependency chains: supports sequential operations and parent-child relationships (sparse)';
      
      COMMENT ON INDEX public.device_message_request_retry_idx IS 
        'Retry mechanism optimization: finds failed messages eligible for retry processing with scheduling';
      
      COMMENT ON INDEX public.device_message_request_cleanup_idx IS 
        'Archival and cleanup: optimizes maintenance operations for completed messages by status and age';
    `);
  });
}

/**
 * Migration to drop performance indexes for device_message_request table
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw('DROP INDEX IF EXISTS device_message_request_queue_processing_idx');
    await tx.raw('DROP INDEX IF EXISTS device_message_request_device_status_idx');
    await tx.raw('DROP INDEX IF EXISTS device_message_request_property_device_idx');
    await tx.raw('DROP INDEX IF EXISTS device_message_request_scheduled_idx');
    await tx.raw('DROP INDEX IF EXISTS device_message_request_dedup_idx');
    await tx.raw('DROP INDEX IF EXISTS device_message_request_correlation_idx');
    await tx.raw('DROP INDEX IF EXISTS device_message_request_parent_idx');
    await tx.raw('DROP INDEX IF EXISTS device_message_request_retry_idx');
    await tx.raw('DROP INDEX IF EXISTS device_message_request_cleanup_idx');
  });
}