/**
 * Add flow_rate_lh field to water_pump table
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.alterTable("water_pump", (table) => {
      table.decimal("flow_rate_lh", 10, 2).nullable().comment("Flow rate in liters per hour (L/h)");
    });
  });
}

/**
 * Remove flow_rate_lh field from water_pump table
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.alterTable("water_pump", (table) => {
      table.dropColumn("flow_rate_lh");
    });
  });
}
