/**
 * Adds cpf and phone_number fields to directus_users collection
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Add cpf field to directus_users
    await tx.schema.alterTable("directus_users", (table) => {
      table.string("cpf", 14).nullable().unique();
      table.string("phone_number", 20).nullable().unique();
    });
  });
}

/**
 * Reverts the changes made by the up migration
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Remove cpf and phone_number fields from directus_users
    await tx.schema.alterTable("directus_users", (table) => {
      table.dropColumn("cpf");
      table.dropColumn("phone_number");
    });
  });
}
