/**
 * Migration to implement trigger function for automatic population of lic_state_history
 * from lic_state table on INSERT and UPDATE operations
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create trigger function that handles automatic population of lic_state_history
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_lic_state_history_insert()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Insert into lic_state_history using date_created as state_date for new records
        -- If a conflict occurs (duplicate device + state_date), ignore the insert and raise a warning
        INSERT INTO lic_state_history (
          device,
          state_date,
          lic,
          groups,
          devices,
          mesh_devices,
          schedules,
          sector_schedules,
          device_schedules,
          last_devices_request,
          last_scheduling_request,
          last_dev_scheduling_request,
          last_automation_request,
          last_config_request,
          current_devices_timestamp,
          current_scheduling_timestamp,
          current_dev_scheduling_timestamp,
          current_automation_timestamp,
          current_config_timestamp
        ) VALUES (
          NEW.device,
          NEW.state_date,
          NEW.lic,
          NEW.groups,
          NEW.devices,
          NEW.mesh_devices,
          NEW.schedules,
          NEW.sector_schedules,
          NEW.device_schedules,
          NEW.last_devices_request,
          NEW.last_scheduling_request,
          NEW.last_dev_scheduling_request,
          NEW.last_automation_request,
          NEW.last_config_request,
          NEW.current_devices_timestamp,
          NEW.current_scheduling_timestamp,
          NEW.current_dev_scheduling_timestamp,
          NEW.current_automation_timestamp,
          NEW.current_config_timestamp
        ) ON CONFLICT (device, state_date) DO NOTHING;
        
        -- Check if the insert was ignored due to conflict and raise a warning
        IF NOT FOUND THEN
          RAISE WARNING 'Duplicate lic_state_history record ignored for device % at state_date %', NEW.device, NEW.date_created;
        END IF;
        
        -- Allow the insert to proceed normally
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create trigger function that handles automatic population on UPDATE
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_lic_state_history_update()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Insert into lic_state_history using date_updated as state_date for updated records
        -- If a conflict occurs (duplicate device + state_date), ignore the insert and raise a warning
        INSERT INTO lic_state_history (
          device,
          state_date,
          lic,
          groups,
          devices,
          mesh_devices,
          schedules,
          sector_schedules,
          device_schedules,
          last_devices_request,
          last_scheduling_request,
          last_dev_scheduling_request,
          last_automation_request,
          last_config_request,
          current_devices_timestamp,
          current_scheduling_timestamp,
          current_dev_scheduling_timestamp,
          current_automation_timestamp,
          current_config_timestamp
        ) VALUES (
          NEW.device,
          NEW.state_date,
          NEW.lic,
          NEW.groups,
          NEW.devices,
          NEW.mesh_devices,
          NEW.schedules,
          NEW.sector_schedules,
          NEW.device_schedules,
          NEW.last_devices_request,
          NEW.last_scheduling_request,
          NEW.last_dev_scheduling_request,
          NEW.last_automation_request,
          NEW.last_config_request,
          NEW.current_devices_timestamp,
          NEW.current_scheduling_timestamp,
          NEW.current_dev_scheduling_timestamp,
          NEW.current_automation_timestamp,
          NEW.current_config_timestamp
        ) ON CONFLICT (device, state_date) DO NOTHING;
        
        -- Check if the insert was ignored due to conflict and raise a warning
        IF NOT FOUND THEN
          RAISE WARNING 'Duplicate lic_state_history record ignored for device % at state_date %', NEW.device, NEW.date_updated;
        END IF;
        
        -- Allow the update to proceed normally
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create the trigger for INSERT operations on lic_state table
    await tx.raw(`
      CREATE TRIGGER lic_state_history_insert_trigger
      AFTER INSERT ON lic_state
      FOR EACH ROW
      EXECUTE FUNCTION handle_lic_state_history_insert();
    `);

    // Create the trigger for UPDATE operations on lic_state table
    await tx.raw(`
      CREATE TRIGGER lic_state_history_update_trigger
      AFTER UPDATE ON lic_state
      FOR EACH ROW
      EXECUTE FUNCTION handle_lic_state_history_update();
    `);
  });
}

/**
 * Migration to remove the trigger functions and triggers for lic_state_history
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Drop triggers
    await tx.raw(
      `DROP TRIGGER IF EXISTS lic_state_history_insert_trigger ON lic_state;`
    );
    await tx.raw(
      `DROP TRIGGER IF EXISTS lic_state_history_update_trigger ON lic_state;`
    );

    // Drop functions
    await tx.raw(`DROP FUNCTION IF EXISTS handle_lic_state_history_insert();`);
    await tx.raw(`DROP FUNCTION IF EXISTS handle_lic_state_history_update();`);
  });
}
