/**
 * Updates Directus field configuration to reflect nullable irrigation_water_pump change
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Update project.irrigation_water_pump field to not be required
    await tx("directus_fields")
      .where({
        collection: "project",
        field: "irrigation_water_pump",
      })
      .update({
        required: false,
        note: "Irrigation water pump - nullable when no LIC controller is assigned",
      });
  });
}

export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Restore project.irrigation_water_pump field as required
    await tx("directus_fields")
      .where({
        collection: "project",
        field: "irrigation_water_pump",
      })
      .update({
        required: true,
        note: "Irrigation water pump for this project",
      });
  });
}