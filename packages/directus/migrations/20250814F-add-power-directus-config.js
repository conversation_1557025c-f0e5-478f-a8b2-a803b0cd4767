/**
 * Migration to add power field configuration to Directus
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Add power field configuration to directus_fields
    await tx.batchInsert("directus_fields", [
      {
        collection: "sector",
        field: "power",
        special: null,
        interface: "input",
        options: JSON.stringify({
          placeholder: "Power (0-100)",
          min: 0,
          max: 100,
          step: 1
        }),
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 6, // After valve_controller_output (sort: 5)
        width: "half",
        translations: null,
        note: "Power setting for variable frequency inverter (0-100). Use 0 for pumps without frequency inverter.",
        conditions: null,
        required: true,
        group: null,
        validation: JSON.stringify({
          _and: [
            {
              power: {
                _gte: 0
              }
            },
            {
              power: {
                _lte: 100
              }
            }
          ]
        }),
        validation_message: "Power must be between 0 and 100",
      },
    ]);

    // Update permissions to include power in create and update actions
    await tx("directus_permissions")
      .where({
        collection: "sector",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "project,name,valve_controller,valve_controller_output,area,polygon,steps,description,metadata,notes,power",
      });

    await tx("directus_permissions")
      .where({
        collection: "sector",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "project,name,valve_controller,valve_controller_output,area,polygon,steps,description,metadata,notes,power",
      });
  });
}

/**
 * Migration to remove power field configuration from Directus
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Remove power field configuration
    await tx("directus_fields")
      .where({ collection: "sector", field: "power" })
      .del();

    // Revert permissions to exclude power
    await tx("directus_permissions")
      .where({
        collection: "sector",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "project,name,valve_controller,valve_controller_output,area,polygon,steps,description,metadata,notes",
      });

    await tx("directus_permissions")
      .where({
        collection: "sector",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "project,name,valve_controller,valve_controller_output,area,polygon,steps,description,metadata,notes",
      });
  });
}