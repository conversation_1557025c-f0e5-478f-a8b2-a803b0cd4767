/**
 * Migration to create before insert trigger for device_message_request table
 * This trigger automatically fills missing data before inserting new records
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create the trigger function
    await tx.raw(`
      CREATE OR REPLACE FUNCTION device_message_request_before_insert_trigger()
      RETURNS TRIGGER AS $$
      BEGIN
        IF NEW.device IS NULL AND NEW.property_device IS NULL THEN
          RAISE EXCEPTION 'Either device or property_device must be provided';
        END IF;

        -- Set scheduled_at to now() if it is null
        IF NEW.scheduled_at IS NULL THEN
          NEW.scheduled_at := NOW();
        END IF;

        -- Set packet_id to the scheduled_at time epoch seconds if it is null
        IF NEW.packet_id IS NULL OR NEW.packet_id <= 0 THEN
          NEW.packet_id := EXTRACT(EPOCH FROM NEW.scheduled_at)::bigint;
        END IF;

        -- If device is null and property_device is not null, set device to property_device.device_id
        IF NEW.device IS NULL AND NEW.property_device IS NOT NULL THEN
          SELECT device INTO NEW.device 
          FROM property_device 
          WHERE id = NEW.property_device;
        END IF;

        -- If property_device is null and device is not null, find the property_device for the device at scheduled_at time
        IF NEW.property_device IS NULL AND NEW.device IS NOT NULL THEN
          SELECT id INTO NEW.property_device
          FROM property_device 
          WHERE device = NEW.device
            AND NEW.scheduled_at >= COALESCE(start_date, '-infinity'::timestamp)
            AND NEW.scheduled_at <= COALESCE(end_date, 'infinity'::timestamp)
          LIMIT 1;
        END IF;

        -- Set payload_data to jsonb null if it is NULL
        IF NEW.payload_data IS NULL THEN
          NEW.payload_data := 'null'::jsonb;
        END IF;

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create the trigger
    await tx.raw(`
      CREATE TRIGGER device_message_request_before_insert_trigger
        BEFORE INSERT ON public.device_message_request
        FOR EACH ROW
        EXECUTE FUNCTION device_message_request_before_insert_trigger();
    `);

    // Add comment for documentation
    await tx.raw(`
      COMMENT ON FUNCTION device_message_request_before_insert_trigger() IS 
        'Trigger function to automatically fill missing data in device_message_request before insert: scheduled_at, packet_id, device/property_device relationships, and payload_data defaults';
    `);
  });
}

/**
 * Migration to drop the device_message_request before insert trigger and function
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(
      "DROP TRIGGER IF EXISTS device_message_request_before_insert_trigger ON public.device_message_request"
    );
    await tx.raw(
      "DROP FUNCTION IF EXISTS device_message_request_before_insert_trigger()"
    );
  });
}
