/**
 * Migration to update reservoir state trigger functions with start_time-aware logic
 * - Within same cycle (same start_time): do not replace current row; only set end_time once
 * - New cycle (different start_time): replace current row (delete + allow insert)
 * Also keeps inserting history rows on every packet
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Replace BEFORE INSERT handler for current_reservoir_state
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_reservoir_state_upsert()
      RETURNS TRIGGER AS $$
      DECLARE
        existing_record RECORD;
      BEGIN
        -- Always attempt to insert history row (reservoir_state)
        BEGIN
          INSERT INTO reservoir_state (
            reservoir,
            packet_date,
            start_time,
            restart_time,
            end_time,
            date_created
          ) VALUES (
            NEW.reservoir,
            NEW.packet_date,
            NEW.start_time,
            NEW.restart_time,
            NEW.end_time,
            NEW.date_created
          );
        EXCEPTION WHEN unique_violation THEN
          RAISE WARNING 'Duplicate key for reservoir % and packet_date % ignored in reservoir_state', NEW.reservoir, NEW.packet_date;
        END;

        -- Authoritative handling for current row
        IF TG_OP = 'INSERT' THEN
          SELECT * INTO existing_record
          FROM current_reservoir_state
          WHERE reservoir = NEW.reservoir;

          IF FOUND THEN
            -- Same cycle: same start_time
            IF (NEW.start_time IS NOT DISTINCT FROM existing_record.start_time) THEN
              -- Set end_time once if previously NULL and NEW provides it
              IF existing_record.end_time IS NULL AND NEW.end_time IS NOT NULL THEN
                UPDATE current_reservoir_state
                SET end_time = NEW.end_time,
                    date_updated = NOW()
                WHERE reservoir = NEW.reservoir;
              END IF;
              -- Do not replace current row; ignore insert
              RETURN NULL;
            ELSE
              -- New cycle: replace current row
              DELETE FROM current_reservoir_state WHERE reservoir = NEW.reservoir;
              -- Allow insert to proceed for new cycle
              RETURN NEW;
            END IF;
          END IF;
        END IF;

        -- No existing record: allow insert
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Replace UPDATE handler for current_reservoir_state (AFTER UPDATE keeps history; end_time guarded in app and insert trigger)
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_reservoir_state_update()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Insert history row for updates as well
        BEGIN
          INSERT INTO reservoir_state (
            reservoir,
            packet_date,
            start_time,
            restart_time,
            end_time,
            date_created
          ) VALUES (
            NEW.reservoir,
            NEW.packet_date,
            NEW.start_time,
            NEW.restart_time,
            NEW.end_time,
            NOW()
          );
        EXCEPTION WHEN unique_violation THEN
          RAISE WARNING 'Duplicate key for reservoir % and packet_date % ignored in reservoir_state during update', NEW.reservoir, NEW.packet_date;
        END;

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);
  });
}

/**
 * Down migration: restore previous behavior is not strictly needed; keep function names to allow rollback via prior migration if present
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // No-op: functions were replaced; prior migrations can be re-run to restore
    return;
  });
}
