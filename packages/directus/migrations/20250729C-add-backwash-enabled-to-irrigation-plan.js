/**
 * Migration to add backwash_enabled field to irrigation_plan table
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Add backwash_enabled column to irrigation_plan table
    await tx.schema.alterTable("irrigation_plan", (table) => {
      table.boolean("backwash_enabled").notNullable().defaultTo(false);
    });
  });
}

/**
 * Migration to remove backwash_enabled field from irrigation_plan table
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Remove backwash_enabled column from irrigation_plan table
    await tx.schema.alterTable("irrigation_plan", (table) => {
      table.dropColumn("backwash_enabled");
    });
  });
}
