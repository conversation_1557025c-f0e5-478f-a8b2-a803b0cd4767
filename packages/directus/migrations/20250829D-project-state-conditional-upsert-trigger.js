/**
 * Migration to implement trigger function for conditional UPSERT logic between current_project_state and project_state
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create trigger function that handles the conditional UPSERT logic
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_project_state_upsert()
      RETURNS TRIGGER AS $$
      DECLARE
        existing_record RECORD;
      BEGIN
        -- Always insert into project_state (historical record)
        INSERT INTO project_state (project, date_created, date_updated, packet_date, irrigation_status, fertigation_status, backwash_status, sectors)
        VALUES (NEW.project, NEW.date_created, NEW.date_updated, NEW.packet_date, NEW.irrigation_status, NEW.fertigation_status, NEW.backwash_status, NEW.sectors);

        -- Check for existing record in current_project_state with same project
        SELECT * INTO existing_record
        FROM current_project_state
        WHERE project = NEW.project;

        IF existing_record IS NOT NULL THEN
          -- Compare date_updated to determine if new state is newer
          IF NEW.date_updated > existing_record.date_updated THEN
            -- New state is newer: delete existing and allow insert of new
            DELETE FROM current_project_state
            WHERE project = NEW.project;
            -- The new record will be inserted normally by the INSERT operation
          ELSE
            -- New state is older or same: prevent insert into current_project_state
            -- Return NULL to cancel the INSERT operation for current_project_state
            RETURN NULL;
          END IF;
        END IF;
        
        -- If no existing record or new state is newer, allow the insert
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create the trigger on current_project_state table
    await tx.raw(`
      CREATE TRIGGER project_state_conditional_upsert_trigger
      BEFORE INSERT ON current_project_state
      FOR EACH ROW
      EXECUTE FUNCTION handle_project_state_upsert();
    `);

    // Also create a trigger for UPDATE operations on current_project_state
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_project_state_update()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Always insert update into project_state (historical record)
        INSERT INTO project_state (project, date_created, date_updated, packet_date, irrigation_status, fertigation_status, backwash_status, sectors)
        VALUES (NEW.project, NOW(), NEW.date_updated, NEW.packet_date, NEW.irrigation_status, NEW.fertigation_status, NEW.backwash_status, NEW.sectors);

        -- Allow the update to proceed normally
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER project_state_update_trigger
      BEFORE UPDATE ON current_project_state
      FOR EACH ROW
      EXECUTE FUNCTION handle_project_state_update();
    `);

    // Create a trigger to automatically update date_updated on current_project_state
    await tx.raw(`
      CREATE OR REPLACE FUNCTION update_project_state_date_updated()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.date_updated = NOW();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER update_project_state_date_updated_trigger
      BEFORE UPDATE ON current_project_state
      FOR EACH ROW
      EXECUTE FUNCTION update_project_state_date_updated();
    `);
  });
}

/**
 * Migration to remove the trigger function and triggers
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Drop triggers
    await tx.raw(
      `DROP TRIGGER IF EXISTS project_state_conditional_upsert_trigger ON current_project_state;`
    );
    await tx.raw(
      `DROP TRIGGER IF EXISTS project_state_update_trigger ON current_project_state;`
    );
    await tx.raw(
      `DROP TRIGGER IF EXISTS update_project_state_date_updated_trigger ON current_project_state;`
    );

    // Drop functions
    await tx.raw(`DROP FUNCTION IF EXISTS handle_project_state_upsert();`);
    await tx.raw(`DROP FUNCTION IF EXISTS handle_project_state_update();`);
    await tx.raw(
      `DROP FUNCTION IF EXISTS update_project_state_date_updated();`
    );
  });
}
