/**
 * Migration to re-add backwash_enabled field configuration to Directus
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Add backwash_enabled field configuration to directus_fields
    await tx.batchInsert("directus_fields", [
      {
        collection: "irrigation_plan",
        field: "backwash_enabled",
        special: "cast-boolean",
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 7, // After fertigation_enabled (sort: 6)
        width: "half",
        translations: null,
        note: "Indicates if backwash is enabled for this irrigation plan",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);
  });
}

/**
 * Migration to remove backwash_enabled field configuration from Directus
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Remove backwash_enabled field configuration
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "backwash_enabled" })
      .del();
  });
}