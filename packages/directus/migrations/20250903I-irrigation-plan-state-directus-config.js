/**
 * Applies Directus configuration for irrigation plan state collections
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // 1) Add current_irrigation_plan_state collection
    await tx.batchInsert("directus_collections", [
      {
        collection: "current_irrigation_plan_state",
        icon: "schedule",
        note: "Current state of irrigation plan executions based on SchedulingReportPackage MQTT messages. Contains real-time irrigation plan operational status.",
        display_template:
          "{{irrigation_plan.name}} - Started: {{start_time}} | Pump: {{waterpump_working}} | Sectors: {{activated_steps}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
    ]);

    // 2) Add irrigation_plan_state collection
    await tx.batchInsert("directus_collections", [
      {
        collection: "irrigation_plan_state",
        icon: "history",
        note: "Historical state of irrigation plan executions. TimescaleDB hypertable with automatic compression and retention policies.",
        display_template:
          "{{irrigation_plan.name}} - {{packet_date}} | Started: {{start_time}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
    ]);

    // 3) Add current_irrigation_plan_state fields
    await tx.batchInsert("directus_fields", [
      // id
      {
        collection: "current_irrigation_plan_state",
        field: "id",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: true,
        sort: 1,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // irrigation_plan (M2O -> irrigation_plan)
      {
        collection: "current_irrigation_plan_state",
        field: "irrigation_plan",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "related-values",
        display_options: JSON.stringify({
          template: "{{name}} ({{project.name}})",
        }),
        readonly: false,
        hidden: false,
        sort: 2,
        width: "half",
        translations: null,
        note: "Irrigation plan this state belongs to",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // packet_date
      {
        collection: "current_irrigation_plan_state",
        field: "packet_date",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: false,
        hidden: false,
        sort: 3,
        width: "half",
        translations: null,
        note: "When the original device status packet was recorded",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // start_time
      {
        collection: "current_irrigation_plan_state",
        field: "start_time",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: false,
        hidden: false,
        sort: 4,
        width: "half",
        translations: null,
        note: "When the irrigation plan execution started",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // end_time
      {
        collection: "current_irrigation_plan_state",
        field: "end_time",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: false,
        hidden: false,
        sort: 5,
        width: "half",
        translations: null,
        note: "When the irrigation plan execution ended (null if still running)",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // activated_steps
      {
        collection: "current_irrigation_plan_state",
        field: "activated_steps",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 6,
        width: "half",
        translations: null,
        note: "JSONB array with IDs of irrigation_plan_step records for activated sectors",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // activated_ferti_steps
      {
        collection: "current_irrigation_plan_state",
        field: "activated_ferti_steps",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 7,
        width: "half",
        translations: null,
        note: "JSONB array with IDs of irrigation_plan_step records for sectors with fertigation",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // waterpump_working
      {
        collection: "current_irrigation_plan_state",
        field: "waterpump_working",
        special: null,
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: JSON.stringify({
          iconOff: "close",
          iconOn: "check",
          labelOff: "Off",
          labelOn: "On",
        }),
        readonly: false,
        hidden: false,
        sort: 8,
        width: "third",
        translations: null,
        note: "Status of the water pump during the schedule",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // backwash_start_time
      {
        collection: "current_irrigation_plan_state",
        field: "backwash_start_time",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: false,
        hidden: false,
        sort: 9,
        width: "third",
        translations: null,
        note: "When the backwash started (nullable)",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // uses_waterpump
      {
        collection: "current_irrigation_plan_state",
        field: "uses_waterpump",
        special: null,
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: JSON.stringify({
          iconOff: "close",
          iconOn: "check",
          labelOff: "No",
          labelOn: "Yes",
        }),
        readonly: false,
        hidden: false,
        sort: 10,
        width: "third",
        translations: null,
        note: "Whether the water pump should be used in the schedule",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // uses_ferti
      {
        collection: "current_irrigation_plan_state",
        field: "uses_ferti",
        special: null,
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: JSON.stringify({
          iconOff: "close",
          iconOn: "check",
          labelOff: "No",
          labelOn: "Yes",
        }),
        readonly: false,
        hidden: false,
        sort: 11,
        width: "third",
        translations: null,
        note: "Whether fertigation should be used in the schedule",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // date_created
      {
        collection: "current_irrigation_plan_state",
        field: "date_created",
        special: "date-created",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 12,
        width: "half",
        translations: null,
        note: "When this record was created",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // date_updated
      {
        collection: "current_irrigation_plan_state",
        field: "date_updated",
        special: "date-updated",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 13,
        width: "half",
        translations: null,
        note: "When this record was last updated",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // 4) Add irrigation_plan_state fields (read-only historical data)
    await tx.batchInsert("directus_fields", [
      // id
      {
        collection: "irrigation_plan_state",
        field: "id",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 1,
        width: "full",
        translations: null,
        note: "Auto-incrementing primary key",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // irrigation_plan (M2O -> irrigation_plan)
      {
        collection: "irrigation_plan_state",
        field: "irrigation_plan",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "related-values",
        display_options: JSON.stringify({
          template: "{{name}} ({{project.name}})",
        }),
        readonly: true,
        hidden: false,
        sort: 2,
        width: "half",
        translations: null,
        note: "Irrigation plan this state belongs to",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // packet_date
      {
        collection: "irrigation_plan_state",
        field: "packet_date",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 3,
        width: "half",
        translations: null,
        note: "When the original device status packet was recorded - partitioning column",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // start_time
      {
        collection: "irrigation_plan_state",
        field: "start_time",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 4,
        width: "half",
        translations: null,
        note: "When the irrigation plan execution started",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // end_time
      {
        collection: "irrigation_plan_state",
        field: "end_time",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 5,
        width: "half",
        translations: null,
        note: "When the irrigation plan execution ended (null if still running)",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // activated_steps
      {
        collection: "irrigation_plan_state",
        field: "activated_steps",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 6,
        width: "half",
        translations: null,
        note: "JSONB array with IDs of irrigation_plan_step records for activated sectors",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // activated_ferti_steps
      {
        collection: "irrigation_plan_state",
        field: "activated_ferti_steps",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 7,
        width: "half",
        translations: null,
        note: "JSONB array with IDs of irrigation_plan_step records for sectors with fertigation",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // waterpump_working
      {
        collection: "irrigation_plan_state",
        field: "waterpump_working",
        special: null,
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: JSON.stringify({
          iconOff: "close",
          iconOn: "check",
          labelOff: "Off",
          labelOn: "On",
        }),
        readonly: true,
        hidden: false,
        sort: 8,
        width: "third",
        translations: null,
        note: "Status of the water pump during the schedule",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // backwash_start_time
      {
        collection: "irrigation_plan_state",
        field: "backwash_start_time",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 9,
        width: "third",
        translations: null,
        note: "When the backwash started (nullable)",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // uses_waterpump
      {
        collection: "irrigation_plan_state",
        field: "uses_waterpump",
        special: null,
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: JSON.stringify({
          iconOff: "close",
          iconOn: "check",
          labelOff: "No",
          labelOn: "Yes",
        }),
        readonly: true,
        hidden: false,
        sort: 10,
        width: "third",
        translations: null,
        note: "Whether the water pump should be used in the schedule",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // uses_ferti
      {
        collection: "irrigation_plan_state",
        field: "uses_ferti",
        special: null,
        interface: "boolean",
        options: null,
        display: "boolean",
        display_options: JSON.stringify({
          iconOff: "close",
          iconOn: "check",
          labelOff: "No",
          labelOn: "Yes",
        }),
        readonly: true,
        hidden: false,
        sort: 11,
        width: "third",
        translations: null,
        note: "Whether fertigation should be used in the schedule",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // date_created
      {
        collection: "irrigation_plan_state",
        field: "date_created",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 12,
        width: "half",
        translations: null,
        note: "When this state record was created",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // 5) Relations for irrigation plan state tables -> irrigation_plan
    await tx.batchInsert("directus_relations", [
      {
        many_collection: "current_irrigation_plan_state",
        many_field: "irrigation_plan",
        one_collection: "irrigation_plan",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
      {
        many_collection: "irrigation_plan_state",
        many_field: "irrigation_plan",
        one_collection: "irrigation_plan",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
    ]);

    // 6) Permissions
    // Reusing the same policy UUID used in other migrations
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // READ permission for current_irrigation_plan_state - scoped by irrigation plan property ownership
    await tx.batchInsert("directus_permissions", [
      {
        collection: "current_irrigation_plan_state",
        action: "read",
        permissions: {
          _and: [
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { role: { _eq: "admin" } } },
                  },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: policyId,
      },
    ]);

    // CREATE permission for current_irrigation_plan_state
    await tx.batchInsert("directus_permissions", [
      {
        collection: "current_irrigation_plan_state",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "irrigation_plan,packet_date,start_time,end_time,activated_steps,activated_ferti_steps,waterpump_working,backwash_start_time,uses_waterpump,uses_ferti",
        policy: policyId,
      },
    ]);

    // UPDATE permission for current_irrigation_plan_state
    await tx.batchInsert("directus_permissions", [
      {
        collection: "current_irrigation_plan_state",
        action: "update",
        permissions: {
          _and: [
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { role: { _eq: "admin" } } },
                  },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "irrigation_plan,packet_date,start_time,end_time,activated_steps,activated_ferti_steps,waterpump_working,backwash_start_time,uses_waterpump,uses_ferti",
        policy: policyId,
      },
    ]);

    // DELETE permission for current_irrigation_plan_state
    await tx.batchInsert("directus_permissions", [
      {
        collection: "current_irrigation_plan_state",
        action: "delete",
        permissions: {
          _and: [
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { role: { _eq: "admin" } } },
                  },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: policyId,
      },
    ]);

    // READ permission for irrigation_plan_state - scoped by irrigation plan property ownership (read-only)
    await tx.batchInsert("directus_permissions", [
      {
        collection: "irrigation_plan_state",
        action: "read",
        permissions: {
          _and: [
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { role: { _eq: "admin" } } },
                  },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: policyId,
      },
    ]);
  });
}

/**
 * Reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // Remove permissions
    await tx("directus_permissions")
      .where({
        collection: "current_irrigation_plan_state",
        policy: policyId,
      })
      .del();

    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan_state",
        policy: policyId,
      })
      .del();

    // Remove relations
    await tx("directus_relations")
      .where({
        many_collection: "current_irrigation_plan_state",
        many_field: "irrigation_plan",
      })
      .del();

    await tx("directus_relations")
      .where({
        many_collection: "irrigation_plan_state",
        many_field: "irrigation_plan",
      })
      .del();

    // Remove fields
    await tx("directus_fields")
      .where({ collection: "current_irrigation_plan_state" })
      .del();

    await tx("directus_fields")
      .where({ collection: "irrigation_plan_state" })
      .del();

    // Remove collections
    await tx("directus_collections")
      .where({ collection: "current_irrigation_plan_state" })
      .del();

    await tx("directus_collections")
      .where({ collection: "irrigation_plan_state" })
      .del();
  });
}