/**
 * Migration to configure lic_state_history as TimescaleDB hypertable with monthly chunking and compression
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Ensure TimescaleDB extension is enabled
    await tx.raw(`CREATE EXTENSION IF NOT EXISTS timescaledb;`);

    // Convert table to TimescaleDB hypertable with monthly chunking
    await tx.raw(`
      SELECT create_hypertable(
        'lic_state_history',
        'state_date',
        chunk_time_interval => INTERVAL '1 month',
        if_not_exists => TRUE
      );
    `);

    // Add compression policy for data older than 3 months
    await tx.raw(`
      ALTER TABLE lic_state_history SET (
        timescaledb.compress,
        timescaledb.compress_segmentby = 'device'
      );
    `);

    await tx.raw(`
      SELECT add_compression_policy(
        'lic_state_history',
        INTERVAL '3 months'
      );
    `);

    // Add retention policy to automatically drop data older than 2 years
    // await tx.raw(`
    //   SELECT add_retention_policy(
    //     'lic_state_history',
    //     INTERVAL '2 years'
    //   );
    // `);

    // Update table comment to reflect hypertable status
    await tx.raw(`
      COMMENT ON TABLE lic_state_history IS 'Historical tracking of LIC state changes - TimescaleDB hypertable chunked by month for efficient time-series queries';
    `);

    // Add column comments for better documentation
    await tx.raw(`
      COMMENT ON COLUMN lic_state_history.device IS 'Foreign key to device table - partitioning dimension';
      COMMENT ON COLUMN lic_state_history.state_date IS 'Timestamp when the state was recorded - partitioning column for TimescaleDB';
      COMMENT ON COLUMN lic_state_history.lic IS 'Codec configuration and metadata from LICState.lic property';
      COMMENT ON COLUMN lic_state_history.groups IS 'Groups array from LICState.groups property';
      COMMENT ON COLUMN lic_state_history.devices IS 'Devices array from LICState.devices property';
      COMMENT ON COLUMN lic_state_history.mesh_devices IS 'Mesh devices array from LICState.meshDevices property';
      COMMENT ON COLUMN lic_state_history.schedules IS 'Scheduling array from LICState.schedules property';
      COMMENT ON COLUMN lic_state_history.sector_schedules IS 'Sector scheduling array from LICState.sectorSchedules property';
      COMMENT ON COLUMN lic_state_history.device_schedules IS 'Device scheduling array from LICState.deviceSchedules property';
      COMMENT ON COLUMN lic_state_history.last_devices_request IS 'Timestamp of last devices configuration request sent to LIC';
      COMMENT ON COLUMN lic_state_history.last_scheduling_request IS 'Timestamp of last scheduling configuration request sent to LIC';
      COMMENT ON COLUMN lic_state_history.last_dev_scheduling_request IS 'Timestamp of last device scheduling configuration request sent to LIC';
      COMMENT ON COLUMN lic_state_history.last_automation_request IS 'Timestamp of last automation configuration request sent to LIC';
      COMMENT ON COLUMN lic_state_history.last_config_request IS 'Timestamp of last config configuration request sent to LIC';
      COMMENT ON COLUMN lic_state_history.current_devices_timestamp IS 'Current devices timestamp from last InfoPackage received from LIC';
      COMMENT ON COLUMN lic_state_history.current_scheduling_timestamp IS 'Current scheduling timestamp from last InfoPackage received from LIC';
      COMMENT ON COLUMN lic_state_history.current_dev_scheduling_timestamp IS 'Current device scheduling timestamp from last InfoPackage received from LIC';
      COMMENT ON COLUMN lic_state_history.current_automation_timestamp IS 'Current automation timestamp from last InfoPackage received from LIC';
      COMMENT ON COLUMN lic_state_history.current_config_timestamp IS 'Current config timestamp from last InfoPackage received from LIC';
    `);
  });
}

/**
 * Migration to remove TimescaleDB hypertable configuration from lic_state_history
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Remove compression and retention policies
    await tx.raw(`
      SELECT remove_compression_policy('lic_state_history', if_exists => true);
    `);

    await tx.raw(`
      SELECT remove_retention_policy('lic_state_history', if_exists => true);
    `);

    // Drop hypertable (this automatically removes chunks and converts back to regular table)
    await tx.schema.dropTableIfExists("lic_state_history");
  });
}
