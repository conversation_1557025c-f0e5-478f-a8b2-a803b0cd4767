/**
 * Applies the current state of the Directus config to the database.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-06-25T20:33:31.447Z
 * @description This migration applies the current state of the Directus config to the database.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  // if (true) return; // Skip migration if already applied
  await knex.transaction(async (tx) => {
    // Insert 1 records into directus_roles
    await tx.batchInsert("directus_roles", [
      {
        id: "ac5ba5cb-1d74-4df4-99d3-6758fb49255c",
        name: "User",
        icon: "supervised_user_circle",
        description: null,
        parent: null,
      },
    ]);

    // Insert 2 records into directus_policies
    await tx.batchInsert("directus_policies", [
      // {"id":"abf8a154-5b1c-4a46-ac9c-7300570f4f17","name":"$t:public_label","icon":"public","description":"$t:public_description","ip_access":null,"enforce_tfa":false,"admin_access":false,"app_access":false},
      {
        id: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        name: "Account Admin",
        icon: "badge",
        description: null,
        ip_access: null,
        enforce_tfa: false,
        admin_access: false,
        app_access: true,
      },
    ]);

    // Insert 10 records into directus_collections
    await tx.batchInsert("directus_collections", [
      {
        collection: "account",
        icon: null,
        note: null,
        display_template:
          "{{owner.first_name}} {{owner.last_name}} <{{owner.email}}>",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
      {
        collection: "device",
        icon: null,
        note: null,
        display_template: "{{identifier}} - {{model}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
      {
        collection: "account_user",
        icon: null,
        note: null,
        display_template: "{{account.owner}} - {{user}} - {{role}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
      {
        collection: "project",
        icon: null,
        note: null,
        display_template: "{{property.name}} - {{name}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
      {
        collection: "water_pump",
        icon: null,
        note: null,
        display_template: "{{label}} <{{identifier}}>",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
      {
        collection: "property",
        icon: null,
        note: null,
        display_template: "{{name}} <{{account.owner}}>",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
      {
        collection: "property_device",
        icon: null,
        note: null,
        display_template: "{{property.name}} - {{device.identifier}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
      {
        collection: "sector",
        icon: null,
        note: null,
        display_template:
          "{{project.name}} - {{name}} / {{valve_controller_output}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
      {
        collection: "irrigation_plan_step",
        icon: null,
        note: null,
        display_template:
          "{{sector.project.name}} / {{sector.name}} - {{irrigation_plan.name}} / {{order}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
      {
        collection: "irrigation_plan",
        icon: null,
        note: null,
        display_template: "{{project.name}} - {{name}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
    ]);

    // Insert 1 records into directus_flows
    await tx.batchInsert("directus_flows", [
      {
        id: "abea1ed0-8d1a-42a9-920d-8ceaac1e97f8",
        name: "Account Creation",
        icon: "bolt",
        color: null,
        description: null,
        status: "active",
        trigger: "event",
        accountability: "all",
        options: {
          type: "filter",
          scope: ["items.create"],
          collections: ["account"],
          return: "$last",
        },
        operation: "9cf9c2aa-0e7a-48d5-8fa1-4b01addf60f8",
        date_created: "2025-06-03T17:17:10.479+00:00",
        user_created: null,
      },
    ]);

    // Insert 152 records into directus_fields
    await tx.batchInsert(
      "directus_fields",
      [
        {
          id: 71,
          collection: "irrigation_plan_step",
          field: "id",
          special: "uuid",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 77,
          collection: "irrigation_plan_step",
          field: "date_created",
          special: "date-created",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 78,
          collection: "irrigation_plan_step",
          field: "user_created",
          special: "user-created",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 79,
          collection: "irrigation_plan_step",
          field: "date_updated",
          special: "date-updated",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 80,
          collection: "irrigation_plan_step",
          field: "user_updated",
          special: "user-updated",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 72,
          collection: "irrigation_plan_step",
          field: "irrigation_plan",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 7,
          collection: "directus_users",
          field: "account",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 1,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 73,
          collection: "irrigation_plan_step",
          field: "sector",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 74,
          collection: "irrigation_plan_step",
          field: "order",
          special: null,
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 75,
          collection: "irrigation_plan_step",
          field: "duration_seconds",
          special: null,
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 76,
          collection: "irrigation_plan_step",
          field: "delay_seconds_after",
          special: null,
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 117,
          collection: "irrigation_plan_step",
          field: "description",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 118,
          collection: "irrigation_plan_step",
          field: "metadata",
          special: "cast-json",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 119,
          collection: "irrigation_plan_step",
          field: "notes",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 134,
          collection: "water_pump",
          field: "pump_type",
          interface: "select-dropdown",
          options: JSON.stringify({
            choices: [
              { text: "Irrigation Water Pump", value: "IRRIGATION" },
              { text: "Fertigation Water Pump", value: "FERTIGATION" },
              { text: "Service Water Pump", value: "SERVICE" },
            ],
          }),
          display: "raw",
          required: true,
          validation: JSON.stringify({
            _and: [
              {
                pump_type: {
                  _in: ["IRRIGATION", "FERTIGATION", "SERVICE"],
                },
              },
            ],
          }),
          validation_message:
            "Pump type must be one of: IRRIGATION, FERTIGATION, or SERVICE",
          special: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 4,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          group: null,
        },
        {
          id: 129,
          collection: "water_pump",
          field: "user_updated",
          special: "user-updated",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 13,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: -1,
          collection: "water_pump",
          field: "has_frequency_inverter",
          special: null,
          interface: "boolean",
          options: JSON.stringify({
            label: "Has Frequency Inverter",
          }),
          display: "boolean",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 6, // After pump_model which is sort 5
          width: "half",
          translations: null,
          note: "Indicates if the water pump has a frequency inverter",
          conditions: null,
          required: true,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: -1,
          collection: "water_pump",
          field: "monitor_operation",
          special: null,
          interface: "boolean",
          options: JSON.stringify({
            label: "Monitor Operation",
          }),
          display: "boolean",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 8,
          width: "half",
          translations: null,
          note: "Indicates whether the water pump controller should monitor this pump's operation status",
          conditions: null,
          required: true,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 160,
          collection: "sector",
          field: "name",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 7,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 135,
          collection: "water_pump",
          field: "pump_model",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 5,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 138,
          collection: "property",
          field: "metadata",
          special: "cast-json",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 139,
          collection: "property",
          field: "water_pumps",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 21,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 40,
          collection: "project",
          field: "irrigation_pump_controller",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 4,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 45,
          collection: "project",
          field: "fertigation_pump_controller",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 5,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 64,
          collection: "irrigation_plan",
          field: "is_enabled",
          special: "cast-boolean",
          interface: "boolean",
          options: null,
          display: "boolean",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 3,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 145,
          collection: "device",
          field: "water_pumps",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 9,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 144,
          collection: "device",
          field: "properties",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 8,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 142,
          collection: "device",
          field: "notes",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 13,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 8,
          collection: "device",
          field: "identifier",
          special: null,
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 141,
          collection: "device",
          field: "metadata",
          special: "cast-json",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 12,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 146,
          collection: "property_device",
          field: "metadata",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 10,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 147,
          collection: "property_device",
          field: "notes",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 11,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 148,
          collection: "irrigation_plan",
          field: "project",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 8,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 68,
          collection: "irrigation_plan",
          field: "user_created",
          special: "user-created",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 11,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 66,
          collection: "irrigation_plan",
          field: "end_date",
          special: null,
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 7,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 69,
          collection: "irrigation_plan",
          field: "date_updated",
          special: "date-updated",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 12,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 67,
          collection: "irrigation_plan",
          field: "date_created",
          special: "date-created",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 10,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 70,
          collection: "irrigation_plan",
          field: "user_updated",
          special: "user-updated",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 13,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 149,
          collection: "irrigation_plan",
          field: "description",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 15,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 60,
          collection: "irrigation_plan",
          field: "total_irrigation_duration",
          special: null,
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 9,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 150,
          collection: "irrigation_plan",
          field: "metadata",
          special: "cast-json",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 16,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 151,
          collection: "irrigation_plan",
          field: "notes",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 17,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 59,
          collection: "irrigation_plan",
          field: "id",
          special: "uuid",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 1,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 61,
          collection: "irrigation_plan",
          field: "name",
          special: null,
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 62,
          collection: "irrigation_plan",
          field: "start_time",
          special: null,
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: { use24: true, format: "short" },
          readonly: false,
          hidden: false,
          sort: 4,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 63,
          collection: "irrigation_plan",
          field: "days_of_week",
          special: '{"cast-json"}',
          interface: "select-multiple-dropdown",
          options: {
            choices: [
              { text: "Monday", value: "MON" },
              { text: "Tuesday", value: "TUE" },
              { text: "Wednesday", value: "WED" },
              { text: "Thursday", value: "THU" },
              { text: "Friday", value: "FRI" },
              { text: "Saturday", value: "SAT" },
              { text: "Sunday", value: "SUN" },
            ],
          },
          display: "labels",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 4,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 65,
          collection: "irrigation_plan",
          field: "start_date",
          special: null,
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 6,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 47,
          collection: "project",
          field: "description",
          special: null,
          interface: "input-multiline",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 15,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 125,
          collection: "water_pump",
          field: "id",
          special: "uuid",
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 1,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 48,
          collection: "project",
          field: "start_date",
          special: null,
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 5,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 49,
          collection: "project",
          field: "end_date",
          special: null,
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 6,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 41,
          collection: "project",
          field: "date_created",
          special: "date-created",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 7,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 42,
          collection: "project",
          field: "user_created",
          special: "user-created",
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 8,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 43,
          collection: "project",
          field: "date_updated",
          special: "date-updated",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 9,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 44,
          collection: "project",
          field: "user_updated",
          special: "user-updated",
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 10,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 156,
          collection: "project",
          field: "notes",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 17,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 153,
          collection: "project",
          field: "irrigation_water_pump",
          special: "uuid",
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 11,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 152,
          collection: "project",
          field: "irrigation_plans",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 14,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 155,
          collection: "project",
          field: "metadata",
          special: "cast-json",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 16,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 37,
          collection: "project",
          field: "id",
          special: "uuid",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 1,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 136,
          collection: "water_pump",
          field: "notes",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 14,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 39,
          collection: "project",
          field: "name",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 3,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 133,
          collection: "water_pump",
          field: "identifier",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: true,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 132,
          collection: "water_pump",
          field: "label",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 3,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: true,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 137,
          collection: "water_pump",
          field: "metadata",
          special: "cast-json",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 15,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 130,
          collection: "water_pump",
          field: "property",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 6,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 131,
          collection: "water_pump",
          field: "water_pump_controller",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 7,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 128,
          collection: "water_pump",
          field: "date_updated",
          special: "date-updated",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 12,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 50,
          collection: "sector",
          field: "id",
          special: "uuid",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 1,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 3,
          collection: "account",
          field: "date_created",
          special: "date-created",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 3,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 4,
          collection: "account",
          field: "user_created",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 4,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 5,
          collection: "account",
          field: "date_updated",
          special: "date-updated",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 5,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 6,
          collection: "account",
          field: "user_updated",
          special: "user-updated",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 6,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 9,
          collection: "account",
          field: "properties",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 7,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 1,
          collection: "account",
          field: "id",
          special: "uuid",
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 1,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 22,
          collection: "property",
          field: "id",
          special: "uuid",
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 1,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 23,
          collection: "property",
          field: "account",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 24,
          collection: "property",
          field: "name",
          special: null,
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 3,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 26,
          collection: "property",
          field: "point",
          special: null,
          interface: "map",
          options: {
            defaultView: {
              center: { lng: 0, lat: 0 },
              zoom: 0,
              bearing: 0,
              pitch: 0,
            },
            geometryType: "Point",
          },
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 5,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 27,
          collection: "property",
          field: "address_postal_code",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 6,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 28,
          collection: "property",
          field: "address_street_name",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 7,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 29,
          collection: "property",
          field: "address_street_number",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 8,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 30,
          collection: "property",
          field: "address_complement",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 9,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 31,
          collection: "property",
          field: "address_neighborhood",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 10,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 32,
          collection: "property",
          field: "address_city",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 11,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 33,
          collection: "property",
          field: "address_state",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 12,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 34,
          collection: "property",
          field: "address_country",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 13,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 35,
          collection: "property",
          field: "notes",
          special: null,
          interface: "input-multiline",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 14,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 17,
          collection: "property",
          field: "date_created",
          special: "date-created",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 15,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 18,
          collection: "property",
          field: "user_created",
          special: "user-created",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 16,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 19,
          collection: "property",
          field: "date_updated",
          special: "date-updated",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 17,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 20,
          collection: "property",
          field: "user_updated",
          special: "user-updated",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 18,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 36,
          collection: "property",
          field: "projects",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 19,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 52,
          collection: "sector",
          field: "label",
          special: null,
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 3,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 25,
          collection: "property",
          field: "timezone",
          special: null,
          interface: "select-dropdown",
          options: {
            choices: [
              { text: "America/Sao_Paulo", value: "America/Sao_Paulo" },
            ],
          },
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 4,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 51,
          collection: "sector",
          field: "irrigation_valve_actuator_output",
          special: null,
          interface: "select-dropdown",
          options: {
            choices: [
              { text: "1", value: 1 },
              { text: "2", value: 2 },
              { text: "3", value: 3 },
              { text: "4", value: 4 },
            ],
          },
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 4,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 92,
          collection: "account",
          field: "users",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 9,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 2,
          collection: "account",
          field: "owner",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: true,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 94,
          collection: "directus_users",
          field: "accounts",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 96,
          collection: "property_device",
          field: "label",
          special: null,
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 105,
          collection: "property",
          field: "devices",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 20,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 16,
          collection: "device",
          field: "user_updated",
          special: "user-updated",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 5,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 82,
          collection: "account_user",
          field: "id",
          special: "uuid",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 1,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 11,
          collection: "device",
          field: "id",
          special: "uuid",
          interface: "input",
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 1,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 100,
          collection: "property_device",
          field: "end_date",
          special: null,
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 3,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 14,
          collection: "device",
          field: "user_created",
          special: "user-created",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 3,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 15,
          collection: "device",
          field: "date_updated",
          special: "date-updated",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 4,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 99,
          collection: "property_device",
          field: "start_date",
          special: null,
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 98,
          collection: "property_device",
          field: "device",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 5,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 103,
          collection: "property_device",
          field: "date_updated",
          special: "date-updated",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 8,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 104,
          collection: "property_device",
          field: "user_updated",
          special: "user-updated",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 9,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 81,
          collection: "irrigation_plan",
          field: "steps",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 14,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 101,
          collection: "property_device",
          field: "date_created",
          special: "date-created",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 6,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 102,
          collection: "property_device",
          field: "user_created",
          special: "user-created",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 7,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 95,
          collection: "property_device",
          field: "id",
          special: "uuid",
          interface: "input",
          options: null,
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 1,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 97,
          collection: "property_device",
          field: "property",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 4,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 56,
          collection: "sector",
          field: "date_updated",
          special: "date-updated",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 4,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 57,
          collection: "sector",
          field: "user_updated",
          special: "user-updated",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 5,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 127,
          collection: "water_pump",
          field: "user_created",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 11,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 53,
          collection: "sector",
          field: "polygon",
          special: null,
          interface: "map",
          options: {
            defaultView: {
              center: { lng: 0, lat: 0 },
              zoom: 0,
              bearing: 0,
              pitch: 0,
            },
            geometryType: "Polygon",
          },
          display: "raw",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 11,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 54,
          collection: "sector",
          field: "date_created",
          special: "date-created",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 13,
          collection: "device",
          field: "date_created",
          special: "date-created",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 55,
          collection: "sector",
          field: "user_created",
          special: "user-created",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 3,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 126,
          collection: "water_pump",
          field: "date_created",
          special: "date-created",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 10,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 83,
          collection: "account_user",
          field: "account",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 84,
          collection: "account_user",
          field: "user",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 3,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 85,
          collection: "account_user",
          field: "role",
          special: null,
          interface: "select-dropdown",
          options: {
            choices: [
              { text: "Admin", value: "admin" },
              { text: "User", value: "user" },
              { text: "Guest", value: "guest" },
            ],
          },
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 4,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 86,
          collection: "account_user",
          field: "end_date",
          special: null,
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 5,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 87,
          collection: "account_user",
          field: "start_date",
          special: null,
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 6,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 88,
          collection: "account_user",
          field: "date_created",
          special: "date-created",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 7,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 89,
          collection: "account_user",
          field: "user_created",
          special: "user-created",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 8,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 90,
          collection: "account_user",
          field: "date_updated",
          special: "date-updated",
          interface: "datetime",
          options: null,
          display: "datetime",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 9,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 91,
          collection: "account_user",
          field: "user_updated",
          special: "user-updated",
          interface: "select-dropdown-m2o",
          options: null,
          display: "user",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 10,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 46,
          collection: "project",
          field: "localized_irrigation_controller",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 4,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 157,
          collection: "water_pump",
          field: "irrigation_projects",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 8,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 158,
          collection: "water_pump",
          field: "fertigation_projects",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 9,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 38,
          collection: "project",
          field: "property",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 2,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 168,
          collection: "project",
          field: "sectors",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 13,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 159,
          collection: "sector",
          field: "project",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 6,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 162,
          collection: "sector",
          field: "valve_controller",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 8,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 163,
          collection: "sector",
          field: "valve_controller_output",
          special: null,
          interface: "select-radio",
          options: {
            choices: [
              { text: "1", value: 1 },
              { text: "2", value: 2 },
              { text: "3", value: 3 },
              { text: "4", value: 4 },
            ],
          },
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 9,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: true,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 164,
          collection: "sector",
          field: "area",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 10,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 167,
          collection: "sector",
          field: "steps",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 12,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 161,
          collection: "sector",
          field: "description",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 13,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 165,
          collection: "sector",
          field: "metadata",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 14,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 166,
          collection: "sector",
          field: "notes",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 15,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 170,
          collection: "account",
          field: "metadata",
          special: "cast-json",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 171,
          collection: "account",
          field: "notes",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: null,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 12,
          collection: "device",
          field: "model",
          special: null,
          interface: "select-dropdown",
          options: {
            choices: [
              { text: "Localized Irrigation Controller", value: "LIC" },
              {
                text: "Water Pump Controller - PL10 Variant",
                value: "WPC-PL10",
              },
              {
                text: "Water Pump Controller - PL50 Variant",
                value: "WPC-PL50",
              },
              { text: "Valve Controller", value: "VC" },
              { text: "Reservoir Monitor", value: "RM" },
            ],
          },
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 6,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: true,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 169,
          collection: "device",
          field: "valve_sectors",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 10,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 172,
          collection: "device",
          field: "localized_irrigation_controller_projects",
          special: "o2m",
          interface: "list-o2m",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 11,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 174,
          collection: "account_user",
          field: "notes",
          special: null,
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 11,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 173,
          collection: "account_user",
          field: "metadata",
          special: "cast-json",
          interface: null,
          options: null,
          display: null,
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 12,
          width: "full",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
        {
          id: 154,
          collection: "project",
          field: "fertigation_water_pump",
          special: null,
          interface: "select-dropdown-m2o",
          options: null,
          display: "related-values",
          display_options: null,
          readonly: false,
          hidden: false,
          sort: 12,
          width: "half",
          translations: null,
          note: null,
          conditions: null,
          required: false,
          group: null,
          validation: null,
          validation_message: null,
        },
      ].map((item) => {
        delete item.id; // Remove id to let the database auto-generate it
        return item;
      })
    );

    // Insert 17 records into directus_relations
    await tx.batchInsert(
      "directus_relations",
      [
        {
          id: 1,
          many_collection: "account",
          many_field: "owner",
          one_collection: "directus_users",
          one_field: "account",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 2,
          many_collection: "property",
          many_field: "account",
          one_collection: "account",
          one_field: "properties",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 3,
          many_collection: "project",
          many_field: "property",
          one_collection: "property",
          one_field: "projects",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 8,
          many_collection: "account_user",
          many_field: "account",
          one_collection: "account",
          one_field: "users",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 9,
          many_collection: "account_user",
          many_field: "user",
          one_collection: "directus_users",
          one_field: "accounts",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 10,
          many_collection: "property_device",
          many_field: "property",
          one_collection: "property",
          one_field: "devices",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 12,
          many_collection: "water_pump",
          many_field: "property",
          one_collection: "property",
          one_field: "water_pumps",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 13,
          many_collection: "property_device",
          many_field: "device",
          one_collection: "device",
          one_field: "properties",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 14,
          many_collection: "water_pump",
          many_field: "water_pump_controller",
          one_collection: "device",
          one_field: "water_pumps",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 5,
          many_collection: "irrigation_plan",
          many_field: "project",
          one_collection: "project",
          one_field: "irrigation_plans",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 15,
          many_collection: "project",
          many_field: "irrigation_water_pump",
          one_collection: "water_pump",
          one_field: "irrigation_projects",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 16,
          many_collection: "project",
          many_field: "fertigation_water_pump",
          one_collection: "water_pump",
          one_field: "fertigation_projects",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 17,
          many_collection: "irrigation_plan_step",
          many_field: "sector",
          one_collection: "sector",
          one_field: "steps",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 4,
          many_collection: "sector",
          many_field: "project",
          one_collection: "project",
          one_field: "sectors",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 18,
          many_collection: "sector",
          many_field: "valve_controller",
          one_collection: "device",
          one_field: "valve_sectors",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 19,
          many_collection: "project",
          many_field: "localized_irrigation_controller",
          one_collection: "device",
          one_field: "localized_irrigation_controller_projects",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: null,
          one_deselect_action: "delete",
        },
        {
          id: 6,
          many_collection: "irrigation_plan_step",
          many_field: "irrigation_plan",
          one_collection: "irrigation_plan",
          one_field: "steps",
          one_collection_field: null,
          one_allowed_collections: null,
          junction_field: null,
          sort_field: "order",
          one_deselect_action: "delete",
        },
      ].map((item) => {
        delete item.id; // Remove id to let the database auto-generate it
        return item;
      })
    );

    // Insert 1 records into directus_operations
    await tx.batchInsert("directus_operations", [
      {
        id: "9cf9c2aa-0e7a-48d5-8fa1-4b01addf60f8",
        name: "append_user_association",
        key: "append_user_association",
        type: "exec",
        position_x: 19,
        position_y: 1,
        options: {
          code: 'module.exports = async function(data) {\n\tdata.$trigger.payload.users = {\n        create: [\n            {\n                "user": data.$trigger.payload.owner,\n                "start_date": new Date().toISOString(),\n                "role": "admin"\n            }\n        ]\n    };\n\treturn data.$trigger.payload;\n}',
        },
        resolve: null,
        reject: null,
        flow: "abea1ed0-8d1a-42a9-920d-8ceaac1e97f8",
        date_created: "2025-06-03T17:34:27.065+00:00",
        user_created: null,
      },
    ]);

    // Insert 37 records into directus_permissions
    await tx.batchInsert(
      "directus_permissions",
      [
        {
          id: 1,
          collection: "directus_comments",
          action: "read",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 2,
          collection: "directus_files",
          action: "create",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 3,
          collection: "directus_files",
          action: "read",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 4,
          collection: "directus_files",
          action: "update",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 5,
          collection: "directus_files",
          action: "delete",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 6,
          collection: "directus_dashboards",
          action: "create",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 7,
          collection: "directus_dashboards",
          action: "read",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 8,
          collection: "directus_dashboards",
          action: "update",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 9,
          collection: "directus_dashboards",
          action: "delete",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 10,
          collection: "directus_panels",
          action: "create",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 11,
          collection: "directus_panels",
          action: "read",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 12,
          collection: "directus_panels",
          action: "update",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 13,
          collection: "directus_panels",
          action: "delete",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 14,
          collection: "directus_folders",
          action: "create",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 15,
          collection: "directus_folders",
          action: "read",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 16,
          collection: "directus_folders",
          action: "update",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 17,
          collection: "directus_folders",
          action: "delete",
          permissions: {},
          validation: null,
          presets: null,
          fields: null,
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 18,
          collection: "directus_users",
          action: "read",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 19,
          collection: "directus_users",
          action: "update",
          permissions: { id: { _eq: "$CURRENT_USER" } },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 20,
          collection: "directus_roles",
          action: "read",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 21,
          collection: "directus_shares",
          action: "read",
          permissions: {
            _or: [
              { role: { _eq: "$CURRENT_ROLE" } },
              { role: { _null: true } },
            ],
          },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 22,
          collection: "directus_shares",
          action: "create",
          permissions: {},
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 23,
          collection: "directus_shares",
          action: "update",
          permissions: { user_created: { _eq: "$CURRENT_USER" } },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 24,
          collection: "directus_shares",
          action: "delete",
          permissions: { user_created: { _eq: "$CURRENT_USER" } },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 25,
          collection: "directus_flows",
          action: "read",
          permissions: { trigger: { _eq: "manual" } },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 28,
          collection: "project",
          action: "read",
          permissions: {
            _and: [
              {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
              { property: { account: { users: { role: { _eq: "admin" } } } } },
            ],
          },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 29,
          collection: "property",
          action: "read",
          permissions: {
            _and: [
              { account: { users: { user: { _eq: "$CURRENT_USER" } } } },
              { account: { users: { role: { _eq: "admin" } } } },
            ],
          },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 30,
          collection: "sector",
          action: "read",
          permissions: {
            _and: [
              {
                project: {
                  property: {
                    account: {
                      users: {
                        user: { _eq: "$CURRENT_USER" },
                        role: { _eq: "admin" },
                      },
                    },
                  },
                },
              },
            ],
          },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 33,
          collection: "device",
          action: "create",
          permissions: null,
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 34,
          collection: "property_device",
          action: "create",
          permissions: null,
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 36,
          collection: "property_device",
          action: "read",
          permissions: {
            _and: [
              {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
              { property: { account: { users: { role: { _eq: "admin" } } } } },
            ],
          },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 27,
          collection: "account",
          action: "read",
          permissions: {
            _and: [
              { users: { user: { _eq: "$CURRENT_USER" } } },
              { users: { role: { _eq: "admin" } } },
            ],
          },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 35,
          collection: "account_user",
          action: "read",
          permissions: {
            _and: [
              { user: { _eq: "$CURRENT_USER" } },
              { role: { _eq: "admin" } },
            ],
          },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 26,
          collection: "device",
          action: "read",
          permissions: {
            _and: [
              {
                properties: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
              {
                properties: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
              {
                _or: [
                  { properties: { start_date: { _null: true } } },
                  { properties: { start_date: { _lte: "$NOW" } } },
                ],
              },
              {
                _or: [
                  { properties: { end_date: { _null: true } } },
                  { properties: { end_date: { _gt: "$NOW" } } },
                ],
              },
            ],
          },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 31,
          collection: "irrigation_plan",
          action: "read",
          permissions: {
            _and: [
              {
                project: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
              {
                project: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            ],
          },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 37,
          collection: "water_pump",
          action: "read",
          permissions: {
            _and: [
              {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
              { property: { account: { users: { role: { _eq: "admin" } } } } },
            ],
          },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
        {
          id: 32,
          collection: "irrigation_plan_step",
          action: "read",
          permissions: {
            _and: [
              {
                irrigation_plan: {
                  project: {
                    property: {
                      account: { users: { user: { _eq: "$CURRENT_USER" } } },
                    },
                  },
                },
              },
              {
                irrigation_plan: {
                  project: {
                    property: {
                      account: { users: { role: { _eq: "admin" } } },
                    },
                  },
                },
              },
            ],
          },
          validation: null,
          presets: null,
          fields: "*",
          policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        },
      ].map((item) => {
        delete item.id; // Remove id to let the database auto-generate it
        return item;
      })
    );

    // Insert 2 records into directus_access
    await tx.batchInsert("directus_access", [
      // {"id":"0b963f2c-e3e0-434f-96cf-6e2d4ab69acf","role":null,"user":null,"policy":"abf8a154-5b1c-4a46-ac9c-7300570f4f17","sort":1},
      {
        id: "1e2382a5-f348-48a8-9060-afa424bca664",
        role: "ac5ba5cb-1d74-4df4-99d3-6758fb49255c",
        user: null,
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        sort: 1,
      },
    ]);
  });
}

/**
 * Reverts the changes made by the up migration.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-06-25T20:33:31.447Z
 * @description This migration reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Delete 2 records from directus_access (reverse of insert)
    // await tx('directus_access').where({"policy":"abf8a154-5b1c-4a46-ac9c-7300570f4f17","role":null,"user":null}).del();
    await tx("directus_access")
      .where({
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
        role: "ac5ba5cb-1d74-4df4-99d3-6758fb49255c",
        user: null,
      })
      .del();

    // Delete 37 records from directus_permissions (reverse of insert)
    await tx("directus_permissions")
      .where({
        collection: "directus_comments",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_files",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_files",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_files",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_files",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_dashboards",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_dashboards",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_dashboards",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_dashboards",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_panels",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_panels",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_panels",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_panels",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_folders",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_folders",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_folders",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_folders",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_users",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_users",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_roles",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_shares",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_shares",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_shares",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_shares",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_flows",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "property",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "sector",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "device",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "property_device",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "property_device",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "account",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "account_user",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "device",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan_step",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();

    // Delete 1 records from directus_operations (reverse of insert)
    await tx("directus_operations")
      .where({ id: "9cf9c2aa-0e7a-48d5-8fa1-4b01addf60f8" })
      .del();

    // Delete 17 records from directus_relations (reverse of insert)
    await tx("directus_relations")
      .where({ many_collection: "account", many_field: "owner" })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "property", many_field: "account" })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "project", many_field: "property" })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "account_user", many_field: "account" })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "account_user", many_field: "user" })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "property_device", many_field: "property" })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "water_pump", many_field: "property" })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "property_device", many_field: "device" })
      .del();
    await tx("directus_relations")
      .where({
        many_collection: "water_pump",
        many_field: "water_pump_controller",
      })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "irrigation_plan", many_field: "project" })
      .del();
    await tx("directus_relations")
      .where({
        many_collection: "project",
        many_field: "irrigation_water_pump",
      })
      .del();
    await tx("directus_relations")
      .where({
        many_collection: "project",
        many_field: "fertigation_water_pump",
      })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "irrigation_plan_step", many_field: "sector" })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "sector", many_field: "project" })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "sector", many_field: "valve_controller" })
      .del();
    await tx("directus_relations")
      .where({
        many_collection: "project",
        many_field: "localized_irrigation_controller",
      })
      .del();
    await tx("directus_relations")
      .where({
        many_collection: "irrigation_plan_step",
        many_field: "irrigation_plan",
      })
      .del();

    // Delete 152 records from directus_fields (reverse of insert)
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "id" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "date_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "user_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "date_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "user_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "irrigation_plan" })
      .del();
    await tx("directus_fields")
      .where({ collection: "directus_users", field: "account" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "sector" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "order" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "duration_seconds" })
      .del();
    await tx("directus_fields")
      .where({
        collection: "irrigation_plan_step",
        field: "delay_seconds_after",
      })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "description" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "metadata" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan_step", field: "notes" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "pump_type" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "user_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "name" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "pump_model" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "has_frequency_inverter" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "monitor_operation" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "metadata" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "water_pumps" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "irrigation_pump_controller" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "fertigation_pump_controller" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "is_enabled" })
      .del();
    await tx("directus_fields")
      .where({ collection: "device", field: "water_pumps" })
      .del();
    await tx("directus_fields")
      .where({ collection: "device", field: "properties" })
      .del();
    await tx("directus_fields")
      .where({ collection: "device", field: "notes" })
      .del();
    await tx("directus_fields")
      .where({ collection: "device", field: "identifier" })
      .del();
    await tx("directus_fields")
      .where({ collection: "device", field: "metadata" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property_device", field: "metadata" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property_device", field: "notes" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "project" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "user_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "end_date" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "date_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "date_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "user_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "description" })
      .del();
    await tx("directus_fields")
      .where({
        collection: "irrigation_plan",
        field: "total_irrigation_duration",
      })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "metadata" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "notes" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "id" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "name" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "start_time" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "days_of_week" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "start_date" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "description" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "id" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "start_date" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "end_date" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "date_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "user_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "date_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "user_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "notes" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "irrigation_water_pump" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "irrigation_plans" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "metadata" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "id" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "notes" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "name" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "identifier" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "label" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "metadata" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "property" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "water_pump_controller" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "date_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "id" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account", field: "date_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account", field: "user_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account", field: "date_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account", field: "user_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account", field: "properties" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account", field: "id" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "id" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "account" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "name" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "point" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "address_postal_code" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "address_street_name" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "address_street_number" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "address_complement" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "address_neighborhood" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "address_city" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "address_state" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "address_country" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "notes" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "date_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "user_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "date_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "user_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "projects" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "label" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "timezone" })
      .del();
    await tx("directus_fields")
      .where({
        collection: "sector",
        field: "irrigation_valve_actuator_output",
      })
      .del();
    await tx("directus_fields")
      .where({ collection: "account", field: "users" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account", field: "owner" })
      .del();
    await tx("directus_fields")
      .where({ collection: "directus_users", field: "accounts" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property_device", field: "label" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property", field: "devices" })
      .del();
    await tx("directus_fields")
      .where({ collection: "device", field: "user_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account_user", field: "id" })
      .del();
    await tx("directus_fields")
      .where({ collection: "device", field: "id" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property_device", field: "end_date" })
      .del();
    await tx("directus_fields")
      .where({ collection: "device", field: "user_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "device", field: "date_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property_device", field: "start_date" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property_device", field: "device" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property_device", field: "date_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property_device", field: "user_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "irrigation_plan", field: "steps" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property_device", field: "date_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property_device", field: "user_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property_device", field: "id" })
      .del();
    await tx("directus_fields")
      .where({ collection: "property_device", field: "property" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "date_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "user_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "user_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "polygon" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "date_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "device", field: "date_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "user_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "date_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account_user", field: "account" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account_user", field: "user" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account_user", field: "role" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account_user", field: "end_date" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account_user", field: "start_date" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account_user", field: "date_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account_user", field: "user_created" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account_user", field: "date_updated" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account_user", field: "user_updated" })
      .del();
    await tx("directus_fields")
      .where({
        collection: "project",
        field: "localized_irrigation_controller",
      })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "irrigation_projects" })
      .del();
    await tx("directus_fields")
      .where({ collection: "water_pump", field: "fertigation_projects" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "property" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "sectors" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "project" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "valve_controller" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "valve_controller_output" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "area" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "steps" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "description" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "metadata" })
      .del();
    await tx("directus_fields")
      .where({ collection: "sector", field: "notes" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account", field: "metadata" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account", field: "notes" })
      .del();
    await tx("directus_fields")
      .where({ collection: "device", field: "model" })
      .del();
    await tx("directus_fields")
      .where({ collection: "device", field: "valve_sectors" })
      .del();
    await tx("directus_fields")
      .where({
        collection: "device",
        field: "localized_irrigation_controller_projects",
      })
      .del();
    await tx("directus_fields")
      .where({ collection: "account_user", field: "notes" })
      .del();
    await tx("directus_fields")
      .where({ collection: "account_user", field: "metadata" })
      .del();
    await tx("directus_fields")
      .where({ collection: "project", field: "fertigation_water_pump" })
      .del();

    // Delete 1 records from directus_flows (reverse of insert)
    await tx("directus_flows")
      .where({ id: "abea1ed0-8d1a-42a9-920d-8ceaac1e97f8" })
      .del();

    // Delete 10 records from directus_collections (reverse of insert)
    await tx("directus_collections").where({ collection: "account" }).del();
    await tx("directus_collections").where({ collection: "device" }).del();
    await tx("directus_collections")
      .where({ collection: "account_user" })
      .del();
    await tx("directus_collections").where({ collection: "project" }).del();
    await tx("directus_collections").where({ collection: "water_pump" }).del();
    await tx("directus_collections").where({ collection: "property" }).del();
    await tx("directus_collections")
      .where({ collection: "property_device" })
      .del();
    await tx("directus_collections").where({ collection: "sector" }).del();
    await tx("directus_collections")
      .where({ collection: "irrigation_plan_step" })
      .del();
    await tx("directus_collections")
      .where({ collection: "irrigation_plan" })
      .del();

    // Delete 2 records from directus_policies (reverse of insert)
    // await tx('directus_policies').where({"id":"abf8a154-5b1c-4a46-ac9c-7300570f4f17"}).del();
    await tx("directus_policies")
      .where({ id: "b6e0a767-dc9a-478d-b588-61aa700a519a" })
      .del();

    // Delete 1 records from directus_roles (reverse of insert)
    await tx("directus_roles")
      .where({ id: "ac5ba5cb-1d74-4df4-99d3-6758fb49255c" })
      .del();
  });
}
