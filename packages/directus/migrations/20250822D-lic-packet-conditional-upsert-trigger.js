/**
 * Migration to implement trigger function for conditional UPSERT logic between current_lic_packet and lic_packet
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create trigger function that handles the conditional UPSERT logic
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_lic_packet_upsert()
      RETURNS TRIGGER AS $$
      DECLARE
        existing_record RECORD;
      BEGIN
        -- Always insert into lic_packet (historical record)
        INSERT INTO lic_packet (device, date_created, packet_date, payload_type, payload_data)
        VALUES (NEW.device, NEW.date_created, NEW.packet_date, NEW.payload_type, NEW.payload_data);

        -- Check for existing record in current_lic_packet with same device and payload_type
        SELECT * INTO existing_record
        FROM current_lic_packet
        WHERE device = NEW.device AND payload_type = NEW.payload_type;

        IF existing_record IS NOT NULL THEN
          -- Compare packet_date to determine if new packet is newer
          IF NEW.packet_date > existing_record.packet_date THEN
            -- New packet is newer: delete existing and allow insert of new
            DELETE FROM current_lic_packet
            WHERE device = NEW.device AND payload_type = NEW.payload_type;
            -- The new record will be inserted normally by the INSERT operation
          ELSE
            -- New packet is older or same: prevent insert into current_lic_packet
            -- Return NULL to cancel the INSERT operation for current_lic_packet
            RETURN NULL;
          END IF;
        END IF;
        
        -- If no existing record or new packet is newer, allow the insert
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create the trigger on current_lic_packet table
    await tx.raw(`
      CREATE TRIGGER lic_packet_conditional_upsert_trigger
      BEFORE INSERT ON current_lic_packet
      FOR EACH ROW
      EXECUTE FUNCTION handle_lic_packet_upsert();
    `);

    // Also create a trigger for UPDATE operations on current_lic_packet
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_lic_packet_update()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Always insert update into lic_packet (historical record)
        INSERT INTO lic_packet (device, date_created, packet_date, payload_type, payload_data)
        VALUES (NEW.device, NOW(), NEW.packet_date, NEW.payload_type, NEW.payload_data);

        -- Allow the update to proceed normally
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER lic_packet_update_trigger
      BEFORE UPDATE ON current_lic_packet
      FOR EACH ROW
      EXECUTE FUNCTION handle_lic_packet_update();
    `);
  });
}

/**
 * Migration to remove the trigger function and triggers
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Drop triggers
    await tx.raw(`DROP TRIGGER IF EXISTS lic_packet_conditional_upsert_trigger ON current_lic_packet;`);
    await tx.raw(`DROP TRIGGER IF EXISTS lic_packet_update_trigger ON current_lic_packet;`);
    
    // Drop functions
    await tx.raw(`DROP FUNCTION IF EXISTS handle_lic_packet_upsert();`);
    await tx.raw(`DROP FUNCTION IF EXISTS handle_lic_packet_update();`);
  });
}