/**
 * Applies the current state of the Directus config to the database.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-08-29T17:36:24.119Z
 * @description This migration applies the current state of the Directus config to the database.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  // if (true) return; // Skip migration if already applied
  await knex.transaction(async (tx) => {
    // Insert 1 records into directus_policies
    await tx.batchInsert("directus_policies", [
      {
        id: "69a0500f-874c-4c21-a485-9911ebadd379",
        name: "Searchable Devices",
        icon: "badge",
        description: null,
        ip_access: null,
        enforce_tfa: false,
        admin_access: false,
        app_access: false,
      },
    ]);

    // Insert 2 records into directus_permissions
    await tx.batchInsert("directus_permissions", [
      {
        collection: "property_device",
        action: "read",
        permissions: null,
        validation: null,
        presets: null,
        fields: "device,start_date,end_date,id",
        policy: "69a0500f-874c-4c21-a485-9911ebadd379",
      },
      {
        collection: "device",
        action: "read",
        permissions: null,
        validation: null,
        presets: null,
        fields: "id,identifier,properties,model",
        policy: "69a0500f-874c-4c21-a485-9911ebadd379",
      },
    ]);

    // Insert 1 records into directus_access
    await tx.batchInsert("directus_access", [
      {
        id: "9e1b0aeb-d6e6-4335-b30f-d186a51aa9ca",
        role: "ac5ba5cb-1d74-4df4-99d3-6758fb49255c",
        user: null,
        policy: "69a0500f-874c-4c21-a485-9911ebadd379",
        sort: 2,
      },
    ]);
  });
}

/**
 * Reverts the changes made by the up migration.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-08-29T17:36:24.119Z
 * @description This migration reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Delete 1 records from directus_access (reverse of insert)
    await tx("directus_access")
      .where({
        policy: "69a0500f-874c-4c21-a485-9911ebadd379",
        role: "ac5ba5cb-1d74-4df4-99d3-6758fb49255c",
        user: null,
      })
      .del();

    // Delete 2 records from directus_permissions (reverse of insert)
    await tx("directus_permissions")
      .where({
        collection: "property_device",
        action: "read",
        policy: "69a0500f-874c-4c21-a485-9911ebadd379",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "device",
        action: "read",
        policy: "69a0500f-874c-4c21-a485-9911ebadd379",
      })
      .del();

    // Delete 1 records from directus_policies (reverse of insert)
    await tx("directus_policies")
      .where({ id: "69a0500f-874c-4c21-a485-9911ebadd379" })
      .del();
  });
}
