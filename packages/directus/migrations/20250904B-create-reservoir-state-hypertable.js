/**
 * Migration to create reservoir_state table as TimescaleDB hypertable with monthly chunking and compression
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.createTable("reservoir_state", (table) => {
      table.bigIncrements("id");
      table.uuid("reservoir").notNullable();
      table.timestamp("packet_date").notNullable();
      table.timestamp("start_time").nullable();
      table.timestamp("restart_time").nullable();
      table.timestamp("end_time").nullable();
      table.timestamp("date_created").notNullable().defaultTo(knex.fn.now());

      table.foreign("reservoir").references("id").inTable("reservoir");

      table.primary(["reservoir", "packet_date"]);
    });

    await tx.raw(`CREATE EXTENSION IF NOT EXISTS timescaledb;`);

    await tx.raw(`
      SELECT create_hypertable(
        'reservoir_state',
        'packet_date',
        chunk_time_interval => INTERVAL '1 month',
        if_not_exists => TRUE
      );
    `);

    await tx.raw(`
      ALTER TABLE reservoir_state SET (
        timescaledb.compress,
        timescaledb.compress_segmentby = 'reservoir'
      );
    `);

    await tx.raw(`
      SELECT add_compression_policy(
        'reservoir_state',
        INTERVAL '3 months'
      );
    `);

    await tx.raw(`
      COMMENT ON TABLE reservoir_state IS 'Historical state of reservoir automation based on automation_report MQTT messages - TimescaleDB hypertable';
    `);

    await tx.raw(`
      COMMENT ON COLUMN reservoir_state.id IS 'Primary key - auto-incrementing';
      COMMENT ON COLUMN reservoir_state.reservoir IS 'Foreign key to reservoir table';
      COMMENT ON COLUMN reservoir_state.packet_date IS 'When the original device automation_report packet was recorded - partitioning column';
      COMMENT ON COLUMN reservoir_state.start_time IS 'When the automation started (nullable until known)';
      COMMENT ON COLUMN reservoir_state.restart_time IS 'When the automation restarted (nullable)';
      COMMENT ON COLUMN reservoir_state.end_time IS 'When the automation ended (nullable)';
      COMMENT ON COLUMN reservoir_state.date_created IS 'When this state record was created';
    `);
  });
}

/**
 * Migration to drop reservoir_state hypertable
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(`
      SELECT remove_compression_policy('reservoir_state', if_exists => true);
    `);

    await tx.raw(`
      SELECT remove_retention_policy('reservoir_state', if_exists => true);
    `);

    await tx.schema.dropTableIfExists("reservoir_state");
  });
}

