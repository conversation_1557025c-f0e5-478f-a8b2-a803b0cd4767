/**
 * Migration to implement trigger functions populating reservoir_state from current_reservoir_state upserts
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_reservoir_state_upsert()
      RETURNS TRIGGER AS $$
      DECLARE
        existing_record RECORD;
      BEGIN
        -- Insert into reservoir_state (historical record)
        BEGIN
          INSERT INTO reservoir_state (
            reservoir,
            packet_date,
            start_time,
            restart_time,
            end_time,
            date_created
          ) VALUES (
            NEW.reservoir,
            NEW.packet_date,
            NEW.start_time,
            NEW.restart_time,
            NEW.end_time,
            NEW.date_created
          );
        EXCEPTION WHEN unique_violation THEN
          RAISE WARNING 'Duplicate key for reservoir % and packet_date % ignored in reservoir_state', NEW.reservoir, NEW.packet_date;
        END;

        IF TG_OP = 'INSERT' THEN
          SELECT * INTO existing_record
          FROM current_reservoir_state
          WHERE reservoir = NEW.reservoir;

          IF existing_record IS NOT NULL THEN
            IF NEW.packet_date > existing_record.packet_date THEN
              DELETE FROM current_reservoir_state WHERE reservoir = NEW.reservoir;
            ELSE
              RETURN NULL; -- cancel insert
            END IF;
          END IF;
        END IF;

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER reservoir_state_upsert_trigger
      BEFORE INSERT ON current_reservoir_state
      FOR EACH ROW
      EXECUTE FUNCTION handle_reservoir_state_upsert();
    `);

    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_reservoir_state_update()
      RETURNS TRIGGER AS $$
      BEGIN
        BEGIN
          INSERT INTO reservoir_state (
            reservoir,
            packet_date,
            start_time,
            restart_time,
            end_time,
            date_created
          ) VALUES (
            NEW.reservoir,
            NEW.packet_date,
            NEW.start_time,
            NEW.restart_time,
            NEW.end_time,
            NOW()
          );
        EXCEPTION WHEN unique_violation THEN
          RAISE WARNING 'Duplicate key for reservoir % and packet_date % ignored in reservoir_state during update', NEW.reservoir, NEW.packet_date;
        END;

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER reservoir_state_update_trigger
      AFTER UPDATE ON current_reservoir_state
      FOR EACH ROW
      EXECUTE FUNCTION handle_reservoir_state_update();
    `);

    await tx.raw(`
      CREATE OR REPLACE FUNCTION update_current_reservoir_state_date_updated()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.date_updated = NOW();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER update_current_reservoir_state_date_updated_trigger
      BEFORE UPDATE ON current_reservoir_state
      FOR EACH ROW
      EXECUTE FUNCTION update_current_reservoir_state_date_updated();
    `);
  });
}

/**
 * Migration to remove the trigger functions and triggers
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(`DROP TRIGGER IF EXISTS reservoir_state_upsert_trigger ON current_reservoir_state;`);
    await tx.raw(`DROP TRIGGER IF EXISTS reservoir_state_update_trigger ON current_reservoir_state;`);
    await tx.raw(`DROP TRIGGER IF EXISTS update_current_reservoir_state_date_updated_trigger ON current_reservoir_state;`);
    await tx.raw(`DROP FUNCTION IF EXISTS handle_reservoir_state_upsert();`);
    await tx.raw(`DROP FUNCTION IF EXISTS handle_reservoir_state_update();`);
    await tx.raw(`DROP FUNCTION IF EXISTS update_current_reservoir_state_date_updated();`);
  });
}

