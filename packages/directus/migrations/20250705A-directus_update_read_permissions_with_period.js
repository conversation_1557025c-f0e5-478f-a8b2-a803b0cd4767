/**
 * Applies the current state of the Directus config to the database.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-05T03:47:06.104Z
 * @description This migration applies the current state of the Directus config to the database.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Update 10 records in directus_permissions
    await tx("directus_permissions")
      .where({
        collection: "account_user",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        permissions: {
          _and: [
            { user: { _eq: "$CURRENT_USER" } },
            { role: { _eq: "admin" } },
            {
              _or: [
                { start_date: { _null: true } },
                { start_date: { _lte: "$NOW" } },
              ],
            },
            {
              _or: [
                { end_date: { _null: true } },
                { end_date: { _gt: "$NOW" } },
              ],
            },
          ],
        },
      });
    await tx("directus_permissions")
      .where({
        collection: "account",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        permissions: {
          _and: [
            { users: { user: { _eq: "$CURRENT_USER" } } },
            { users: { role: { _eq: "admin" } } },
            {
              _or: [
                { users: { start_date: { _null: true } } },
                { users: { start_date: { _lte: "$NOW" } } },
              ],
            },
            {
              _or: [
                { users: { end_date: { _null: true } } },
                { users: { end_date: { _gt: "$NOW" } } },
              ],
            },
          ],
        },
      });
    await tx("directus_permissions")
      .where({
        collection: "property",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        permissions: {
          _and: [
            { account: { users: { user: { _eq: "$CURRENT_USER" } } } },
            { account: { users: { role: { _eq: "admin" } } } },
            {
              _or: [
                { account: { users: { start_date: { _null: true } } } },
                { account: { users: { start_date: { _lte: "$NOW" } } } },
              ],
            },
            {
              _or: [
                { account: { users: { end_date: { _null: true } } } },
                { account: { users: { end_date: { _gt: "$NOW" } } } },
              ],
            },
          ],
        },
      });
    await tx("directus_permissions")
      .where({
        collection: "property_device",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                { start_date: { _null: true } },
                { start_date: { _lte: "$NOW" } },
              ],
            },
            {
              _or: [
                { end_date: { _null: true } },
                { end_date: { _gt: "$NOW" } },
              ],
            },
          ],
        },
      });
    await tx("directus_permissions")
      .where({
        collection: "device",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        permissions: {
          _and: [
            {
              properties: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              properties: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
            {
              _or: [
                {
                  properties: {
                    property: {
                      account: { users: { start_date: { _null: true } } },
                    },
                  },
                },
                {
                  properties: {
                    property: {
                      account: { users: { start_date: { _lte: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  properties: {
                    property: {
                      account: { users: { end_date: { _null: true } } },
                    },
                  },
                },
                {
                  properties: {
                    property: {
                      account: { users: { end_date: { _gt: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                { properties: { start_date: { _null: true } } },
                { properties: { start_date: { _lte: "$NOW" } } },
              ],
            },
            {
              _or: [
                { properties: { end_date: { _null: true } } },
                { properties: { end_date: { _gt: "$NOW" } } },
              ],
            },
          ],
        },
      });
    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
          ],
        },
      });
    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
          ],
        },
      });
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        permissions: {
          _and: [
            {
              project: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              project: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _lte: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _gt: "$NOW" } } },
                    },
                  },
                },
              ],
            },
          ],
        },
      });
    await tx("directus_permissions")
      .where({
        collection: "sector",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        permissions: {
          _and: [
            {
              project: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              project: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _lte: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _gt: "$NOW" } } },
                    },
                  },
                },
              ],
            },
          ],
        },
      });
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan_step",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        permissions: {
          _and: [
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              irrigation_plan: {
                project: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            },
            {
              _or: [
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { start_date: { _null: true } } },
                      },
                    },
                  },
                },
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { start_date: { _lte: "$NOW" } } },
                      },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { end_date: { _null: true } } },
                      },
                    },
                  },
                },
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { end_date: { _gt: "$NOW" } } },
                      },
                    },
                  },
                },
              ],
            },
          ],
        },
      });
  });
}

/**
 * Reverts the changes made by the up migration.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-05T03:47:06.104Z
 * @description This migration reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Revert 10 updates in directus_permissions
    await tx("directus_permissions")
      .where({
        collection: "account_user",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "account_user",
        action: "read",
        permissions: {
          _and: [
            { user: { _eq: "$CURRENT_USER" } },
            { role: { _eq: "admin" } },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "account",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "account",
        action: "read",
        permissions: {
          _and: [
            { users: { user: { _eq: "$CURRENT_USER" } } },
            { users: { role: { _eq: "admin" } } },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "property",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "property",
        action: "read",
        permissions: {
          _and: [
            { account: { users: { user: { _eq: "$CURRENT_USER" } } } },
            { account: { users: { role: { _eq: "admin" } } } },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "property_device",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "property_device",
        action: "read",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "device",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "device",
        action: "read",
        permissions: {
          _and: [
            {
              properties: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              properties: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
            {
              _or: [
                { properties: { start_date: { _null: true } } },
                { properties: { start_date: { _lte: "$NOW" } } },
              ],
            },
            {
              _or: [
                { properties: { end_date: { _null: true } } },
                { properties: { end_date: { _gt: "$NOW" } } },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "project",
        action: "read",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "water_pump",
        action: "read",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "irrigation_plan",
        action: "read",
        permissions: {
          _and: [
            {
              project: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              project: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "sector",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "sector",
        action: "read",
        permissions: {
          _and: [
            {
              project: {
                property: {
                  account: {
                    users: {
                      user: { _eq: "$CURRENT_USER" },
                      role: { _eq: "admin" },
                    },
                  },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan_step",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "irrigation_plan_step",
        action: "read",
        permissions: {
          _and: [
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              irrigation_plan: {
                project: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
  });
}
