/**
 * Add mode column to water_pump table
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.alterTable("water_pump", (table) => {
      table
        .string("mode", 255)
        .notNullable()
        .defaultTo("PULSE")
        .checkIn(["PULSE", "CONTINUOUS"], "water_pump_mode_check");
    });
  });
}

/**
 * Remove mode column from water_pump table
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.alterTable("water_pump", (table) => {
      table.dropColumn("mode");
    });
  });
}