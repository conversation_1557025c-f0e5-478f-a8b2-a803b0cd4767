/**
 * Migration to implement trigger function for populating irrigation_plan_state from current_irrigation_plan_state upserts
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create trigger function that handles the INSERT/UPDATE operations
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_irrigation_plan_state_upsert()
      RETURNS TRIGGER AS $$
      DECLARE
        existing_record RECORD;
      BEGIN
        -- Always try to insert into irrigation_plan_state (historical record)
        BEGIN
          INSERT INTO irrigation_plan_state (
            irrigation_plan, 
            packet_date, 
            start_time, 
            end_time, 
            activated_steps, 
            activated_ferti_steps, 
            waterpump_working, 
            backwash_start_time, 
            uses_waterpump, 
            uses_ferti, 
            date_created
          )
          VALUES (
            NEW.irrigation_plan, 
            NEW.packet_date, 
            NEW.start_time, 
            NEW.end_time, 
            NEW.activated_steps, 
            NEW.activated_ferti_steps, 
            NEW.waterpump_working, 
            NEW.backwash_start_time, 
            NEW.uses_waterpump, 
            NEW.uses_ferti, 
            NEW.date_created
          );
        EXCEPTION WHEN unique_violation THEN
          -- Log warning about duplicate key and continue
          RAISE WARNING 'Duplicate key for irrigation_plan % and packet_date % ignored in irrigation_plan_state', NEW.irrigation_plan, NEW.packet_date;
        END;

        -- Handle UPSERT logic for current_irrigation_plan_state
        IF TG_OP = 'INSERT' THEN
          -- Check for existing record in current_irrigation_plan_state with same irrigation_plan
          SELECT * INTO existing_record
          FROM current_irrigation_plan_state
          WHERE irrigation_plan = NEW.irrigation_plan;

          IF existing_record IS NOT NULL THEN
            -- Compare packet_date to determine if new state is newer
            IF NEW.packet_date > existing_record.packet_date THEN
              -- New state is newer: delete existing and allow insert of new
              DELETE FROM current_irrigation_plan_state
              WHERE irrigation_plan = NEW.irrigation_plan;
              -- The new record will be inserted normally by the INSERT operation
            ELSE
              -- New state is older or same: prevent insert into current_irrigation_plan_state
              -- Return NULL to cancel the INSERT operation for current_irrigation_plan_state
              RETURN NULL;
            END IF;
          END IF;
        END IF;
        
        -- If no existing record or new state is newer, allow the insert/update
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create the trigger on current_irrigation_plan_state table for INSERT operations
    await tx.raw(`
      CREATE TRIGGER irrigation_plan_state_upsert_trigger
      BEFORE INSERT ON current_irrigation_plan_state
      FOR EACH ROW
      EXECUTE FUNCTION handle_irrigation_plan_state_upsert();
    `);

    // Create a trigger for UPDATE operations on current_irrigation_plan_state
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_irrigation_plan_state_update()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Always try to insert update into irrigation_plan_state (historical record)
        BEGIN
          INSERT INTO irrigation_plan_state (
            irrigation_plan, 
            packet_date, 
            start_time, 
            end_time, 
            activated_steps, 
            activated_ferti_steps, 
            waterpump_working, 
            backwash_start_time, 
            uses_waterpump, 
            uses_ferti, 
            date_created
          )
          VALUES (
            NEW.irrigation_plan, 
            NEW.packet_date, 
            NEW.start_time, 
            NEW.end_time, 
            NEW.activated_steps, 
            NEW.activated_ferti_steps, 
            NEW.waterpump_working, 
            NEW.backwash_start_time, 
            NEW.uses_waterpump, 
            NEW.uses_ferti, 
            NOW()
          );
        EXCEPTION WHEN unique_violation THEN
          -- Log warning about duplicate key and continue
          RAISE WARNING 'Duplicate key for irrigation_plan % and packet_date % ignored in irrigation_plan_state during update', NEW.irrigation_plan, NEW.packet_date;
        END;

        -- Allow the update to proceed normally
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER irrigation_plan_state_update_trigger
      AFTER UPDATE ON current_irrigation_plan_state
      FOR EACH ROW
      EXECUTE FUNCTION handle_irrigation_plan_state_update();
    `);

    // Create a trigger to automatically update date_updated on current_irrigation_plan_state
    await tx.raw(`
      CREATE OR REPLACE FUNCTION update_irrigation_plan_state_date_updated()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.date_updated = NOW();
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER update_irrigation_plan_state_date_updated_trigger
      BEFORE UPDATE ON current_irrigation_plan_state
      FOR EACH ROW
      EXECUTE FUNCTION update_irrigation_plan_state_date_updated();
    `);
  });
}

/**
 * Migration to remove the trigger functions and triggers
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Drop triggers
    await tx.raw(
      `DROP TRIGGER IF EXISTS irrigation_plan_state_upsert_trigger ON current_irrigation_plan_state;`
    );
    await tx.raw(
      `DROP TRIGGER IF EXISTS irrigation_plan_state_update_trigger ON current_irrigation_plan_state;`
    );
    await tx.raw(
      `DROP TRIGGER IF EXISTS update_irrigation_plan_state_date_updated_trigger ON current_irrigation_plan_state;`
    );

    // Drop functions
    await tx.raw(`DROP FUNCTION IF EXISTS handle_irrigation_plan_state_upsert();`);
    await tx.raw(`DROP FUNCTION IF EXISTS handle_irrigation_plan_state_update();`);
    await tx.raw(
      `DROP FUNCTION IF EXISTS update_irrigation_plan_state_date_updated();`
    );
  });
}