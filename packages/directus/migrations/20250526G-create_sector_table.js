/**
 * This migration file is a placeholder for the up migration.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.createTable("sector", (table) => {
      table
        .uuid("id")
        .primary()
        .notNullable()
        .defaultTo(tx.raw("gen_random_uuid()"));
      table
        .uuid("project")
        .notNullable()
        .references("id")
        .inTable("project")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.string("name", 255).notNullable();
      table.text("description").nullable();
      table
        .uuid("valve_controller")
        .notNullable()
        .references("id")
        .inTable("device")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.smallint("valve_controller_output").notNullable();
      table.double("area").nullable();
      table.specificType("polygon", "geometry(Polygon,4326)").nullable();
      table
        .timestamp("date_created", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_created")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table
        .timestamp("date_updated", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now());
      table
        .uuid("user_updated")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT");
      table.jsonb("metadata").nullable();
      table.text("notes").nullable();
      table.unique(["project", "name"], {
        indexName: "sector_project_name_unq",
      });
      table.unique(["valve_controller", "valve_controller_output"], {
        indexName: "sector_valve_controller_valve_controller_output_unq",
      });
    });
    await tx.raw(`
      ALTER TABLE sector
      ADD CONSTRAINT chk_valve_controller_output
      CHECK (valve_controller_output BETWEEN 1 AND 4);
    `);
    await tx.raw(`
      CREATE TRIGGER set_sector_date_updated
      BEFORE UPDATE ON sector
      FOR EACH ROW
      EXECUTE FUNCTION update_timestamp_column();
    `);
  });
}

/**
 * This migration file is a placeholder for the down migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw("DROP TRIGGER IF EXISTS set_sector_date_updated ON sector;");
    await tx.schema.dropTableIfExists("sector");
  });
}
