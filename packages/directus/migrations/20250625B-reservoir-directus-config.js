/**
 * Applies Directus configuration for the reservoir collection.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Insert reservoir collection configuration
    await tx.batchInsert("directus_collections", [
      {
        collection: "reservoir",
        icon: "water_drop",
        note: null,
        display_template: "{{name}} - {{property.name}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
    ]);

    // Insert field configurations for reservoir
    await tx.batchInsert("directus_fields", [
      // ID field
      {
        collection: "reservoir",
        field: "id",
        special: "uuid",
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: true,
        sort: 1,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Property field (many-to-one relationship)
      {
        collection: "reservoir",
        field: "property",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "related-values",
        display_options: JSON.stringify({
          template: "{{name}}",
        }),
        readonly: false,
        hidden: false,
        sort: 2,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Name field
      {
        collection: "reservoir",
        field: "name",
        special: null,
        interface: "input",
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 3,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Reservoir Monitor field (many-to-one relationship to device)
      {
        collection: "reservoir",
        field: "reservoir_monitor",
        special: null,
        interface: "select-dropdown-m2o",
        options: JSON.stringify({
          filter: {
            model: {
              _eq: "RM",
            },
          },
        }),
        display: "related-values",
        display_options: JSON.stringify({
          template: "{{identifier}} - {{model}}",
        }),
        readonly: false,
        hidden: false,
        sort: 4,
        width: "half",
        translations: null,
        note: "Optional RM device monitoring this reservoir",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Water Pump field (many-to-one relationship to water_pump)
      {
        collection: "reservoir",
        field: "water_pump",
        special: null,
        interface: "select-dropdown-m2o",
        options: JSON.stringify({
          filter: {
            pump_type: {
              _eq: "SERVICE",
            },
          },
        }),
        display: "related-values",
        display_options: JSON.stringify({
          template: "{{label}} - {{identifier}}",
        }),
        readonly: false,
        hidden: false,
        sort: 5,
        width: "half",
        translations: null,
        note: "Optional SERVICE water pump associated with this reservoir",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Description field
      {
        collection: "reservoir",
        field: "description",
        special: null,
        interface: "input-multiline",
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 6,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Capacity field
      {
        collection: "reservoir",
        field: "capacity",
        special: null,
        interface: "input",
        options: JSON.stringify({
          placeholder: "Volume in liters",
          iconRight: "water_drop",
        }),
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 7,
        width: "half",
        translations: null,
        note: "Volume capacity in liters",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Location field (geometry)
      {
        collection: "reservoir",
        field: "location",
        special: null,
        interface: "map",
        options: JSON.stringify({
          geometryType: "Point",
        }),
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 8,
        width: "full",
        translations: null,
        note: "GPS coordinates",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Enabled field
      {
        collection: "reservoir",
        field: "enabled",
        special: null,
        interface: "boolean",
        options: JSON.stringify({
          label: "Enabled",
        }),
        display: "boolean",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 9,
        width: "half",
        translations: null,
        note: "Whether the reservoir is enabled",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Notes field
      {
        collection: "reservoir",
        field: "notes",
        special: null,
        interface: "input-multiline",
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 10,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Metadata field
      {
        collection: "reservoir",
        field: "metadata",
        special: "cast-json",
        interface: null,
        options: null,
        display: null,
        display_options: null,
        readonly: false,
        hidden: true,
        sort: 11,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Date created field
      {
        collection: "reservoir",
        field: "date_created",
        special: "date-created",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({
          relative: true,
        }),
        readonly: true,
        hidden: true,
        sort: 12,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // User created field
      {
        collection: "reservoir",
        field: "user_created",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "user",
        display_options: null,
        readonly: true,
        hidden: true,
        sort: 13,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Date updated field
      {
        collection: "reservoir",
        field: "date_updated",
        special: "date-updated",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({
          relative: true,
        }),
        readonly: true,
        hidden: true,
        sort: 14,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // User updated field
      {
        collection: "reservoir",
        field: "user_updated",
        special: "user-updated",
        interface: "select-dropdown-m2o",
        options: null,
        display: "user",
        display_options: null,
        readonly: true,
        hidden: true,
        sort: 15,
        width: "half",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // Insert relationship configurations
    await tx.batchInsert("directus_relations", [
      // Reservoir -> Property relationship
      {
        many_collection: "reservoir",
        many_field: "property",
        one_collection: "property",
        one_field: "reservoirs",
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
      // Reservoir -> Device (reservoir_monitor) relationship
      {
        many_collection: "reservoir",
        many_field: "reservoir_monitor",
        one_collection: "device",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
      // Reservoir -> WaterPump relationship
      {
        many_collection: "reservoir",
        many_field: "water_pump",
        one_collection: "water_pump",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
    ]);

    // Add reservoirs field to property collection
    await tx.batchInsert("directus_fields", [
      {
        collection: "property",
        field: "reservoirs",
        special: "o2m",
        interface: "list-o2m",
        options: null,
        display: "related-values",
        display_options: JSON.stringify({
          template: "{{name}}",
        }),
        readonly: false,
        hidden: false,
        sort: 100, // High sort value to place at end
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // Insert read permission for reservoir
    await tx.batchInsert("directus_permissions", [
      {
        collection: "reservoir",
        action: "read",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            {
              property: {
                account: { users: { role: { _eq: "admin" } } },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
    ]);
  });
}

/**
 * Reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Delete read permission
    await tx("directus_permissions")
      .where({
        collection: "reservoir",
        action: "read",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();

    // Delete reservoirs field from property collection
    await tx("directus_fields")
      .where({ collection: "property", field: "reservoirs" })
      .del();

    // Delete relationships
    await tx("directus_relations")
      .where({ many_collection: "reservoir", many_field: "property" })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "reservoir", many_field: "reservoir_monitor" })
      .del();
    await tx("directus_relations")
      .where({ many_collection: "reservoir", many_field: "water_pump" })
      .del();

    // Delete field configurations
    await tx("directus_fields").where({ collection: "reservoir" }).del();

    // Delete collection configuration
    await tx("directus_collections").where({ collection: "reservoir" }).del();
  });
}
