/**
 * Migration to create project_state table as TimescaleDB hypertable with monthly chunking and compression
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create project_state table with same structure as current_project_state
    await tx.schema.createTable("project_state", (table) => {
      table.bigIncrements("id");
      table.uuid("project").notNullable();
      table.timestamp("date_created").notNullable().defaultTo(knex.fn.now());
      table.timestamp("date_updated").notNullable().defaultTo(knex.fn.now());
      table.timestamp("packet_date").notNullable().defaultTo(knex.fn.now());
      table.string("irrigation_status", 20).notNullable();
      table.string("fertigation_status", 20).notNullable();
      table.string("backwash_status", 20).notNullable();
      table.jsonb("sectors").notNullable();

      // Foreign key constraint
      table.foreign("project").references("id").inTable("project");

      // Index for time-series queries (will be created before hypertable conversion)
      table.index(["packet_date"], "project_state_packet_date_idx");
      table.index(
        ["project", "packet_date"],
        "project_state_project_packet_date_idx"
      );
      table.index(["date_created"], "project_state_date_created_idx");

      // Primary key constraint for (project, packet_date)
      table.primary(["project", "packet_date"]);

      // Check constraints for status values
      table.check(
        "irrigation_status IN ('active', 'inactive', 'error')",
        [],
        "project_state_irrigation_status_check"
      );
      table.check(
        "fertigation_status IN ('active', 'inactive', 'error')",
        [],
        "project_state_fertigation_status_check"
      );
      table.check(
        "backwash_status IN ('active', 'inactive', 'error')",
        [],
        "project_state_backwash_status_check"
      );
    });

    // Ensure TimescaleDB extension is enabled
    await tx.raw(`CREATE EXTENSION IF NOT EXISTS timescaledb;`);

    // Convert table to TimescaleDB hypertable with monthly chunking
    await tx.raw(`
      SELECT create_hypertable(
        'project_state',
        'packet_date',
        chunk_time_interval => INTERVAL '1 month',
        if_not_exists => TRUE
      );
    `);

    // Add compression policy for data older than 3 months
    await tx.raw(`
      ALTER TABLE project_state SET (
        timescaledb.compress,
        timescaledb.compress_segmentby = 'project'
      );
    `);

    await tx.raw(`
      SELECT add_compression_policy(
        'project_state',
        INTERVAL '3 months'
      );
    `);

    // Add retention policy to automatically drop data older than 2 years
    await tx.raw(`
      SELECT add_retention_policy(
        'project_state',
        INTERVAL '2 years'
      );
    `);

    // Add table comment
    await tx.raw(`
      COMMENT ON TABLE project_state IS 'Historical state of irrigation projects based on LIC device status - TimescaleDB hypertable';
    `);

    // Add column comments
    await tx.raw(`
      COMMENT ON COLUMN project_state.id IS 'Primary key - auto-incrementing';
      COMMENT ON COLUMN project_state.project IS 'Foreign key to project table';
      COMMENT ON COLUMN project_state.date_created IS 'When this state record was created';
      COMMENT ON COLUMN project_state.date_updated IS 'When the original current state was last updated';
      COMMENT ON COLUMN project_state.packet_date IS 'When the original device status packet was recorded - partitioning column';
      COMMENT ON COLUMN project_state.irrigation_status IS 'Status of irrigation system: active, inactive, or error';
      COMMENT ON COLUMN project_state.fertigation_status IS 'Status of fertigation system: active, inactive, or error';
      COMMENT ON COLUMN project_state.backwash_status IS 'Status of backwash system: active, inactive, or error';
      COMMENT ON COLUMN project_state.sectors IS 'JSON array of sector states with sector ID, name, and status';
    `);
  });
}

/**
 * Migration to drop project_state hypertable
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Remove compression and retention policies
    await tx.raw(`
      SELECT remove_compression_policy('project_state', if_exists => true);
    `);

    await tx.raw(`
      SELECT remove_retention_policy('project_state', if_exists => true);
    `);

    // Drop hypertable (this automatically removes chunks)
    await tx.schema.dropTableIfExists("project_state");
  });
}
