/**
 * Applies the current state of the Directus config to the database.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-09-06T01:45:45.742Z
 * @description This migration applies the current state of the Directus config to the database.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  // if (true) return; // Skip migration if already applied
  await knex.transaction(async (tx) => {
    // Insert 1 records into directus_policies
    await tx.batchInsert("directus_policies", [
      {
        id: "c2d593ac-d610-465c-8d3b-3a738b8a1a07",
        name: "Basic User Permissions",
        icon: "badge",
        description: null,
        ip_access: null,
        enforce_tfa: false,
        admin_access: false,
        app_access: false,
      },
    ]);

    // Insert 2 records into directus_permissions
    await tx.batchInsert("directus_permissions", [
      {
        collection: "directus_users",
        action: "update",
        permissions: { _and: [{ id: { _eq: "$CURRENT_USER" } }] },
        validation: null,
        presets: null,
        fields:
          "first_name,last_name,password,title,description,language,email_notifications,cpf,phone_number",
        policy: "c2d593ac-d610-465c-8d3b-3a738b8a1a07",
      },
      {
        collection: "directus_users",
        action: "read",
        permissions: { _and: [{ id: { _eq: "$CURRENT_USER" } }] },
        validation: null,
        presets: null,
        fields:
          "first_name,last_name,email,account,accounts,avatar,location,title,description,language,email_notifications,cpf,phone_number,status,id",
        policy: "c2d593ac-d610-465c-8d3b-3a738b8a1a07",
      },
    ]);

    // Insert 1 records into directus_access
    await tx.batchInsert("directus_access", [
      {
        id: "7e88dad4-b487-4a17-a898-40cac95b14a2",
        role: "ac5ba5cb-1d74-4df4-99d3-6758fb49255c",
        user: null,
        policy: "c2d593ac-d610-465c-8d3b-3a738b8a1a07",
        sort: 3,
      },
    ]);
  });
}

/**
 * Reverts the changes made by the up migration.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-09-06T01:45:45.742Z
 * @description This migration reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Delete 1 records from directus_access (reverse of insert)
    await tx("directus_access")
      .where({
        policy: "c2d593ac-d610-465c-8d3b-3a738b8a1a07",
        role: "ac5ba5cb-1d74-4df4-99d3-6758fb49255c",
        user: null,
      })
      .del();

    // Delete 2 records from directus_permissions (reverse of insert)
    await tx("directus_permissions")
      .where({
        collection: "directus_users",
        action: "update",
        policy: "c2d593ac-d610-465c-8d3b-3a738b8a1a07",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "directus_users",
        action: "read",
        policy: "c2d593ac-d610-465c-8d3b-3a738b8a1a07",
      })
      .del();

    // Delete 1 records from directus_policies (reverse of insert)
    await tx("directus_policies")
      .where({ id: "c2d593ac-d610-465c-8d3b-3a738b8a1a07" })
      .del();
  });
}
