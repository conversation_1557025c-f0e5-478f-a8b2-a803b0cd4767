/**
 * Applies Directus configuration for lic_packet collection (historical data)
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // 1) Add lic_packet collection
    await tx.batchInsert("directus_collections", [
      {
        collection: "lic_packet",
        icon: "history",
        note: "Historical packet data from LIC devices. TimescaleDB hypertable with monthly chunking and compression for time-series data.",
        display_template:
          "{{device.identifier}} - {{payload_type}} ({{packet_date}})",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: "packet_date",
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
    ]);

    // 2) Add lic_packet fields
    await tx.batchInsert("directus_fields", [
      // id
      {
        collection: "lic_packet",
        field: "id",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: true,
        sort: 1,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // device (M2O -> device)
      {
        collection: "lic_packet",
        field: "device",
        special: null,
        interface: "select-dropdown-m2o",
        options: JSON.stringify({
          filter: {
            model: { _eq: "LIC" },
          },
        }),
        display: "related-values",
        display_options: JSON.stringify({
          template: "{{identifier}} ({{model}})",
        }),
        readonly: false,
        hidden: false,
        sort: 2,
        width: "half",
        translations: null,
        note: "LIC device that sent this packet",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // date_created
      {
        collection: "lic_packet",
        field: "date_created",
        special: "date-created",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 3,
        width: "half",
        translations: null,
        note: "When this record was created in the system",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // packet_date
      {
        collection: "lic_packet",
        field: "packet_date",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: false,
        hidden: false,
        sort: 4,
        width: "half",
        translations: null,
        note: "Timestamp when the packet was generated by the device",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // payload_type
      {
        collection: "lic_packet",
        field: "payload_type",
        special: null,
        interface: "input",
        options: JSON.stringify({
          trim: true,
          maxLength: 50,
        }),
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 5,
        width: "half",
        translations: null,
        note: "Type/category of the packet payload",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // payload_data
      {
        collection: "lic_packet",
        field: "payload_data",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
          template: JSON.stringify({
            readOnly: true,
          }),
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 6,
        width: "full",
        translations: null,
        note: "JSON payload data from the device (read-only for historical data)",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // 3) Relations for lic_packet -> device
    await tx.batchInsert("directus_relations", [
      {
        many_collection: "lic_packet",
        many_field: "device",
        one_collection: "device",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
    ]);

    // 4) Permissions
    // Reusing the same policy UUID used in other migrations
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // READ permission for lic_packet - scoped by device property ownership
    await tx.batchInsert("directus_permissions", [
      {
        collection: "lic_packet",
        action: "read",
        permissions: {
          _and: [
            {
              device: {
                properties: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              device: {
                properties: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: policyId,
      },
    ]);

    // CREATE permission for lic_packet (for system/MQTT integration use)
    await tx.batchInsert("directus_permissions", [
      {
        collection: "lic_packet",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields: "device,packet_date,payload_type,payload_data",
        policy: policyId,
      },
    ]);

    // UPDATE permission - restricted since this is historical data
    await tx.batchInsert("directus_permissions", [
      {
        collection: "lic_packet",
        action: "update",
        permissions: {
          _and: [
            {
              device: {
                properties: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              device: {
                properties: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "device,packet_date,payload_type,payload_data",
        policy: policyId,
      },
    ]);

    // DELETE permission - restricted since this is historical data
    await tx.batchInsert("directus_permissions", [
      {
        collection: "lic_packet",
        action: "delete",
        permissions: {
          _and: [
            {
              device: {
                properties: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              device: {
                properties: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: policyId,
      },
    ]);
  });
}

/**
 * Reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // Remove permissions
    await tx("directus_permissions")
      .where({
        collection: "lic_packet",
        policy: policyId,
      })
      .del();

    // Remove relations
    await tx("directus_relations")
      .where({
        many_collection: "lic_packet",
        many_field: "device",
      })
      .del();

    // Remove fields
    await tx("directus_fields").where({ collection: "lic_packet" }).del();

    // Remove collection
    await tx("directus_collections").where({ collection: "lic_packet" }).del();
  });
}
