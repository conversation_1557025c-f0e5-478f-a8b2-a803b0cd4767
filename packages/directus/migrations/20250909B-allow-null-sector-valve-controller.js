/**
 * Migration to allow NULL values for valve_controller and valve_controller_output in sector table
 * This is needed to support device removal from properties without breaking sector references
 */
export async function up(knex) {
	// Remove the unique constraint that includes valve_controller_output first
	await knex.raw(`
		ALTER TABLE public.sector 
		DROP CONSTRAINT IF EXISTS sector_valve_controller_valve_controller_output_unq;
	`);

	// Allow NULL values for valve_controller column
	await knex.raw(`
		ALTER TABLE public.sector 
		ALTER COLUMN valve_controller DROP NOT NULL;
	`);

	// Allow NULL values for valve_controller_output column
	await knex.raw(`
		ALTER TABLE public.sector 
		ALTER COLUMN valve_controller_output DROP NOT NULL;
	`);

	// Update the valve_controller_output constraint to allow NULL
	await knex.raw(`
		ALTER TABLE public.sector 
		DROP CONSTRAINT IF EXISTS chk_valve_controller_output;
	`);

	await knex.raw(`
		ALTER TABLE public.sector 
		ADD CONSTRAINT chk_valve_controller_output 
		CHECK (valve_controller_output IS NULL OR (valve_controller_output >= 1 AND valve_controller_output <= 4));
	`);

	// Re-add the unique constraint but allow NULL combinations
	// In PostgreSQL, NULL values are not considered equal, so multiple NULL combinations are allowed
	await knex.raw(`
		ALTER TABLE public.sector 
		ADD CONSTRAINT sector_valve_controller_valve_controller_output_unq 
		UNIQUE (valve_controller, valve_controller_output);
	`);

	// Update comments to reflect the changes
	await knex.raw(`
		COMMENT ON COLUMN public.sector.valve_controller IS 'Foreign key to device table (valve controller devices) - nullable to support device removal';
	`);

	await knex.raw(`
		COMMENT ON COLUMN public.sector.valve_controller_output IS 'Output port on valve controller (1-4) - nullable when valve_controller is NULL';
	`);
}

export async function down(knex) {
	// Remove the unique constraint
	await knex.raw(`
		ALTER TABLE public.sector 
		DROP CONSTRAINT IF EXISTS sector_valve_controller_valve_controller_output_unq;
	`);

	// For rollback safety, set NULL valve_controller values to a placeholder
	// This is a simplified approach - in practice, you'd want more sophisticated logic
	await knex.raw(`
		UPDATE public.sector 
		SET valve_controller = (
			SELECT d.id 
			FROM device d 
			INNER JOIN property_device pd ON d.id = pd.device 
			INNER JOIN project p ON p.property = pd.property
			WHERE p.id = sector.project 
			  AND d.device_model LIKE '%VC%'
			  AND pd.end_date IS NULL
			LIMIT 1
		),
		valve_controller_output = 1
		WHERE valve_controller IS NULL;
	`);

	// Restore NOT NULL constraints
	await knex.raw(`
		ALTER TABLE public.sector 
		ALTER COLUMN valve_controller SET NOT NULL;
	`);

	await knex.raw(`
		ALTER TABLE public.sector 
		ALTER COLUMN valve_controller_output SET NOT NULL;
	`);

	// Restore original constraint
	await knex.raw(`
		ALTER TABLE public.sector 
		DROP CONSTRAINT IF EXISTS chk_valve_controller_output;
	`);

	await knex.raw(`
		ALTER TABLE public.sector 
		ADD CONSTRAINT chk_valve_controller_output 
		CHECK (valve_controller_output >= 1 AND valve_controller_output <= 4);
	`);

	// Re-add the unique constraint
	await knex.raw(`
		ALTER TABLE public.sector 
		ADD CONSTRAINT sector_valve_controller_valve_controller_output_unq 
		UNIQUE (valve_controller, valve_controller_output);
	`);

	// Restore original comments
	await knex.raw(`
		COMMENT ON COLUMN public.sector.valve_controller IS 'Foreign key to device table (valve controller devices)';
	`);

	await knex.raw(`
		COMMENT ON COLUMN public.sector.valve_controller_output IS 'Output port on valve controller (1-4)';
	`);
}