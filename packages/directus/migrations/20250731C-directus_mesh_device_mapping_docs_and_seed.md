# Directus config for mesh_device_mapping and seed/docs updates

This placeholder file exists solely to document that Task 2 includes:

- Directus config migration for mesh_device_mapping and property_device.current_mesh_device_mapping (implemented in 20250731B-directus_mesh_device_mapping_config.js).
- Seed script already updated earlier to generate mesh_device_mapping and call im_update_current_mesh_device_mapping as part of seeding (see directus/src/seed/index.ts section "Seed mesh_device_mapping for mesh PDs to active LIC PDs").
- Documentation updates pending in docs/001-ENTITIES.md, docs/002-ENTITY_DIAGRAMS.md, and docs/DDL.md to include:
  - mesh_device_mapping entity, columns, constraints, and triggers (BEFORE enforce and AFTER updater call).
  - property_device.current_mesh_device_mapping column and im_update_current_mesh_device_mapping function reference.
  - Entity diagram arrows: mesh_device_mapping.mesh_property_device -> property_device; mesh_device_mapping.lic_property_device -> property_device; property_device.current_mesh_device_mapping -> mesh_device_mapping.

These docs updates will be applied in subsequent commits within the same branch to keep migration separation clean.
