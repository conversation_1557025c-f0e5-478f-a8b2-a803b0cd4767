/**
 * Applies Directus configuration for reservoir state collections
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Collections
    await tx.batchInsert("directus_collections", [
      {
        collection: "current_reservoir_state",
        icon: "water",
        note: "Current state of reservoir automation based on automation_report MQTT messages.",
        display_template:
          "{{reservoir.name}} - Started: {{start_time}} | Restart: {{restart_time}} | End: {{end_time}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
      {
        collection: "reservoir_state",
        icon: "history",
        note: "Historical reservoir automation states (TimescaleDB hypertable).",
        display_template:
          "{{reservoir.name}} - {{packet_date}} | Started: {{start_time}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
    ]);

    // Fields: current_reservoir_state
    await tx.batchInsert("directus_fields", [
      { collection: "current_reservoir_state", field: "id", special: null, interface: "input", options: null, display: "raw", display_options: null, readonly: true, hidden: true, sort: 1, width: "full", translations: null, note: null, conditions: null, required: false, group: null, validation: null, validation_message: null },
      { collection: "current_reservoir_state", field: "reservoir", special: null, interface: "select-dropdown-m2o", options: null, display: "related-values", display_options: JSON.stringify({ template: "{{name}} ({{property.name}})" }), readonly: false, hidden: false, sort: 2, width: "half", translations: null, note: "Reservoir this state belongs to", conditions: null, required: true, group: null, validation: null, validation_message: null },
      { collection: "current_reservoir_state", field: "packet_date", special: null, interface: "datetime", options: null, display: "datetime", display_options: JSON.stringify({ relative: true }), readonly: false, hidden: false, sort: 3, width: "half", translations: null, note: "When the automation_report packet was recorded", conditions: null, required: true, group: null, validation: null, validation_message: null },
      { collection: "current_reservoir_state", field: "start_time", special: null, interface: "datetime", options: null, display: "datetime", display_options: JSON.stringify({ relative: true }), readonly: false, hidden: false, sort: 4, width: "half", translations: null, note: "When automation started", conditions: null, required: false, group: null, validation: null, validation_message: null },
      { collection: "current_reservoir_state", field: "restart_time", special: null, interface: "datetime", options: null, display: "datetime", display_options: JSON.stringify({ relative: true }), readonly: false, hidden: false, sort: 5, width: "half", translations: null, note: "When automation restarted (nullable)", conditions: null, required: false, group: null, validation: null, validation_message: null },
      { collection: "current_reservoir_state", field: "end_time", special: null, interface: "datetime", options: null, display: "datetime", display_options: JSON.stringify({ relative: true }), readonly: false, hidden: false, sort: 6, width: "half", translations: null, note: "When automation ended (nullable)", conditions: null, required: false, group: null, validation: null, validation_message: null },
      { collection: "current_reservoir_state", field: "date_created", special: "date-created", interface: "datetime", options: null, display: "datetime", display_options: JSON.stringify({ relative: true }), readonly: true, hidden: false, sort: 7, width: "half", translations: null, note: "Record creation time", conditions: null, required: false, group: null, validation: null, validation_message: null },
      { collection: "current_reservoir_state", field: "date_updated", special: "date-updated", interface: "datetime", options: null, display: "datetime", display_options: JSON.stringify({ relative: true }), readonly: true, hidden: false, sort: 8, width: "half", translations: null, note: "Last update time", conditions: null, required: false, group: null, validation: null, validation_message: null },
    ]);

    // Fields: reservoir_state (read-only)
    await tx.batchInsert("directus_fields", [
      { collection: "reservoir_state", field: "id", special: null, interface: "input", options: null, display: "raw", display_options: null, readonly: true, hidden: false, sort: 1, width: "full", translations: null, note: "Auto-incrementing primary key", conditions: null, required: false, group: null, validation: null, validation_message: null },
      { collection: "reservoir_state", field: "reservoir", special: null, interface: "select-dropdown-m2o", options: null, display: "related-values", display_options: JSON.stringify({ template: "{{name}} ({{property.name}})" }), readonly: true, hidden: false, sort: 2, width: "half", translations: null, note: "Reservoir this state belongs to", conditions: null, required: true, group: null, validation: null, validation_message: null },
      { collection: "reservoir_state", field: "packet_date", special: null, interface: "datetime", options: null, display: "datetime", display_options: JSON.stringify({ relative: true }), readonly: true, hidden: false, sort: 3, width: "half", translations: null, note: "When the automation_report packet was recorded", conditions: null, required: true, group: null, validation: null, validation_message: null },
      { collection: "reservoir_state", field: "start_time", special: null, interface: "datetime", options: null, display: "datetime", display_options: JSON.stringify({ relative: true }), readonly: true, hidden: false, sort: 4, width: "half", translations: null, note: "When automation started", conditions: null, required: false, group: null, validation: null, validation_message: null },
      { collection: "reservoir_state", field: "restart_time", special: null, interface: "datetime", options: null, display: "datetime", display_options: JSON.stringify({ relative: true }), readonly: true, hidden: false, sort: 5, width: "half", translations: null, note: "When automation restarted (nullable)", conditions: null, required: false, group: null, validation: null, validation_message: null },
      { collection: "reservoir_state", field: "end_time", special: null, interface: "datetime", options: null, display: "datetime", display_options: JSON.stringify({ relative: true }), readonly: true, hidden: false, sort: 6, width: "half", translations: null, note: "When automation ended (nullable)", conditions: null, required: false, group: null, validation: null, validation_message: null },
      { collection: "reservoir_state", field: "date_created", special: null, interface: "datetime", options: null, display: "datetime", display_options: JSON.stringify({ relative: true }), readonly: true, hidden: false, sort: 7, width: "half", translations: null, note: "Record creation time", conditions: null, required: false, group: null, validation: null, validation_message: null },
    ]);

    // Relations
    await tx.batchInsert("directus_relations", [
      {
        many_collection: "current_reservoir_state",
        many_field: "reservoir",
        one_collection: "reservoir",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
      {
        many_collection: "reservoir_state",
        many_field: "reservoir",
        one_collection: "reservoir",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
    ]);

    // Permissions (reuse policy used in other migrations)
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // READ current_reservoir_state (scoped by reservoir property ownership)
    await tx.batchInsert("directus_permissions", [
      {
        collection: "current_reservoir_state",
        action: "read",
        permissions: {
          _and: [
            { reservoir: { property: { account: { users: { user: { _eq: "$CURRENT_USER" } } } } } },
            { reservoir: { property: { account: { users: { role: { _eq: "admin" } } } } } },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: policyId,
      },
    ]);

    // CREATE/UPDATE/DELETE for current_reservoir_state (system-managed)
    await tx.batchInsert("directus_permissions", [
      {
        collection: "current_reservoir_state",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields: "reservoir,packet_date,start_time,restart_time,end_time",
        policy: policyId,
      },
      {
        collection: "current_reservoir_state",
        action: "update",
        permissions: {
          _and: [
            { reservoir: { property: { account: { users: { user: { _eq: "$CURRENT_USER" } } } } } },
            { reservoir: { property: { account: { users: { role: { _eq: "admin" } } } } } },
          ],
        },
        validation: null,
        presets: null,
        fields: "reservoir,packet_date,start_time,restart_time,end_time",
        policy: policyId,
      },
      {
        collection: "current_reservoir_state",
        action: "delete",
        permissions: {
          _and: [
            { reservoir: { property: { account: { users: { user: { _eq: "$CURRENT_USER" } } } } } },
            { reservoir: { property: { account: { users: { role: { _eq: "admin" } } } } } },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: policyId,
      },
    ]);

    // READ reservoir_state (read-only history)
    await tx.batchInsert("directus_permissions", [
      {
        collection: "reservoir_state",
        action: "read",
        permissions: {
          _and: [
            { reservoir: { property: { account: { users: { user: { _eq: "$CURRENT_USER" } } } } } },
            { reservoir: { property: { account: { users: { role: { _eq: "admin" } } } } } },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: policyId,
      },
    ]);
  });
}

/**
 * Reverts the changes made by the up migration
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    await tx("directus_permissions").where({ collection: "current_reservoir_state", policy: policyId }).del();
    await tx("directus_permissions").where({ collection: "reservoir_state", policy: policyId }).del();

    await tx("directus_relations").where({ many_collection: "current_reservoir_state", many_field: "reservoir" }).del();
    await tx("directus_relations").where({ many_collection: "reservoir_state", many_field: "reservoir" }).del();

    await tx("directus_fields").where({ collection: "current_reservoir_state" }).del();
    await tx("directus_fields").where({ collection: "reservoir_state" }).del();

    await tx("directus_collections").where({ collection: "current_reservoir_state" }).del();
    await tx("directus_collections").where({ collection: "reservoir_state" }).del();
  });
}

