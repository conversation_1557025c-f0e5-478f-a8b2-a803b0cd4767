/**
 * Applies the current state of the Directus config to the database.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-05T04:05:31.904Z
 * @description This migration applies the current state of the Directus config to the database.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Insert 7 records into directus_permissions
    await tx.batchInsert("directus_permissions", [
      {
        collection: "account_user",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields: "account,user,role,start_date,end_date,notes,metadata",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "irrigation_plan",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "name,is_enabled,start_time,days_of_week,start_date,end_date,project,steps,description,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "irrigation_plan_step",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "irrigation_plan,sector,description,order,duration_seconds,delay_seconds_after,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "project",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "property,name,localized_irrigation_controller,start_date,end_date,irrigation_water_pump,fertigation_water_pump,sectors,irrigation_plans,description,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "property",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "account,name,timezone,point,address_postal_code,address_street_name,address_street_number,address_complement,address_neighborhood,address_city,address_state,address_country,notes,projects,devices,water_pumps,metadata,backwash_duration_minutes,backwash_period_minutes,backwash_delay_seconds,rain_gauge_enabled,rain_gauge_resolution_mm,precipitation_volume_limit_mm,precipitation_suspended_duration_hours",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "sector",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "project,name,valve_controller,valve_controller_output,area,polygon,steps,description,metadata,notes",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "water_pump",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "identifier,label,pump_type,pump_model,has_frequency_inverter,monitor_operation,property,water_pump_controller,notes,metadata",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "reservoir",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "property,name,reservoir_monitor,water_pump,description,capacity,location,enabled,notes,metadata",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
    ]);

    // Update 2 records in directus_permissions
    await tx("directus_permissions")
      .where({
        collection: "device",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({ fields: "identifier,model,metadata,notes" });
    await tx("directus_permissions")
      .where({
        collection: "property_device",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({ fields: "start_date,end_date,property,device,metadata,notes" });
  });
}

/**
 * Reverts the changes made by the up migration.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-05T04:05:31.904Z
 * @description This migration reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Revert 2 updates in directus_permissions
    await tx("directus_permissions")
      .where({
        collection: "device",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "device",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });
    await tx("directus_permissions")
      .where({
        collection: "property_device",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        collection: "property_device",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields: "*",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      });

    // Delete 7 records from directus_permissions (reverse of insert)
    await tx("directus_permissions")
      .where({
        collection: "account_user",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan_step",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "property",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "sector",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "reservoir",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
  });
}
