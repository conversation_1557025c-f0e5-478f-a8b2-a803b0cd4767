/**
 * Applies Directus configuration for lic_state_history collection
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // 1) Add lic_state_history collection
    await tx.batchInsert("directus_collections", [
      {
        collection: "lic_state_history",
        icon: "history",
        note: "Historical tracking of LIC device state changes - TimescaleDB hypertable",
        display_template: "{{device.identifier}} - {{state_date}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: "state_date",
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
    ]);

    // 2) Add lic_state_history fields
    await tx.batchInsert("directus_fields", [
      // device (M2O -> device) - Primary key component
      {
        collection: "lic_state_history",
        field: "device",
        special: null,
        interface: "select-dropdown-m2o",
        options: JSON.stringify({
          filter: {
            model: { _eq: "LIC" },
          },
          template: "{{identifier}} ({{model}})",
        }),
        display: "related-values",
        display_options: JSON.stringify({
          template: "{{identifier}} ({{model}})",
        }),
        readonly: true,
        hidden: false,
        sort: 1,
        width: "half",
        translations: null,
        note: "LIC device associated with this historical state",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // state_date (Primary key component)
      {
        collection: "lic_state_history",
        field: "state_date",
        special: null,
        interface: "datetime",
        options: JSON.stringify({
          includeSeconds: true,
        }),
        display: "datetime",
        display_options: JSON.stringify({
          relative: false,
          format: "YYYY-MM-DD HH:mm:ss",
        }),
        readonly: true,
        hidden: false,
        sort: 2,
        width: "half",
        translations: null,
        note: "Timestamp when this state was recorded",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // lic (JSON field)
      {
        collection: "lic_state_history",
        field: "lic",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 3,
        width: "full",
        translations: null,
        note: "Codec configuration and metadata from LICState.lic property",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // groups (JSON field)
      {
        collection: "lic_state_history",
        field: "groups",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 4,
        width: "full",
        translations: null,
        note: "Groups array from LICState.groups property",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // devices (JSON field)
      {
        collection: "lic_state_history",
        field: "devices",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 5,
        width: "full",
        translations: null,
        note: "Devices array from LICState.devices property",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // mesh_devices (JSON field)
      {
        collection: "lic_state_history",
        field: "mesh_devices",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 6,
        width: "full",
        translations: null,
        note: "Mesh devices array from LICState.meshDevices property",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // schedules (JSON field)
      {
        collection: "lic_state_history",
        field: "schedules",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 7,
        width: "full",
        translations: null,
        note: "Scheduling array from LICState.schedules property",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // sector_schedules (JSON field)
      {
        collection: "lic_state_history",
        field: "sector_schedules",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 8,
        width: "full",
        translations: null,
        note: "Sector scheduling array from LICState.sectorSchedules property",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // device_schedules (JSON field)
      {
        collection: "lic_state_history",
        field: "device_schedules",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 9,
        width: "full",
        translations: null,
        note: "Device scheduling array from LICState.deviceSchedules property",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Request timestamp fields
      {
        collection: "lic_state_history",
        field: "last_devices_request",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 10,
        width: "half",
        translations: null,
        note: "Timestamp of last devices configuration request",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "lic_state_history",
        field: "last_scheduling_request",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 11,
        width: "half",
        translations: null,
        note: "Timestamp of last scheduling configuration request",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "lic_state_history",
        field: "last_dev_scheduling_request",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 12,
        width: "half",
        translations: null,
        note: "Timestamp of last device scheduling configuration request",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "lic_state_history",
        field: "last_automation_request",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 13,
        width: "half",
        translations: null,
        note: "Timestamp of last automation configuration request",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "lic_state_history",
        field: "last_config_request",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 14,
        width: "half",
        translations: null,
        note: "Timestamp of last config configuration request",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Current timestamp fields from InfoPackage
      {
        collection: "lic_state_history",
        field: "current_devices_timestamp",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 15,
        width: "half",
        translations: null,
        note: "Current devices timestamp from InfoPackage",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "lic_state_history",
        field: "current_scheduling_timestamp",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 16,
        width: "half",
        translations: null,
        note: "Current scheduling timestamp from InfoPackage",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "lic_state_history",
        field: "current_dev_scheduling_timestamp",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 17,
        width: "half",
        translations: null,
        note: "Current device scheduling timestamp from InfoPackage",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "lic_state_history",
        field: "current_automation_timestamp",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 18,
        width: "half",
        translations: null,
        note: "Current automation timestamp from InfoPackage",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      {
        collection: "lic_state_history",
        field: "current_config_timestamp",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 19,
        width: "half",
        translations: null,
        note: "Current config timestamp from InfoPackage",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // 3) Relations for lic_state_history -> device
    await tx.batchInsert("directus_relations", [
      {
        many_collection: "lic_state_history",
        many_field: "device",
        one_collection: "device",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
    ]);

    // 4) Permissions - Read-only access with same scoping as lic_state
    // Reusing the same policy UUID used in other migrations
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // READ permission for lic_state_history - scoped by device property ownership
    await tx.batchInsert("directus_permissions", [
      {
        collection: "lic_state_history",
        action: "read",
        permissions: {
          _and: [
            {
              device: {
                properties: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              device: {
                properties: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: policyId,
      },
    ]);

    // Note: No CREATE, UPDATE, or DELETE permissions for lic_state_history
    // This table is populated automatically via triggers and should be read-only
  });
}

/**
 * Reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // Remove permissions
    await tx("directus_permissions")
      .where({
        collection: "lic_state_history",
        policy: policyId,
      })
      .del();

    // Remove relations
    await tx("directus_relations")
      .whereIn("many_collection", ["lic_state_history"])
      .del();

    // Remove fields
    await tx("directus_fields")
      .where({ collection: "lic_state_history" })
      .del();

    // Remove collection
    await tx("directus_collections")
      .where({ collection: "lic_state_history" })
      .del();
  });
}
