/**
 * Migration to allow NULL values for localized_irrigation_controller in project table
 * This is needed to support device removal from properties without breaking project references
 */
export async function up(knex) {
	// Allow NULL values for localized_irrigation_controller column
	await knex.raw(`
		ALTER TABLE public.project 
		ALTER COLUMN localized_irrigation_controller DROP NOT NULL;
	`);

	// Update the comment to reflect the change
	await knex.raw(`
		COMMENT ON COLUMN public.project.localized_irrigation_controller IS 'Foreign key to device table (LIC devices) - nullable to support device removal';
	`);
}

export async function down(knex) {
	// First, set any NULL values to a placeholder or handle them appropriately
	// For rollback safety, we'll need to ensure no NULL values exist
	await knex.raw(`
		UPDATE public.project 
		SET localized_irrigation_controller = (
			SELECT d.id 
			FROM device d 
			INNER JOIN property_device pd ON d.id = pd.device 
			WHERE pd.property = project.property 
			  AND d.device_model = 'LIC'
			  AND pd.end_date IS NULL
			LIMIT 1
		)
		WHERE localized_irrigation_controller IS NULL;
	`);

	// Then restore NOT NULL constraint
	await knex.raw(`
		ALTER TABLE public.project 
		ALTER COLUMN localized_irrigation_controller SET NOT NULL;
	`);

	// Restore original comment
	await knex.raw(`
		COMMENT ON COLUMN public.project.localized_irrigation_controller IS 'Foreign key to device table (LIC devices)';
	`);
}