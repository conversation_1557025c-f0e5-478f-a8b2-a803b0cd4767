/**
 * Migration to drop the retention policy from the project_state hypertable.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Remove the retention policy from the project_state hypertable
    await tx.raw(`
      SELECT remove_retention_policy('project_state', if_exists => true);
    `);
  });
}

/**
 * Migration to add back the retention policy to the project_state hypertable.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Add back the retention policy to drop data older than 2 years
    await tx.raw(`
      SELECT add_retention_policy(
        'project_state',
        INTERVAL '2 years'
      );
    `);
  });
}
