/**
 * Updates Directus field configurations to reflect nullable foreign key changes
 * for project.localized_irrigation_controller and sector valve controller fields
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Update project.localized_irrigation_controller field to not be required
    await tx("directus_fields")
      .where({
        collection: "project",
        field: "localized_irrigation_controller",
      })
      .update({
        required: false,
        note: "LIC device for this project - nullable to support device removal",
      });

    // Update sector.valve_controller field to not be required
    await tx("directus_fields")
      .where({
        collection: "sector",
        field: "valve_controller",
      })
      .update({
        required: false,
        note: "Valve controller device for this sector - nullable to support device removal",
      });

    // Update sector.valve_controller_output field to not be required
    await tx("directus_fields")
      .where({
        collection: "sector",
        field: "valve_controller_output",
      })
      .update({
        required: false,
        note: "Output port on valve controller (1-4) - nullable when valve_controller is NULL",
      });
  });
}

export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Restore project.localized_irrigation_controller field as required
    await tx("directus_fields")
      .where({
        collection: "project",
        field: "localized_irrigation_controller",
      })
      .update({
        required: true,
        note: "LIC device for this project",
      });

    // Restore sector.valve_controller field as required
    await tx("directus_fields")
      .where({
        collection: "sector",
        field: "valve_controller",
      })
      .update({
        required: true,
        note: "Valve controller device for this sector",
      });

    // Restore sector.valve_controller_output field as required
    await tx("directus_fields")
      .where({
        collection: "sector",
        field: "valve_controller_output",
      })
      .update({
        required: true,
        note: "Output port on valve controller (1-4)",
      });
  });
}