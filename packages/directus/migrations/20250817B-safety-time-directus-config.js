/**
 * Applies Directus configuration for the safety_time_minutes field in reservoir collection.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Insert field configuration for safety_time_minutes
    await tx.batchInsert("directus_fields", [
      {
        collection: "reservoir",
        field: "safety_time_minutes",
        special: null,
        interface: "input",
        options: JSON.stringify({
          placeholder: "Safety time in minutes",
          iconRight: "timer",
          min: 1,
          step: 1,
        }),
        display: null,
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 7,
        width: "half",
        translations: null,
        note: "Safety time in minutes for pump operation when reservoir is being refilled",
        conditions: null,
        required: false,
        group: null,
        validation: JSON.stringify({
          _or: [
            { safety_time_minutes: { _gte: 1 } },
            { safety_time_minutes: { _null: true } },
          ],
        }),
        validation_message: "Safety time must be at least 1 minute",
      },
    ]);
  });
}

/**
 * Reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Delete field configuration
    await tx("directus_fields")
      .where({ collection: "reservoir", field: "safety_time_minutes" })
      .del();
  });
}
