/**
 * Updates reservoir permissions to include safety_time_minutes field in create and update actions.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Update create permission to include safety_time_minutes field
    await tx("directus_permissions")
      .where({
        collection: "reservoir",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "property,name,reservoir_monitor,water_pump,description,capacity,safety_time_minutes,location,enabled,notes,metadata",
      });

    // Update update permission to include safety_time_minutes field
    await tx("directus_permissions")
      .where({
        collection: "reservoir",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "property,name,reservoir_monitor,water_pump,description,capacity,safety_time_minutes,location,enabled,notes,metadata",
      });
  });
}

/**
 * Reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Revert create permission to exclude safety_time_minutes field
    await tx("directus_permissions")
      .where({
        collection: "reservoir",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "property,name,reservoir_monitor,water_pump,description,capacity,location,enabled,notes,metadata",
      });

    // Revert update permission to exclude safety_time_minutes field
    await tx("directus_permissions")
      .where({
        collection: "reservoir",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "property,name,reservoir_monitor,water_pump,description,capacity,location,enabled,notes,metadata",
      });
  });
}