/**
 * Migration to add backwash_pump_type field configuration to Directus
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Add backwash_pump_type field configuration to directus_fields
    await tx.batchInsert("directus_fields", [
      {
        collection: "project",
        field: "backwash_pump_type",
        special: null,
        interface: "select-dropdown",
        options: JSON.stringify({
          choices: [
            { text: "Irrigation Pump", value: "IRRIGATION" },
            { text: "Fertigation Pump", value: "FERTIGATION" }
          ],
          allow_other: false,
          allow_none: true,
          placeholder: "Select backwash pump type..."
        }),
        display: "labels",
        display_options: JSON.stringify({
          choices: [
            { text: "Irrigation Pump", value: "IRRIGATION", foreground: "#FFFFFF", background: "#2563EB" },
            { text: "Fertigation Pump", value: "FERTIGATION", foreground: "#FFFFFF", background: "#059669" }
          ],
          show_selection: true,
          format: false
        }),
        readonly: false,
        hidden: false,
        sort: 16, // After backwash_period_seconds (sort: 15)
        width: "half",
        translations: null,
        note: "Type of pump to use for backwashing operations. Leave empty to disable backwashing.",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // Update permissions to include backwash_pump_type in create and update actions
    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "property,name,localized_irrigation_controller,start_date,end_date,irrigation_water_pump,fertigation_water_pump,sectors,irrigation_plans,description,metadata,notes,pipe_wash_time_seconds,backwash_duration_seconds,backwash_period_seconds,backwash_pump_type",
      });

    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "property,name,localized_irrigation_controller,start_date,end_date,irrigation_water_pump,fertigation_water_pump,sectors,irrigation_plans,description,metadata,notes,backwash_period_seconds,backwash_duration_seconds,pipe_wash_time_seconds,backwash_pump_type",
      });
  });
}

/**
 * Migration to remove backwash_pump_type field configuration from Directus
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Remove backwash_pump_type field configuration
    await tx("directus_fields")
      .where({ collection: "project", field: "backwash_pump_type" })
      .del();

    // Revert permissions to exclude backwash_pump_type
    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "create",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "property,name,localized_irrigation_controller,start_date,end_date,irrigation_water_pump,fertigation_water_pump,sectors,irrigation_plans,description,metadata,notes,pipe_wash_time_seconds,backwash_duration_seconds,backwash_period_seconds",
      });

    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "update",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .update({
        fields:
          "property,name,localized_irrigation_controller,start_date,end_date,irrigation_water_pump,fertigation_water_pump,sectors,irrigation_plans,description,metadata,notes,backwash_period_seconds,backwash_duration_seconds,pipe_wash_time_seconds",
      });
  });
}