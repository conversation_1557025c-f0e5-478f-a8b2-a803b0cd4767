/**
 * Migration to create BEFORE INSERT trigger to set current_reservoir_state.id = reservoir field value
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(`
      CREATE OR REPLACE FUNCTION set_reservoir_state_id()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.id = NEW.reservoir;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await tx.raw(`
      CREATE TRIGGER set_reservoir_state_id_trigger
      BEFORE INSERT ON current_reservoir_state
      FOR EACH ROW
      EXECUTE FUNCTION set_reservoir_state_id();
    `);
  });
}

/**
 * Migration to remove the trigger function and trigger
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(
      `DROP TRIGGER IF EXISTS set_reservoir_state_id_trigger ON current_reservoir_state;`
    );
    await tx.raw(`DROP FUNCTION IF EXISTS set_reservoir_state_id();`);
  });
}

