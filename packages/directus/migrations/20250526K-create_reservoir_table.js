/**
 * This migration file creates the reservoir table.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.createTable("reservoir", (table) => {
      table
        .uuid("id")
        .primary()
        .notNullable()
        .defaultTo(tx.raw("gen_random_uuid()"))
        .comment("Unique identifier for the reservoir (UUID)");
      table
        .uuid("property")
        .notNullable()
        .references("id")
        .inTable("property")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT")
        .comment("ID of the property to which this reservoir belongs");
      table.string("name", 255).notNullable().comment("Name of the reservoir");
      table
        .uuid("reservoir_monitor")
        .nullable()
        .references("id")
        .inTable("device")
        .unique()
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT")
        .comment("ID of the RM device monitoring this reservoir (optional)");
      table
        .uuid("water_pump")
        .nullable()
        .references("id")
        .inTable("water_pump")
        .unique()
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT")
        .comment(
          "ID of the water pump associated with this reservoir (optional)"
        );
      table
        .text("description")
        .nullable()
        .comment("Brief description of the reservoir");
      table
        .decimal("capacity", 15, 3)
        .nullable()
        .comment("Volume capacity in liters");
      table
        .specificType("location", "geometry(Point,4326)")
        .nullable()
        .comment("GPS coordinates using PostGIS");
      table.text("notes").nullable().comment("Additional notes");
      table
        .timestamp("date_created", { useTz: true })
        .notNullable()
        .defaultTo(tx.fn.now())
        .comment("Date and time when the reservoir was created");
      table
        .uuid("user_created")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT")
        .comment("ID of the user who created the reservoir");
      table
        .timestamp("date_updated", { useTz: true })
        .nullable()
        .comment("Date and time when the reservoir was last updated");
      table
        .uuid("user_updated")
        .nullable()
        .references("id")
        .inTable("directus_users")
        .onDelete("RESTRICT")
        .onUpdate("RESTRICT")
        .comment("ID of the user who last updated the reservoir");
      table
        .jsonb("metadata")
        .nullable()
        .comment("Additional metadata for the reservoir (JSON)");
      table
        .boolean("enabled")
        .notNullable()
        .defaultTo(true)
        .comment("Soft delete flag - whether the reservoir is enabled");

      // Create unique constraint for property + name combination
      table.unique(["property", "name"], {
        indexName: "reservoir_property_name_unq",
      });

      // Create indexes for better query performance
      table.index("property");
      table.index("reservoir_monitor");
      table.index("water_pump");
    });

    // Add trigger function and trigger to ensure only SERVICE water pumps can be associated with reservoirs
    await tx.raw(`
      CREATE OR REPLACE FUNCTION check_reservoir_water_pump_service_only()
      RETURNS trigger AS $$
      BEGIN
        IF NEW.water_pump IS NOT NULL THEN
          PERFORM 1 FROM water_pump WHERE id = NEW.water_pump AND pump_type = 'SERVICE';
          IF NOT FOUND THEN
            RAISE EXCEPTION 'Associated water_pump must have pump_type = SERVICE';
          END IF;
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);
    await tx.raw(`
      CREATE TRIGGER trg_reservoir_water_pump_service_only
      BEFORE INSERT OR UPDATE ON reservoir
      FOR EACH ROW
      EXECUTE FUNCTION check_reservoir_water_pump_service_only();
    `);

    // Create trigger to automatically update date_updated
    await tx.raw(`
      CREATE TRIGGER set_reservoir_date_updated
      BEFORE UPDATE ON reservoir
      FOR EACH ROW
      EXECUTE FUNCTION update_timestamp_column();
    `);
  });
}

/**
 * This migration file is a placeholder for the down migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw(
      "DROP TRIGGER IF EXISTS set_reservoir_date_updated ON reservoir;"
    );
    await tx.raw(
      "DROP TRIGGER IF EXISTS reservoir_water_pump_service_only_trigger ON reservoir;"
    );
    await tx.raw(
      "DROP FUNCTION IF EXISTS check_reservoir_water_pump_service_only();"
    );
    await tx.schema.dropTableIfExists("reservoir");
  });
}
