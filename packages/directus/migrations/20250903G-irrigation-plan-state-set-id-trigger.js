/**
 * Migration to create BEFORE INSERT trigger to set current_irrigation_plan_state.id = irrigation_plan field value
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create trigger function to set id to irrigation_plan value
    await tx.raw(`
      CREATE OR REPLACE FUNCTION set_irrigation_plan_state_id()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Set id to the same value as irrigation_plan
        NEW.id = NEW.irrigation_plan;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Create the BEFORE INSERT trigger on current_irrigation_plan_state table
    await tx.raw(`
      CREATE TRIGGER set_irrigation_plan_state_id_trigger
      BEFORE INSERT ON current_irrigation_plan_state
      FOR EACH ROW
      EXECUTE FUNCTION set_irrigation_plan_state_id();
    `);
  });
}

/**
 * Migration to remove the trigger function and trigger
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Drop trigger
    await tx.raw(
      `DROP TRIGGER IF EXISTS set_irrigation_plan_state_id_trigger ON current_irrigation_plan_state;`
    );

    // Drop function
    await tx.raw(`DROP FUNCTION IF EXISTS set_irrigation_plan_state_id();`);
  });
}