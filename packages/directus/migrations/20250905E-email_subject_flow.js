/**
 * Applies the current state of the Directus config to the database.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-09-05T22:34:57.287Z
 * @description This migration applies the current state of the Directus config to the database.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  // if (true) return; // Skip migration if already applied
  await knex.transaction(async (tx) => {
    await tx("directus_settings").update({
      project_name: "ByAGRO Irriga+",
      public_registration: true,
      public_registration_verify_email: true,
      public_registration_role: "ac5ba5cb-1d74-4df4-99d3-6758fb49255c",
    });

    // Insert 1 records into directus_flows
    await tx.batchInsert("directus_flows", [
      {
        id: "94d7f959-0996-418b-a29f-171821c25f81",
        name: "Verification Email  Subject",
        icon: "bolt",
        color: null,
        description: null,
        status: "active",
        trigger: "event",
        accountability: "all",
        options: { type: "filter", scope: ["email.send"], return: "$last" },
      },
    ]);

    // Insert 1 records into directus_operations
    await tx.batchInsert("directus_operations", [
      {
        id: "5ddf0c48-47f3-4f0b-a4a4-e2135e3a370a",
        name: "Run Script",
        key: "exec_ktuhk",
        type: "exec",
        position_x: 19,
        position_y: 1,
        options: {
          code: "module.exports = async function(data) {\nif(data.$trigger.payload.template.name === 'user-registration'){\n        return {...data.$trigger.payload, subject: 'ByAGRO Irriga+ - Confirmação de email'}\n    } else if (data.$trigger.payload.template.name === 'password-reset'){\n        return {...data.$trigger.payload, subject: 'ByAGRO Irriga+ - Redefinição de senha'}\n    }\nreturn data.$trigger.payload;\n}",
        },
        resolve: null,
        reject: null,
        flow: "94d7f959-0996-418b-a29f-171821c25f81",
      },
    ]);

    await tx("directus_flows")
      .where({ id: "94d7f959-0996-418b-a29f-171821c25f81" })
      .update({
        operation: "5ddf0c48-47f3-4f0b-a4a4-e2135e3a370a",
      });
  });
}

/**
 * Reverts the changes made by the up migration.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-09-05T22:34:57.287Z
 * @description This migration reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Delete 1 records from directus_operations (reverse of insert)
    await tx("directus_operations")
      .where({ id: "5ddf0c48-47f3-4f0b-a4a4-e2135e3a370a" })
      .del();

    // Delete 1 records from directus_flows (reverse of insert)
    await tx("directus_flows")
      .where({ id: "94d7f959-0996-418b-a29f-171821c25f81" })
      .del();

    await tx("directus_settings").update({
      public_registration: false,
      public_registration_verify_email: false,
      public_registration_role: null,
    });
  });
}
