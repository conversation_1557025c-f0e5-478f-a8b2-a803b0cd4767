/**
 * Migration to create current_project_state table for storing current project states
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create current_project_state table
    await tx.schema.createTable("current_project_state", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.uuid("project").notNullable();
      table.timestamp("date_created").notNullable().defaultTo(knex.fn.now());
      table.timestamp("date_updated").notNullable().defaultTo(knex.fn.now());
      table.timestamp("packet_date").notNullable().defaultTo(knex.fn.now());
      table.string("irrigation_status", 20).notNullable().defaultTo("inactive");
      table
        .string("fertigation_status", 20)
        .notNullable()
        .defaultTo("inactive");
      table.string("backwash_status", 20).notNullable().defaultTo("inactive");
      table.jsonb("sectors").notNullable().defaultTo("[]");

      // Foreign key constraint
      table.foreign("project").references("id").inTable("project");

      // Unique constraint for project to ensure only one current state per project
      table.unique(["project"], "current_project_state_project_unique");

      // Performance index for project
      table.index(["project"], "current_project_state_project_idx");

      // Index for date_updated for efficient queries
      table.index(["date_updated"], "current_project_state_date_updated_idx");

      // Index for packet_date for efficient queries
      table.index(["packet_date"], "current_project_state_packet_date_idx");

      // Check constraints for status values
      table.check(
        "irrigation_status IN ('active', 'inactive', 'error')",
        [],
        "current_project_state_irrigation_status_check"
      );
      table.check(
        "fertigation_status IN ('active', 'inactive', 'error')",
        [],
        "current_project_state_fertigation_status_check"
      );
      table.check(
        "backwash_status IN ('active', 'inactive', 'error')",
        [],
        "current_project_state_backwash_status_check"
      );
    });

    // Add table comment
    await tx.raw(`
      COMMENT ON TABLE current_project_state IS 'Current state of irrigation projects based on LIC device status';
    `);

    // Add column comments
    await tx.raw(`
      COMMENT ON COLUMN current_project_state.id IS 'Primary key';
      COMMENT ON COLUMN current_project_state.project IS 'Foreign key to project table';
      COMMENT ON COLUMN current_project_state.date_created IS 'When this record was created';
      COMMENT ON COLUMN current_project_state.date_updated IS 'When this record was last updated';
      COMMENT ON COLUMN current_project_state.packet_date IS 'When the original device status packet was recorded';
      COMMENT ON COLUMN current_project_state.irrigation_status IS 'Status of irrigation system: active, inactive, or error';
      COMMENT ON COLUMN current_project_state.fertigation_status IS 'Status of fertigation system: active, inactive, or error';
      COMMENT ON COLUMN current_project_state.backwash_status IS 'Status of backwash system: active, inactive, or error';
      COMMENT ON COLUMN current_project_state.sectors IS 'JSON array of sector states with sector ID, name, and status';
    `);
  });
}

/**
 * Migration to drop current_project_state table
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.dropTableIfExists("current_project_state");
  });
}
