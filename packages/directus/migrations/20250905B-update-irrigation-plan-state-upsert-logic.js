/**
 * Migration to update irrigation plan state trigger functions with start_time-aware logic
 * - Within same cycle (same start_time): do not replace current row; only set end_time once
 * - New cycle (different start_time): replace current row (delete + allow insert)
 * Also keeps inserting history rows on every packet
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Replace BEFORE INSERT handler for current_irrigation_plan_state
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_irrigation_plan_state_upsert()
      RETURNS TRIGGER AS $$
      DECLARE
        existing_record RECORD;
      BEGIN
        -- Always insert history row
        BEGIN
          INSERT INTO irrigation_plan_state (
            irrigation_plan,
            packet_date,
            start_time,
            end_time,
            activated_steps,
            activated_ferti_steps,
            waterpump_working,
            backwash_start_time,
            uses_waterpump,
            uses_ferti,
            date_created
          ) VALUES (
            NEW.irrigation_plan,
            NEW.packet_date,
            NEW.start_time,
            NEW.end_time,
            NEW.activated_steps,
            NEW.activated_ferti_steps,
            NEW.waterpump_working,
            NEW.backwash_start_time,
            NEW.uses_waterpump,
            NEW.uses_ferti,
            NEW.date_created
          );
        EXCEPTION WHEN unique_violation THEN
          RAISE WARNING 'Duplicate key for irrigation_plan % and packet_date % ignored in irrigation_plan_state', NEW.irrigation_plan, NEW.packet_date;
        END;

        IF TG_OP = 'INSERT' THEN
          SELECT * INTO existing_record
          FROM current_irrigation_plan_state
          WHERE irrigation_plan = NEW.irrigation_plan;

          IF FOUND THEN
            -- Same cycle
            IF (NEW.start_time IS NOT DISTINCT FROM existing_record.start_time) THEN
              -- One-time end_time set if previously NULL
              IF existing_record.end_time IS NULL AND NEW.end_time IS NOT NULL THEN
                UPDATE current_irrigation_plan_state
                SET end_time = NEW.end_time,
                    date_updated = NOW()
                WHERE irrigation_plan = NEW.irrigation_plan;
              END IF;
              RETURN NULL; -- Do not replace row within same cycle
            ELSE
              -- New cycle: delete current to allow new insert
              DELETE FROM current_irrigation_plan_state WHERE irrigation_plan = NEW.irrigation_plan;
              RETURN NEW;
            END IF;
          END IF;
        END IF;

        RETURN NEW; -- No existing: allow insert
      END;
      $$ LANGUAGE plpgsql;
    `);

    // Replace UPDATE handler (AFTER UPDATE) to keep history only
    await tx.raw(`
      CREATE OR REPLACE FUNCTION handle_irrigation_plan_state_update()
      RETURNS TRIGGER AS $$
      BEGIN
        BEGIN
          INSERT INTO irrigation_plan_state (
            irrigation_plan,
            packet_date,
            start_time,
            end_time,
            activated_steps,
            activated_ferti_steps,
            waterpump_working,
            backwash_start_time,
            uses_waterpump,
            uses_ferti,
            date_created
          ) VALUES (
            NEW.irrigation_plan,
            NEW.packet_date,
            NEW.start_time,
            NEW.end_time,
            NEW.activated_steps,
            NEW.activated_ferti_steps,
            NEW.waterpump_working,
            NEW.backwash_start_time,
            NEW.uses_waterpump,
            NEW.uses_ferti,
            NOW()
          );
        EXCEPTION WHEN unique_violation THEN
          RAISE WARNING 'Duplicate key for irrigation_plan % and packet_date % ignored in irrigation_plan_state during update', NEW.irrigation_plan, NEW.packet_date;
        END;

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);
  });
}

export async function down(knex) {
  await knex.transaction(async (tx) => {
    // No-op: functions were replaced; prior migrations can be re-run to restore
    return;
  });
}
