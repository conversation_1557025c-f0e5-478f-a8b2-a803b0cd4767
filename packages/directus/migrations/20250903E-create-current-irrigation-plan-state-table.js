/**
 * Migration to create current_irrigation_plan_state table for storing current irrigation plan states
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create current_irrigation_plan_state table
    await tx.schema.createTable("current_irrigation_plan_state", (table) => {
      table.uuid("id").primary().defaultTo(knex.raw("gen_random_uuid()"));
      table.uuid("irrigation_plan").notNullable();
      table.timestamp("packet_date").notNullable().defaultTo(knex.fn.now());
      table.timestamp("start_time").notNullable();
      table.timestamp("end_time").nullable();
      table.jsonb("activated_steps").notNullable().defaultTo("[]");
      table.jsonb("activated_ferti_steps").notNullable().defaultTo("[]");
      table.boolean("waterpump_working").notNullable().defaultTo(false);
      table.timestamp("backwash_start_time").nullable();
      table.boolean("uses_waterpump").notNullable().defaultTo(false);
      table.boolean("uses_ferti").notNullable().defaultTo(false);
      table.timestamp("date_created").notNullable().defaultTo(knex.fn.now());
      table.timestamp("date_updated").notNullable().defaultTo(knex.fn.now());

      // Foreign key constraint
      table
        .foreign("irrigation_plan")
        .references("id")
        .inTable("irrigation_plan");

      // Unique constraint for irrigation_plan to ensure only one current state per plan
      table.unique(["irrigation_plan"], {
        indexName: "current_irrigation_plan_state_irrigation_plan_unique",
      });

      // Index for packet_date for efficient queries
      table.index(
        ["packet_date"],
        "current_irrigation_plan_state_packet_date_idx"
      );
    });

    // Add table comment
    await tx.raw(`
      COMMENT ON TABLE current_irrigation_plan_state IS 'Current state of irrigation plan executions based on SchedulingReportPackage MQTT messages';
    `);

    // Add column comments
    await tx.raw(`
      COMMENT ON COLUMN current_irrigation_plan_state.id IS 'Primary key (same value as irrigation_plan field)';
      COMMENT ON COLUMN current_irrigation_plan_state.irrigation_plan IS 'Foreign key to irrigation_plan table (unique constraint)';
      COMMENT ON COLUMN current_irrigation_plan_state.packet_date IS 'When the original device status packet was recorded';
      COMMENT ON COLUMN current_irrigation_plan_state.start_time IS 'When the irrigation plan execution started';
      COMMENT ON COLUMN current_irrigation_plan_state.end_time IS 'When the irrigation plan execution ended (null if still running)';
      COMMENT ON COLUMN current_irrigation_plan_state.activated_steps IS 'JSONB array with IDs of irrigation_plan_step records for activated sectors';
      COMMENT ON COLUMN current_irrigation_plan_state.activated_ferti_steps IS 'JSONB array with IDs of irrigation_plan_step records for sectors with fertigation';
      COMMENT ON COLUMN current_irrigation_plan_state.waterpump_working IS 'Status of the water pump during the schedule';
      COMMENT ON COLUMN current_irrigation_plan_state.backwash_start_time IS 'When the backwash started (nullable)';
      COMMENT ON COLUMN current_irrigation_plan_state.uses_waterpump IS 'Whether the water pump should be used in the schedule';
      COMMENT ON COLUMN current_irrigation_plan_state.uses_ferti IS 'Whether fertigation should be used in the schedule';
      COMMENT ON COLUMN current_irrigation_plan_state.date_created IS 'When this record was created';
      COMMENT ON COLUMN current_irrigation_plan_state.date_updated IS 'When this record was last updated';
    `);
  });
}

/**
 * Migration to drop current_irrigation_plan_state table
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.dropTableIfExists("current_irrigation_plan_state");
  });
}
