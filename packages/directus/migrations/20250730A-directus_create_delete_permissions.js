/**
 * Applies the current state of the Directus config to the database.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-05T04:05:31.904Z
 * @description This migration applies the current state of the Directus config to the database.
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Insert 9 records into directus_permissions
    await tx.batchInsert("directus_permissions", [
      {
        collection: "account_user",
        action: "delete",
        permissions: {
          _and: [
            { user: { _eq: "$CURRENT_USER" } },
            { role: { _eq: "admin" } },
            {
              _or: [
                { start_date: { _null: true } },
                { start_date: { _lte: "$NOW" } },
              ],
            },
            {
              _or: [
                { end_date: { _null: true } },
                { end_date: { _gt: "$NOW" } },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "irrigation_plan",
        action: "delete",
        permissions: {
          _and: [
            {
              project: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              project: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _lte: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _gt: "$NOW" } } },
                    },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "irrigation_plan_step",
        action: "delete",
        permissions: {
          _and: [
            {
              irrigation_plan: {
                project: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              irrigation_plan: {
                project: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            },
            {
              _or: [
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { start_date: { _null: true } } },
                      },
                    },
                  },
                },
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { start_date: { _lte: "$NOW" } } },
                      },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { end_date: { _null: true } } },
                      },
                    },
                  },
                },
                {
                  irrigation_plan: {
                    project: {
                      property: {
                        account: { users: { end_date: { _gt: "$NOW" } } },
                      },
                    },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "project",
        action: "delete",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "property",
        action: "delete",
        permissions: {
          _and: [
            { account: { users: { user: { _eq: "$CURRENT_USER" } } } },
            { account: { users: { role: { _eq: "admin" } } } },
            {
              _or: [
                { account: { users: { start_date: { _null: true } } } },
                { account: { users: { start_date: { _lte: "$NOW" } } } },
              ],
            },
            {
              _or: [
                { account: { users: { end_date: { _null: true } } } },
                { account: { users: { end_date: { _gt: "$NOW" } } } },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "sector",
        action: "delete",
        permissions: {
          _and: [
            {
              project: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              project: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { start_date: { _lte: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _null: true } } },
                    },
                  },
                },
                {
                  project: {
                    property: {
                      account: { users: { end_date: { _gt: "$NOW" } } },
                    },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "water_pump",
        action: "delete",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "reservoir",
        action: "delete",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "device",
        action: "delete",
        permissions: {
          _and: [
            {
              properties: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              properties: {
                property: { account: { users: { role: { _eq: "admin" } } } },
              },
            },
            {
              _or: [
                {
                  properties: {
                    property: {
                      account: { users: { start_date: { _null: true } } },
                    },
                  },
                },
                {
                  properties: {
                    property: {
                      account: { users: { start_date: { _lte: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  properties: {
                    property: {
                      account: { users: { end_date: { _null: true } } },
                    },
                  },
                },
                {
                  properties: {
                    property: {
                      account: { users: { end_date: { _gt: "$NOW" } } },
                    },
                  },
                },
              ],
            },
            {
              _or: [
                { properties: { start_date: { _null: true } } },
                { properties: { start_date: { _lte: "$NOW" } } },
              ],
            },
            {
              _or: [
                { properties: { end_date: { _null: true } } },
                { properties: { end_date: { _gt: "$NOW" } } },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
      {
        collection: "property_device",
        action: "delete",
        permissions: {
          _and: [
            {
              property: {
                account: { users: { user: { _eq: "$CURRENT_USER" } } },
              },
            },
            { property: { account: { users: { role: { _eq: "admin" } } } } },
            {
              _or: [
                {
                  property: {
                    account: { users: { start_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { start_date: { _lte: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                {
                  property: {
                    account: { users: { end_date: { _null: true } } },
                  },
                },
                {
                  property: {
                    account: { users: { end_date: { _gt: "$NOW" } } },
                  },
                },
              ],
            },
            {
              _or: [
                { start_date: { _null: true } },
                { start_date: { _lte: "$NOW" } },
              ],
            },
            {
              _or: [
                { end_date: { _null: true } },
                { end_date: { _gt: "$NOW" } },
              ],
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      },
    ]);
  });
}

/**
 * Reverts the changes made by the up migration.
 * This migration file is auto-generated and should not be manually edited.
 * @module directus/migration/gen
 * @version 2025-07-05T04:05:31.904Z
 * @description This migration reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Delete 9 records from directus_permissions (reverse of insert)
    await tx("directus_permissions")
      .where({
        collection: "device",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "property_device",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "account_user",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "irrigation_plan_step",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "project",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "property",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "sector",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "water_pump",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
    await tx("directus_permissions")
      .where({
        collection: "reservoir",
        action: "delete",
        policy: "b6e0a767-dc9a-478d-b588-61aa700a519a",
      })
      .del();
  });
}
