/**
 * Applies Directus configuration for current_project_state collection
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // 1) Add current_project_state collection
    await tx.batchInsert("directus_collections", [
      {
        collection: "current_project_state",
        icon: "dashboard",
        note: "Current state of irrigation projects based on LIC device status. Contains real-time project operational status.",
        display_template:
          "{{project.name}} - Irrigation: {{irrigation_status}} | Fertigation: {{fertigation_status}} | Backwash: {{backwash_status}}",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: null,
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
    ]);

    // 2) Add current_project_state fields
    await tx.batchInsert("directus_fields", [
      // id
      {
        collection: "current_project_state",
        field: "id",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: true,
        sort: 1,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // project (M2O -> project)
      {
        collection: "current_project_state",
        field: "project",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "related-values",
        display_options: JSON.stringify({
          template: "{{name}} ({{property.name}})",
        }),
        readonly: false,
        hidden: false,
        sort: 2,
        width: "half",
        translations: null,
        note: "Project this state belongs to",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // date_created
      {
        collection: "current_project_state",
        field: "date_created",
        special: "date-created",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 3,
        width: "half",
        translations: null,
        note: "When this record was created",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // date_updated
      {
        collection: "current_project_state",
        field: "date_updated",
        special: "date-updated",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 4,
        width: "half",
        translations: null,
        note: "When this record was last updated",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // packet_date
      {
        collection: "current_project_state",
        field: "packet_date",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: false,
        hidden: false,
        sort: 5,
        width: "half",
        translations: null,
        note: "When the original device status packet was recorded",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // irrigation_status
      {
        collection: "current_project_state",
        field: "irrigation_status",
        special: null,
        interface: "select-dropdown",
        options: JSON.stringify({
          choices: [
            { text: "Active", value: "active" },
            { text: "Inactive", value: "inactive" },
            { text: "Error", value: "error" },
          ],
        }),
        display: "labels",
        display_options: JSON.stringify({
          choices: [
            {
              text: "Active",
              value: "active",
              foreground: "#FFFFFF",
              background: "#22C55E",
            },
            {
              text: "Inactive",
              value: "inactive",
              foreground: "#FFFFFF",
              background: "#6B7280",
            },
            {
              text: "Error",
              value: "error",
              foreground: "#FFFFFF",
              background: "#EF4444",
            },
          ],
        }),
        readonly: false,
        hidden: false,
        sort: 6,
        width: "third",
        translations: null,
        note: "Current status of irrigation system",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // fertigation_status
      {
        collection: "current_project_state",
        field: "fertigation_status",
        special: null,
        interface: "select-dropdown",
        options: JSON.stringify({
          choices: [
            { text: "Active", value: "active" },
            { text: "Inactive", value: "inactive" },
            { text: "Error", value: "error" },
          ],
        }),
        display: "labels",
        display_options: JSON.stringify({
          choices: [
            {
              text: "Active",
              value: "active",
              foreground: "#FFFFFF",
              background: "#22C55E",
            },
            {
              text: "Inactive",
              value: "inactive",
              foreground: "#FFFFFF",
              background: "#6B7280",
            },
            {
              text: "Error",
              value: "error",
              foreground: "#FFFFFF",
              background: "#EF4444",
            },
          ],
        }),
        readonly: false,
        hidden: false,
        sort: 7,
        width: "third",
        translations: null,
        note: "Current status of fertigation system",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // backwash_status
      {
        collection: "current_project_state",
        field: "backwash_status",
        special: null,
        interface: "select-dropdown",
        options: JSON.stringify({
          choices: [
            { text: "Active", value: "active" },
            { text: "Inactive", value: "inactive" },
            { text: "Error", value: "error" },
          ],
        }),
        display: "labels",
        display_options: JSON.stringify({
          choices: [
            {
              text: "Active",
              value: "active",
              foreground: "#FFFFFF",
              background: "#22C55E",
            },
            {
              text: "Inactive",
              value: "inactive",
              foreground: "#FFFFFF",
              background: "#6B7280",
            },
            {
              text: "Error",
              value: "error",
              foreground: "#FFFFFF",
              background: "#EF4444",
            },
          ],
        }),
        readonly: false,
        hidden: false,
        sort: 8,
        width: "third",
        translations: null,
        note: "Current status of backwash system",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // sectors
      {
        collection: "current_project_state",
        field: "sectors",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 9,
        width: "full",
        translations: null,
        note: "JSON array of sector states with sector ID, name, and status",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // 3) Relations for current_project_state -> project
    await tx.batchInsert("directus_relations", [
      {
        many_collection: "current_project_state",
        many_field: "project",
        one_collection: "project",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
    ]);

    // 4) Permissions
    // Reusing the same policy UUID used in other migrations
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // READ permission for current_project_state - scoped by project property ownership
    await tx.batchInsert("directus_permissions", [
      {
        collection: "current_project_state",
        action: "read",
        permissions: {
          _and: [
            {
              project: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              project: {
                property: {
                  account: { users: { role: { _eq: "admin" } } },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: policyId,
      },
    ]);

    // CREATE permission for current_project_state
    await tx.batchInsert("directus_permissions", [
      {
        collection: "current_project_state",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields:
          "project,irrigation_status,fertigation_status,backwash_status,sectors",
        policy: policyId,
      },
    ]);

    // UPDATE permission for current_project_state
    await tx.batchInsert("directus_permissions", [
      {
        collection: "current_project_state",
        action: "update",
        permissions: {
          _and: [
            {
              project: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              project: {
                property: {
                  account: { users: { role: { _eq: "admin" } } },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields:
          "project,irrigation_status,fertigation_status,backwash_status,sectors",
        policy: policyId,
      },
    ]);

    // DELETE permission for current_project_state
    await tx.batchInsert("directus_permissions", [
      {
        collection: "current_project_state",
        action: "delete",
        permissions: {
          _and: [
            {
              project: {
                property: {
                  account: { users: { user: { _eq: "$CURRENT_USER" } } },
                },
              },
            },
            {
              project: {
                property: {
                  account: { users: { role: { _eq: "admin" } } },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: policyId,
      },
    ]);
  });
}

/**
 * Reverts the changes made by the up migration.
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // Remove permissions
    await tx("directus_permissions")
      .where({
        collection: "current_project_state",
        policy: policyId,
      })
      .del();

    // Remove relations
    await tx("directus_relations")
      .where({
        many_collection: "current_project_state",
        many_field: "project",
      })
      .del();

    // Remove fields
    await tx("directus_fields")
      .where({ collection: "current_project_state" })
      .del();

    // Remove collection
    await tx("directus_collections")
      .where({ collection: "current_project_state" })
      .del();
  });
}
