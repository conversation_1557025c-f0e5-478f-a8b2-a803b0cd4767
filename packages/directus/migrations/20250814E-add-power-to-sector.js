/**
 * Migration to add power field to sector table
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Add power column to sector table
    await tx.schema.alterTable("sector", (table) => {
      table.smallint("power")
        .notNullable()
        .defaultTo(0)
        .comment("Power setting for variable frequency inverter (0-100). 0 for pumps without frequency inverter.");
    });

    // Add constraint to ensure power is between 0 and 100
    await tx.raw(`
      ALTER TABLE sector
      ADD CONSTRAINT chk_sector_power
      CHECK (power BETWEEN 0 AND 100);
    `);
  });
}

/**
 * Migration to remove power field from sector table
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Remove constraint first
    await tx.raw("ALTER TABLE sector DROP CONSTRAINT IF EXISTS chk_sector_power;");
    
    // Remove power column from sector table
    await tx.schema.alterTable("sector", (table) => {
      table.dropColumn("power");
    });
  });
}