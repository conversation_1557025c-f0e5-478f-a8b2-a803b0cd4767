{"name": "directus", "type": "module", "private": true, "version": "1.0.0-b8", "scripts": {"migration:create": "bun run ./src/migration/tools/create-migration.ts", "migration:directus-config-diff": "bun run ./src/migration/tools/migration/directus/gen.ts", "docker:build": "docker build --rm -t docker-registry.saas.tecnologia.vix.br/byagro/irriga-mais/directus:${npm_package_version} .", "docker:push": "docker push docker-registry.saas.tecnologia.vix.br/byagro/irriga-mais/directus:${npm_package_version}", "docker:build-and-push": "bun run docker:build && bun run docker:push", "docker:directus:cli": "docker compose exec directus ./cli.js", "docker:directus:cli:db": "bun run docker:directus:cli database", "docker:directus:cli:db:migrate:latest": "bun run docker:directus:cli:db migrate:latest", "docker:directus:cli:db:migrate:down": "bun run docker:directus:cli:db migrate:down", "docker:directus:cli:db:migrate:up": "bun run docker:directus:cli:db migrate:up", "docker:directus:restart": "docker compose restart directus --timeout 2", "docker:directus:recreate": "docker compose stop directus --timeout 5; docker compose up directus --force-recreate --no-deps -d", "docker:directus:recreate:loop": "bun run scripts/docker-directus-recreate-loop.ts", "docker:down": "docker compose kill directus && docker compose down --remove-orphans -v", "docker:down-up": "bun run docker:down && docker compose up --build", "docker:down-up:loop": "bun run scripts/docker-monitor.ts", "docker:database": "docker compose exec -u postgres database", "docker:database:psql": "docker compose exec -u postgres database psql", "docker:database:recreate-test-db": "docker compose exec -u postgres database dropdb -f test_db; docker compose exec -u postgres database createdb test_db; docker compose exec -u postgres database psql -d test_db -c \"CREATE EXTENSION IF NOT EXISTS postgis;\"; docker compose exec -e DB_DATABASE=test_db directus ./cli.js bootstrap", "docker:database:create-db": "./scripts/create-docker-db.sh", "docker:database:restore-backup": "docker compose exec -u postgres database dropdb -f bkp; docker compose exec -u postgres database createdb bkp; docker compose exec -u postgres database psql -d bkp -c \"CREATE EXTENSION IF NOT EXISTS postgis;\"; bzcat .local/irriga_mais.sql.bz2 | docker compose exec -T -u postgres database psql -d bkp -f -", "seed:populate": "bun run src/seed/index.ts", "seed:cleanup": "bun run src/seed/index.ts --cleanup"}, "devDependencies": {"@types/bun": "latest", "bun-types": "^1.2.14", "knex": "^3.1.0", "pg": "^8.16.0"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"argon2": "^0.43.0"}}