# Minimal mosquitto config for local development
# Listen for plain MQTT
listener 1883

# Listen for MQTT over WebSockets
listener 9001
protocol websockets

# Persistence
persistence true
persistence_location /mosquitto/data/

# Logging
log_dest file /mosquitto/log/mosquitto.log

# Allow anonymous (local dev only). Set to false to require credentials from the password_file below.
allow_anonymous false

# Password file for authenticated users (local dev)
# For production, generate hashed passwords with `mosquitto_passwd` and keep this file secure.
password_file /mosquitto/config/passwd.txt

# Default message size and other sensible defaults
message_size_limit 65536

# Increase max in-flight messages for local testing
max_inflight_messages 100
