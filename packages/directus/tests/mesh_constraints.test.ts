import { describe, it, beforeEach, afterEach, expect } from "bun:test";
import { Knex } from "knex";
import {
  begin,
  rollbackAndDestroy,
  createKnex,
  expectRejectWithinSavepoint,
} from "./helpers/db";
import {
  daysAgo,
  insertUser,
  insertAccount,
  insertProperty,
  insertDevice,
  insertPropertyDevice,
  insertMeshMapping,
  insertWaterPump,
  insertReservoir,
  insertProject,
  insertSector,
} from "./helpers/fixtures";

let knex: Knex;
let trx: Knex.Transaction;

describe("Mesh Network Constraints", () => {
  beforeEach(async () => {
    knex = createKnex();
    trx = await begin(knex);
  });

  afterEach(async () => {
    await rollbackAndDestroy(trx);
  });

  async function setupMeshNetworks() {
    const userId = await insertUser(trx);
    const accountId = await insertAccount(trx, userId);
    const propertyId = await insertProperty(trx, accountId, "Test Property");

    // Create devices
    const lic1Device = await insertDevice(trx, "LIC", "LIC-1");
    const lic2Device = await insertDevice(trx, "LIC", "LIC-2");
    const rm1Device = await insertDevice(trx, "RM", "RM-1");
    const rm2Device = await insertDevice(trx, "RM", "RM-2");
    const pump1Controller = await insertDevice(trx, "WPC-PL10", "PUMP-CTRL-1");
    const pump2Controller = await insertDevice(trx, "WPC-PL10", "PUMP-CTRL-2");
    const irrigationPumpController = await insertDevice(
      trx,
      "WPC-PL50",
      "IRR-PUMP-CTRL"
    );
    const vc1Device = await insertDevice(trx, "VC", "VC-1");

    const now = new Date();
    const activeStart = daysAgo(5);

    // Create property devices
    const lic1PropDevice = await insertPropertyDevice(
      trx,
      lic1Device,
      propertyId,
      activeStart,
      null
    );
    const lic2PropDevice = await insertPropertyDevice(
      trx,
      lic2Device,
      propertyId,
      activeStart,
      null
    );
    const rm1PropDevice = await insertPropertyDevice(
      trx,
      rm1Device,
      propertyId,
      activeStart,
      null
    );
    const rm2PropDevice = await insertPropertyDevice(
      trx,
      rm2Device,
      propertyId,
      activeStart,
      null
    );
    const pump1PropDevice = await insertPropertyDevice(
      trx,
      pump1Controller,
      propertyId,
      activeStart,
      null
    );
    const pump2PropDevice = await insertPropertyDevice(
      trx,
      pump2Controller,
      propertyId,
      activeStart,
      null
    );
    const irrigationPumpPropDevice = await insertPropertyDevice(
      trx,
      irrigationPumpController,
      propertyId,
      activeStart,
      null
    );
    const vc1PropDevice = await insertPropertyDevice(
      trx,
      vc1Device,
      propertyId,
      activeStart,
      null
    );

    // Create water pumps
    const pump1 = await insertWaterPump(
      trx,
      propertyId,
      pump1Controller,
      "SERVICE",
      "Pump 1"
    );
    const pump2 = await insertWaterPump(
      trx,
      propertyId,
      pump2Controller,
      "SERVICE",
      "Pump 2"
    );
    const irrigationPump = await insertWaterPump(
      trx,
      propertyId,
      irrigationPumpController,
      "IRRIGATION",
      "Irrigation Pump"
    );

    // Create mesh mappings
    // Network 1: RM1, Pump1Controller, IrrigationPumpController, VC1 -> LIC1
    await insertMeshMapping(
      trx,
      rm1PropDevice,
      lic1PropDevice,
      activeStart,
      null
    );
    await insertMeshMapping(
      trx,
      pump1PropDevice,
      lic1PropDevice,
      activeStart,
      null
    );
    await insertMeshMapping(
      trx,
      irrigationPumpPropDevice,
      lic1PropDevice,
      activeStart,
      null
    );
    await insertMeshMapping(
      trx,
      vc1PropDevice,
      lic1PropDevice,
      activeStart,
      null
    );

    // Network 2: RM2, Pump2Controller -> LIC2
    await insertMeshMapping(
      trx,
      rm2PropDevice,
      lic2PropDevice,
      activeStart,
      null
    );
    await insertMeshMapping(
      trx,
      pump2PropDevice,
      lic2PropDevice,
      activeStart,
      null
    );

    return {
      propertyId,
      lic1Device,
      lic2Device,
      rm1Device,
      rm2Device,
      pump1Controller,
      pump2Controller,
      irrigationPumpController,
      vc1Device,
      lic1PropDevice,
      lic2PropDevice,
      rm1PropDevice,
      rm2PropDevice,
      pump1PropDevice,
      pump2PropDevice,
      irrigationPumpPropDevice,
      vc1PropDevice,
      pump1,
      pump2,
      irrigationPump,
    };
  }

  it("Reservoir: should succeed if RM and pump are in the same mesh network", async () => {
    const setup = await setupMeshNetworks();

    const reservoir = await insertReservoir(
      trx,
      setup.propertyId,
      "Same Mesh Reservoir",
      setup.rm1Device,
      setup.pump1
    );
    expect(reservoir).toBeDefined();
  });

  it("Reservoir: should fail if RM and pump are in different mesh networks", async () => {
    const setup = await setupMeshNetworks();

    await expectRejectWithinSavepoint(trx, async () => {
      await insertReservoir(
        trx,
        setup.propertyId,
        "Different Mesh Reservoir",
        setup.rm1Device,
        setup.pump2
      );
    });
  });

  it("Project: should succeed if pumps are in the same mesh network as LIC", async () => {
    const setup = await setupMeshNetworks();

    const project = await insertProject(
      trx,
      setup.propertyId,
      "Same Mesh Project",
      setup.lic1Device,
      setup.irrigationPump
    );
    expect(project).toBeDefined();
  });

  it("Project: should fail if irrigation pump is in a different mesh network from LIC", async () => {
    const setup = await setupMeshNetworks();

    await expectRejectWithinSavepoint(trx, async () => {
      await insertProject(
        trx,
        setup.propertyId,
        "Different Mesh Project",
        setup.lic2Device,
        setup.irrigationPump
      );
    });
  });

  it("Sector: should succeed if VC is in the same mesh network as project LIC", async () => {
    const setup = await setupMeshNetworks();

    const project = await insertProject(
      trx,
      setup.propertyId,
      "Sector Project",
      setup.lic1Device,
      setup.irrigationPump
    );

    const sector = await insertSector(
      trx,
      project,
      "Same Mesh Sector",
      setup.vc1Device,
      1
    );
    expect(sector).toBeDefined();
  });

  it("Sector: should fail if VC is in a different mesh network from project LIC", async () => {
    const setup = await setupMeshNetworks();

    // Create a pump that's in the same network as lic2 for this test
    const pump2ForLic2 = await insertWaterPump(
      trx,
      setup.propertyId,
      setup.pump2Controller,
      "IRRIGATION",
      "Pump for LIC2"
    );

    const project = await insertProject(
      trx,
      setup.propertyId,
      "Sector Project Diff Mesh",
      setup.lic2Device,
      pump2ForLic2
    );

    await expectRejectWithinSavepoint(trx, async () => {
      await insertSector(
        trx,
        project,
        "Different Mesh Sector",
        setup.vc1Device,
        1
      );
    });
  });
});
