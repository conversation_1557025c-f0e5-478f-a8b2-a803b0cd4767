import knexFactory, { <PERSON><PERSON> } from "knex";
import { spawn } from "child_process";
import path from "path";
/**
 * Create a Knex instance for tests using env:
 * TEST_DB_HOST, TEST_DB_PORT, TEST_DB_USER, TEST_DB_PASSWORD, TEST_DB_DATABASE
 *
 * Any of those values can be overridden by passing an optional `overrides` object.
 */
export function createKnex(overrides?: {
  host?: string;
  port?: number | string;
  user?: string;
  password?: string;
  database?: string;
}): Knex {
  const {
    TEST_DB_HOST,
    TEST_DB_PORT,
    TEST_DB_USER,
    TEST_DB_PASSWORD,
    TEST_DB_DATABASE,
  } = process.env;

  const host = overrides?.host ?? TEST_DB_HOST;
  const user = overrides?.user ?? TEST_DB_USER;
  const database = overrides?.database ?? TEST_DB_DATABASE;
  const password = overrides?.password ?? TEST_DB_PASSWORD;
  const portRaw = overrides?.port ?? TEST_DB_PORT;

  if (!host || !user || !database) {
    throw new Error(
      "Missing DB config. Required: host, user, database. Provide via env vars or overrides."
    );
  }

  const port =
    typeof portRaw === "number"
      ? portRaw
      : typeof portRaw === "string"
      ? parseInt(portRaw, 10) || 5432
      : 5432;

  return knexFactory({
    client: "pg",
    connection: {
      host,
      port,
      user,
      password,
      database,
    },
    pool: { min: 0, max: 5 },
  });
}

/**
 * Begin a transaction for a test. Must be rolled back by the caller.
 */
export async function begin(trxOrKnex?: Knex): Promise<Knex.Transaction> {
  const knex = trxOrKnex ?? createKnex();
  return knex.transaction();
}

/**
 * Rollback and destroy (safely) after a test.
 */
export async function rollbackAndDestroy(trx: Knex.Transaction): Promise<void> {
  try {
    await trx.rollback();
  } catch {
    // ignore
  }
  try {
    await (trx as unknown as Knex).destroy?.();
  } catch {
    // ignore
  }
}

/**
 * Executes a function within a database savepoint and expects it to reject/throw an error.
 * If the function succeeds unexpectedly, this helper will throw an error.
 * If the function fails as expected, the savepoint is rolled back while keeping the outer transaction intact.
 *
 * This utility is useful for testing error scenarios in database operations without affecting
 * the main transaction state.
 *
 * @param trx - The Knex transaction object to use for savepoint operations
 * @param fn - The async function that is expected to throw an error or reject
 *
 * @throws {Error} Throws an error if the provided function succeeds when it was expected to fail
 *
 * @example
 * ```typescript
 * await expectRejectWithinSavepoint(transaction, async () => {
 *   await someOperationThatShouldFail();
 * });
 * ```
 */
export async function expectRejectWithinSavepoint(
  trx: Knex,
  fn: () => Promise<any>
) {
  await trx.raw("SAVEPOINT sp_err");
  try {
    await fn();
    // If no error, release to keep the transaction clean, but assert fail
    await trx.raw("RELEASE SAVEPOINT sp_err");
    throw new Error("Expected statement to fail, but it succeeded");
  } catch (e) {
    // Rollback only the failed statement; keep outer transaction usable
    await trx.raw("ROLLBACK TO SAVEPOINT sp_err");
  }
}

export async function createDatabase(databaseName: string, recreate = false) {
  // Create test database programmatically
  const args = recreate ? [databaseName, "--recreate"] : [databaseName];
  const createDbProcess = spawn("scripts/create-docker-db.sh", args, {
    cwd: path.resolve(__dirname, "../.."),
    stdio: "pipe",
  });

  // Wait for the process to complete
  return await new Promise<void>((resolve, reject) => {
    createDbProcess.on("close", (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Database creation failed with code ${code}`));
      }
    });

    createDbProcess.on("error", (error) => {
      reject(error);
    });
  });
}
