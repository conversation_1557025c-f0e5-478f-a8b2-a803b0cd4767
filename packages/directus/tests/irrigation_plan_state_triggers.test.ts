import {
  afterEach,
  beforeAll,
  beforeEach,
  describe,
  expect,
  it,
} from "bun:test";
import { Knex } from "knex";
import {
  begin,
  createDatabase,
  createKnex,
  rollbackAndDestroy,
} from "./helpers/db";
import { insertDevice, insertUser, insertProperty } from "./helpers/fixtures";

let knex: Knex;
let trx: Knex.Transaction;

describe("Irrigation Plan State Triggers", () => {
  beforeAll(async () => {
    // Create test database programmatically
    await createDatabase("irrigation_plan_state_test");
  });

  beforeEach(async () => {
    knex = createKnex({ database: "irrigation_plan_state_test" });
    trx = await begin(knex);
  });

  afterEach(async () => {
    await rollbackAndDestroy(trx);
  });

  it("should verify the test database was created successfully", async () => {
    const result = await trx.raw("SELECT current_database() as db_name");
    expect(result.rows[0].db_name).toBe("irrigation_plan_state_test");
  });

  async function setupTestData() {
    const userId = await insertUser(trx);
    const licDevice = await insertDevice(trx, "LIC", "TEST-LIC-001");
    const accountId = await trx("account")
      .insert({ owner: userId })
      .returning(["id"])
      .then((rows) => rows[0].id);
    const propertyId = await insertProperty(trx, accountId, "Test Property");

    // Create required water pump and device for project
    const waterPumpId = await trx("water_pump")
      .insert({
        property: propertyId,
        pump_type: "IRRIGATION",
        label: "Test Water Pump",
        identifier: "TEST-PUMP-001",
      })
      .returning(["id"])
      .then((rows) => rows[0].id);

    const localizedDevice = await insertDevice(
      trx,
      "LIC",
      "TEST-LIC-LOCAL-001"
    );

    const projectId = await trx("project")
      .insert({
        property: propertyId,
        name: "Test Project",
        irrigation_water_pump: waterPumpId,
        localized_irrigation_controller: localizedDevice,
        fertigation_water_pump: null,
      })
      .returning(["id"])
      .then((rows) => rows[0].id);

    // Create irrigation plan
    const [irrigationPlan] = await trx("irrigation_plan")
      .insert({
        project: projectId,
        name: "Test Irrigation Plan",
        description: "Test plan for trigger testing",
        start_time: "08:00:00", // Required field
        user_created: userId,
        user_updated: userId,
      })
      .returning(["id"]);

    // For testing purposes, we'll use mock step IDs since the triggers don't validate step existence
    const mockStepIds = [
      "00000000-0000-0000-0000-000000000001",
      "00000000-0000-0000-0000-000000000002",
      "00000000-0000-0000-0000-000000000003",
    ];

    return {
      userId,
      licDevice,
      irrigationPlanId: irrigationPlan.id,
      stepIds: mockStepIds,
    };
  }

  function closeMs(a: Date, b: Date, toleranceMs = 1000): boolean {
    return Math.abs(a.getTime() - b.getTime()) <= toleranceMs;
  }

  it("should populate irrigation_plan_state when inserting into current_irrigation_plan_state", async () => {
    const { userId, irrigationPlanId, stepIds } = await setupTestData();

    // Clean up any existing records to avoid conflicts
    await trx("current_irrigation_plan_state")
      .where({ irrigation_plan: irrigationPlanId })
      .delete();
    await trx("irrigation_plan_state")
      .where({ irrigation_plan: irrigationPlanId })
      .delete();

    const testData = {
      irrigation_plan: irrigationPlanId,
      packet_date: new Date("2025-09-03T10:00:00Z"),
      start_time: new Date("2025-09-03T10:00:00Z"),
      end_time: new Date("2025-09-03T10:30:00Z"),
      activated_steps: JSON.stringify([stepIds[0]]),
      activated_ferti_steps: JSON.stringify([]),
      waterpump_working: true,
      backwash_start_time: null,
      uses_waterpump: true,
      uses_ferti: false,
      date_created: new Date("2025-09-03T10:00:00Z"),
      date_updated: new Date("2025-09-03T10:00:00Z"),
    };

    const [currentState] = await trx("current_irrigation_plan_state")
      .insert(testData)
      .returning(["*"]);

    // Verify the current_irrigation_plan_state record was created
    expect(currentState).toBeDefined();
    expect(currentState.irrigation_plan).toBe(irrigationPlanId);
    expect(currentState.id).toBe(irrigationPlanId); // ID should equal irrigation_plan

    // Verify the irrigation_plan_state record was created by the trigger
    const historyRecords = await trx("irrigation_plan_state")
      .where({ irrigation_plan: irrigationPlanId })
      .orderBy("packet_date", "desc");

    expect(historyRecords).toHaveLength(1);

    const historyRecord = historyRecords[0];
    expect(historyRecord.irrigation_plan).toBe(irrigationPlanId);
    expect(historyRecord.packet_date).toEqual(testData.packet_date);
    expect(historyRecord.start_time).toEqual(testData.start_time);
    expect(historyRecord.end_time).toEqual(testData.end_time);
    expect(JSON.stringify(historyRecord.activated_steps)).toEqual(
      testData.activated_steps
    );
    expect(JSON.stringify(historyRecord.activated_ferti_steps)).toEqual(
      testData.activated_ferti_steps
    );
    expect(historyRecord.waterpump_working).toBe(testData.waterpump_working);
    expect(historyRecord.uses_waterpump).toBe(testData.uses_waterpump);
    expect(historyRecord.uses_ferti).toBe(testData.uses_ferti);

    // Verify date_created is close to the insert time
    expect(
      closeMs(new Date(historyRecord.date_created), testData.date_created)
    ).toBe(true);
  });

  it("should populate irrigation_plan_state when updating current_irrigation_plan_state", async () => {
    const { userId, irrigationPlanId, stepIds } = await setupTestData();

    // Clean up any existing records to avoid conflicts
    await trx("current_irrigation_plan_state")
      .where({ irrigation_plan: irrigationPlanId })
      .delete();
    await trx("irrigation_plan_state")
      .where({ irrigation_plan: irrigationPlanId })
      .delete();

    // First insert
    const initialData = {
      irrigation_plan: irrigationPlanId,
      packet_date: new Date("2025-09-03T10:00:00Z"),
      start_time: new Date("2025-09-03T10:00:00Z"),
      end_time: new Date("2025-09-03T10:30:00Z"),
      activated_steps: JSON.stringify([stepIds[0]]),
      activated_ferti_steps: JSON.stringify([]),
      waterpump_working: true,
      backwash_start_time: null,
      uses_waterpump: true,
      uses_ferti: false,
      date_created: new Date("2025-09-03T10:00:00Z"),
      date_updated: new Date("2025-09-03T10:00:00Z"),
    };

    const [initialState] = await trx("current_irrigation_plan_state")
      .insert(initialData)
      .returning(["*"]);

    // Wait to ensure different timestamps
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Update the record
    const updateData = {
      packet_date: new Date("2025-09-03T11:00:00Z"),
      end_time: new Date("2025-09-03T11:00:00Z"),
      activated_steps: JSON.stringify([stepIds[0], stepIds[1]]),
      waterpump_working: false,
    };

    const beforeUpdate = new Date();
    await trx("current_irrigation_plan_state")
      .where({ irrigation_plan: irrigationPlanId })
      .update(updateData);

    // The UPDATE trigger should create a new historical record
    // Wait a bit to ensure the trigger has time to execute
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Verify we now have 2 history records (one from insert, one from update)
    const updatedHistoryRecords = await trx("irrigation_plan_state")
      .where({ irrigation_plan: irrigationPlanId })
      .orderBy("packet_date", "asc");

    expect(updatedHistoryRecords).toHaveLength(2);

    const [insertRecord, updateRecord] = updatedHistoryRecords;

    // Verify the insert record has the initial data
    expect(insertRecord.irrigation_plan).toBe(irrigationPlanId);
    expect(insertRecord.packet_date).toEqual(initialData.packet_date);
    expect(insertRecord.start_time).toEqual(initialData.start_time);
    expect(insertRecord.end_time).toEqual(initialData.end_time);
    expect(JSON.stringify(insertRecord.activated_steps)).toEqual(
      initialData.activated_steps
    );
    expect(insertRecord.waterpump_working).toBe(initialData.waterpump_working);

    // Verify the update record has the updated data
    expect(updateRecord.irrigation_plan).toBe(irrigationPlanId);
    expect(updateRecord.packet_date).toEqual(updateData.packet_date);
    expect(updateRecord.start_time).toEqual(initialData.start_time);
    expect(updateRecord.end_time).toEqual(updateData.end_time);
    expect(JSON.stringify(updateRecord.activated_steps)).toEqual(
      updateData.activated_steps
    );
    expect(updateRecord.waterpump_working).toBe(updateData.waterpump_working);

    // Verify the update record date_created is close to the update time
    expect(closeMs(new Date(updateRecord.date_created), beforeUpdate)).toBe(
      true
    );
  });

  it("should handle duplicate records in irrigation_plan_state by ignoring with warning", async () => {
    const { irrigationPlanId } = await setupTestData();

    const testData = {
      irrigation_plan: irrigationPlanId,
      packet_date: new Date("2025-09-03T10:00:00Z"),
      start_time: new Date("2025-09-03T10:00:00Z"),
      end_time: new Date("2025-09-03T10:30:00Z"),
      activated_steps: JSON.stringify([]),
      activated_ferti_steps: JSON.stringify([]),
      waterpump_working: true,
      backwash_start_time: null,
      uses_waterpump: true,
      uses_ferti: false,
      date_created: new Date("2025-09-03T10:00:00Z"),
      date_updated: new Date("2025-09-03T10:00:00Z"),
    };

    // First insert
    await trx("current_irrigation_plan_state").insert(testData);

    // Verify we have 1 history record
    let historyRecords = await trx("irrigation_plan_state").where({
      irrigation_plan: irrigationPlanId,
    });
    expect(historyRecords).toHaveLength(1);

    // Try to insert again with same irrigation_plan and packet_date
    // This should be handled by the trigger and not create a duplicate
    await trx("current_irrigation_plan_state")
      .update({
        ...testData,
        activated_steps: JSON.stringify(["different-step-id"]), // Different data but same key
      })
      .where({ id: irrigationPlanId });

    // Should still have only 1 history record (duplicate ignored)
    historyRecords = await trx("irrigation_plan_state").where({
      irrigation_plan: irrigationPlanId,
    });
    expect(historyRecords).toHaveLength(1);

    // The original record should remain unchanged
    const historyRecord = historyRecords[0];
    expect(JSON.stringify(historyRecord.activated_steps)).toEqual(
      testData.activated_steps
    );
  });

  it("should set id equal to irrigation_plan in current_irrigation_plan_state", async () => {
    const { irrigationPlanId } = await setupTestData();

    const testData = {
      irrigation_plan: irrigationPlanId,
      packet_date: new Date("2025-09-03T10:00:00Z"),
      start_time: new Date("2025-09-03T10:00:00Z"),
      end_time: new Date("2025-09-03T10:30:00Z"),
      activated_steps: JSON.stringify([]),
      activated_ferti_steps: JSON.stringify([]),
      waterpump_working: true,
      backwash_start_time: null,
      uses_waterpump: true,
      uses_ferti: false,
    };

    const [currentState] = await trx("current_irrigation_plan_state")
      .insert(testData)
      .returning(["*"]);

    // Verify the ID was set to the irrigation_plan value by the trigger
    expect(currentState.id).toBe(irrigationPlanId);
    expect(currentState.irrigation_plan).toBe(irrigationPlanId);
    expect(currentState.id).toBe(currentState.irrigation_plan);
  });

  it("should update date_updated automatically on UPDATE operations", async () => {
    const { irrigationPlanId } = await setupTestData();

    const initialData = {
      irrigation_plan: irrigationPlanId,
      packet_date: new Date("2025-09-03T10:00:00Z"),
      start_time: new Date("2025-09-03T10:00:00Z"),
      end_time: new Date("2025-09-03T10:30:00Z"),
      activated_steps: JSON.stringify([]),
      activated_ferti_steps: JSON.stringify([]),
      waterpump_working: true,
      backwash_start_time: null,
      uses_waterpump: true,
      uses_ferti: false,
    };

    const [initialState] = await trx("current_irrigation_plan_state")
      .insert(initialData)
      .returning(["*"]);

    const initialDateUpdated = new Date(initialState.date_updated);

    // Wait to ensure different timestamps
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Update the record
    const beforeUpdate = new Date();
    await trx("current_irrigation_plan_state")
      .where({ irrigation_plan: irrigationPlanId })
      .update({
        waterpump_working: false,
        // Don't set date_updated manually - let trigger handle it
      });

    // Get the updated record
    const updatedState = await trx("current_irrigation_plan_state")
      .where({ irrigation_plan: irrigationPlanId })
      .first();

    // Verify date_updated was automatically updated
    expect(updatedState.waterpump_working).toBe(false);
    // The date_updated should be greater than or equal to the initial date
    // Due to transaction timing, it might be the same, so we use toBeGreaterThanOrEqual
    expect(
      new Date(updatedState.date_updated).getTime()
    ).toBeGreaterThanOrEqual(initialDateUpdated.getTime());
    expect(closeMs(new Date(updatedState.date_updated), beforeUpdate)).toBe(
      true
    );
  });

  it("should handle multiple operations and maintain data consistency", async () => {
    const { irrigationPlanId, stepIds } = await setupTestData();

    const operations = [
      {
        packet_date: new Date("2025-09-03T10:00:00Z"),
        data: {
          start_time: new Date("2025-09-03T10:00:00Z"),
          end_time: new Date("2025-09-03T10:30:00Z"),
          activated_steps: JSON.stringify([stepIds[0]]),
          activated_ferti_steps: JSON.stringify([]),
          waterpump_working: true,
          uses_waterpump: true,
          uses_ferti: false,
        },
      },
      {
        packet_date: new Date("2025-09-03T11:00:00Z"),
        data: {
          start_time: new Date("2025-09-03T11:00:00Z"),
          end_time: new Date("2025-09-03T11:45:00Z"),
          activated_steps: JSON.stringify([stepIds[0], stepIds[1]]),
          activated_ferti_steps: JSON.stringify([stepIds[1]]),
          waterpump_working: true,
          uses_waterpump: true,
          uses_ferti: true,
        },
      },
      {
        packet_date: new Date("2025-09-03T12:00:00Z"),
        data: {
          start_time: new Date("2025-09-03T12:00:00Z"),
          end_time: null, // Still running
          activated_steps: JSON.stringify([stepIds[0], stepIds[1], stepIds[2]]),
          activated_ferti_steps: JSON.stringify([stepIds[1], stepIds[2]]),
          waterpump_working: false,
          uses_waterpump: true,
          uses_ferti: true,
        },
      },
    ];

    for (const operation of operations) {
      await trx("current_irrigation_plan_state")
        .insert({
          irrigation_plan: irrigationPlanId,
          packet_date: operation.packet_date,
          ...operation.data,
        })
        .onConflict(["id"])
        .merge({
          packet_date: operation.packet_date,
          ...operation.data,
        });

      // Small delay between operations
      await new Promise((resolve) => setTimeout(resolve, 50));
    }

    // Verify all operations are recorded in history
    const historyRecords = await trx("irrigation_plan_state")
      .where({ irrigation_plan: irrigationPlanId })
      .orderBy("packet_date", "asc");

    expect(historyRecords).toHaveLength(operations.length);

    // Verify each history record corresponds to the correct operation
    for (let i = 0; i < operations.length; i++) {
      const historyRecord = historyRecords[i]!;
      const operation = operations[i]!;

      expect(historyRecord.irrigation_plan).toBe(irrigationPlanId);
      expect(historyRecord.packet_date).toEqual(operation.packet_date);
      expect(historyRecord.start_time).toEqual(operation.data.start_time);
      expect(historyRecord.end_time).toEqual(operation.data.end_time);
      expect(JSON.stringify(historyRecord.activated_steps)).toEqual(
        operation.data.activated_steps
      );
      expect(JSON.stringify(historyRecord.activated_ferti_steps)).toEqual(
        operation.data.activated_ferti_steps
      );
      expect(historyRecord.waterpump_working).toBe(
        operation.data.waterpump_working
      );
      expect(historyRecord.uses_waterpump).toBe(operation.data.uses_waterpump);
      expect(historyRecord.uses_ferti).toBe(operation.data.uses_ferti);
    }

    // Verify the current state is the most recent one
    const currentState = await trx("current_irrigation_plan_state")
      .where({ irrigation_plan: irrigationPlanId })
      .first();

    const mostRecentOperation = operations[operations.length - 1]!;
    expect(currentState.packet_date).toEqual(mostRecentOperation.packet_date);
    expect(currentState.start_time).toEqual(
      mostRecentOperation.data.start_time
    );
    expect(currentState.end_time).toEqual(mostRecentOperation.data.end_time);
    expect(JSON.stringify(currentState.activated_steps)).toEqual(
      mostRecentOperation.data.activated_steps
    );
  });

  it("should not move end_time within same start_time cycle and reset on new cycle (irrigation plan)", async () => {
    const { irrigationPlanId } = await setupTestData();

    await trx("current_irrigation_plan_state").where({ irrigation_plan: irrigationPlanId }).delete();
    await trx("irrigation_plan_state").where({ irrigation_plan: irrigationPlanId }).delete();

    const startA = new Date("2025-09-05T08:00:00Z");
    const t1 = new Date("2025-09-05T08:05:00Z");
    const t2 = new Date("2025-09-05T08:10:00Z");
    const t3 = new Date("2025-09-05T08:15:00Z");

    // First packet: start A, no end_time
    await trx("current_irrigation_plan_state").insert({
      irrigation_plan: irrigationPlanId,
      packet_date: t1,
      start_time: startA,
      end_time: null,
      activated_steps: JSON.stringify([]),
      activated_ferti_steps: JSON.stringify([]),
      waterpump_working: true,
      backwash_start_time: null,
      uses_waterpump: true,
      uses_ferti: false,
    });

    // Same cycle packet providing end_time once
    await trx("current_irrigation_plan_state").insert({
      irrigation_plan: irrigationPlanId,
      packet_date: t2,
      start_time: startA,
      end_time: t2,
      activated_steps: JSON.stringify([]),
      activated_ferti_steps: JSON.stringify([]),
      waterpump_working: true,
      backwash_start_time: null,
      uses_waterpump: true,
      uses_ferti: false,
    });

    let current = await trx("current_irrigation_plan_state").where({ irrigation_plan: irrigationPlanId }).first();
    expect(current.end_time).toEqual(t2);

    // Another same-cycle packet tries to change end_time -> must not move
    await trx("current_irrigation_plan_state").insert({
      irrigation_plan: irrigationPlanId,
      packet_date: t3,
      start_time: startA,
      end_time: t3,
      activated_steps: JSON.stringify([]),
      activated_ferti_steps: JSON.stringify([]),
      waterpump_working: true,
      backwash_start_time: null,
      uses_waterpump: true,
      uses_ferti: false,
    });

    current = await trx("current_irrigation_plan_state").where({ irrigation_plan: irrigationPlanId }).first();
    expect(current.end_time).toEqual(t2);

    // New cycle: different start_time, end_time resets
    const startB = new Date("2025-09-05T09:00:00Z");
    const t4 = new Date("2025-09-05T09:01:00Z");
    await trx("current_irrigation_plan_state").insert({
      irrigation_plan: irrigationPlanId,
      packet_date: t4,
      start_time: startB,
      end_time: null,
      activated_steps: JSON.stringify([]),
      activated_ferti_steps: JSON.stringify([]),
      waterpump_working: false,
      backwash_start_time: null,
      uses_waterpump: true,
      uses_ferti: false,
    });

    current = await trx("current_irrigation_plan_state").where({ irrigation_plan: irrigationPlanId }).first();
    expect(current.start_time).toEqual(startB);
    expect(current.end_time).toBeNull();

    const history = await trx("irrigation_plan_state").where({ irrigation_plan: irrigationPlanId }).orderBy("packet_date", "asc");
    expect(history.length).toBe(4);
    expect(history[0]!.packet_date).toEqual(t1);
    expect(history[1]!.packet_date).toEqual(t2);
    expect(history[2]!.packet_date).toEqual(t3);
    expect(history[3]!.packet_date).toEqual(t4);
  });
});
