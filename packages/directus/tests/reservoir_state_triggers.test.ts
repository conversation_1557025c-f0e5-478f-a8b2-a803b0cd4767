import {
  afterEach,
  beforeAll,
  beforeEach,
  describe,
  expect,
  it,
} from "bun:test";
import { Knex } from "knex";
import {
  begin,
  createDatabase,
  createKnex,
  rollbackAndDestroy,
} from "./helpers/db";
import {
  insertUser,
  insertAccount,
  insertProperty,
  insertReservoir,
} from "./helpers/fixtures";

let knex: Knex;
let trx: Knex.Transaction;

describe("Reservoir State Triggers", () => {
  beforeAll(async () => {
    await createDatabase("reservoir_state_test");
  });

  beforeEach(async () => {
    knex = createKnex({ database: "reservoir_state_test" });
    trx = await begin(knex);
  });

  afterEach(async () => {
    await rollbackAndDestroy(trx);
  });

  async function setupTestData() {
    const userId = await insertUser(trx);
    const accountId = await insertAccount(trx, userId);
    const propertyId = await insertProperty(trx, accountId, "Test Property");
    const reservoirId = await insertReservoir(
      trx,
      propertyId,
      "Test Reservoir"
    );
    return { userId, accountId, propertyId, reservoirId };
  }

  function closeMs(a: Date, b: Date, toleranceMs = 1000): boolean {
    return Math.abs(a.getTime() - b.getTime()) <= toleranceMs;
  }

  it("should populate reservoir_state when inserting into current_reservoir_state", async () => {
    const { reservoirId } = await setupTestData();

    await trx("current_reservoir_state")
      .where({ reservoir: reservoirId })
      .delete();
    await trx("reservoir_state").where({ reservoir: reservoirId }).delete();

    const testData = {
      reservoir: reservoirId,
      packet_date: new Date("2025-09-04T10:00:00Z"),
      start_time: new Date("2025-09-04T10:00:00Z"),
      restart_time: null,
      end_time: new Date("2025-09-04T10:30:00Z"),
      date_created: new Date("2025-09-04T10:00:00Z"),
      date_updated: new Date("2025-09-04T10:00:00Z"),
    };

    const [currentState] = await trx("current_reservoir_state")
      .insert(testData)
      .returning(["*"]);

    expect(currentState).toBeDefined();
    expect(currentState.reservoir).toBe(reservoirId);
    expect(currentState.id).toBe(reservoirId); // id set by trigger

    const historyRecords = await trx("reservoir_state")
      .where({ reservoir: reservoirId })
      .orderBy("packet_date", "desc");
    expect(historyRecords).toHaveLength(1);

    const historyRecord = historyRecords[0];
    expect(historyRecord.reservoir).toBe(reservoirId);
    expect(historyRecord.packet_date).toEqual(testData.packet_date);
    expect(historyRecord.start_time).toEqual(testData.start_time);
    expect(historyRecord.restart_time).toEqual(testData.restart_time);
    expect(historyRecord.end_time).toEqual(testData.end_time);
    expect(
      closeMs(new Date(historyRecord.date_created), testData.date_created)
    ).toBe(true);
  });

  it("should populate reservoir_state when updating current_reservoir_state", async () => {
    const { reservoirId } = await setupTestData();

    await trx("current_reservoir_state")
      .where({ reservoir: reservoirId })
      .delete();
    await trx("reservoir_state").where({ reservoir: reservoirId }).delete();

    const initialData = {
      reservoir: reservoirId,
      packet_date: new Date("2025-09-04T10:00:00Z"),
      start_time: new Date("2025-09-04T10:00:00Z"),
      restart_time: null,
      end_time: new Date("2025-09-04T10:30:00Z"),
      date_created: new Date("2025-09-04T10:00:00Z"),
      date_updated: new Date("2025-09-04T10:00:00Z"),
    };

    await trx("current_reservoir_state").insert(initialData);

    await new Promise((r) => setTimeout(r, 50));

    const updateData = {
      packet_date: new Date("2025-09-04T11:00:00Z"),
      restart_time: new Date("2025-09-04T10:45:00Z"),
      end_time: new Date("2025-09-04T11:00:00Z"),
    };

    const beforeUpdate = new Date();
    await trx("current_reservoir_state")
      .where({ reservoir: reservoirId })
      .update(updateData);

    await new Promise((r) => setTimeout(r, 50));

    const historyRecords = await trx("reservoir_state")
      .where({ reservoir: reservoirId })
      .orderBy("packet_date", "asc");
    expect(historyRecords).toHaveLength(2);

    const [insertRecord, updateRecord] = historyRecords;
    expect(insertRecord.packet_date).toEqual(initialData.packet_date);
    expect(insertRecord.restart_time).toEqual(initialData.restart_time);
    expect(updateRecord.packet_date).toEqual(updateData.packet_date);
    expect(updateRecord.restart_time).toEqual(updateData.restart_time);
    expect(closeMs(new Date(updateRecord.date_created), beforeUpdate)).toBe(
      true
    );
  });

  it("should ignore duplicate reservoir_state records on conflict", async () => {
    const { reservoirId } = await setupTestData();

    const testData = {
      reservoir: reservoirId,
      packet_date: new Date("2025-09-04T10:00:00Z"),
      start_time: new Date("2025-09-04T10:00:00Z"),
      restart_time: null,
      end_time: new Date("2025-09-04T10:30:00Z"),
      date_created: new Date("2025-09-04T10:00:00Z"),
      date_updated: new Date("2025-09-04T10:00:00Z"),
    };

    await trx("current_reservoir_state").insert(testData);

    // Attempt to create a duplicate in history via update with same key
    await trx("current_reservoir_state")
      .where({ id: reservoirId })
      .update({ ...testData, end_time: new Date("2025-09-04T10:35:00Z") });

    const historyRecords = await trx("reservoir_state").where({
      reservoir: reservoirId,
    });
    expect(historyRecords).toHaveLength(1);
  });

  it("should set id equal to reservoir in current_reservoir_state", async () => {
    const { reservoirId } = await setupTestData();

    const testData = {
      reservoir: reservoirId,
      packet_date: new Date("2025-09-04T10:00:00Z"),
      start_time: new Date("2025-09-04T10:00:00Z"),
      restart_time: null,
      end_time: null,
    };

    const [currentState] = await trx("current_reservoir_state")
      .insert(testData)
      .returning(["*"]);
    expect(currentState.id).toBe(reservoirId);
    expect(currentState.reservoir).toBe(reservoirId);
  });

  it("should auto-update date_updated on UPDATE", async () => {
    const { reservoirId } = await setupTestData();

    const initialData = {
      reservoir: reservoirId,
      packet_date: new Date("2025-09-04T10:00:00Z"),
      start_time: new Date("2025-09-04T10:00:00Z"),
      restart_time: null,
      end_time: null,
    };

    const [initialState] = await trx("current_reservoir_state")
      .insert(initialData)
      .returning(["*"]);
    const initialDateUpdated = new Date(initialState.date_updated);

    await new Promise((r) => setTimeout(r, 50));

    await trx("current_reservoir_state")
      .where({ reservoir: reservoirId })
      .update({ end_time: new Date("2025-09-04T10:30:00Z") });
    const updated = await trx("current_reservoir_state")
      .where({ reservoir: reservoirId })
      .first();
    expect(new Date(updated.date_updated).getTime()).toBeGreaterThanOrEqual(
      initialDateUpdated.getTime()
    );
  });

  it("should not move end_time within same start_time cycle and reset on new cycle", async () => {
    const { reservoirId } = await setupTestData();

    // Clean tables
    await trx("current_reservoir_state")
      .where({ reservoir: reservoirId })
      .delete();
    await trx("reservoir_state").where({ reservoir: reservoirId }).delete();

    const startA = new Date("2025-09-05T10:00:00Z");
    const t1 = new Date("2025-09-05T10:05:00Z");
    const t2 = new Date("2025-09-05T10:10:00Z");
    const t3 = new Date("2025-09-05T10:15:00Z");

    // Initial packet: start A, no end_time
    await trx("current_reservoir_state").insert({
      reservoir: reservoirId,
      packet_date: t1,
      start_time: startA,
      restart_time: null,
      end_time: null,
    });

    // Next packet same cycle provides end_time = t2 -> set once
    await trx("current_reservoir_state").insert({
      reservoir: reservoirId,
      packet_date: t2,
      start_time: startA,
      restart_time: null,
      end_time: t2,
    });

    let current = await trx("current_reservoir_state")
      .where({ reservoir: reservoirId })
      .first();
    expect(current.end_time).toEqual(t2);

    // Another packet same cycle with different end_time should NOT move end_time
    await trx("current_reservoir_state").insert({
      reservoir: reservoirId,
      packet_date: t3,
      start_time: startA,
      restart_time: null,
      end_time: t3,
    });

    current = await trx("current_reservoir_state")
      .where({ reservoir: reservoirId })
      .first();
    expect(current.end_time).toEqual(t2);

    // New cycle: different start_time, end_time resets (null)
    const startB = new Date("2025-09-05T11:00:00Z");
    const t4 = new Date("2025-09-05T11:01:00Z");
    await trx("current_reservoir_state").insert({
      reservoir: reservoirId,
      packet_date: t4,
      start_time: startB,
      restart_time: null,
      end_time: null,
    });

    current = await trx("current_reservoir_state")
      .where({ reservoir: reservoirId })
      .first();
    expect(current.start_time).toEqual(startB);
    expect(current.end_time).toBeNull();

    const history = await trx("reservoir_state")
      .where({ reservoir: reservoirId })
      .orderBy("packet_date", "asc");
    expect(history.length).toBe(4);
    expect(history[0]!.packet_date).toEqual(t1);
    expect(history[1]!.packet_date).toEqual(t2);
    expect(history[2]!.packet_date).toEqual(t3);
    expect(history[3]!.packet_date).toEqual(t4);
  });
});
