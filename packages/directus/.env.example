# Database Configuration
DB_USER=directus
DB_PASSWORD=directus
DB_DATABASE=directus

# Directus Configuration
DIRECTUS_PORT=8055
DIRECTUS_SECRET=6116487b-cda1-52c2-b5b5-c8022c45e263

# Cache Configuration
CACHE_ENABLED=true
CACHE_AUTO_PURGE=true

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=d1r3ctu5

# WebSocket Configuration
WEBSOCKETS_ENABLED=true

# URL Configuration
PUBLIC_URL=http://localhost:8055

# CORS Configuration:
# Development: It's ok to use "*" or "http://localhost:3000" for local testing
# Production: Specify exact origins like:
#  - Single origin: "https://your-domain.com"
#  - Multiple origins: "https://app.domain.com,https://admin.domain.com"
# Warning: Using "*" in production is a security risk - always specify allowed origins
CORS_ENABLED=true
CORS_ORIGIN=*

# Cookie Configuration
REFRESH_TOKEN_COOKIE_SECURE=false
REFRESH_TOKEN_COOKIE_SAME_SITE=lax
REFRESH_TOKEN_COOKIE_DOMAIN=localhost

SESSION_COOKIE_SECURE=false
SESSION_COOKIE_SAME_SITE=lax
SESSION_COOKIE_DOMAIN=localhost

# Extensions Configuration
EXTENSIONS_PATH=./extensions
EXTENSIONS_AUTO_RELOAD=true

# Content Security Policy
CONTENT_SECURITY_POLICY_DIRECTIVES__FRAME_SRC=http://localhost:3000,http://localhost:4321,http://localhost:5173,https://*.youtube.com,https://*.vimeo.com,https://*.wistia.net,https://*.loom.com


EMAIL_TRANSPORT=smtp
EMAIL_FROM=ByAgro Irriga Mais <<EMAIL>>
EMAIL_SMTP_HOST=smtp.hostinger.com
EMAIL_SMTP_PORT=465
EMAIL_SMTP_USER=<EMAIL>
EMAIL_SMTP_PASSWORD=*********
EMAIL_VERIFY_SETUP=true