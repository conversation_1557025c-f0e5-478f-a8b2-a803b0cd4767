import { codec } from "proto";

const USERNAME = "byagro";
const PASSWORD = "i8dEYH7tcNxVf18";

function getAuthHeader({ user, pass }: { user: string; pass: string }): string {
  const credentials = `${user}:${pass}`;
  if (typeof Buffer !== "undefined" && typeof Buffer.from === "function") {
    return "Basic " + Buffer.from(credentials, "utf8").toString("base64");
  }
  if (typeof btoa === "function") {
    // handle UTF-8 safely in browsers
    return "Basic " + btoa(unescape(encodeURIComponent(credentials)));
  }
  throw new Error("No base64 encoding available in this environment");
}

export const REPORT_TYPES = {
  info: 2,
  status: 3,
} as const;

export async function requestCodecReport(
  ip: string,
  reportType: keyof typeof REPORT_TYPES,
  auth: { user: string; pass: string }
) {
  const url = `http://${ip}/report`;
  const headers = {
    Authorization: getAuthHeader(auth),
    "Content-Type": "application/x-protobuf",
  };
  const body = new Uint8Array([REPORT_TYPES[reportType]]);
  return fetch(url, {
    method: "POST",
    headers,
    body,
  }).then(async (response) => {
    if (!response.ok)
      throw new Error(
        `Failed to fetch codec info from ${ip}: ${response.status} - ${
          response.statusText
        } - ${JSON.stringify(response.headers)}`
      );
    const buffer = await response.arrayBuffer();
    // Process the ArrayBuffer as needed
    const packet = codec.out.OutgoingPacket.decode(new Uint8Array(buffer));
    return packet;
  });
}

if (import.meta.main) {
  requestCodecReport("*************", "status", {
    user: USERNAME,
    pass: PASSWORD,
  })
    .then((data) => {
      console.log(data.toJSON());
    })
    .catch((error) => {
      console.error(error);
    });
}
