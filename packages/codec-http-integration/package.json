{"name": "codec-http-integration", "version": "1.0.0", "type": "module", "main": "dist/cjs/index.js", "module": "dist/index.js", "types": "dist/src/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/cjs/index.js", "types": "./dist/src/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "bun run build.ts", "clean": "rm -rf dist", "prebuild": "bun run clean"}, "dependencies": {"proto": "workspace:*"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}}