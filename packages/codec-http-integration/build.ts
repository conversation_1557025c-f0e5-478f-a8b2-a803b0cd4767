#!/usr/bin/env bun

import { build } from "bun";
import { rmSync } from "fs";
import { join } from "path";

// Clean dist directory
const distDir = join(import.meta.dir, "dist");
try {
  rmSync(distDir, { recursive: true, force: true });
  console.log("🧹 Cleaned dist directory");
} catch {
  console.log("📁 No existing dist directory to clean");
}

const entrypoint = join(import.meta.dir, "src/index.ts");

console.log("🔨 Building codec-http-integration package...");

try {
  // Build ESM bundle
  const esmResult = await build({
    entrypoints: [entrypoint],
    outdir: distDir,
    target: "browser",
    format: "esm",
    minify: false,
    sourcemap: "external",
    external: ["proto"], // External workspace dependency
  });

  if (esmResult.success) {
    console.log("✅ ESM build completed successfully");
  } else {
    console.error("❌ ESM build failed:");
    for (const log of esmResult.logs) {
      console.error(log);
    }
    process.exit(1);
  }

  // Build CommonJS bundle
  const cjsResult = await build({
    entrypoints: [entrypoint],
    outdir: join(distDir, "cjs"),
    target: "node",
    format: "cjs",
    minify: false,
    sourcemap: "external",
    external: ["proto"], // External workspace dependency
  });

  if (cjsResult.success) {
    console.log("✅ CJS build completed successfully");
  } else {
    console.error("❌ CJS build failed:");
    for (const log of cjsResult.logs) {
      console.error(log);
    }
    process.exit(1);
  }

  // Generate TypeScript declarations using tsc
  console.log("📝 Generating TypeScript declarations...");

  const tscProcess = Bun.spawn(
    [
      "bunx",
      "tsc",
      "--project",
      "tsconfig.json",
      "--declaration",
      "--emitDeclarationOnly",
      "--outDir",
      distDir,
      "--noEmit",
      "false",
    ],
    {
      cwd: import.meta.dir,
      stdio: ["inherit", "inherit", "inherit"],
    }
  );

  const tscExitCode = await tscProcess.exited;

  if (tscExitCode === 0) {
    console.log("✅ TypeScript declarations generated successfully");
  } else {
    console.error("❌ TypeScript declaration generation failed");
    process.exit(1);
  }

  console.log("🎉 Build completed successfully!");
  console.log(`📦 Output: ${distDir}`);
  console.log("📄 ESM: dist/index.js");
  console.log("📄 CJS: dist/cjs/index.js");
  console.log("📄 Types: dist/index.d.ts");
} catch (error) {
  console.error("💥 Build failed:", error);
  process.exit(1);
}
