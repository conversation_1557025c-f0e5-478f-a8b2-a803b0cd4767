#!/bin/bash

if [ $# -lt 1 ]; then
  echo "Usage: $0 <test-suite-filename> [other bun test args...]"
  exit 1
fi


# Extract the test suite file from the first parameter
TEST_SUITE_FILE="$2"

# Find the nearest directory upwards from the test suite file that contains package.json
find_package_dir() {
  local dir="$(dirname "$1")"
  while [ "$dir" != "." ] && [ "$dir" != "/" ]; do
    if [ -f "$dir/package.json" ]; then
      echo "$dir"
      return 0
    fi
    dir="$(dirname "$dir")"
  done
  return 1
}

TEST_PACKAGE=$(find_package_dir "$TEST_SUITE_FILE")
if [ -z "$TEST_PACKAGE" ]; then
  echo "Error: Could not find package.json for $TEST_SUITE_FILE"
  exit 2
fi

# Call bun with all original parameters
echo "Running tests in package: $TEST_PACKAGE"
bun --cwd="$TEST_PACKAGE" test --timeout=6000000 "$@"