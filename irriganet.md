# IrrigaNet - Technical Documentation

## Overview

IrrigaNet is an Android mobile application designed for configuring and monitoring Localized Irrigation Controllers (LIC) in agricultural irrigation systems. The application serves as a simplified configuration tool that communicates with irrigation hardware via MQTT protocol using Protocol Buffers (protobuf) for message serialization.

## Structure & Architecture

### Directory Structure

```
irriganet/
├── app/                           # Main Android application
│   ├── build.gradle              # Android build configuration
│   ├── proguard-rules.pro        # ProGuard configuration
│   └── src/main/
│       ├── AndroidManifest.xml   # App manifest
│       ├── java/br/com/byagro/irriganet/  # Kotlin source code
│       ├── proto/                 # Protocol Buffer definitions
│       └── res/                   # Android resources
├── build.gradle                   # Project-level build configuration
├── gradle/                        # Gradle wrapper and dependencies
├── gradle.properties             # Gradle properties
├── gradlew                       # Gradle wrapper script (Unix)
├── gradlew.bat                   # Gradle wrapper script (Windows)
└── settings.gradle               # Gradle settings
```

### Module Hierarchy

The application follows Android's standard architecture with these key components:

- **MainActivity.kt**: Central coordinator managing MQTT communication and navigation
- **UI Fragments**: Modular screens for different functionalities
- **DBHelper.kt**: SQLite database management layer
- **MqttManager.kt**: MQTT client wrapper using HiveMQ library
- **WebService.kt**: HTTP API communication utilities
- **SharedData.kt**: Global state management

### Entry Points

- **Main Entry**: `MainActivity` - Launched activity with navigation drawer
- **Navigation**: Fragment-based navigation using Android Navigation Component
- **Default Screen**: Report fragment (`nav_report`) showing system overview

## Technical Implementation

### Programming Languages & Frameworks

- **Primary Language**: Kotlin for Android
- **UI Framework**: Android Jetpack with Navigation Component
- **Database**: SQLite with custom ORM (DBHelper)
- **MQTT Client**: HiveMQ MQTT Client v1.3.7
- **Serialization**: Google Protocol Buffers v3.25.3
- **Async Processing**: Kotlin Coroutines
- **HTTP Client**: Android Volley v1.2.1

### Key Dependencies

```gradle
// Core Android
implementation libs.androidx.core.ktx
implementation libs.androidx.appcompat
implementation libs.material
implementation libs.androidx.constraintlayout

// Navigation
implementation libs.androidx.navigation.fragment.ktx
implementation libs.androidx.navigation.ui.ktx

// MQTT Communication
implementation("com.hivemq:hivemq-mqtt-client:1.3.7")

// Protocol Buffers
implementation 'com.google.protobuf:protobuf-kotlin-lite:3.25.3'

// JSON Processing
implementation 'com.google.code.gson:gson:2.8.9'

// HTTP Client
implementation 'com.android.volley:volley:1.2.1'

// Coroutines
implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.8.0")
implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.0")

// CBOR Serialization
implementation("com.upokecenter:cbor:4.5.2")
```

### Build System Configuration

- **Build Tool**: Gradle with Android Gradle Plugin
- **Compile SDK**: 35 (Android 14)
- **Target SDK**: 35
- **Minimum SDK**: 24 (Android 7.0)
- **Java Version**: 11
- **Kotlin JVM Target**: 11
- **Protobuf Plugin**: v0.9.5 for automatic code generation

### Database Schema

The application uses SQLite with 7 main tables:

1. **Codecs**: LIC device registry with WiFi credentials
2. **Groups**: Logical groupings of irrigation devices
3. **Mesh_Devices**: Mesh network device definitions
4. **Devices**: Individual device endpoints with operational parameters
5. **Schedulings**: Irrigation schedule definitions
6. **Device_Schedulings**: Device-specific scheduling steps
7. **Sector_Schedulings**: Sector-based scheduling configurations

## Communication & Protocols

### MQTT Communication Architecture

**Broker Configuration**:

- **Host**: `mosquitto-codec.saas.byagro.dev.br`
- **Port**: 8003
- **Protocol**: MQTT v3.1.1 and v5.0 support
- **Authentication**: Username/password (`codec` / base64 encoded)
- **Client ID**: `IrrigaNet-{UUID}` for unique identification

**Topic Structure**:

```
/codec/{device_identifier}/report    # Uplink messages (FROM device)
/codec/{device_identifier}/downlink  # Downlink messages (TO device)
```

### Protocol Buffer Message Definitions

#### Incoming Messages (TO LIC Devices)

**Container Message**:

```protobuf
message IncomingPacket {
  uint64 id = 1;
  oneof payload {
    codec.in.config.ConfigPackage config = 2;
    codec.in.devices.DevicesPackage devices = 3;
    codec.in.scheduling.SchedulingPackage scheduling = 4;
    codec.in.device_scheduling.DeviceSchedulingPackage dev_scheduling = 5;
    codec.in.automation.AutomationPackage automation = 6;
    codec.in.control.ControlPackage control = 7;
    codec.in.pause.PauseSchedulingPackage pause = 8;
    codec.in.request_info.RequestInfoPackage request_info = 9;
    codec.in.firmware_update.FirmwareUpdatePackage firmware_update = 10;
  }
}
```

#### Outgoing Messages (FROM LIC Devices)

**Container Message**:

```protobuf
message OutgoingPacket {
  uint64 id = 1;
  oneof payload {
    codec.out.info.InfoPackage info = 2;
    codec.out.status.SystemStatusPackage status = 3;
    codec.out.scheduling_report.SchedulingReportPackage scheduling_report = 4;
    codec.out.automation_report.AutomationReportPackage automation_report = 5;
    codec.out.ack.AckPackage ack = 6;
  }
}
```

### Message Exchange Mechanisms

1. **Configuration Updates**: App sends device/scheduling configurations
2. **Status Monitoring**: Devices report operational status and sensor data
3. **Control Commands**: Real-time device control (start/stop irrigation)
4. **Firmware Updates**: OTA firmware update coordination
5. **Acknowledgments**: Confirmation of received and processed messages

## User Interaction

### User Interface Architecture

**Navigation Pattern**: Material Design Navigation Drawer with Fragment-based screens

**Main Screens**:

1. **Report (Visão Geral)**: System overview and status dashboard
2. **Irrigation (Irrigação)**: Irrigation scheduling and group management
3. **Individual Pumps (Bombas)**: Service pump control interface
4. **Configuration**: System settings and device management

### User Workflows

#### Device Registration Workflow

1. Navigate to Codec Registration (`nav_codec_reg`)
2. Enter device identity, name, and WiFi credentials
3. System stores codec information and establishes MQTT subscription
4. Device appears in system overview

#### Irrigation Configuration Workflow

1. Access Irrigation screen
2. Create/modify irrigation groups
3. Configure scheduling parameters
4. Deploy configuration to LIC devices via MQTT
5. Monitor execution status

#### Device Control Workflow

1. Select device from device manager
2. Access device control interface
3. Send real-time control commands (start/stop)
4. Monitor device response and status updates

### Authentication & Authorization

- **Local Authentication**: No centralized authentication system
- **Device Access**: Direct MQTT connection to configured devices
- **Security**: WiFi credentials stored locally in SQLite database
- **Network Security**: MQTT over TLS (implied by broker configuration)

### Configuration Options

**Application Settings**:

- Rain gauge configuration (enabled/disabled, sensitivity)
- Backwash cycle parameters
- WiFi network configuration for devices
- Mesh network encryption settings
- Rainfall limits and pause durations

## Data Flow & Processing

### Message Processing Pipeline

1. **MQTT Message Reception**: HiveMqttManager receives binary messages
2. **Protocol Buffer Deserialization**: Messages parsed using generated protobuf classes
3. **Database Updates**: Relevant data stored/updated in SQLite database
4. **UI Updates**: LiveData/ViewModel pattern updates UI components
5. **User Feedback**: Status indicators and notifications

### Message Serialization/Deserialization

- **Outbound**: Kotlin objects → Protobuf binary → MQTT publish
- **Inbound**: MQTT message → Protobuf binary → Kotlin objects → Database/UI
- **Error Handling**: CRC validation and message integrity checks
- **Logging**: Comprehensive MQTT message logging with hex dump

### Error Handling & Logging

**Error Categories**:

- MQTT connection failures with automatic reconnection
- Protobuf parsing errors with graceful degradation
- Database operation failures with transaction rollback
- Network timeouts with retry mechanisms

**Logging Strategy**:

- MQTT message traffic logging with hex representation
- Database operation logging
- UI interaction logging
- Error stack trace capture

### Performance Considerations

- **Background Processing**: MQTT handling on IO dispatcher
- **Database Optimization**: Indexed queries and prepared statements
- **Memory Management**: Efficient protobuf object lifecycle
- **Battery Optimization**: Optimized MQTT keepalive intervals

## Integration Points

### External System Integrations

**MQTT Broker Integration**:

- Mosquitto broker hosted at `mosquitto-codec.saas.byagro.dev.br`
- Supports multiple concurrent client connections
- Message persistence and QoS level management

**Web Service Integration**:

- HTTP API endpoints for additional data synchronization
- JSON-based communication using Volley HTTP client
- Base64 encoding for binary data transmission

### Hardware Interface Patterns

**LIC Device Communication**:

- ESP32-based irrigation controllers
- Mesh networking capability for extended range
- Multiple I/O channels for sensors and actuators
- Firmware update capability via MQTT

**Sensor Integration**:

- Rain gauge sensors with configurable sensitivity
- Soil moisture sensors for automation triggers
- Flow sensors for irrigation monitoring
- Level sensors for reservoir management

### Database Integration

**Local Data Persistence**:

- SQLite database with foreign key constraints
- Automatic schema migration support
- Concurrent access handling with proper locking
- Data export/import capabilities for backup

**Data Synchronization**:

- Timestamp-based change detection
- Incremental updates to minimize bandwidth
- Conflict resolution for concurrent modifications
- Offline operation support with sync on reconnection

## Related System Components

### MQTT Integration Service

The broader system includes a Node.js/TypeScript MQTT integration service (`mqtt-integration/`) that:

- Processes MQTT messages from multiple LIC devices
- Integrates with the main Directus-based backend system
- Handles message queuing and reliable delivery
- Provides comprehensive logging and monitoring

### Protobuf Definitions

Shared protobuf definitions (`protobuf/`) used across:

- Android application (this component)
- MQTT integration service
- Backend API systems
- Hardware firmware implementations

### Database Integration

The Android app operates independently but shares conceptual models with the main system's PostgreSQL database managed through Directus CMS, enabling future integration and data synchronization capabilities.

## Complete SQLite Database Schema Documentation

### Database Configuration

- **Database Name**: `irriganet.db`
- **Database Version**: 55
- **Foreign Key Constraints**: Enabled
- **Migration Strategy**: Drop and recreate all tables on version upgrade

### Table Definitions

#### 1. Codecs Table

**Purpose**: Registry of LIC (Localized Irrigation Controller) devices with connection credentials and synchronization timestamps.

```sql
CREATE TABLE Codecs (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    identity TEXT NOT NULL,                    -- Unique device identifier/serial number
    name TEXT NOT NULL,                        -- Human-readable device name
    wifi_ssid TEXT,                           -- WiFi network SSID for device connection
    wifi_passwd TEXT,                         -- WiFi network password
    last_devices_update INTEGER,              -- Timestamp of last device configuration update
    last_scheduling_update INTEGER,           -- Timestamp of last scheduling update
    last_device_scheduling_update INTEGER,    -- Timestamp of last device scheduling update
    last_automation_update INTEGER,           -- Timestamp of last automation update
    last_config_update INTEGER,               -- Timestamp of last general configuration update
    enabled INTEGER DEFAULT 1                 -- Device enabled status (1=enabled, 0=disabled)
)
```

**Business Rules**:

- `identity` serves as the unique device identifier used in MQTT topics
- Timestamp fields track when each configuration type was last sent to the device
- Used for change detection to avoid unnecessary MQTT message transmission

#### 2. Groups Table

**Purpose**: Logical groupings of irrigation devices for coordinated scheduling and management.

```sql
CREATE TABLE Groups (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                       -- Group display name
    codec_idx INTEGER,                        -- Reference to parent codec
    FOREIGN KEY (codec_idx) REFERENCES Codecs(idx) ON DELETE CASCADE
)
```

**Indexes**:

```sql
CREATE INDEX idx_groups_codec_idx ON Groups(codec_idx)
```

**Business Rules**:

- Groups belong to a specific codec/LIC device
- Used to organize devices for batch scheduling operations
- Cascade delete ensures cleanup when codec is removed

#### 3. Mesh_Devices Table

**Purpose**: Mesh network device definitions representing physical irrigation hardware connected via mesh networking.

```sql
CREATE TABLE Mesh_Devices (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    identity TEXT NOT NULL,                   -- Mesh device identifier
    name TEXT NOT NULL,                       -- Device display name
    type INTEGER DEFAULT 0,                   -- Device type (valve, pump, sensor, etc.)
    mode INTEGER DEFAULT 0,                   -- Operating mode
    equipament INTEGER DEFAULT 0,             -- Equipment type/version
    check_input INTEGER DEFAULT 0,            -- Input monitoring enabled flag
    devices_bitmask INTEGER DEFAULT 0,        -- Bitmask of connected sub-devices
    level_pump_idx INTEGER DEFAULT 0,         -- Associated level sensor index
    level_pump_enable INTEGER DEFAULT 0,      -- Level-based pump control enabled
    level_pump_working_time INTEGER DEFAULT 0, -- Pump operation duration (minutes)
    codec_idx INTEGER,                        -- Parent codec reference
    group_idx INTEGER,                        -- Parent group reference
    FOREIGN KEY (codec_idx) REFERENCES Codecs(idx) ON DELETE CASCADE,
    FOREIGN KEY (group_idx) REFERENCES Groups(idx) ON DELETE CASCADE
)
```

**Indexes**:

```sql
CREATE INDEX idx_mesh_devices_codec_idx ON Mesh_Devices(codec_idx)
CREATE INDEX idx_mesh_devices_group_idx ON Mesh_Devices(group_idx)
```

**Business Rules**:

- Represents physical mesh network nodes
- Supports automation through level sensor integration
- Bitmask field tracks which sub-devices are active

#### 4. Devices Table

**Purpose**: Individual device endpoints with operational parameters and sector assignments.

```sql
CREATE TABLE Devices (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    ord_idx INTEGER NOT NULL,                 -- Ordered index for device sequencing
    mesh_idx INTEGER NOT NULL,                -- Parent mesh device reference
    identity TEXT NOT NULL,                   -- Device endpoint identifier
    type INTEGER NOT NULL,                    -- Device type code
    out1 INTEGER,                            -- Output 1 configuration
    out2 INTEGER,                            -- Output 2 configuration
    input INTEGER,                           -- Input configuration
    mode INTEGER,                            -- Device operating mode
    sector INTEGER,                          -- Irrigation sector assignment
    eqpt_ver INTEGER,                        -- Equipment version
    FOREIGN KEY (mesh_idx) REFERENCES Mesh_Devices(idx) ON DELETE CASCADE
)
```

**Indexes**:

```sql
CREATE INDEX idx_devices_mesh_idx ON Devices(mesh_idx)
CREATE INDEX idx_devices_type ON Devices(type)
CREATE INDEX idx_devices_sector ON Devices(sector)
CREATE UNIQUE INDEX u_mesh_identity ON Devices(mesh_idx, identity)
```

**Business Rules**:

- Each device belongs to a mesh device and has a unique identity within that mesh
- `ord_idx` determines execution order in scheduling
- Sector assignment enables zone-based irrigation control

#### 5. Schedulings Table

**Purpose**: Irrigation schedule definitions with timing, duration, and feature settings.

```sql
CREATE TABLE Schedulings (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    ord_idx INTEGER NOT NULL,                 -- Execution order index
    group_idx INTEGER NOT NULL,               -- Parent group reference
    name TEXT NOT NULL,                       -- Schedule display name
    hour INTEGER NOT NULL,                    -- Start hour (0-23)
    min INTEGER NOT NULL,                     -- Start minute (0-59)
    start_time INTEGER NOT NULL,              -- Start time in minutes since midnight
    end_time INTEGER DEFAULT NULL,            -- End time in minutes since midnight
    days_of_week INTEGER NOT NULL,            -- Days bitmask (bit 0=Sunday, bit 6=Saturday)
    number_of_steps INTEGER NOT NULL,         -- Total number of irrigation steps
    allow_ferti INTEGER NOT NULL,             -- Fertigation enabled (1=yes, 0=no)
    allow_backwash INTEGER NOT NULL,          -- Backwash enabled (1=yes, 0=no)
    waterpump_idx INTEGER DEFAULT NULL,       -- Water pump device index
    waterpump_ord_idx INTEGER DEFAULT NULL,   -- Water pump order index
    waterpump_working_time INTEGER DEFAULT NULL, -- Pump operation time (minutes)
    ferti_idx INTEGER DEFAULT NULL,           -- Fertigation device index
    ferti_ord_idx INTEGER DEFAULT NULL,       -- Fertigation order index
    backwash_idx INTEGER DEFAULT NULL,        -- Backwash device index
    backwash_ord_idx INTEGER DEFAULT NULL,    -- Backwash order index
    enabled INTEGER DEFAULT 1,                -- Schedule enabled status
    FOREIGN KEY (group_idx) REFERENCES Groups(idx) ON DELETE CASCADE
)
```

**Indexes**:

```sql
CREATE INDEX idx_schedulings_group_idx ON Schedulings(group_idx)
```

**Business Rules**:

- Schedules belong to device groups
- Time stored both as hour/minute and total minutes for flexibility
- Bitmask encoding for days of week (0b01111111 = all days)
- Optional features (fertigation, backwash) controlled by separate flags

#### 6. Sector_Schedulings Table

**Purpose**: Sector-based scheduling steps with fertigation and timing parameters.

```sql
CREATE TABLE Sector_Schedulings (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    scheduling_idx INTEGER NOT NULL,          -- Parent schedule reference
    device_idx INTEGER NOT NULL,             -- Target device reference
    n_order INTEGER NOT NULL,                -- Execution order within schedule
    enabled INTEGER NOT NULL,                -- Step enabled status
    type INTEGER NOT NULL,                   -- Step type (irrigation, fertigation, etc.)
    ferti INTEGER NOT NULL,                  -- Fertigation enabled for this step
    ferti_delay INTEGER NOT NULL,            -- Fertigation start delay (minutes)
    working_time INTEGER NOT NULL,           -- Step duration (minutes)
    FOREIGN KEY (scheduling_idx) REFERENCES Schedulings(idx) ON DELETE CASCADE
)
```

**Indexes**:

```sql
CREATE INDEX idx_sector_schedulings_scheduling_idx ON Sector_Schedulings(scheduling_idx)
CREATE INDEX idx_sector_schedulings_device_idx ON Sector_Schedulings(device_idx)
```

**Business Rules**:

- Defines sector-level irrigation steps within a schedule
- Execution order determined by `n_order` field
- Supports per-step fertigation control with configurable delay

#### 7. Device_Schedulings Table

**Purpose**: Device-specific scheduling steps with detailed timing and fertigation parameters.

```sql
CREATE TABLE Device_Schedulings (
    idx INTEGER PRIMARY KEY AUTOINCREMENT,
    ord_idx INTEGER NOT NULL,                 -- Ordered index for sequencing
    scheduling_idx INTEGER NOT NULL,          -- Parent schedule reference
    device_idx INTEGER NOT NULL,             -- Target device reference
    n_order INTEGER NOT NULL,                -- Execution order within schedule
    status INTEGER NOT NULL,                 -- Step status
    type INTEGER NOT NULL,                   -- Step type
    time INTEGER NOT NULL,                   -- Step start time offset (minutes)
    sector_working_time INTEGER NOT NULL,    -- Sector operation duration (minutes)
    ferti_working_time INTEGER DEFAULT NULL, -- Fertigation duration (minutes)
    ferti_delay INTEGER NOT NULL,            -- Fertigation start delay (minutes)
    FOREIGN KEY (scheduling_idx) REFERENCES Schedulings(idx) ON DELETE CASCADE
)
```

**Indexes**:

```sql
CREATE INDEX idx_device_schedulings_scheduling_idx ON Device_Schedulings(scheduling_idx)
CREATE INDEX idx_device_schedulings_device_idx ON Device_Schedulings(device_idx)
```

**Business Rules**:

- Provides device-level granularity for scheduling
- Time offsets allow precise coordination between devices
- Separate timing for sector operation and fertigation
- Status field tracks execution state

## Complete Protocol Buffer Message Documentation

### Message Flow Direction

- **Downlink Messages (TO LIC)**: Sent from Android app to irrigation devices via MQTT
- **Uplink Messages (FROM LIC)**: Sent from irrigation devices to Android app via MQTT

### Downlink Messages (IncomingPacket Container)

All downlink messages are wrapped in the `IncomingPacket` container:

```protobuf
message IncomingPacket {
  uint64 id = 1;                                    // Message timestamp/ID for tracking
  oneof payload {
    codec.in.config.ConfigPackage config = 2;
    codec.in.devices.DevicesPackage devices = 3;
    codec.in.scheduling.SchedulingPackage scheduling = 4;
    codec.in.device_scheduling.DeviceSchedulingPackage dev_scheduling = 5;
    codec.in.automation.AutomationPackage automation = 6;
    codec.in.control.ControlPackage control = 7;
    codec.in.pause.PauseSchedulingPackage pause = 8;
    codec.in.request_info.RequestInfoPackage request_info = 9;
    codec.in.firmware_update.FirmwareUpdatePackage firmware_update = 10;
  }
}
```

#### 1. ConfigPackage - System Configuration

**Purpose**: Configure LIC device system settings including WiFi, mesh networking, and irrigation parameters.

```protobuf
message ConfigPackage {
  int32 backwash_cycle = 1;                 // Backwash cycle number
  int32 backwash_duration = 2;              // Backwash duration (minutes)
  int32 backwash_delay = 3;                 // Delay before backwash (minutes)
  bool raingauge_enabled = 4;               // Rain gauge sensor enabled
  int32 raingauge_factor = 5;               // Rain gauge resolution factor
  int32 rainfall_limit = 6;                 // Rainfall limit threshold (mm)
  int32 rainfall_pause_duration = 7;        // Pause duration after rain (minutes)
  WifiConfig wifi = 8;                      // WiFi network configuration
  MeshConfig mesh = 9;                      // Mesh network configuration
}

message WifiConfig {
  string ssid = 1;                          // WiFi network SSID
  string password = 2;                      // WiFi network password
}

message MeshConfig {
  bytes key = 1;                            // Mesh network encryption key
  uint32 channel = 2;                       // Mesh network channel
}
```

**Field Descriptions**:

- `backwash_cycle`: Number of irrigation cycles before automatic backwash
- `raingauge_factor`: Pulses per millimeter for rain gauge calibration
- `rainfall_limit`: Minimum rainfall to trigger irrigation pause
- `wifi`: Network credentials for internet connectivity
- `mesh`: Mesh network settings for device-to-device communication

#### 2. DevicesPackage - Device Configuration

**Purpose**: Configure mesh network devices and their operational parameters.

```protobuf
message DevicesPackage {
  repeated DevicesData data = 1;            // List of device configurations
}

message DevicesData {
  int32 idx = 1;                           // Device slot index in array
  int32 mesh_id = 2;                       // Mesh network device ID
  int32 device_id = 3;                     // Unique device identifier
  int32 device_type = 4;                   // Device type code
  int32 out1 = 5;                          // Output 1 state/configuration
  int32 out2 = 6;                          // Output 2 state/configuration
  int32 input = 7;                         // Input state/configuration
  int32 mode = 8;                          // Device operating mode
  int32 sector = 9;                        // Irrigation sector assignment
  int32 group_idx = 10;                    // Group index for organization
  int32 eqpt_ver = 11;                     // Equipment version
}
```

**Field Descriptions**:

- `idx`: Position in device array for ordered operations
- `mesh_id`: Identifies device within mesh network
- `device_type`: Valve, pump, sensor, or other device types
- `out1/out2`: Digital output configurations for device control
- `input`: Digital input configuration for sensor reading
- `sector`: Irrigation zone assignment for scheduling

#### 3. SchedulingPackage - Irrigation Scheduling

**Purpose**: Define irrigation schedules with timing and device coordination.

```protobuf
message SchedulingPackage {
  MsgType type = 1;                         // Message content type
  repeated Scheduling scheduling_data = 2;   // List of schedules
  repeated DeviceScheduling device_scheduling_data = 3; // Device-specific steps
}

message Scheduling {
  int32 idx = 1;                           // Schedule index
  int32 start_time = 2;                    // Start time (minutes since midnight)
  int32 days_of_week = 3;                  // Days bitmask (0b01111111 = all days)
  int32 number_of_steps = 4;               // Total irrigation steps
  int32 waterpump_idx = 5;                 // Water pump device index
  int32 waterpump_working_time = 6;        // Pump operation time (minutes)
  int32 allow_ferti = 7;                   // Fertigation enabled (1=yes, 0=no)
  int32 ferti_idx = 8;                     // Fertigation device index
  int32 allow_backwash = 9;                // Backwash enabled (1=yes, 0=no)
  int32 backwash_idx = 10;                 // Backwash device index
  int32 group = 11;                        // Device group assignment
}

enum MsgType {
  MSG_NONE = 0;                            // No specific type
  MSG_SCHEDULING_ONLY = 1;                 // Only schedule data
  MSG_DEV_SCHEDULING_ONLY = 2;             // Only device scheduling data
  MSG_SCHEDULING_ALL = 3;                  // Both schedule and device data
}
```

**Field Descriptions**:

- `start_time`: Schedule start time in minutes from midnight (e.g., 360 = 6:00 AM)
- `days_of_week`: Bitmask where bit 0=Sunday, bit 1=Monday, etc.
- `number_of_steps`: Count of irrigation steps in the schedule
- `allow_ferti/allow_backwash`: Feature enable flags for optional operations

#### 4. DeviceSchedulingPackage - Device-Level Scheduling

**Purpose**: Define detailed device-specific scheduling steps with precise timing.

```protobuf
message DeviceSchedulingPackage {
  repeated DeviceScheduling data = 1;       // List of device scheduling steps
}

message DeviceScheduling {
  int32 idx = 1;                           // Device scheduling index
  int32 scheduling_idx = 2;                // Parent schedule reference
  int32 device_idx = 3;                    // Target device index
  int32 order = 4;                         // Execution order
  int32 sector_working_time = 5;           // Sector operation time (minutes)
  int32 ferti_working_time = 6;            // Fertigation time (minutes)
  int32 ferti_delay = 7;                   // Fertigation start delay (minutes)
}
```

**Field Descriptions**:

- `order`: Determines sequence of device activation within schedule
- `sector_working_time`: Duration for main irrigation operation
- `ferti_working_time`: Duration for fertigation if enabled
- `ferti_delay`: Time to wait before starting fertigation

#### 5. AutomationPackage - Automated Control Rules

**Purpose**: Configure automated responses based on sensor inputs.

```protobuf
message AutomationPackage {
  repeated AutomationData data = 1;         // List of automation rules
}

message AutomationData {
  int32 level_idx = 1;                     // Level sensor device index
  int32 pump_idx = 2;                      // Pump device index
  int32 mask = 3;                          // Condition evaluation mask
  int32 value = 4;                         // Trigger threshold value
  int32 working_time = 5;                  // Pump operation duration (minutes)
}
```

**Field Descriptions**:

- `level_idx`: Index of water level sensor device
- `pump_idx`: Index of pump to activate when conditions are met
- `mask`: Bitmask defining trigger conditions (e.g., low level = 6)
- `value`: Threshold value for sensor comparison
- `working_time`: Duration to run pump when triggered

#### 6. ControlPackage - Real-Time Device Control

**Purpose**: Send immediate control commands to specific devices.

```protobuf
message ControlPackage {
  int32 idx = 1;                           // Target device index
  MsgAction action = 2;                    // Control action to perform
  int32 working_time = 3;                  // Operation duration (minutes)
}

enum MsgAction {
  MSG_NONE = 0;                            // No action
  MSG_TURN_ON = 1;                         // Turn device on
  MSG_TURN_OFF = 2;                        // Turn device off
}
```

**Field Descriptions**:

- `idx`: Index of device to control
- `action`: Immediate action to perform (on/off)
- `working_time`: Duration for timed operations

#### 7. PauseSchedulingPackage - Schedule Pause Control

**Purpose**: Pause or resume irrigation scheduling system-wide.

```protobuf
message PauseSchedulingPackage {
  int32 state = 1;                         // Pause state (1=pause, 0=resume)
  int32 duration = 2;                      // Pause duration (minutes, 0=indefinite)
}
```

**Field Descriptions**:

- `state`: 1 to pause scheduling, 0 to resume
- `duration`: Minutes to pause (0 for indefinite pause)

#### 8. RequestInfoPackage - Information Request

**Purpose**: Request specific information from LIC device.

```protobuf
message RequestInfoPackage {
  int32 type = 1;                          // Information type requested
}
```

**Information Types**:

- `0`: System status information
- `1`: Device status information
- `2`: Scheduling status information
- `3`: Automation status information

#### 9. FirmwareUpdatePackage - Firmware Update Control

**Purpose**: Initiate firmware update process on LIC device.

```protobuf
message FirmwareUpdatePackage {
  MsgType type = 1;                        // Update target component
  MsgProtocol protocol = 2;                // Download protocol
  int32 activation_code = 3;               // Unique activation code
  int32 version = 4;                       // Target firmware version
}

enum MsgType {
  MSG_ESP = 0;                             // ESP32 firmware update
  MSG_STM = 1;                             // STM32 firmware update
}

enum MsgProtocol {
  MSG_HTTPS = 0;                           // HTTPS download
  MSG_HTTP = 1;                            // HTTP download
}
```

**Field Descriptions**:

- `type`: Target microcontroller for update
- `protocol`: Network protocol for firmware download
- `activation_code`: Security code to authorize update
- `version`: Target firmware version number

### Uplink Messages (OutgoingPacket Container)

All uplink messages are wrapped in the `OutgoingPacket` container:

```protobuf
message OutgoingPacket {
  uint64 id = 1;                                    // Message timestamp/ID
  oneof payload {
    codec.out.info.InfoPackage info = 2;
    codec.out.status.SystemStatusPackage status = 3;
    codec.out.scheduling_report.SchedulingReportPackage scheduling_report = 4;
    codec.out.automation_report.AutomationReportPackage automation_report = 5;
    codec.out.ack.AckPackage ack = 6;
  }
}
```

#### 1. InfoPackage - Device Information

**Purpose**: Report device identification, firmware versions, and synchronization status.

```protobuf
message InfoPackage {
  string codec_id = 1;                     // Device serial number
  uint32 firmware_esp = 2;                 // ESP32 firmware version
  uint32 firmware_mesh = 3;                // Mesh firmware version
  uint32 hardware_version = 4;             // Hardware version
  uint32 resets = 5;                       // Number of system resets
  uint32 scheduling_running = 6;           // Active scheduling count
  uint32 scheduling_paused = 7;            // Scheduling pause state
  uint32 devices_id = 8;                   // Last device configuration ID
  uint32 scheduling_id = 9;                // Last scheduling configuration ID
  uint32 dev_scheduling_id = 10;           // Last device scheduling ID
  uint32 automation_id = 11;               // Last automation configuration ID
  uint32 config_id = 12;                   // Last general configuration ID
}
```

**Field Descriptions**:

- `codec_id`: Unique device identifier matching MQTT topic
- `firmware_esp/firmware_mesh`: Version numbers for firmware components
- `resets`: System stability indicator
- `*_id` fields: Configuration version tracking for synchronization

#### 2. SystemStatusPackage - Operational Status

**Purpose**: Report current system operational state and sensor readings.

```protobuf
message SystemStatusPackage {
  uint32 resets = 1;                       // Number of system resets
  uint32 scheduling_running = 2;           // Active scheduling count
  uint32 scheduling_paused = 3;            // Scheduling pause state
  uint32 paused_time = 4;                  // Minutes since pause activated
  uint32 raining = 5;                      // Rain detection status (1=raining)
  uint32 rainfall = 6;                     // 24-hour rainfall accumulation (mm)
  uint64 sync_bitmask = 7;                 // Synchronized devices bitmask
  uint64 on_bitmask = 8;                   // Active devices bitmask
  uint64 input_bitmask = 9;                // Device input states bitmask
  uint32 failed_bitmask = 12;              // System failure bitmask
}
```

**Field Descriptions**:

- `scheduling_running`: Number of currently executing schedules
- `scheduling_paused`: 1 if scheduling is paused, 0 if active
- `raining`: Current rain detection status
- `rainfall`: Accumulated rainfall in last 24 hours
- `sync_bitmask`: Bitmask showing which devices are synchronized
- `on_bitmask`: Bitmask showing which devices are currently active
- `input_bitmask`: Bitmask showing device input states
- `failed_bitmask`: Bitmask indicating system failures

#### 3. SchedulingReportPackage - Scheduling Execution Report

**Purpose**: Report irrigation schedule execution status and results.

```protobuf
message SchedulingReportPackage {
  repeated SchedulingReportData data = 1;   // List of scheduling reports
}

message SchedulingReportData {
  int32 scheduling_idx = 1;                // Schedule index
  uint64 start_time = 2;                   // Schedule start timestamp
  uint64 end_time = 3;                     // Schedule end timestamp
  uint64 sector_bitmask1 = 4;              // Sectors activated (bits 0-63)
  uint64 sector_bitmask2 = 5;              // Sectors activated (bits 64-127)
  uint64 ferti_bitmask1 = 6;               // Fertigation sectors (bits 0-63)
  uint64 ferti_bitmask2 = 7;               // Fertigation sectors (bits 64-127)
  uint32 waterpump = 8;                    // Water pump status
  uint32 backwash = 9;                     // Backwash status
  uint64 backwash_time = 10;               // Backwash start timestamp
  int32 status = 11;                       // Schedule execution status
}
```

**Field Descriptions**:

- `scheduling_idx`: Reference to executed schedule
- `start_time/end_time`: Execution time window (Unix timestamps)
- `sector_bitmask1/2`: 128-bit bitmask showing which sectors were activated
- `ferti_bitmask1/2`: 128-bit bitmask showing which sectors received fertigation
- `waterpump`: Water pump operational status during schedule
- `backwash`: Backwash system status
- `status`: Overall execution status code

#### 4. AutomationReportPackage - Automation Execution Report

**Purpose**: Report automated control actions triggered by sensor inputs.

```protobuf
message AutomationReportPackage {
  repeated AutomationReportData data = 1;   // List of automation reports
}

message AutomationReportData {
  int32 auto_idx = 1;                      // Automation rule index
  uint64 start_time = 2;                   // Automation start timestamp
  uint64 restart_time = 3;                 // Restart timestamp (0 if no restart)
  uint64 end_time = 4;                     // End timestamp (0 if still running)
  int32 status = 5;                        // Execution status
}
```

**Field Descriptions**:

- `auto_idx`: Reference to automation rule that triggered
- `start_time`: When automation was first triggered
- `restart_time`: When automation was restarted (if applicable)
- `end_time`: When automation completed (0 if still active)
- `status`: Execution status code

#### 5. AckPackage - Acknowledgment

**Purpose**: Acknowledge receipt and processing of downlink messages.

```protobuf
message AckPackage {
  uint32 package = 1;                      // Acknowledged message type
  uint32 value = 2;                        // Acknowledgment value/status
}
```

**Field Descriptions**:

- `package`: Type of message being acknowledged
- `value`: Acknowledgment status or result code

## Database-Protobuf Relationship Mapping

### Downlink Message Construction (Database → Protobuf)

#### ConfigPackage Construction

**Source Tables**: Application SharedPreferences and system configuration
**Database Fields → Protobuf Fields**:

```kotlin
// From SharedPreferences and system settings
ConfigPackage {
  backwash_cycle = sharedPref.getInt("backwashCycle", 10)
  backwash_duration = sharedPref.getInt("backwashDuration", 5)
  backwash_delay = sharedPref.getInt("backwashDelay", 2)
  raingauge_enabled = sharedPref.getBoolean("rainGaugeEnabled", false)
  raingauge_factor = sharedPref.getInt("rainGaugeFactor", 1)
  rainfall_limit = sharedPref.getInt("rainfallLimit", 5)
  rainfall_pause_duration = sharedPref.getInt("rainfallPauseDuration", 60)
  wifi {
    ssid = codec.wifi_ssid
    password = codec.wifi_passwd
  }
  mesh {
    key = meshEncryptionKey
    channel = meshChannel
  }
}
```

#### DevicesPackage Construction

**Source Tables**: `Devices`, `Mesh_Devices`, `Groups`
**SQL Query**:

```sql
SELECT
  d.ord_idx AS ix,
  d.mesh_idx AS mi,
  d.identity AS di,
  d.type AS tp,
  d.out1 AS o1,
  d.out2 AS o2,
  d.input AS ip,
  d.mode AS md,
  d.sector AS sc,
  m.group_idx AS gp,
  d.eqpt_ver AS eq
FROM Devices d
JOIN Mesh_Devices m ON d.mesh_idx = m.idx
WHERE m.codec_idx = ?
ORDER BY d.ord_idx
```

**Database Fields → Protobuf Fields**:

```kotlin
DevicesData {
  idx = row["ix"]                    // d.ord_idx
  mesh_id = row["mi"]                // d.mesh_idx
  device_id = row["di"]              // d.identity
  device_type = row["tp"]            // d.type
  out1 = row["o1"]                   // d.out1
  out2 = row["o2"]                   // d.out2
  input = row["ip"]                  // d.input
  mode = row["md"]                   // d.mode
  sector = row["sc"]                 // d.sector
  group_idx = row["gp"]              // m.group_idx
  eqpt_ver = row["eq"]               // d.eqpt_ver
}
```

#### SchedulingPackage Construction

**Source Tables**: `Schedulings`, `Groups`
**SQL Query**:

```sql
SELECT
  s.ord_idx AS si,
  s.start_time AS st,
  s.days_of_week AS dw,
  s.number_of_steps AS ns,
  s.waterpump_ord_idx AS wi,
  s.waterpump_working_time AS wt,
  s.allow_ferti AS af,
  s.ferti_ord_idx AS fi,
  s.allow_backwash AS ab,
  s.backwash_ord_idx AS bi,
  s.group_idx AS gi
FROM Schedulings s
JOIN Groups g ON s.group_idx = g.idx
WHERE g.codec_idx = ?
ORDER BY s.ord_idx
```

**Database Fields → Protobuf Fields**:

```kotlin
Scheduling {
  idx = row["si"]                    // s.ord_idx
  start_time = row["st"]             // s.start_time
  days_of_week = row["dw"]           // s.days_of_week
  number_of_steps = row["ns"]        // s.number_of_steps
  waterpump_idx = row["wi"]          // s.waterpump_ord_idx
  waterpump_working_time = row["wt"] // s.waterpump_working_time
  allow_ferti = row["af"]            // s.allow_ferti
  ferti_idx = row["fi"]              // s.ferti_ord_idx
  allow_backwash = row["ab"]         // s.allow_backwash
  backwash_idx = row["bi"]           // s.backwash_ord_idx
  group = row["gi"]                  // s.group_idx
}
```

#### DeviceSchedulingPackage Construction

**Source Tables**: `Device_Schedulings`, `Schedulings`
**SQL Query**:

```sql
SELECT
  ds.ord_idx AS di,
  ds.scheduling_idx AS si,
  ds.device_idx AS dx,
  ds.n_order AS no,
  ds.sector_working_time AS st,
  ds.ferti_working_time AS ft,
  ds.ferti_delay AS fd
FROM Device_Schedulings ds
JOIN Schedulings s ON ds.scheduling_idx = s.idx
JOIN Groups g ON s.group_idx = g.idx
WHERE g.codec_idx = ?
ORDER BY ds.ord_idx
```

**Database Fields → Protobuf Fields**:

```kotlin
DeviceScheduling {
  idx = row["di"]                    // ds.ord_idx
  scheduling_idx = row["si"]         // ds.scheduling_idx
  device_idx = row["dx"]             // ds.device_idx
  order = row["no"]                  // ds.n_order
  sector_working_time = row["st"]    // ds.sector_working_time
  ferti_working_time = row["ft"]     // ds.ferti_working_time
  ferti_delay = row["fd"]            // ds.ferti_delay
}
```

#### AutomationPackage Construction

**Source Tables**: `Mesh_Devices`
**SQL Query**:

```sql
SELECT
  level_pump_idx AS li,
  ord_idx AS pi,
  6 AS mk,                          -- Fixed mask value for level automation
  0 AS vl,                          -- Fixed trigger value
  level_pump_working_time AS wt
FROM Mesh_Devices
WHERE codec_idx = ?
  AND level_pump_enable = 1
  AND level_pump_idx IS NOT NULL
```

**Database Fields → Protobuf Fields**:

```kotlin
AutomationData {
  level_idx = row["li"]              // level_pump_idx
  pump_idx = row["pi"]               // ord_idx (pump device index)
  mask = row["mk"]                   // Fixed value: 6 (level trigger)
  value = row["vl"]                  // Fixed value: 0 (threshold)
  working_time = row["wt"]           // level_pump_working_time
}
```

### Uplink Message Processing (Protobuf → Database)

#### InfoPackage Processing

**Target Tables**: `Codecs` table timestamp updates
**Protobuf Fields → Database Updates**:

```kotlin
// Update codec synchronization timestamps
UPDATE Codecs SET
  last_devices_update = info.devices_id,
  last_scheduling_update = info.scheduling_id,
  last_device_scheduling_update = info.dev_scheduling_id,
  last_automation_update = info.automation_id,
  last_config_update = info.config_id
WHERE identity = info.codec_id
```

#### SystemStatusPackage Processing

**Target Tables**: In-memory state management (codecUpdateOnMap)
**Protobuf Fields → Application State**:

```kotlin
LatestCodecUpdates {
  codecFirmware = status.firmware_esp
  pauseScheduling = (status.scheduling_paused == 1)
  schedulingRunning = (status.scheduling_running > 0)
  syncBitmask = status.sync_bitmask
  onBitmask = status.on_bitmask
  inputBitmask = status.input_bitmask
  failedBitmask = status.failed_bitmask
  rainStatus = (status.raining == 1)
  rainfall = status.rainfall
  updated = true
}
```

#### Report Message Processing

**Target Tables**: Message storage in ConcurrentHashMap
**Processing Logic**:

```kotlin
// Store scheduling reports for later analysis
messageList[codecIdentity to SCHEDULING_REPORT] = packet.toByteArray()

// Store automation reports for later analysis
messageList[codecIdentity to AUTOMATION_REPORT] = packet.toByteArray()

// Persist message list to SharedPreferences
saveMessageList()
```

## Data Construction and Source Documentation

### Message Construction Requirements

#### ConfigPackage Data Sources

**Required Data**:

- **SharedPreferences**: `backwashCycle`, `backwashDuration`, `backwashDelay`, `rainGaugeEnabled`, `rainGaugeFactor`, `rainfallLimit`, `rainfallPauseDuration`
- **Codecs Table**: `wifi_ssid`, `wifi_passwd` from target codec record
- **System Constants**: Mesh encryption key and channel (hardcoded or configuration)

**Construction SQL**: N/A (uses SharedPreferences and constants)

**Validation Rules**:

- WiFi SSID and password must not be null for device connectivity
- Rain gauge factor must be positive integer
- Rainfall limit must be non-negative
- Duration values must be positive integers

#### DevicesPackage Data Sources

**Required Tables**: `Devices`, `Mesh_Devices`, `Groups`
**Construction SQL**:

```sql
SELECT
  d.ord_idx, d.mesh_idx, d.identity, d.type,
  d.out1, d.out2, d.input, d.mode, d.sector,
  m.group_idx, d.eqpt_ver
FROM Devices d
JOIN Mesh_Devices m ON d.mesh_idx = m.idx
JOIN Groups g ON m.group_idx = g.idx
WHERE g.codec_idx = ?
ORDER BY d.ord_idx
```

**Validation Rules**:

- Device identity must be unique within mesh device
- Device type must be valid integer code
- Sector assignment must be positive integer or null
- ord_idx determines message order and must be sequential

**Default Values**:

- `out1`, `out2`, `input`, `mode`: Default to 0 if null
- `sector`: Default to 0 if null
- `eqpt_ver`: Default to 0 if null

#### SchedulingPackage Data Sources

**Required Tables**: `Schedulings`, `Groups`
**Construction SQL**:

```sql
SELECT
  s.ord_idx, s.start_time, s.days_of_week, s.number_of_steps,
  s.waterpump_ord_idx, s.waterpump_working_time,
  s.allow_ferti, s.ferti_ord_idx,
  s.allow_backwash, s.backwash_ord_idx,
  s.group_idx
FROM Schedulings s
JOIN Groups g ON s.group_idx = g.idx
WHERE g.codec_idx = ? AND s.enabled = 1
ORDER BY s.ord_idx
```

**Validation Rules**:

- `start_time` must be 0-1439 (minutes in day)
- `days_of_week` must be valid bitmask (0-127)
- `number_of_steps` must match actual device scheduling count
- Optional pump indices must reference valid devices

**Business Logic**:

- Only enabled schedules are included in message
- Pump working times default to 0 if not specified
- Feature flags (ferti, backwash) control optional operations

#### DeviceSchedulingPackage Data Sources

**Required Tables**: `Device_Schedulings`, `Schedulings`, `Groups`
**Construction SQL**:

```sql
SELECT
  ds.ord_idx, ds.scheduling_idx, ds.device_idx, ds.n_order,
  ds.sector_working_time, ds.ferti_working_time, ds.ferti_delay
FROM Device_Schedulings ds
JOIN Schedulings s ON ds.scheduling_idx = s.idx
JOIN Groups g ON s.group_idx = g.idx
WHERE g.codec_idx = ?
ORDER BY ds.ord_idx
```

**Validation Rules**:

- `n_order` must be sequential within each scheduling
- `sector_working_time` must be positive integer
- `ferti_working_time` can be null (defaults to 0)
- `ferti_delay` must be non-negative

**Business Logic**:

- Device scheduling steps define precise irrigation timing
- Order determines sequence of device activation
- Fertigation timing is optional and controlled per step

#### AutomationPackage Data Sources

**Required Tables**: `Mesh_Devices`
**Construction SQL**:

```sql
SELECT
  level_pump_idx, ord_idx, level_pump_working_time
FROM Mesh_Devices
WHERE codec_idx = ?
  AND level_pump_enable = 1
  AND level_pump_idx IS NOT NULL
  AND level_pump_working_time > 0
```

**Validation Rules**:

- `level_pump_idx` must reference valid device
- `level_pump_working_time` must be positive
- Level pump automation must be explicitly enabled

**Business Logic**:

- Automation rules are generated from mesh device level pump configuration
- Fixed mask value (6) represents low water level trigger
- Fixed threshold value (0) for binary level detection

### Message Processing Requirements

#### Change Detection Logic

**Timestamp Comparison**:

```kotlin
// Check if update needed based on timestamps
if (codec["last_devices_update"] > codecUpdateOnMap[identity]?.devicesId) {
    // Send DevicesPackage
}
if (codec["last_scheduling_update"] > codecUpdateOnMap[identity]?.schedulingId) {
    // Send SchedulingPackage
}
// Similar checks for other message types
```

**Update Triggers**:

- Database modifications update corresponding timestamp in Codecs table
- Timestamp comparison determines which messages need transmission
- Prevents unnecessary MQTT traffic for unchanged configurations

#### Message Serialization Process

**Protobuf Serialization**:

```kotlin
val packet = IncomingPacketOuterClass.IncomingPacket.newBuilder()
    .setId(timestamp)
    .setDevices(devicePackage)
    .build()

val payload = packet.toByteArray()
val crc = Utils.crc16(payload)
val crcBytes = byteArrayOf(
    ((crc ushr 8) and 0xFF).toByte(),
    (crc and 0xFF).toByte()
)
val finalPayload = payload + crcBytes
```

**Quality Assurance**:

- CRC16 checksum appended to all messages
- Message ID uses timestamp for tracking and deduplication
- Binary payload transmitted via MQTT with QoS level 0

#### Error Handling and Recovery

**Database Transaction Management**:

- Batch operations use database transactions
- Rollback on any insertion failure
- Foreign key constraints prevent orphaned records

**MQTT Communication Resilience**:

- Automatic reconnection on connection loss
- Message queuing during disconnection periods
- Retry logic for failed transmissions

**Data Validation**:

- Input validation before database insertion
- Protobuf field validation before serialization
- Range checking for numeric values
- Null handling for optional fields

## SQLite Database Enumerated Columns Analysis

Based on my analysis of the IrrigaNet Android application codebase, I have identified all columns that function as enumeration fields storing integer codes representing categorical values. Here is the comprehensive mapping:

### **Table: Codecs**

**Enumerated Column: `enabled`**

- **Values**:
  - `0` = Disabled
  - `1` = Enabled (default)
- **Source**: Database schema default value
- **Business Rules**: Controls whether the codec device is active in the system

---

### **Table: Mesh_Devices**

#### **Column: `type`**

- **Values** (from `MainType` enum in `MeshDeviceConfigFragment.kt`):
  - `0` = Valve (Acionador de válvulas)
  - `1` = Pump (Bomba d'água)
  - `2` = Level (Controle de nível)
- **Source**: `MeshDeviceConfigFragment.kt` lines 47-51
- **Business Rules**: Determines the primary function of the mesh device

#### **Column: `mode`**

- **Values** (from device configuration logic):
  - `0` = Continuous mode (Modo contínuo)
  - `1` = Pulse mode (Modo pulso)
- **Bitmask Constants** (from `MeshDeviceConfigFragment.kt` lines 23-24):
  - `PULSE = 0x01` (bit 0)
  - `MONI = 0x02` (bit 1)
- **Source**: `MeshDeviceConfigFragment.kt` lines 191-200, 522
- **Business Rules**:
  - Continuous mode (0) enables fertigation visibility
  - Pulse mode (1) disables fertigation visibility
  - Can be combined with monitoring flag using bitwise OR

#### **Column: `equipament`**

- **Values** (from `EquiptType` enum in `MeshDeviceConfigFragment.kt`):
  - `0` = PL10 equipment type
  - `1` = PL50 equipment type
- **Source**: `MeshDeviceConfigFragment.kt` lines 62-65
- **Business Rules**:
  - PL10: Enables fertigation and backwash switches, hides pump mode layout
  - PL50: Conditional fertigation visibility, hides backwash, shows pump mode layout

#### **Column: `check_input`**

- **Values**:
  - `0` = Input monitoring disabled
  - `1` = Input monitoring enabled
- **Source**: UI switch state in mesh device configuration
- **Business Rules**: Controls whether device monitors input signals

#### **Column: `level_pump_enable`**

- **Values**:
  - `0` = Level-based pump control disabled
  - `1` = Level-based pump control enabled
- **Source**: Database insertion logic
- **Business Rules**: Enables automatic pump activation based on level sensor readings

---

### **Table: Devices**

#### **Column: `type`**

- **Values** (from `DevType` enum in `MeshDeviceConfigFragment.kt`):
  - `0` = Valve (Válvula)
  - `1` = IrrigationPump (Bomba de irrigação)
  - `2` = Ferti (Fertirrigação)
  - `3` = Backwash (Retrolavagem)
  - `4` = ServicePump (Bomba de serviço/individual)
  - `5` = Level (Sensor de nível)
- **Source**: `MeshDeviceConfigFragment.kt` lines 53-60
- **Business Rules**:
  - Only one irrigation pump (type 1) or ferti pump (type 2) allowed per group
  - Valve devices (type 0) are used in sector scheduling
  - Service pumps (type 4) can operate independently

#### **Column: `mode`**

- **Values** (device-specific mode configuration):
  - `0` = Standard/continuous mode
  - `1` = Pulse mode (PULSE bit set)
  - `2` = Monitoring mode (MONI bit set)
  - `3` = Pulse + Monitoring mode (both bits set)
- **Source**: Calculated from PULSE and MONI constants, `MeshDeviceConfigFragment.kt` line 522
- **Business Rules**: Bitmask combination of operational modes

#### **Column: `out1` and `out2`**

- **Values** (output pin assignments):
  - For valves: `out1` = `2*index + 1`, `out2` = `2*index + 2`
  - For pumps: `out1` = `"1"`, `out2` = `"2"` (pulse mode) or `"0"` (continuous)
  - For ferti: `out1` = `"2"`, `out2` = `"0"`
  - For backwash: `out1` = `"3"`, `out2` = `"0"`
- **Source**: `MeshDeviceConfigFragment.kt` lines 530-531, 574-575, 587-588, 604-605
- **Business Rules**: Maps to physical output pins on the device hardware

#### **Column: `input`**

- **Values**:
  - `"0"` = No input monitoring
  - `"1"` = Input monitoring enabled
- **Source**: `MeshDeviceConfigFragment.kt` lines 598, 606, 618
- **Business Rules**: Controls input signal monitoring for pumps

---

### **Table: Schedulings**

#### **Column: `days_of_week`**

- **Values** (bitmask for days):
  - Bit 0 = Sunday
  - Bit 1 = Monday
  - Bit 2 = Tuesday
  - Bit 3 = Wednesday
  - Bit 4 = Thursday
  - Bit 5 = Friday
  - Bit 6 = Saturday
- **Example Values**:
  - `127` (0b01111111) = All days
  - `62` (0b00111110) = Monday through Friday
  - `65` (0b01000001) = Sunday and Saturday
- **Source**: `SectorSchedulingFragment.kt` lines 56-66
- **Business Rules**: Bitmask encoding allows multiple day selection

#### **Column: `allow_ferti`**

- **Values**:
  - `0` = Fertigation disabled
  - `1` = Fertigation enabled
- **Source**: Boolean to integer conversion in `DBHelper.kt` line 541
- **Business Rules**: Controls fertigation feature availability for the schedule

#### **Column: `allow_backwash`**

- **Values**:
  - `0` = Backwash disabled
  - `1` = Backwash enabled
- **Source**: Boolean to integer conversion in `DBHelper.kt` line 542
- **Business Rules**: Controls backwash feature availability for the schedule

#### **Column: `enabled`**

- **Values**:
  - `0` = Schedule disabled
  - `1` = Schedule enabled (default)
- **Source**: Database schema default value
- **Business Rules**: Only enabled schedules are sent to devices

---

### **Table: Sector_Schedulings**

#### **Column: `enabled`**

- **Values**:
  - `0` = Sector step disabled
  - `1` = Sector step enabled
- **Source**: UI checkbox state in sector scheduling
- **Business Rules**: Controls whether individual sector steps execute

#### **Column: `type`**

- **Values**:
  - `0` = Standard irrigation step
- **Source**: `SectorSchedulingFragment.kt` line 146, database query line 785
- **Business Rules**: Currently only type 0 (standard irrigation) is used in queries

#### **Column: `ferti`**

- **Values**:
  - `0` = No fertigation for this step
  - `1` = Fertigation enabled for this step
- **Source**: Sector scheduling configuration
- **Business Rules**: Per-step fertigation control

---

### **Table: Device_Schedulings**

#### **Column: `status`**

- **Values** (execution status codes):
  - Specific values not explicitly defined in retrieved code
  - Used to track execution state of device scheduling steps
- **Source**: Database schema, exact values need further investigation
- **Business Rules**: Tracks the execution status of individual device steps

#### **Column: `type`**

- **Values** (step type codes):
  - Specific values not explicitly defined in retrieved code
  - Used to categorize different types of device scheduling steps
- **Source**: Database schema, exact values need further investigation
- **Business Rules**: Categorizes the type of operation for device steps

---

### **Protocol Buffer Enumerations**

#### **MsgAction (Control Messages)**

- **Values** (from `control.proto`):
  - `0` = MSG_NONE (No action)
  - `1` = MSG_TURN_ON (Turn device on)
  - `2` = MSG_TURN_OFF (Turn device off)
- **Source**: `control.proto` lines 11-15
- **Business Rules**: Used for real-time device control commands

#### **MsgType (Scheduling Messages)**

- **Values** (from `scheduling.proto`):
  - `0` = MSG_NONE (No specific type)
  - `1` = MSG_SCHEDULING_ONLY (Only schedule data)
  - `2` = MSG_DEV_SCHEDULING_ONLY (Only device scheduling data)
  - `3` = MSG_SCHEDULING_ALL (Both schedule and device data)
- **Source**: `scheduling.proto` lines 35-40
- **Business Rules**: Controls which data is included in scheduling messages

#### **System Status Flags**

- **Values** (boolean flags in status messages):
  - `scheduling_paused`: `0` = Active, `1` = Paused
  - `raining`: `0` = Not raining, `1` = Raining
- **Source**: `status.proto` and system status processing
- **Business Rules**: Binary status indicators for system state

### **Device Model Enumerations (Broader System)**

#### **Device Models** (from broader system context)

- **Values**:
  - `"LIC"` = Localized Irrigation Controller
  - `"WPC-PL10"` = Water Pump Controller - PL10 variant
  - `"WPC-PL50"` = Water Pump Controller - PL50 variant
  - `"VC"` = Valve Controller
  - `"RM"` = Reservoir Monitor
- **Source**: `device.ts` lines 11-17
- **Business Rules**: Defines the hardware model types in the broader irrigation system

### **Summary of Key Findings**

1. **Device Types**: The system uses a hierarchical device type system with main types (Valve, Pump, Level) and specific device types (Valve, IrrigationPump, Ferti, Backwash, ServicePump, Level)

2. **Mode Encoding**: Device modes use bitmask encoding combining PULSE (0x01) and MONI (0x02) flags

3. **Boolean Flags**: Many columns store boolean values as integers (0/1) for enabled/disabled states

4. **Bitmask Fields**: Days of week and device bitmasks use bit-level encoding for compact storage

5. **Equipment Variants**: PL10/PL50 equipment types control UI behavior and feature availability

6. **Status Tracking**: Multiple status and type fields exist but some specific enumeration values require deeper code analysis to fully document

This comprehensive mapping provides the foundation for understanding how categorical data is encoded and used throughout the IrrigaNet application's database schema and communication protocols.

## Timestamp-Based Synchronization System Documentation

### Overview

The IrrigaNet application uses a sophisticated timestamp-based synchronization mechanism to ensure that configuration changes are efficiently propagated to LIC devices. This system minimizes unnecessary MQTT traffic by only sending messages when actual changes have occurred.

### Timestamp Columns in Codecs Table

The `Codecs` table contains five timestamp columns that track the last update time for different configuration categories:

```sql
CREATE TABLE Codecs (
    -- ... other columns ...
    last_devices_update INTEGER,              -- Device configuration timestamp
    last_scheduling_update INTEGER,           -- Scheduling configuration timestamp
    last_device_scheduling_update INTEGER,    -- Device scheduling steps timestamp
    last_automation_update INTEGER,           -- Automation rules timestamp
    last_config_update INTEGER,               -- General configuration timestamp
    -- ... other columns ...
)
```

**Timestamp Format**: Unix timestamp in seconds (System.currentTimeMillis() / 1000)

### Change Detection Mechanism

#### Database Operations That Trigger Timestamp Updates

**1. Device Configuration Changes**

- **Triggers**: INSERT, UPDATE, DELETE operations on `Devices` and `Mesh_Devices` tables
- **Updated Field**: `last_devices_update`
- **Update Location**: `SectorSchedulingFragment.kt` line 350-356

```kotlin
val timeStamp = System.currentTimeMillis() / 1000
val codecIdx = dbHelper.getCodecIdxByGroup(groupIdx)
dbHelper.updateCodecFields(codecIdx ?: 0, mapOf(
    "last_devices_update" to timeStamp
))
```

**2. Scheduling Configuration Changes**

- **Triggers**: INSERT, UPDATE, DELETE operations on `Schedulings` table
- **Updated Field**: `last_scheduling_update`
- **Update Location**: `SectorSchedulingFragment.kt` line 352

```kotlin
dbHelper.updateCodecFields(codecIdx ?: 0, mapOf(
    "last_scheduling_update" to timeStamp
))
```

**3. Device Scheduling Changes**

- **Triggers**: INSERT, UPDATE, DELETE operations on `Device_Schedulings` and `Sector_Schedulings` tables
- **Updated Field**: `last_device_scheduling_update`
- **Update Location**: `SectorSchedulingFragment.kt` line 353

```kotlin
dbHelper.updateCodecFields(codecIdx ?: 0, mapOf(
    "last_device_scheduling_update" to timeStamp
))
```

**4. Automation Configuration Changes**

- **Triggers**: Changes to mesh device level pump automation settings
- **Updated Field**: `last_automation_update`
- **Update Location**: Mesh device configuration updates

**5. General Configuration Changes**

- **Triggers**: WiFi settings, rain gauge configuration, backwash settings
- **Updated Field**: `last_config_update`
- **Update Location**: `SectorSchedulingFragment.kt` line 354

```kotlin
dbHelper.updateCodecFields(codecIdx ?: 0, mapOf(
    "last_config_update" to timeStamp
))
```

#### Timestamp Comparison Logic

The synchronization system compares database timestamps with device-reported timestamps stored in the `codecUpdateOnMap` data structure:

```kotlin
// MainActivity.kt lines 224-227
if (codec["last_devices_update"] as Long > codecUpdateOnMap[identity]?.devicesId!! ||
    codec["last_scheduling_update"] as Long > codecUpdateOnMap[identity]?.schedulingId!! ||
    codec["last_device_scheduling_update"] as Long > codecUpdateOnMap[identity]?.devSchedulingId!! ||
    codec["last_automation_update"] as Long > codecUpdateOnMap[identity]?.automationId!! ||
    codec["last_config_update"] as Long > codecUpdateOnMap[identity]?.configId!!) {
    updateCodec()
    codecUpdateOnMap[identity]?.updated = false
}
```

**Comparison Rules**:

- If database timestamp > device timestamp: Send update message
- If database timestamp ≤ device timestamp: No update needed
- Missing device timestamp: Send RequestInfo to get current device state

### Message Type Determination

Based on timestamp comparison results, specific protobuf messages are constructed and sent:

#### 1. DevicesPackage (when `last_devices_update` > device timestamp)

**Trigger Condition**: `codec["last_devices_update"] > codecUpdateOnMap[identity]?.devicesId`
**Message Construction**: `MainActivity.kt` lines 309-363
**Data Source**: `dbHelper.getDevicesByCodec(codecIdx)`

#### 2. SchedulingPackage (when `last_scheduling_update` > device timestamp)

**Trigger Condition**: `codec["last_scheduling_update"] > codecUpdateOnMap[identity]?.schedulingId`
**Message Construction**: `MainActivity.kt` lines 369-427
**Data Source**: `dbHelper.getSchedulingsByCodec(codecIdx)`

#### 3. DeviceSchedulingPackage (when `last_device_scheduling_update` > device timestamp)

**Trigger Condition**: `codec["last_device_scheduling_update"] > codecUpdateOnMap[identity]?.devSchedulingId`
**Message Construction**: `MainActivity.kt` lines 429-482
**Data Source**: `dbHelper.getDevicesSchedulingsByCodec(codecIdx)`

#### 4. AutomationPackage (when `last_automation_update` > device timestamp)

**Trigger Condition**: `codec["last_automation_update"] > codecUpdateOnMap[identity]?.automationId`
**Message Construction**: `MainActivity.kt` lines 484-527
**Data Source**: `dbHelper.getLevelPumpDevices(codecIdx)`

#### 5. ConfigPackage (when `last_config_update` > device timestamp)

**Trigger Condition**: `codec["last_config_update"] > codecUpdateOnMap[identity]?.configId`
**Message Construction**: `MainActivity.kt` lines 529-586
**Data Source**: SharedPreferences and codec WiFi settings

### Message Construction Process

#### DevicesPackage Construction

**SQL Query** (`DBHelper.kt` lines 1428-1435):

```sql
SELECT d.idx, d.ord_idx, d.mesh_idx, d.identity, d.type, d.out1, d.out2, d.input, d.mode, d.sector,
       m.identity AS mesh_id, m.group_idx
FROM Devices d
JOIN Mesh_Devices m ON d.mesh_idx = m.idx
WHERE m.codec_idx = ?
ORDER BY d.idx ASC
```

**Field Mapping** (`DBHelper.kt` lines 1442-1452):

```kotlin
item["ix"] = cursor.getInt("ord_idx")           // Device order index
item["mi"] = cursor.getInt("mesh_id")           // Mesh device ID
item["di"] = cursor.getString("identity").toInt() // Device identity
item["tp"] = cursor.getInt("type")              // Device type
item["o1"] = cursor.getInt("out1")              // Output 1 configuration
item["o2"] = cursor.getInt("out2")              // Output 2 configuration
item["ip"] = cursor.getInt("input")             // Input configuration
item["md"] = cursor.getInt("mode")              // Operating mode
item["sc"] = cursor.getInt("sector")            // Sector assignment
item["gp"] = cursor.getInt("group_idx")         // Group index
item["eq"] = 0                                  // Equipment version (hardcoded)
```

#### SchedulingPackage Construction

**SQL Query** (`DBHelper.kt` lines 1540-1547):

```sql
SELECT DISTINCT s.*
FROM Schedulings s
JOIN Groups g ON s.group_idx = g.idx
JOIN Mesh_Devices m ON g.idx = m.group_idx
WHERE m.codec_idx = ? AND s.enabled = 1
ORDER BY s.idx ASC
```

**Data Transformation**: Raw scheduling data converted to protobuf format with nickname resolution for device references.

#### DeviceSchedulingPackage Construction

**SQL Query** (`DBHelper.kt` lines 1654-1665):

```sql
SELECT ds.idx, ds.ord_idx, d.ord_idx AS device_ord_idx, ds.scheduling_idx, ds.device_idx, ds.n_order, ds.status, ds.type, ds.time, ds.sector_working_time,
       ds.ferti_working_time, ds.ferti_delay, s.days_of_week, s.ord_idx AS scheduling_ord_idx
FROM Device_Schedulings ds
JOIN Schedulings s ON ds.scheduling_idx = s.idx
JOIN Groups g ON s.group_idx = g.idx
JOIN Mesh_Devices m ON g.idx = m.group_idx
JOIN Devices d ON ds.device_idx = d.idx
WHERE m.codec_idx = ? AND s.enabled = 1
GROUP BY ds.idx
ORDER BY ds.idx ASC
```

#### AutomationPackage Construction

**SQL Query** (`DBHelper.kt` lines 2031-2050):

```sql
SELECT d.idx AS device_id, d.mesh_idx AS device_mesh_idx, d.identity AS device_identity, d.type AS device_type,
       d.ord_idx AS device_ord_idx, dp.ord_idx AS level_pump_ord_idx, md.idx AS mesh_id, md.identity AS mesh_identity,
       md.level_pump_idx AS mesh_level_pump_idx, md.level_pump_working_time AS level_pump_working_time, md.name AS mesh_name
FROM Mesh_Devices md
JOIN Devices d ON d.mesh_idx = md.idx
LEFT JOIN Devices dp ON dp.idx = md.level_pump_idx
WHERE md.type = ? AND md.level_pump_enable = 1 AND md.codec_idx = ?
```

**Field Mapping** (`DBHelper.kt` lines 2058-2060):

```kotlin
item["li"] = cursor.getInt("device_ord_idx")           // Level sensor index
item["pi"] = cursor.getInt("level_pump_ord_idx")       // Pump index
item["wt"] = cursor.getInt("level_pump_working_time")  // Working time
// Fixed values added in MainActivity.kt lines 489-490:
map["mk"] = 6  // Automation mask (level trigger)
map["vl"] = 0  // Trigger value (threshold)
```

### Scheduling Running Protection

The system includes protection against configuration updates during active irrigation schedules:

```kotlin
// MainActivity.kt lines 298-307
if(codecUpdateOnMap[identity]?.schedulingRunning == true &&
   (codec["last_devices_update"] as Long > codecUpdateOnMap[identity]?.devicesId!! ||
    codec["last_scheduling_update"] as Long > codecUpdateOnMap[identity]?.schedulingId!! ||
    codec["last_device_scheduling_update"] as Long > codecUpdateOnMap[identity]?.devSchedulingId!!)) {
    // Show warning message and defer updates
    val snackbar = Snackbar.make(findViewById(android.R.id.content),
        "A configuração será realizada após a conclusão do agendamento em andamento.",
        Snackbar.LENGTH_LONG)
}
```

**Business Rule**: Configuration updates are deferred when irrigation schedules are actively running to prevent disruption of ongoing operations.

## Received MQTT Message Processing Documentation

### Overview

The IrrigaNet application processes incoming MQTT messages from LIC devices to maintain system state, update device status, and store operational reports. Each message type has specific processing logic and affects different parts of the application.

### Message Reception Flow

#### MQTT Topic Subscription

**Topic Pattern**: `/codec/{device_identity}/report`
**Subscription Setup** (`MainActivity.kt` lines 138-156):

```kotlin
val topics = mutableListOf<String>()
for (codec in codecList) {
    val identity = codec["identity"] as String
    topics.add("/codec/$identity/report")
}

mqttManager = HiveMqttManager(
    serverHost = "mosquitto-codec.saas.byagro.dev.br",
    clientId   = "IrrigaNet-"+UUID.randomUUID(),
    user       = "codec",
    pass       = "Y29kZWM=",
    topics     = topics
) { topic, data ->
    val hex = data.joinToString(" ") { "%02X".format(it) }
    Log.i("MQTT", "MQTT: [$topic] $hex")

    val identity = topic.split("/").getOrNull(2)
    handleResponse(identity, data)
}
```

#### Message Parsing and Routing

**Entry Point**: `handleResponse()` method (`MainActivity.kt` lines 707-823)
**Protobuf Parsing**:

```kotlin
val packet = OutgoingPacketOuterClass.OutgoingPacket.parseFrom(response)

when (packet.payloadCase) {
    OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.INFO -> { /* Process InfoPackage */ }
    OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.STATUS -> { /* Process SystemStatusPackage */ }
    OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.SCHEDULING_REPORT -> { /* Process SchedulingReportPackage */ }
    OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.AUTOMATION_REPORT -> { /* Process AutomationReportPackage */ }
    else -> { Log.d("CallbackHandler", "Unknown payload type received") }
}
```

### Individual Message Type Processing

#### 1. InfoPackage Processing

**Purpose**: Device identification, firmware versions, and configuration synchronization status
**Processing Location**: `MainActivity.kt` lines 722-777

**Database Effects**: None (InfoPackage doesn't directly update database tables)

**Application State Updates**:

```kotlin
val codecFirmware = infoPackage.firmwareEsp.toInt()
val pauseScheduling = infoPackage.schedulingPaused.toInt() == 1

// Update codecUpdateOnMap with device-reported timestamps
codecUpdateOnMap[codecIdentity] = LatestCodecUpdates(
    devicesId = infoPackage.devicesId.toLong(),
    schedulingId = infoPackage.schedulingId.toLong(),
    devSchedulingId = infoPackage.devSchedulingId.toLong(),
    automationId = infoPackage.automationId.toLong(),
    configId = infoPackage.configId.toLong(),
    codecFirmware = codecFirmware,
    pauseScheduling = pauseScheduling,
    // ... other fields
)
```

**Special Handling**:

- **Device Registration**: If device identity is unknown, shows codec registration dialog
- **WiFi Connection Detection**: Sets `SharedData.codecWifiIsConnected` flag
- **Timestamp Synchronization**: Updates local timestamp tracking for change detection

#### 2. SystemStatusPackage Processing

**Purpose**: Real-time operational status and sensor readings
**Processing Location**: `MainActivity.kt` lines 778-802

**Database Effects**: None (status stored in memory only)

**Application State Updates**:

```kotlin
val updated = codecUpdateOnMap[codecIdentity]?.copy(
    pauseScheduling = statusPackage.schedulingPaused.toInt() == 1,
    schedulingRunning = statusPackage.schedulingRunning.toInt() == 1,
    lastInfo = System.currentTimeMillis() / 1000,
    syncBitmask = statusPackage.syncBitmask.toLong(),
    onBitmask = statusPackage.onBitmask.toLong(),
    inputBitmask = statusPackage.inputBitmask.toLong(),
    failedBitmask = statusPackage.failedBitmask.toLong()
)

// Optional rainfall data
if(statusPackage.hasRainfall()){
    updated?.rainStatus = statusPackage.raining == 1
    updated?.rainfall = statusPackage.rainfall
}
```

**Status Indicators**:

- **Scheduling State**: Running/paused status affects UI and configuration updates
- **Device Bitmasks**: Track which devices are synchronized, active, and failed
- **Environmental Data**: Rain detection and accumulation for irrigation control

#### 3. SchedulingReportPackage Processing

**Purpose**: Irrigation schedule execution reports and results
**Processing Location**: `MainActivity.kt` lines 804-808

**Database Effects**: None (reports stored in message list)

**Message Storage**:

```kotlin
messageList[identity.toString() to OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.SCHEDULING_REPORT.number] = packet.toByteArray()
saveMessageList()
```

**Persistence Mechanism**:

- **Storage**: ConcurrentHashMap with Base64 serialization to SharedPreferences
- **Key Format**: `(device_identity, message_type_number)`
- **Purpose**: Historical analysis and debugging of irrigation execution

#### 4. AutomationReportPackage Processing

**Purpose**: Automated control action reports triggered by sensors
**Processing Location**: `MainActivity.kt` lines 810-813

**Database Effects**: None (reports stored in message list)

**Message Storage**:

```kotlin
messageList[identity.toString() to OutgoingPacketOuterClass.OutgoingPacket.PayloadCase.AUTOMATION_REPORT.number] = packet.toByteArray()
saveMessageList()
```

**Use Cases**:

- **Level Control Tracking**: Monitor automatic pump activation based on water levels
- **System Diagnostics**: Analyze automation trigger patterns and performance

#### 5. AckPackage Processing

**Purpose**: Acknowledgment of received downlink messages
**Processing Location**: Not explicitly implemented in current codebase

**Expected Behavior**: Confirmation that device received and processed configuration updates

### Message Validation and Error Handling

#### CRC Validation

**Implementation**: Messages include CRC16 checksum for integrity verification
**Location**: CRC calculation in message construction, validation on reception

#### Protobuf Parsing Errors

```kotlin
try {
    val packet = OutgoingPacketOuterClass.OutgoingPacket.parseFrom(response)
    // Process message
} catch (e: Exception) {
    e.printStackTrace()
    Log.w("CallbackHandler", "Failed to parse protobuf message")
}
```

#### Null Response Handling

```kotlin
if (response == null) {
    Log.w("CallbackHandler", "Received null response")
    return
}
```

### Message Persistence Mechanism

#### SharedPreferences Storage

**Method**: `saveMessageList()` and `loadMessageList()`
**Format**: Base64-encoded serialized ConcurrentHashMap
**Location**: `MainActivity.kt` lines 695-705

```kotlin
fun saveMessageList() {
    val baos = ByteArrayOutputStream()
    ObjectOutputStream(baos).use { oos ->
        oos.writeObject(messageList)
    }
    val b64 = Base64.encodeToString(baos.toByteArray(), Base64.NO_WRAP)

    with(sharedPref.edit()) {
        putString("message_list_b64", b64)
        apply()
    }
}
```

#### Message Retrieval

**Purpose**: Access historical reports for analysis and debugging
**Key Structure**: `Pair<String, Int>` where String is device identity and Int is message type number
**Supported Types**: SCHEDULING_REPORT (4), AUTOMATION_REPORT (5)

## Outbound Message Construction Flow Diagram

The following Mermaid diagram illustrates the complete flow from database change detection to MQTT message transmission:

```mermaid
flowchart TD
    A[Database Change Event] --> B[Update Timestamp in Codecs Table]
    B --> C[Set dataUpdated Flag in SharedPreferences]
    C --> D[Main Loop Timer Trigger<br/>Every 2 seconds]

    D --> E{dataUpdated Flag Set?}
    E -->|No| D
    E -->|Yes| F[Load All Codecs from Database]

    F --> G[For Each Codec Device]
    G --> H{Device in codecUpdateOnMap?}
    H -->|No| I[Send RequestInfo Message]
    H -->|Yes| J[Compare Timestamps]

    I --> K[Construct RequestInfoPackage]
    K --> L[Add CRC16 Checksum]
    L --> M["Publish to MQTT Topic<br/>/codec/{identity}/downlink"]

    J --> N{last_devices_update > device timestamp?}
    N -->|Yes| O[Query Database: getDevicesByCodec]
    O --> P[Build DevicesPackage]
    P --> Q[Serialize Protobuf + CRC]
    Q --> R[Publish DevicesPackage]

    J --> S{last_scheduling_update > device timestamp?}
    S -->|Yes| T[Query Database: getSchedulingsByCodec]
    T --> U[Build SchedulingPackage]
    U --> V[Serialize Protobuf + CRC]
    V --> W[Publish SchedulingPackage]

    J --> X{last_device_scheduling_update > device timestamp?}
    X -->|Yes| Y[Query Database: getDevicesSchedulingsByCodec]
    Y --> Z[Build DeviceSchedulingPackage]
    Z --> AA[Serialize Protobuf + CRC]
    AA --> BB[Publish DeviceSchedulingPackage]

    J --> CC{last_automation_update > device timestamp?}
    CC -->|Yes| DD[Query Database: getLevelPumpDevices]
    DD --> EE[Build AutomationPackage]
    EE --> FF[Serialize Protobuf + CRC]
    FF --> GG[Publish AutomationPackage]

    J --> HH{last_config_update > device timestamp?}
    HH -->|Yes| II[Load SharedPreferences + WiFi Config]
    II --> JJ[Build ConfigPackage]
    JJ --> KK[Serialize Protobuf + CRC]
    KK --> LL[Publish ConfigPackage]

    N -->|No| MM[Mark Device as Updated]
    S -->|No| MM
    X -->|No| MM
    CC -->|No| MM
    HH -->|No| MM

    R --> NN[Check Scheduling Running Status]
    W --> NN
    BB --> NN
    GG --> NN
    LL --> NN

    NN --> OO{Scheduling Currently Running?}
    OO -->|Yes| PP[Show Warning Snackbar<br/>Defer Configuration]
    OO -->|No| QQ[Send Message Immediately]

    PP --> RR[Wait for Schedule Completion]
    QQ --> SS[MQTT Publish Success]
    RR --> QQ

    SS --> TT[Continue to Next Codec]
    MM --> TT
    TT --> UU{More Codecs?}
    UU -->|Yes| G
    UU -->|No| VV[Clear dataUpdated Flag]
    VV --> D

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style O fill:#fff3e0
    style T fill:#fff3e0
    style Y fill:#fff3e0
    style DD fill:#fff3e0
    style II fill:#fff3e0
    style QQ fill:#e8f5e8
    style PP fill:#ffebee
```

### Flow Diagram Annotations

**Change Detection Phase**:

- Database operations trigger timestamp updates in the Codecs table
- SharedPreferences flags signal the need for synchronization checks

**Timestamp Comparison Phase**:

- Each codec's database timestamps are compared with device-reported timestamps
- Missing device data triggers RequestInfo message to establish baseline

**Message Construction Phase**:

- Specific database queries extract relevant data for each message type
- Data is transformed and mapped to protobuf message structures
- CRC16 checksums are added for message integrity

**Scheduling Protection**:

- Active irrigation schedules prevent immediate configuration updates
- Warning messages inform users of deferred updates

**MQTT Transmission**:

- Messages are published to device-specific downlink topics
- Success/failure handling ensures reliable delivery

## Inbound Message Processing Flow Diagram

The following Mermaid diagram illustrates the complete flow from MQTT message reception to database/state updates:

```mermaid
flowchart TD
    A["MQTT Message Received<br/>Topic: /codec/{identity}/report"] --> B[Extract Device Identity from Topic]
    B --> C[Log Message in Hex Format]
    C --> D[Call handleResponse Method]

    D --> E{Response Data Null?}
    E -->|Yes| F[Log Warning: Null Response]
    E -->|No| G[Parse Protobuf OutgoingPacket]

    F --> Z[End Processing]

    G --> H{Parsing Successful?}
    H -->|No| I[Log Exception<br/>Invalid Protobuf]
    H -->|Yes| J[Route by PayloadCase]

    I --> Z

    J --> K{InfoPackage?}
    J --> L{SystemStatusPackage?}
    J --> M{SchedulingReportPackage?}
    J --> N{AutomationReportPackage?}
    J --> O{AckPackage?}
    J --> P[Unknown Message Type<br/>Log Debug Message]

    K -->|Yes| Q[Extract Device Information]
    Q --> R[Get Firmware Versions]
    R --> S[Extract Configuration Timestamps]
    S --> T{Device Identity Unknown?}
    T -->|Yes| U[Show Codec Registration Dialog]
    T -->|No| V[Update codecUpdateOnMap]

    U --> W[Set SharedData.codecId]
    V --> W
    W --> X[Update WiFi Connection Status]
    X --> Y[Store Device Timestamps for Sync]

    L -->|Yes| AA[Extract System Status Data]
    AA --> BB[Parse Scheduling State]
    BB --> CC[Extract Device Bitmasks]
    CC --> DD[Parse Environmental Data]
    DD --> EE[Update codecUpdateOnMap Status]
    EE --> FF{Has Rainfall Data?}
    FF -->|Yes| GG[Update Rain Status & Accumulation]
    FF -->|No| HH[Status Update Complete]
    GG --> HH

    M -->|Yes| II[Store in messageList HashMap]
    II --> JJ["Key: (identity, SCHEDULING_REPORT)"]
    JJ --> KK[Serialize to SharedPreferences]

    N -->|Yes| LL[Store in messageList HashMap]
    LL --> MM["Key: (identity, AUTOMATION_REPORT)"]
    MM --> NN[Serialize to SharedPreferences]

    O -->|Yes| OO[Process Acknowledgment]
    OO --> PP[Update Message Status]

    P --> QQ[Log Unknown Payload Type]

    Y --> RR[Trigger UI Updates]
    HH --> RR
    KK --> RR
    NN --> RR
    PP --> RR
    QQ --> RR

    RR --> SS[Update Device Status Indicators]
    SS --> TT[Refresh Navigation Drawer]
    TT --> UU[Update Report Fragment]
    UU --> VV[Notify Fragment Observers]

    VV --> WW{More Messages in Queue?}
    WW -->|Yes| A
    WW -->|No| XX[Processing Complete]

    XX --> Z

    style A fill:#e1f5fe
    style G fill:#fff3e0
    style Q fill:#e8f5e8
    style AA fill:#e8f5e8
    style II fill:#f3e5f5
    style LL fill:#f3e5f5
    style I fill:#ffebee
    style P fill:#ffebee
```

### Inbound Flow Diagram Annotations

**Message Reception Phase**:

- MQTT client receives binary messages on report topics
- Device identity extracted from topic structure for routing
- Comprehensive hex logging for debugging and analysis

**Protobuf Deserialization**:

- Binary payload parsed into structured protobuf objects
- Error handling for malformed or corrupted messages
- Message type routing based on protobuf oneof fields

**InfoPackage Processing**:

- Device identification and firmware version tracking
- Configuration timestamp synchronization for change detection
- Automatic device registration for unknown devices
- WiFi connection status management

**SystemStatusPackage Processing**:

- Real-time operational status updates
- Device synchronization and activity bitmasks
- Environmental sensor data (rain gauge, etc.)
- Scheduling state tracking for UI updates

**Report Message Storage**:

- Historical scheduling and automation reports
- Persistent storage in SharedPreferences
- Key-based retrieval for analysis and debugging

**Application State Updates**:

- In-memory data structure updates (codecUpdateOnMap)
- UI component refresh and notification
- Fragment observer pattern for reactive updates

**Error Handling**:

- Graceful handling of null responses and parsing errors
- Comprehensive logging for troubleshooting
- Unknown message type handling for future extensibility

### Message Processing Performance

**Asynchronous Processing**: All message handling occurs on background threads to prevent UI blocking

**Memory Management**: Efficient protobuf object lifecycle with proper cleanup

**Persistence Strategy**: Critical data persisted immediately, transient status kept in memory

**UI Responsiveness**: Observer pattern ensures UI updates only when necessary

### Integration with Synchronization System

The inbound message processing directly feeds the timestamp-based synchronization system:

1. **InfoPackage** updates device timestamps in `codecUpdateOnMap`
2. **SystemStatusPackage** provides real-time status for scheduling protection
3. **Report messages** enable historical analysis and system diagnostics
4. **Error conditions** trigger automatic RequestInfo messages for recovery

This bidirectional communication ensures robust synchronization between the Android application and LIC devices, with comprehensive error handling and recovery mechanisms.
