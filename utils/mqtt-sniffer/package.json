{"name": "bun-react-template", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "bun --hot src/index.tsx", "start": "NODE_ENV=production bun src/index.tsx", "build": "bun run build.ts"}, "dependencies": {"bun-plugin-tailwind": "^0.0.15", "mqtt": "^5.14.1", "proto": "../../packages/protobuf", "react": "^19", "react-dom": "^19", "tailwindcss": "^4.1.11"}, "devDependencies": {"@types/react": "^19", "@types/react-dom": "^19", "@types/bun": "latest"}}