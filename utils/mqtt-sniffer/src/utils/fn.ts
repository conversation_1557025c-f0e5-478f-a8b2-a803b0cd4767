/**
 * Creates a debounced version of a function that delays its execution
 * until after the specified delay has elapsed without another call.
 *
 * @template T - The type of the function being debounced
 * @param {T} fn - The function to debounce
 * @param {number} delay - The delay in milliseconds to wait before executing the function
 * @returns {(...args: Parameters<T>) => void} - A debounced version of the function
 *
 * @example
 * // Debounce a function that will only execute 300ms after the last call
 * const debouncedFn = debounce(someFunction, 300);
 *
 * // This will execute the function only once, 300ms after the last button click
 * button.addEventListener('click', debouncedFn);
 */
export function debounce<T extends (...args: any[]) => void>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      fn(...args);
    }, delay);
  };
}

/**
 * Creates a throttled version of a function that ensures it can only be called
 * once within the specified time limit.
 *
 * @template T - The type of the function being throttled
 * @param {T} fn - The function to throttle
 * @param {number} limit - The time limit in milliseconds between function calls
 * @returns {(...args: Parameters<T>) => void} - A throttled version of the function
 *
 * @example
 * // Throttle a function to execute at most once every 500ms
 * const throttledFn = throttle(someFunction, 500);
 *
 * // This will execute the function at most once every 500ms during scrolling
 * window.addEventListener('scroll', throttledFn);
 */
export function throttle<T extends (...args: any[]) => void>(
  fn: T,
  limit: number
): (...args: Parameters<T>) => void {
  let lastCall: number | null = null;

  return (...args: Parameters<T>) => {
    const now = Date.now();

    if (lastCall === null || now - lastCall >= limit) {
      lastCall = now;
      fn(...args);
    }
  };
}

/**
 * Creates a throttled version of a function that ensures it can only be called
 * once within the specified time limit, but also guarantees that the last call
 * will be executed after the throttle period ends.
 *
 * This is particularly useful for scenarios like window resizing or scrolling
 * where you want the final state to be captured even if events come in rapidly.
 *
 * @param {(...args: any[]) => void} fn - The function to throttle
 * @param {number} limit - The time limit in milliseconds between function calls
 * @returns {(...args: any[]) => void} - A throttled version of the function that guarantees final execution
 *
 * @example
 * // Throttle a resize handler with final execution guarantee
 * const handleResize = throttleWithFinal(updateLayout, 200);
 *
 * // This will execute updateLayout at most once every 200ms,
 * // but will also guarantee that the final resize event is processed
 * window.addEventListener('resize', handleResize);
 */
export function throttleWithFinal(
  fn: (...args: any[]) => void,
  limit: number
): (...args: any[]) => void {
  let lastCall: number | null = null;
  let timeoutId: NodeJS.Timeout | null = null;
  let pendingArgs: any[] | null = null;

  return (...args: any[]) => {
    const now = Date.now();
    pendingArgs = args;

    if (lastCall === null || now - lastCall >= limit) {
      // Execute immediately if enough time has passed
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      lastCall = now;
      fn(...pendingArgs);
      pendingArgs = null;
    } else if (!timeoutId) {
      // Schedule execution for after the limit period
      timeoutId = setTimeout(() => {
        lastCall = Date.now();
        fn(...pendingArgs!);
        pendingArgs = null;
        timeoutId = null;
      }, limit - (now - lastCall!));
    }
  };
}
