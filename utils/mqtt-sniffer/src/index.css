@import "tailwindcss";

@layer base {
  :root {
    @apply text-[rgba(255,255,255,0.87)] bg-[#242424] font-sans;
  }

  body {
    @apply grid place-items-center min-w-[320px] min-h-screen relative m-0;
  }
}

body::before {
  content: "";
  position: fixed;
  inset: 0;
  z-index: -1;
  opacity: 0.05;
  background: url("./logo.svg");
  background-size: 256px;
  transform: rotate(-12deg) scale(1.35);
  animation: slide 30s linear infinite;
  pointer-events: none;
}

@keyframes slide {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 256px 224px;
  }
}

@keyframes spin {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion) {
  *,
  ::before,
  ::after {
    animation: none !important;
  }
}
