import type { OnMessageCallback } from "mqtt";

import { logger } from "../log";
import type { MQTTTopicInfo, MQTTTopicMessage, MQTTTopicType } from "../types";

/**
 * RegExp for extracting the device ID from the report topic.
 * This assumes the topic format is like "/codec/{deviceId}/report".
 */
const reportTopicRegex = /^\/codec\/([^\/]+)\/report$/;

/**
 * RegExp for extracting the device ID from the downlink topic.
 * This assumes the topic format is like "/codec/{deviceId}/downlink".
 */
const downlinkTopicRegex = /^\/codec\/([^\/]+)\/downlink$/;

/**
 * RegExp for extracting the device ID and unknown codec topic type.
 * This assumes the topic format is like "/codec/{deviceId}/{unknownTopicType}".
 */
const unknownTopicRegex = /^\/codec\/([^\/]+)\/([^\/]+)$/;

function parseTopic(topic: string): MQTTTopicInfo<MQTTTopicType> | null {
  let match = reportTopicRegex.exec(topic);
  if (match && match[1]) {
    return { deviceId: match[1], topicType: "report" };
  }
  match = downlinkTopicRegex.exec(topic);
  if (match && match[1]) {
    return { deviceId: match[1], topicType: "downlink" };
  }
  match = unknownTopicRegex.exec(topic);
  if (match && match[1] && match[2]) {
    return {
      deviceId: match[1],
      topicType: "unknown_codec_topic",
      topic: match[2],
    };
  }
  return null;
}

/**
 * Handles incoming MQTT messages.
 * @param topic - The topic on which the message was received.
 * @param payload - The message payload.
 * @param packet - The MQTT packet containing additional information.
 * @returns void
 */
export const handleMessage = (
  topic: Parameters<OnMessageCallback>[0],
  payload: Parameters<OnMessageCallback>[1],
  _packet: Parameters<OnMessageCallback>[2]
) => {
  const parsedTopic = parseTopic(topic);
  if (parsedTopic) {
    const message: MQTTTopicMessage<typeof parsedTopic.topicType> = {
      ...parsedTopic,
      payload,
    };
    return message;
  }
  return null;
};
