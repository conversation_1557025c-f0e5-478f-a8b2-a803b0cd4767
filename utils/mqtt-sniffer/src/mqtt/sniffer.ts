import { create<PERSON>Q<PERSON><PERSON>lient } from "./mqtt-client";
import type { MqttClient } from "mqtt";
import { handleMessage } from "./handle-message";
import { LoggerManager } from "@/log";
import { ConfigHolder } from "@/config";
import { codec } from "proto";
const logger = LoggerManager.getLogger("MQTTPackageSniffer");
export class MQTTPackageSniffer {
  constructor(private client: MqttClient) {}

  start() {
    this.client.subscribe("/codec/+/report");
    this.client.subscribe("/codec/+/downlink");
    this.client.on("message", (topic, payload, packet) => {
      const message = handleMessage(topic, payload, packet);
      if (message) {
        if (logger.isLevelEnabled("trace")) {
          logger.debug(
            `Received message on topic: ${topic}, deviceId: ${message.deviceId}, payload length: ${payload.length}`
          );
        }
        if (message.topicType === "report") {
          const decoded = codec.out.OutgoingPacket.decode(message.payload);
          const obj = codec.out.OutgoingPacket.toObject(decoded, {
            longs: String,
            enums: String,
            bytes: String,
            json: true,
            defaults: true,
          });
          logger.info(
            `Decoded OutgoingPacket for device ${
              message.deviceId
            }: ${JSON.stringify(obj)}, payload=${message.payload.toString(
              "hex"
            )}`
          );
        }
      } else {
        logger.warn(`Received message on unknown topic format: ${topic}`);
      }
    });
  }

  stop() {
    this.client.end();
  }
}

if (import.meta.main) {
  // Example usage
  ConfigHolder.init();
  //   const client = createMQTTClient();
  //   const sniffer = new MQTTPackageSniffer(client);
  //   sniffer.start();
  const data = Buffer.from("08f5c0e8c5062a080a0610f0bfe8c506", "hex");
  const decoded = codec.out.OutgoingPacket.decode(data);
  const obj = codec.out.OutgoingPacket.toObject(decoded, {
    longs: String,
    enums: String,
    bytes: String,
    json: true,
    defaults: true,
  });
  logger.info(`Decoded OutgoingPacket: `, obj);
  logger.info(`Decoded OutgoingPacket: `, decoded.toJSON());
  //   sniffer.stop();
}
