# Codec HTTP Scanner & CORS Tester

This directory contains two complementary tools for discovering and testing codec devices on a local network:

1. **`codec-http-scanner.ts`** - A TypeScript-based network scanner for discovering codec devices
2. **`codec-cors-tester.html`** - A web-based CORS testing tool for the discovered devices

## Overview

These tools are designed to work together in a two-step process:

1. First, run the scanner to discover codec devices on your network
2. Then, use the web-based CORS tester to verify browser compatibility

## Prerequisites

- **Bun runtime** for running the TypeScript scanner
- **Web browser** for running the CORS tester
- Network access to the target subnet
- Codec devices must be accessible via HTTP on port 80

## File Descriptions

### codec-http-scanner.ts

A network scanner that discovers codec devices by:

- Scanning IP ranges using CIDR notation (e.g., "***********/24")
- Making authenticated HTTP POST requests to `/report` endpoint
- Using Basic Authentication with hardcoded credentials
- Sending protobuf payload (single byte: `[2]`)
- Processing requests concurrently (10 simultaneous connections)
- Saving responses to individual JSON files and a consolidated list

### codec-cors-tester.html

A web application that:

- <PERSON>ads discovered IPs from `discovered_ips.json`
- Tests CORS compatibility by making the same requests from a browser
- Decodes protobuf responses using protobufjs library
- Provides visual feedback on success/failure status
- Shows detailed error information and response data

## Usage Instructions

### Step 1: Run the Network Scanner

1. **Install Bun** (if not already installed):

   ```bash
   curl -fsSL https://bun.sh/install | bash
   ```

2. **Configure the scanner** using environment variables:

   ```bash
   # Copy the example configuration
   cp .env.example .env

   # Edit the .env file with your settings
   nano .env  # or use your preferred editor
   ```

3. **Run the scanner** (Bun automatically loads .env files):

   ```bash
   bun run codec-http-scanner.ts
   ```

4. **Monitor the output**:

   - Console will show real-time discovery progress
   - Successful discoveries will be logged with response data
   - Failed attempts will show error messages

5. **Check the results**:
   - `discovered_ips.json` - List of successfully discovered IP addresses
   - `192_168_X_X.json` - Individual response files for each device
   - Console output shows summary of discovered devices

### Step 2: Test CORS Compatibility

1. **Start a local web server** in the scanner directory:

   ```bash
   # Using Python 3
   python -m http.server 8000

   # Using Node.js (if you have http-server installed)
   npx http-server

   # Using Bun
   bun --serve .
   ```

2. **Open the CORS tester** in your browser:

   ```
   http://localhost:8000/codec-cors-tester.html
   ```

3. **Load discovered IPs**:

   - Click "Load IPs from discovered_ips.json"
   - The tool will load the devices found by the scanner

4. **Run CORS tests**:

   - Click "Test All IPs" to test all discovered devices
   - Monitor the progress bar and individual results
   - Green status indicates CORS is working
   - Red status indicates CORS is blocked or other errors

5. **Review results**:
   - Each device shows success/failure status
   - CORS-allowed devices will show decoded protobuf data
   - CORS-blocked devices will show appropriate error messages
   - Toggle options to show raw response data

## Configuration

The scanner now uses environment variables for configuration, making it easy to customize without modifying the source code.

### Environment Variables Configuration

1. **Copy the example environment file**:

   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file** with your preferred settings:

```bash
# Codec HTTP Scanner Configuration

# Authentication credentials for codec devices
USERNAME=byagro
PASSWORD=i8dEYH7tcNxVf18

# Network range to scan (CIDR notation or single IP)
IP_RANGE=***********/24

# Number of simultaneous requests (concurrency)
CONCURRENCY=10

# API endpoint path
ENDPOINT_PATH=/report

# Protobuf payload byte value (decimal)
PROTOBUF_PAYLOAD=2

# Output file names
DISCOVERED_IPS_FILE=discovered_ips.json
LOG_FILE=scan.log

# Request timeout in milliseconds
TIMEOUT_MS=5000

# Enable/disable verbose logging (true/false)
VERBOSE_LOGGING=false
```

### Available Configuration Options

- **`USERNAME`**: Authentication username (default: `byagro`)
- **`PASSWORD`**: Authentication password (default: `i8dEYH7tcNxVf18`)
- **`IP_RANGE`**: Network range in CIDR notation or single IP (default: `***********/24`)
- **`CONCURRENCY`**: Number of simultaneous requests (default: `10`)
- **`ENDPOINT_PATH`**: API endpoint path (default: `/report`)
- **`PROTOBUF_PAYLOAD`**: Byte value to send in protobuf payload (default: `2`)
- **`DISCOVERED_IPS_FILE`**: Output file for discovered IPs (default: `discovered_ips.json`)
- **`TIMEOUT_MS`**: Request timeout in milliseconds (default: `5000`)
- **`VERBOSE_LOGGING`**: Enable verbose console logging (default: `false`)

### Configuration Examples

**Scan a different network:**

```bash
IP_RANGE=********/24
```

**Use different credentials:**

```bash
USERNAME=admin
PASSWORD=secret123
```

**Increase concurrency for faster scanning:**

```bash
CONCURRENCY=20
```

**Change the API endpoint:**

```bash
ENDPOINT_PATH=/api/report
```

**Enable verbose logging:**

```bash
VERBOSE_LOGGING=true
```

## Output Files

The scanner generates several files:

- **`discovered_ips.json`** - JSON array of successfully discovered IP addresses
- **`192_168_X_X.json`** - Individual response files (IP dots replaced with underscores)
- **`scan.log`** - Detailed log of all scan attempts (if logging is enabled)

## Protobuf Support

The CORS tester includes protobuf decoding capabilities:

- Automatically loads protobuf definitions from `proto/` directory
- Falls back to inline protobuf definitions if files aren't found
- Decodes `OutgoingPacket` messages containing various payload types:
  - InfoPackage
  - SystemStatusPackage
  - SchedulingReportPackage
  - AutomationReportPackage
  - AckPackage
  - RawPackage

## Troubleshooting

### Common Issues

1. **"Unable to connect" errors**:

   - Device is offline or not responding
   - Firewall blocking connections
   - Wrong network range

2. **"401 Unauthorized" errors**:

   - Incorrect credentials
   - Device requires different authentication

3. **"404 Not Found" errors**:

   - Device doesn't have `/report` endpoint
   - Wrong URL path

4. **CORS blocked in browser**:
   - Expected behavior for codec devices
   - Indicates device doesn't allow browser requests
   - Device may still work with server-side requests

### Performance Tips

- Reduce concurrency for slower networks
- Use smaller CIDR ranges for faster scans
- Monitor network load during scanning

## Security Considerations

- Credentials are stored in `.env` file (keep this file secure)
- Scanner makes requests to all IPs in the specified range
- Consider network security policies before scanning
- CORS testing reveals which devices allow browser access
- The `.env` file should not be committed to version control

## Dependencies

### TypeScript Scanner

- **Bun runtime** - JavaScript/TypeScript runtime with built-in fetch
- **Network access** - HTTP connectivity to target devices

### CORS Tester

- **Modern web browser** - Support for ES6+ and Fetch API
- **protobufjs** - Loaded from CDN for protobuf decoding
- **Local web server** - To serve the HTML file and JSON data
