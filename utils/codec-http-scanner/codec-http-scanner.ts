// Configuration with environment variable fallbacks (Bun auto-loads .env files)
const USERNAME = process.env.USERNAME || "byagro";
const PASSWORD = process.env.PASSWORD || "i8dEYH7tcNxVf18";
const IP_RANGE = process.env.IP_RANGE || "***********/24";
const CONCURRENCY = parseInt(process.env.CONCURRENCY || "10");
const ENDPOINT_PATH = process.env.ENDPOINT_PATH || "/report";
const PROTOBUF_PAYLOAD = parseInt(process.env.PROTOBUF_PAYLOAD || "2");
const DISCOVERED_IPS_FILE =
  process.env.DISCOVERED_IPS_FILE || "discovered_ips.json";
const LOG_FILE = process.env.LOG_FILE || "scan.log";
const TIMEOUT_MS = parseInt(process.env.TIMEOUT_MS || "5000");
const VERBOSE_LOGGING = process.env.VERBOSE_LOGGING === "true";

function getAuthHeader(): string {
  const credentials = `${USERNAME}:${PASSWORD}`;
  if (typeof Buffer !== "undefined" && typeof Buffer.from === "function") {
    return "Basic " + Buffer.from(credentials, "utf8").toString("base64");
  }
  if (typeof btoa === "function") {
    // handle UTF-8 safely in browsers
    return "Basic " + btoa(unescape(encodeURIComponent(credentials)));
  }
  throw new Error("No base64 encoding available in this environment");
}

function ipToInt(ip: string): number {
  const parts = ip.trim().split(".");
  if (parts.length !== 4) throw new Error(`Invalid IPv4 address: ${ip}`);
  return (
    parts.reduce((acc, part) => {
      const n = Number(part);
      if (!Number.isInteger(n) || n < 0 || n > 255)
        throw new Error(`Invalid IPv4 octet: ${part}`);
      return ((acc << 8) >>> 0) + n;
    }, 0) >>> 0
  );
}

function intToIp(num: number): string {
  return [
    (num >>> 24) & 0xff,
    (num >>> 16) & 0xff,
    (num >>> 8) & 0xff,
    num & 0xff,
  ].join(".");
}

/**
 * Generator that yields every IPv4 address for the given mask.
 * - If ipMask is a CIDR (e.g. "***********/24") it yields all addresses from network to broadcast (inclusive).
 * - If ipMask has no "/" it yields the single IP (e.g. "***********").
 */
function* iterateIpMask(ipMask: string): Generator<string> {
  const cidr = ipMask.trim();
  const m = cidr.match(/^(.+)\/(\d{1,2})$/);
  if (!m) {
    // single IP
    yield cidr;
    return;
  }

  const baseIp = m[1];
  const prefix = Number(m[2]);
  if (!Number.isInteger(prefix) || prefix < 0 || prefix > 32) {
    throw new Error(`Invalid CIDR prefix: ${m[2]}`);
  }

  const baseInt = ipToInt(baseIp);
  const mask = prefix === 0 ? 0 : (0xffffffff << (32 - prefix)) >>> 0;
  const network = baseInt & mask;
  const broadcast = (network | (~mask >>> 0)) >>> 0;

  let cur = network >>> 0;
  while (true) {
    yield intToIp(cur);
    if (cur === broadcast) break;
    cur = (cur + 1) >>> 0;
  }
}

function requestCodecInfo(ip: string) {
  const url = `http://${ip}${ENDPOINT_PATH}`;
  const headers = {
    Authorization: getAuthHeader(),
    "Content-Type": "application/x-protobuf",
  };

  return fetch(url, {
    method: "POST",
    headers,
    body: new Uint8Array([PROTOBUF_PAYLOAD]),
    signal: AbortSignal.timeout(TIMEOUT_MS),
  }).then((response) => {
    if (!response.ok)
      throw new Error(
        `Failed to fetch codec info from ${ip}: ${response.status} - ${
          response.statusText
        } - ${JSON.stringify(response.headers)}`
      );
    return response.text();
  });
}

/**
 * A queue of IP addresses to scan.
 */
const queue: string[] = [];
const completed: string[] = [];
function processQueue(
  concurrency: number,
  cb: (
    ip: string,
    result:
      | { success: true; data: any }
      | { success: false; error: Error }
      | { completed: true }
  ) => void
) {
  // Process the queue with the given concurrency
  const activeRequests: Promise<void>[] = [];
  for (let i = 0; i < concurrency; i++) {
    activeRequests.push(processNext());
  }

  function processNext(): Promise<void> {
    if (queue.length === 0) return Promise.resolve();

    const ip = queue.shift();
    if (!ip) return Promise.resolve();

    return requestCodecInfo(ip)
      .then((response) => {
        cb(ip, { success: true, data: response });
      })
      .catch((error) => {
        cb(ip, { success: false, error });
      })
      .finally(() => {
        if (VERBOSE_LOGGING) {
          console.log(
            "Queue length:",
            queue.length,
            "active requests:",
            activeRequests.length,
            "completed:",
            completed.length
          );
        }
        completed.push(ip);
        return processNext();
      });
  }

  return Promise.all(activeRequests).then(() => {
    cb("", { completed: true });
  });
}

/**
 * Scans a range of IP addresses for codec http info discovery and calls the callback with the results.
 * Performs a GET request on the http address http://<ip>/info to check for codec information.
 * @param ipMask The IP address range to scan (e.g., "***********/24").
 * @param cb The callback to call with each discovered IP address and its response.
 */
function scan(
  ipMask: string,
  cb: (
    ip: string,
    result:
      | { success: true; data: any }
      | { success: false; error: Error }
      | { completed: true }
  ) => void
) {
  console.log(`Starting scan of ${ipMask} with concurrency ${CONCURRENCY}`);
  console.log(`Using endpoint: ${ENDPOINT_PATH}, payload: ${PROTOBUF_PAYLOAD}`);

  for (const ip of iterateIpMask(ipMask)) {
    queue.push(ip);
  }

  // Process the queue with configurable concurrency
  processQueue(CONCURRENCY, cb);
}

const ips: string[] = [];
scan(IP_RANGE, (ip, result) => {
  if ("completed" in result) {
    console.log("Scan completed. Found", ips.length, "devices.");
    Bun.file(DISCOVERED_IPS_FILE).write(JSON.stringify(ips, null, 2));
    return;
  }
  if (result.success) {
    console.log(`[${ip}]: Success`);
    Bun.file(`${ip.replaceAll(".", "_")}.json`).write(result.data);
    ips.push(ip);
  } else {
    console.error(`[${ip}]:`, result.error.message);
  }
});

// Example for testing single IP
// requestCodecInfo("*************")
//   .then((data) => {
//     console.log(data);
//   })
//   .catch((error) => {
//     console.error(error);
//   });
