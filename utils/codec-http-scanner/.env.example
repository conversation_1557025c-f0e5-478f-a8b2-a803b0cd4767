# Codec HTTP Scanner Configuration
# Copy this file to .env and customize the values

# Authentication credentials for codec devices
USERNAME=byagro
PASSWORD=i8dEYH7tcNxVf18

# Network range to scan (CIDR notation or single IP)
IP_RANGE=***********/24

# Number of simultaneous requests (concurrency)
CONCURRENCY=10

# API endpoint path
ENDPOINT_PATH=/report

# Protobuf payload byte value (decimal)
PROTOBUF_PAYLOAD=2

# Output file names
DISCOVERED_IPS_FILE=discovered_ips.json
LOG_FILE=scan.log

# Request timeout in milliseconds
TIMEOUT_MS=5000

# Enable/disable verbose logging (true/false)
VERBOSE_LOGGING=false