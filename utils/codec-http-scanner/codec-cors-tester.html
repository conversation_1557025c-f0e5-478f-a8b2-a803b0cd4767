<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Codec CORS Tester</title>
    <script src="https://cdn.jsdelivr.net/npm/protobufjs@7.5.4/dist/protobuf.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .controls {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .data-toggle-container {
            display: flex;
            flex-direction: column;
            flex-wrap: wrap;
        }

        .button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }

        .button:hover {
            background-color: #2980b9;
        }

        .button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        .results {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .result-item {
            border-bottom: 1px solid #ecf0f1;
            padding: 15px;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .ip-address {
            font-weight: bold;
            color: #2c3e50;
            font-size: 18px;
        }

        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }

        .status.success {
            background-color: #2ecc71;
            color: white;
        }

        .status.error {
            background-color: #e74c3c;
            color: white;
        }

        .status.pending {
            background-color: #f39c12;
            color: white;
        }

        .response-data {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }

        .error-message {
            color: #e74c3c;
            font-style: italic;
        }

        .cors-info {
            background-color: #e8f5e8;
            border-left: 4px solid #2ecc71;
            padding: 10px;
            margin-top: 10px;
        }

        .cors-blocked {
            background-color: #ffeaea;
            border-left: 4px solid #e74c3c;
            padding: 10px;
            margin-top: 10px;
        }

        .summary {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .progress {
            background-color: #ecf0f1;
            border-radius: 4px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-bar {
            background-color: #3498db;
            height: 100%;
            transition: width 0.3s ease;
        }

        .decoded-data {
            background-color: #e8f8f5;
            border-left: 4px solid #2ecc71;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .decoding-error {
            background-color: #fdf2e9;
            border-left: 4px solid #f39c12;
            padding: 10px;
            margin-top: 10px;
            color: #e67e22;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>Codec CORS Tester</h1>
        <p>Tests CORS compatibility for discovered codec devices</p>
    </div>

    <div class="controls">
        <button id="loadIpsBtn" class="button">Load IPs from discovered_ips.json</button>
        <button id="testAllBtn" class="button" disabled>Test All IPs</button>
        <button id="clearBtn" class="button">Clear Results</button>
        <span class="data-toggle-container">
            <label>
                <input type="checkbox" id="showRawData"> Show Raw Response Data
            </label>
            <label>
                <input type="checkbox" id="showDecodedData" checked> Decode Protobuf Data
            </label>
        </span>
    </div>

    <div class="summary">
        <div id="summaryText">Load IP addresses to begin testing</div>
        <div class="progress">
            <div id="progressBar" class="progress-bar" style="width: 0%"></div>
        </div>
    </div>

    <div id="results" class="results"></div>

    <script>
        // Codec scanner configuration
        const USERNAME = "byagro";
        const PASSWORD = "i8dEYH7tcNxVf18";

        let discoveredIps = [];
        let testResults = [];
        let protobufRoot = null;

        // Initialize protobuf definitions
        async function initProtobuf() {
            try {
                protobufRoot = await protobuf.load([
                    'proto/outgoing_packet.proto',
                    'proto/info.proto',
                    'proto/status.proto',
                    'proto/scheduling_report.proto',
                    'proto/automation_report.proto',
                    'proto/ack.proto',
                    'proto/raw.proto'
                ]);
                console.log('Protobuf definitions loaded successfully');
                return true;
            } catch (error) {
                console.error('Failed to load protobuf definitions:', error);
                // Create inline protobuf definitions as fallback
                createInlineProtobufDefinitions();
                return false;
            }
        }

        // Create protobuf definitions inline as fallback
        function createInlineProtobufDefinitions() {
            const protoSource = `
                syntax = "proto3";
                
                package codec.out;
                
                message OutgoingPacket {
                    uint64 id = 1;
                    oneof payload {
                        InfoPackage info = 2;
                        SystemStatusPackage status = 3;
                        SchedulingReportPackage scheduling_report = 4;
                        AutomationReportPackage automation_report = 5;
                        AckPackage ack = 6;
                        RawPackage raw = 7;
                    }
                }
                
                message InfoPackage {
                    string codec_id = 1;
                    uint32 firmware_esp = 2;
                    uint32 firmware_mesh = 3;
                    uint32 hardware_version = 4;
                    uint32 resets = 5;
                    uint32 scheduling_running = 6;
                    uint32 scheduling_paused = 7;
                    uint32 devices_id = 8;
                    uint32 scheduling_id = 9;
                    uint32 dev_scheduling_id = 10;
                    uint32 automation_id = 11;
                    uint32 config_id = 12;
                    uint32 failed_bitmask = 13;
                }
                
                message SystemStatusPackage {
                    // Placeholder for status structure
                    string status = 1;
                }
                
                message SchedulingReportPackage {
                    // Placeholder for scheduling report structure
                    string report = 1;
                }
                
                message AutomationReportPackage {
                    // Placeholder for automation report structure
                    string report = 1;
                }
                
                message AckPackage {
                    // Placeholder for ack structure
                    bool success = 1;
                }
                
                message RawPackage {
                    // Placeholder for raw data structure
                    bytes data = 1;
                }
            `;

            try {
                protobufRoot = protobuf.parse(protoSource).root;
                console.log('Inline protobuf definitions created');
            } catch (error) {
                console.error('Failed to create inline protobuf definitions:', error);
            }
        }

        // Decode protobuf response
        function decodeProtobufResponse(data) {
            if (!protobufRoot) {
                return { error: 'Protobuf definitions not loaded' };
            }

            try {
                const OutgoingPacket = protobufRoot.lookupType('codec.out.OutgoingPacket');
                const buffer = new Uint8Array(data.length);
                for (let i = 0; i < data.length; i++) {
                    buffer[i] = data.charCodeAt(i);
                }

                const message = OutgoingPacket.decode(buffer);
                const object = OutgoingPacket.toObject(message, {
                    longs: String,
                    enums: String,
                    bytes: String,
                });

                return { success: true, decoded: object };
            } catch (error) {
                return { error: `Decoding failed: ${error.message}` };
            }
        }

        // Generate Basic Auth header
        function getAuthHeader() {
            const credentials = `${USERNAME}:${PASSWORD}`;
            return "Basic " + btoa(unescape(encodeURIComponent(credentials)));
        }

        // Make the same request as requestCodecInfo from the scanner
        async function requestCodecInfo(ip) {
            const url = `http://${ip}/report`;
            const headers = {
                'Authorization': getAuthHeader(),
                'Content-Type': 'application/x-protobuf',
            };

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: headers,
                    body: new Uint8Array([2]),
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status} - ${response.statusText}`);
                }

                const responseText = await response.text();
                return {
                    success: true,
                    data: responseText,
                    headers: Object.fromEntries(response.headers.entries()),
                    status: response.status
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    corsBlocked: error.message.includes('CORS') || error.message.includes('fetch')
                };
            }
        }

        // Convert binary data to hex representation for display
        function binaryToHex(str) {
            let hex = '';
            for (let i = 0; i < str.length; i++) {
                const charCode = str.charCodeAt(i);
                hex += charCode.toString(16).padStart(2, '0') + ' ';
                if ((i + 1) % 16 === 0) hex += '\n';
            }
            return hex.trim();
        }

        // Detect if response contains binary data
        function isBinaryData(str) {
            for (let i = 0; i < str.length; i++) {
                const charCode = str.charCodeAt(i);
                if (charCode < 32 && charCode !== 9 && charCode !== 10 && charCode !== 13) {
                    return true;
                }
            }
            return false;
        }

        // Load IPs from discovered_ips.json
        async function loadDiscoveredIps() {
            try {
                const response = await fetch('./discovered_ips.json');
                if (!response.ok) {
                    throw new Error(`Failed to load discovered_ips.json: ${response.status}`);
                }
                discoveredIps = await response.json();

                document.getElementById('testAllBtn').disabled = false;
                updateSummary(`Loaded ${discoveredIps.length} IP addresses`);
                displayIpList();
            } catch (error) {
                alert(`Error loading IPs: ${error.message}`);
                console.error('Error loading IPs:', error);
            }
        }

        // Display the list of IPs to be tested
        function displayIpList() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '';

            discoveredIps.forEach(ip => {
                const resultItem = document.createElement('div');
                resultItem.className = 'result-item';
                resultItem.innerHTML = `
                    <div class="ip-address">${ip} <span class="status pending">PENDING</span></div>
                `;
                resultsDiv.appendChild(resultItem);
            });
        }

        // Test all IPs for CORS compatibility
        async function testAllIps() {
            const testBtn = document.getElementById('testAllBtn');
            testBtn.disabled = true;
            testBtn.textContent = 'Testing...';

            testResults = [];
            let completed = 0;

            for (let i = 0; i < discoveredIps.length; i++) {
                const ip = discoveredIps[i];
                updateProgress(completed, discoveredIps.length);
                updateSummary(`Testing ${ip}... (${completed + 1}/${discoveredIps.length})`);

                const result = await requestCodecInfo(ip);
                testResults.push({ ip, ...result });

                displayResult(ip, result, i);
                completed++;
                updateProgress(completed, discoveredIps.length);
            }

            testBtn.disabled = false;
            testBtn.textContent = 'Test All IPs';

            const successful = testResults.filter(r => r.success).length;
            const corsBlocked = testResults.filter(r => r.corsBlocked).length;
            updateSummary(`Testing complete: ${successful} successful, ${corsBlocked} CORS blocked, ${testResults.length - successful} failed`);
        }

        // Display individual test result
        function displayResult(ip, result, index) {
            const resultsDiv = document.getElementById('results');
            const resultItems = resultsDiv.children;

            if (resultItems[index]) {
                const statusSpan = resultItems[index].querySelector('.status');
                const showRawData = document.getElementById('showRawData').checked;
                const showDecodedData = document.getElementById('showDecodedData').checked;

                let statusClass = '';
                let statusText = '';
                let additionalContent = '';

                if (result.success) {
                    statusClass = 'success';
                    statusText = 'SUCCESS';

                    additionalContent = `
                        <div class="cors-info">
                            ✅ CORS is allowed! Server responded successfully.
                        </div>
                    `;

                    // Decode protobuf data if enabled
                    if (showDecodedData && result.data && protobufRoot) {
                        const decodingResult = decodeProtobufResponse(result.data);
                        if (decodingResult.success) {
                            additionalContent += `
                                <div class="decoded-data">Decoded Protobuf Data:
${JSON.stringify(decodingResult.decoded, null, 2)}</div>
                            `;
                        } else {
                            additionalContent += `
                                <div class="decoding-error">Protobuf Decoding Error: ${decodingResult.error}</div>
                            `;
                        }
                    }

                    if (showRawData && result.data) {
                        const displayData = isBinaryData(result.data)
                            ? `Binary data (${result.data.length} bytes):\n${binaryToHex(result.data)}`
                            : result.data;

                        additionalContent += `
                            <div class="response-data">Raw Response Data:\n${displayData}</div>
                        `;
                    }

                    if (result.headers) {
                        additionalContent += `
                            <div class="response-data">Response Headers:\n${JSON.stringify(result.headers, null, 2)}</div>
                        `;
                    }
                } else {
                    statusClass = 'error';
                    statusText = 'FAILED';

                    if (result.corsBlocked) {
                        additionalContent = `
                            <div class="cors-blocked">
                                ❌ CORS blocked! Browser prevented the request due to CORS policy.
                            </div>
                        `;
                    } else {
                        additionalContent = `
                            <div class="cors-info">
                                ℹ️ Request sent but server responded with error (CORS may still be allowed).
                            </div>
                        `;
                    }

                    additionalContent += `
                        <div class="error-message">Error: ${result.error}</div>
                    `;
                }

                statusSpan.className = `status ${statusClass}`;
                statusSpan.textContent = statusText;

                // Remove existing additional content
                const existing = resultItems[index].querySelector('.cors-info, .cors-blocked, .response-data, .error-message, .decoded-data, .decoding-error');
                if (existing) {
                    while (existing.nextSibling) {
                        existing.nextSibling.remove();
                    }
                    existing.remove();
                }

                resultItems[index].insertAdjacentHTML('beforeend', additionalContent);
            }
        }

        // Update progress bar
        function updateProgress(completed, total) {
            const percentage = total > 0 ? (completed / total) * 100 : 0;
            document.getElementById('progressBar').style.width = `${percentage}%`;
        }

        // Update summary text
        function updateSummary(text) {
            document.getElementById('summaryText').textContent = text;
        }

        // Clear all results
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testResults = [];
            updateProgress(0, 1);
            updateSummary('Results cleared');
        }

        // Event listeners
        document.getElementById('loadIpsBtn').addEventListener('click', loadDiscoveredIps);
        document.getElementById('testAllBtn').addEventListener('click', testAllIps);
        document.getElementById('clearBtn').addEventListener('click', clearResults);

        // Show/hide raw data toggle
        document.getElementById('showRawData').addEventListener('change', () => {
            // Re-display results with updated setting
            testResults.forEach((result, index) => {
                displayResult(result.ip, result, index);
            });
        });

        document.getElementById('showDecodedData').addEventListener('change', () => {
            // Re-display results with updated setting
            testResults.forEach((result, index) => {
                displayResult(result.ip, result, index);
            });
        });

        document.getElementById('showDecodedData').addEventListener('change', () => {
            // Re-display results with updated setting
            testResults.forEach((result, index) => {
                displayResult(result.ip, result, index);
            });
        });

        // Auto-load IPs and initialize protobuf on page load
        window.addEventListener('load', async () => {
            await initProtobuf();
            loadDiscoveredIps();
        });
    </script>
</body>

</html>